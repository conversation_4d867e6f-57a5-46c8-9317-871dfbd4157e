{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-f3946ac1.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { l as clamp, j as debounceEvent, i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nfunction getDecimalPlaces(n) {\n  if (n % 1 === 0) return 0;\n  return n.toString().split('.')[1].length;\n}\n/**\n * Fixes floating point rounding errors in a result by rounding\n * to the same specificity, or number of decimal places (*not*\n * significant figures) as provided reference numbers. If multiple\n * references are provided, the highest number of decimal places\n * between them will be used.\n *\n * The main use case is when numbers x and y are added to produce n,\n * but x and y are floats, so n may have rounding errors (such as\n * 3.1000000004 instead of 3.1). As long as only addition/subtraction\n * occurs between x and y, the specificity of the result will never\n * increase, so x and y should be passed in as the references.\n *\n * If multiplication, division, or other operations were used to\n * calculate n, the rounded result may have less specificity than\n * desired. For example, 1 / 3 = 0.33333(...), but\n * roundToMaxDecimalPlaces((1 / 3), 1, 3) will return 0, since both\n * 1 and 3 are whole numbers.\n *\n * Note that extremely precise reference numbers may lead to rounding\n * errors not being trimmed, due to the error result having the same or\n * fewer decimal places as the reference(s). This is acceptable as we\n * would not be able to tell the difference between a rounding error\n * and correct value in this case, but it does mean there is an implicit\n * precision limit. If precision that high is needed, it is recommended\n * to use a third party data type designed to handle floating point\n * errors instead.\n *\n * @param n The number to round.\n * @param references Number(s) used to calculate n, or that should otherwise\n * be used as a reference for the desired specificity.\n */\nfunction roundToMaxDecimalPlaces(n, ...references) {\n  const maxPlaces = Math.max(...references.map(r => getDecimalPlaces(r)));\n  return Number(n.toFixed(maxPlaces));\n}\nconst rangeIosCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}@supports (inset-inline-start: 0){.range-knob-handle{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-knob-handle{left:0}:host-context([dir=rtl]) .range-knob-handle{left:unset;right:unset;right:0}[dir=rtl] .range-knob-handle{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}@supports (inset-inline-start: 0){.range-bar-container{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-bar-container{left:0}:host-context([dir=rtl]) .range-bar-container{left:unset;right:unset;right:0}[dir=rtl] .range-bar-container{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}@supports (inset-inline-start: 0){.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}}@supports not (inset-inline-start: 0){.range-knob{left:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}[dir=rtl] .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}}}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:#ffffff;--knob-box-shadow:0px 0.5px 4px rgba(0, 0, 0, 0.12), 0px 6px 13px rgba(0, 0, 0, 0.12);--knob-size:26px;--bar-height:4px;--bar-background:var(--ion-color-step-900, #e6e6e6);--bar-background-active:var(--ion-color-primary, #3880ff);--bar-border-radius:2px;--height:42px}:host(.legacy-range){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host(.range-item-start-adjustment){-webkit-padding-start:24px;padding-inline-start:24px}:host(.range-item-end-adjustment){-webkit-padding-end:24px;padding-inline-end:24px}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-tick-active{background:var(--ion-color-base)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:calc(8px + 0.75rem)}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:calc(8px + 0.75rem)}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-bar-active.has-ticks{border-radius:0;-webkit-margin-start:-2px;margin-inline-start:-2px;-webkit-margin-end:-2px;margin-inline-end:-2px}.range-tick{-webkit-margin-start:-2px;margin-inline-start:-2px;border-radius:0;position:absolute;top:17px;width:4px;height:8px;background:var(--ion-color-step-900, #e6e6e6);pointer-events:none}.range-tick-active{background:var(--bar-background-active)}.range-pin{-webkit-transform:translate3d(0,  100%,  0) scale(0.01);transform:translate3d(0,  100%,  0) scale(0.01);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;min-width:28px;-webkit-transition:-webkit-transform 120ms ease;transition:-webkit-transform 120ms ease;transition:transform 120ms ease;transition:transform 120ms ease, -webkit-transform 120ms ease;background:transparent;color:var(--ion-text-color, #000);font-size:0.75rem;text-align:center}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 11px), 0) scale(1);transform:translate3d(0, calc(-100% + 11px), 0) scale(1)}:host(.range-disabled){opacity:0.3}\";\nconst IonRangeIosStyle0 = rangeIosCss;\nconst rangeMdCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}@supports (inset-inline-start: 0){.range-knob-handle{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-knob-handle{left:0}:host-context([dir=rtl]) .range-knob-handle{left:unset;right:unset;right:0}[dir=rtl] .range-knob-handle{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}@supports (inset-inline-start: 0){.range-bar-container{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-bar-container{left:0}:host-context([dir=rtl]) .range-bar-container{left:unset;right:unset;right:0}[dir=rtl] .range-bar-container{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}@supports (inset-inline-start: 0){.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}}@supports not (inset-inline-start: 0){.range-knob{left:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}[dir=rtl] .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}}}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:var(--bar-background-active);--knob-box-shadow:none;--knob-size:18px;--bar-height:2px;--bar-background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.26);--bar-background-active:var(--ion-color-primary, #3880ff);--bar-border-radius:0;--height:42px;--pin-background:var(--ion-color-primary, #3880ff);--pin-color:var(--ion-color-primary-contrast, #fff)}:host(.legacy-range) ::slotted([slot=label]){font-size:initial}:host(:not(.legacy-range)) ::slotted(:not(ion-icon)[slot=start]),:host(:not(.legacy-range)) ::slotted(:not(ion-icon)[slot=end]),:host(:not(.legacy-range)) .native-wrapper{font-size:0.75rem}:host(.legacy-range){-webkit-padding-start:14px;padding-inline-start:14px;-webkit-padding-end:14px;padding-inline-end:14px;padding-top:8px;padding-bottom:8px;font-size:0.75rem}:host(.range-item-start-adjustment){-webkit-padding-start:18px;padding-inline-start:18px}:host(.range-item-end-adjustment){-webkit-padding-end:18px;padding-inline-end:18px}:host(.ion-color) .range-bar{background:rgba(var(--ion-color-base-rgb), 0.26)}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-knob,:host(.ion-color) .range-knob::before,:host(.ion-color) .range-pin,:host(.ion-color) .range-pin::before,:host(.ion-color) .range-tick{background:var(--ion-color-base);color:var(--ion-color-contrast)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:1.75rem}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:1.75rem}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-knob{-webkit-transform:scale(0.67);transform:scale(0.67);-webkit-transition-duration:120ms;transition-duration:120ms;-webkit-transition-property:background-color, border, -webkit-transform;transition-property:background-color, border, -webkit-transform;transition-property:transform, background-color, border;transition-property:transform, background-color, border, -webkit-transform;-webkit-transition-timing-function:ease;transition-timing-function:ease;z-index:2}.range-knob::before{border-radius:50%;position:absolute;width:var(--knob-size);height:var(--knob-size);-webkit-transform:scale(1);transform:scale(1);-webkit-transition:0.267s cubic-bezier(0, 0, 0.58, 1);transition:0.267s cubic-bezier(0, 0, 0.58, 1);background:var(--knob-background);content:\\\"\\\";opacity:0.13;pointer-events:none}@supports (inset-inline-start: 0){.range-knob::before{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-knob::before{left:0}:host-context([dir=rtl]) .range-knob::before{left:unset;right:unset;right:0}[dir=rtl] .range-knob::before{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-knob::before:dir(rtl){left:unset;right:unset;right:0}}}.range-tick{position:absolute;top:calc((var(--height) - var(--bar-height)) / 2);width:var(--bar-height);height:var(--bar-height);background:var(--bar-background-active);z-index:1;pointer-events:none}.range-tick-active{background:transparent}.range-pin{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;border-radius:50%;-webkit-transform:translate3d(0,  0,  0) scale(0.01);transform:translate3d(0,  0,  0) scale(0.01);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:1.75rem;height:1.75rem;-webkit-transition:background 120ms ease, -webkit-transform 120ms ease;transition:background 120ms ease, -webkit-transform 120ms ease;transition:transform 120ms ease, background 120ms ease;transition:transform 120ms ease, background 120ms ease, -webkit-transform 120ms ease;background:var(--pin-background);color:var(--pin-color)}.range-pin::before{bottom:-1px;-webkit-margin-start:-13px;margin-inline-start:-13px;border-radius:50% 50% 50% 0;position:absolute;width:26px;height:26px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transition:background 120ms ease;transition:background 120ms ease;background:var(--pin-background);content:\\\"\\\";z-index:-1}@supports (inset-inline-start: 0){.range-pin::before{inset-inline-start:50%}}@supports not (inset-inline-start: 0){.range-pin::before{left:50%}:host-context([dir=rtl]) .range-pin::before{left:unset;right:unset;right:50%}[dir=rtl] .range-pin::before{left:unset;right:unset;right:50%}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset;right:unset;right:50%}}}:host-context([dir=rtl]) .range-pin::before{left:unset}[dir=rtl] .range-pin::before{left:unset}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset}}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 4px), 0) scale(1);transform:translate3d(0, calc(-100% + 4px), 0) scale(1)}@media (any-hover: hover){.range-knob-handle:hover .range-knob:before{-webkit-transform:scale(2);transform:scale(2);opacity:0.13}}.range-knob-handle.ion-activated .range-knob:before,.range-knob-handle.ion-focused .range-knob:before,.range-knob-handle.range-knob-pressed .range-knob:before{-webkit-transform:scale(2);transform:scale(2)}.range-knob-handle.ion-focused .range-knob::before{opacity:0.13}.range-knob-handle.ion-activated .range-knob::before,.range-knob-handle.range-knob-pressed .range-knob::before{opacity:0.25}:host(:not(.range-has-pin)) .range-knob-pressed .range-knob,:host(:not(.range-has-pin)) .range-knob-handle.ion-focused .range-knob{-webkit-transform:scale(1);transform:scale(1)}:host(.range-disabled) .range-bar-active,:host(.range-disabled) .range-bar,:host(.range-disabled) .range-tick{background-color:var(--ion-color-step-250, #bfbfbf)}:host(.range-disabled) .range-knob{-webkit-transform:scale(0.55);transform:scale(0.55);outline:5px solid #fff;background-color:var(--ion-color-step-250, #bfbfbf)}:host(.range-disabled) .label-text-wrapper,:host(.range-disabled) ::slotted([slot=start]),:host(.range-disabled) ::slotted([slot=end]){opacity:0.38}\";\nconst IonRangeMdStyle0 = rangeMdCss;\nconst Range = class {\n  constructor(hostRef) {\n    var _this = this;\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionInput = createEvent(this, \"ionInput\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionKnobMoveStart = createEvent(this, \"ionKnobMoveStart\", 7);\n    this.ionKnobMoveEnd = createEvent(this, \"ionKnobMoveEnd\", 7);\n    this.rangeId = `ion-r-${rangeIds++}`;\n    this.didLoad = false;\n    this.noUpdate = false;\n    this.hasFocus = false;\n    this.inheritedAttributes = {};\n    this.contentEl = null;\n    this.initialContentScrollY = true;\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    this.clampBounds = value => {\n      return clamp(this.min, value, this.max);\n    };\n    this.ensureValueInBounds = value => {\n      if (this.dualKnobs) {\n        return {\n          lower: this.clampBounds(value.lower),\n          upper: this.clampBounds(value.upper)\n        };\n      } else {\n        return this.clampBounds(value);\n      }\n    };\n    this.setupGesture = /*#__PURE__*/_asyncToGenerator(function* () {\n      const rangeSlider = _this.rangeSlider;\n      if (rangeSlider) {\n        _this.gesture = (yield import('./index-2cf77112.js')).createGesture({\n          el: rangeSlider,\n          gestureName: 'range',\n          gesturePriority: 100,\n          /**\n           * Provide a threshold since the drag movement\n           * might be a user scrolling the view.\n           * If this is true, then the range\n           * should not move.\n           */\n          threshold: 10,\n          onStart: () => _this.onStart(),\n          onMove: ev => _this.onMove(ev),\n          onEnd: ev => _this.onEnd(ev)\n        });\n        _this.gesture.enable(!_this.disabled);\n      }\n    });\n    this.handleKeyboard = (knob, isIncrease) => {\n      const {\n        ensureValueInBounds\n      } = this;\n      let step = this.step;\n      step = step > 0 ? step : 1;\n      step = step / (this.max - this.min);\n      if (!isIncrease) {\n        step *= -1;\n      }\n      if (knob === 'A') {\n        this.ratioA = clamp(0, this.ratioA + step, 1);\n      } else {\n        this.ratioB = clamp(0, this.ratioB + step, 1);\n      }\n      this.ionKnobMoveStart.emit({\n        value: ensureValueInBounds(this.value)\n      });\n      this.updateValue();\n      this.emitValueChange();\n      this.ionKnobMoveEnd.emit({\n        value: ensureValueInBounds(this.value)\n      });\n    };\n    this.onBlur = () => {\n      if (this.hasFocus) {\n        this.hasFocus = false;\n        this.ionBlur.emit();\n        this.emitStyle();\n      }\n    };\n    this.onFocus = () => {\n      if (!this.hasFocus) {\n        this.hasFocus = true;\n        this.ionFocus.emit();\n        this.emitStyle();\n      }\n    };\n    this.ratioA = 0;\n    this.ratioB = 0;\n    this.pressedKnob = undefined;\n    this.color = undefined;\n    this.debounce = undefined;\n    this.name = this.rangeId;\n    this.label = undefined;\n    this.dualKnobs = false;\n    this.min = 0;\n    this.max = 100;\n    this.pin = false;\n    this.pinFormatter = value => Math.round(value);\n    this.snaps = false;\n    this.step = 1;\n    this.ticks = true;\n    this.activeBarStart = undefined;\n    this.disabled = false;\n    this.value = 0;\n    this.labelPlacement = 'start';\n    this.legacy = undefined;\n  }\n  debounceChanged() {\n    const {\n      ionInput,\n      debounce,\n      originalIonInput\n    } = this;\n    /**\n     * If debounce is undefined, we have to manually revert the ionInput emitter in case\n     * debounce used to be set to a number. Otherwise, the event would stay debounced.\n     */\n    this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n  }\n  minChanged() {\n    if (!this.noUpdate) {\n      this.updateRatio();\n    }\n  }\n  maxChanged() {\n    if (!this.noUpdate) {\n      this.updateRatio();\n    }\n  }\n  activeBarStartChanged() {\n    const {\n      activeBarStart\n    } = this;\n    if (activeBarStart !== undefined) {\n      if (activeBarStart > this.max) {\n        printIonWarning(`Range: The value of activeBarStart (${activeBarStart}) is greater than the max (${this.max}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n        this.activeBarStart = this.max;\n      } else if (activeBarStart < this.min) {\n        printIonWarning(`Range: The value of activeBarStart (${activeBarStart}) is less than the min (${this.min}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n        this.activeBarStart = this.min;\n      }\n    }\n  }\n  disabledChanged() {\n    if (this.gesture) {\n      this.gesture.enable(!this.disabled);\n    }\n    this.emitStyle();\n  }\n  valueChanged() {\n    if (!this.noUpdate) {\n      this.updateRatio();\n    }\n  }\n  componentWillLoad() {\n    /**\n     * If user has custom ID set then we should\n     * not assign the default incrementing ID.\n     */\n    if (this.el.hasAttribute('id')) {\n      this.rangeId = this.el.getAttribute('id');\n    }\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    this.originalIonInput = this.ionInput;\n    this.setupGesture();\n    this.updateRatio();\n    this.didLoad = true;\n  }\n  connectedCallback() {\n    const {\n      el\n    } = this;\n    this.legacyFormController = createLegacyFormController(el);\n    this.updateRatio();\n    this.debounceChanged();\n    this.disabledChanged();\n    this.activeBarStartChanged();\n    /**\n     * If we have not yet rendered\n     * ion-range, then rangeSlider is not defined.\n     * But if we are moving ion-range via appendChild,\n     * then rangeSlider will be defined.\n     */\n    if (this.didLoad) {\n      this.setupGesture();\n    }\n    this.contentEl = findClosestIonContent(this.el);\n  }\n  disconnectedCallback() {\n    if (this.gesture) {\n      this.gesture.destroy();\n      this.gesture = undefined;\n    }\n  }\n  getValue() {\n    var _a;\n    const value = (_a = this.value) !== null && _a !== void 0 ? _a : 0;\n    if (this.dualKnobs) {\n      if (typeof value === 'object') {\n        return value;\n      }\n      return {\n        lower: 0,\n        upper: value\n      };\n    } else {\n      if (typeof value === 'object') {\n        return value.upper;\n      }\n      return value;\n    }\n  }\n  // TODO FW-2997 remove this\n  emitStyle() {\n    if (this.legacyFormController.hasLegacyControl()) {\n      this.ionStyle.emit({\n        interactive: true,\n        'interactive-disabled': this.disabled,\n        // TODO(FW-2997): remove this\n        legacy: !!this.legacy\n      });\n    }\n  }\n  /**\n   * Emits an `ionChange` event.\n   *\n   * This API should be called for user committed changes.\n   * This API should not be used for external value changes.\n   */\n  emitValueChange() {\n    this.value = this.ensureValueInBounds(this.value);\n    this.ionChange.emit({\n      value: this.value\n    });\n  }\n  /**\n   * The value should be updated on touch end or\n   * when the component is being dragged.\n   * This follows the native behavior of mobile devices.\n   *\n   * For example: When the user lifts their finger from the\n   * screen after tapping the bar or dragging the bar or knob.\n   */\n  onStart() {\n    this.ionKnobMoveStart.emit({\n      value: this.ensureValueInBounds(this.value)\n    });\n  }\n  /**\n   * The value should be updated while dragging the\n   * bar or knob.\n   *\n   * While the user is dragging, the view\n   * should not scroll. This is to prevent the user from\n   * feeling disoriented while dragging.\n   *\n   * The user can scroll on the view if the knob or\n   * bar is not being dragged.\n   *\n   * @param detail The details of the gesture event.\n   */\n  onMove(detail) {\n    const {\n      contentEl,\n      pressedKnob\n    } = this;\n    const currentX = detail.currentX;\n    /**\n     * Since the user is dragging on the bar or knob, the view should not scroll.\n     *\n     * This only needs to be done once.\n     */\n    if (contentEl && this.initialContentScrollY === undefined) {\n      this.initialContentScrollY = disableContentScrollY(contentEl);\n    }\n    /**\n     * The `pressedKnob` can be undefined if the user just\n     * started dragging the knob.\n     *\n     * This is necessary to determine which knob the user is dragging,\n     * especially when it's a dual knob.\n     * Plus, it determines when to apply certain styles.\n     *\n     * This only needs to be done once since the knob won't change\n     * while the user is dragging.\n     */\n    if (pressedKnob === undefined) {\n      this.setPressedKnob(currentX);\n    }\n    this.update(currentX);\n  }\n  /**\n   * The value should be updated on touch end:\n   * - When the user lifts their finger from the screen after\n   * tapping the bar.\n   *\n   * @param detail The details of the gesture or mouse event.\n   */\n  onEnd(detail) {\n    const {\n      contentEl,\n      initialContentScrollY\n    } = this;\n    const currentX = detail.currentX || detail.clientX;\n    /**\n     * The `pressedKnob` can be undefined if the user never\n     * dragged the knob. They just tapped on the bar.\n     *\n     * This is necessary to determine which knob the user is changing,\n     * especially when it's a dual knob.\n     * Plus, it determines when to apply certain styles.\n     */\n    if (this.pressedKnob === undefined) {\n      this.setPressedKnob(currentX);\n    }\n    /**\n     * The user is no longer dragging the bar or\n     * knob (if they were dragging it).\n     *\n     * The user can now scroll on the view in the next gesture event.\n     */\n    if (contentEl && initialContentScrollY !== undefined) {\n      resetContentScrollY(contentEl, initialContentScrollY);\n    }\n    // update the active knob's position\n    this.update(currentX);\n    /**\n     * Reset the pressed knob to undefined since the user\n     * may start dragging a different knob in the next gesture event.\n     */\n    this.pressedKnob = undefined;\n    this.emitValueChange();\n    this.ionKnobMoveEnd.emit({\n      value: this.ensureValueInBounds(this.value)\n    });\n  }\n  update(currentX) {\n    // figure out where the pointer is currently at\n    // update the knob being interacted with\n    const rect = this.rect;\n    let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n    if (isRTL(this.el)) {\n      ratio = 1 - ratio;\n    }\n    if (this.snaps) {\n      // snaps the ratio to the current value\n      ratio = valueToRatio(ratioToValue(ratio, this.min, this.max, this.step), this.min, this.max);\n    }\n    // update which knob is pressed\n    if (this.pressedKnob === 'A') {\n      this.ratioA = ratio;\n    } else {\n      this.ratioB = ratio;\n    }\n    // Update input value\n    this.updateValue();\n  }\n  setPressedKnob(currentX) {\n    const rect = this.rect = this.rangeSlider.getBoundingClientRect();\n    // figure out which knob they started closer to\n    let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n    if (isRTL(this.el)) {\n      ratio = 1 - ratio;\n    }\n    this.pressedKnob = !this.dualKnobs || Math.abs(this.ratioA - ratio) < Math.abs(this.ratioB - ratio) ? 'A' : 'B';\n    this.setFocus(this.pressedKnob);\n  }\n  get valA() {\n    return ratioToValue(this.ratioA, this.min, this.max, this.step);\n  }\n  get valB() {\n    return ratioToValue(this.ratioB, this.min, this.max, this.step);\n  }\n  get ratioLower() {\n    if (this.dualKnobs) {\n      return Math.min(this.ratioA, this.ratioB);\n    }\n    const {\n      activeBarStart\n    } = this;\n    if (activeBarStart == null) {\n      return 0;\n    }\n    return valueToRatio(activeBarStart, this.min, this.max);\n  }\n  get ratioUpper() {\n    if (this.dualKnobs) {\n      return Math.max(this.ratioA, this.ratioB);\n    }\n    return this.ratioA;\n  }\n  updateRatio() {\n    const value = this.getValue();\n    const {\n      min,\n      max\n    } = this;\n    if (this.dualKnobs) {\n      this.ratioA = valueToRatio(value.lower, min, max);\n      this.ratioB = valueToRatio(value.upper, min, max);\n    } else {\n      this.ratioA = valueToRatio(value, min, max);\n    }\n  }\n  updateValue() {\n    this.noUpdate = true;\n    const {\n      valA,\n      valB\n    } = this;\n    this.value = !this.dualKnobs ? valA : {\n      lower: Math.min(valA, valB),\n      upper: Math.max(valA, valB)\n    };\n    this.ionInput.emit({\n      value: this.value\n    });\n    this.noUpdate = false;\n  }\n  setFocus(knob) {\n    if (this.el.shadowRoot) {\n      const knobEl = this.el.shadowRoot.querySelector(knob === 'A' ? '.range-knob-a' : '.range-knob-b');\n      if (knobEl) {\n        knobEl.focus();\n      }\n    }\n  }\n  // TODO FW-2997 remove this\n  renderLegacyRange() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-range now requires providing a label with either the label slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-range><div slot=\"label\">Volume</div></ion-range>\nExample with aria-label: <ion-range aria-label=\"Volume\"></ion-range>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-range is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new range syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const {\n      el,\n      pressedKnob,\n      disabled,\n      pin,\n      rangeId\n    } = this;\n    const mode = getIonMode(this);\n    renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n    return h(Host, {\n      onFocusin: this.onFocus,\n      onFocusout: this.onBlur,\n      id: rangeId,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'range-disabled': disabled,\n        'range-pressed': pressedKnob !== undefined,\n        'range-has-pin': pin,\n        'legacy-range': true\n      })\n    }, h(\"slot\", {\n      name: \"start\"\n    }), this.renderRangeSlider(), h(\"slot\", {\n      name: \"end\"\n    }));\n  }\n  /**\n   * Returns true if content was passed to the \"start\" slot\n   */\n  get hasStartSlotContent() {\n    return this.el.querySelector('[slot=\"start\"]') !== null;\n  }\n  /**\n   * Returns true if content was passed to the \"end\" slot\n   */\n  get hasEndSlotContent() {\n    return this.el.querySelector('[slot=\"end\"]') !== null;\n  }\n  renderRange() {\n    const {\n      disabled,\n      el,\n      hasLabel,\n      rangeId,\n      pin,\n      pressedKnob,\n      labelPlacement,\n      label\n    } = this;\n    const inItem = hostContext('ion-item', el);\n    /**\n     * If there is no start content then the knob at\n     * the min value will be cut off by the item margin.\n     */\n    const hasStartContent = hasLabel && (labelPlacement === 'start' || labelPlacement === 'fixed') || this.hasStartSlotContent;\n    const needsStartAdjustment = inItem && !hasStartContent;\n    /**\n     * If there is no end content then the knob at\n     * the max value will be cut off by the item margin.\n     */\n    const hasEndContent = hasLabel && labelPlacement === 'end' || this.hasEndSlotContent;\n    const needsEndAdjustment = inItem && !hasEndContent;\n    const mode = getIonMode(this);\n    renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n    return h(Host, {\n      onFocusin: this.onFocus,\n      onFocusout: this.onBlur,\n      id: rangeId,\n      class: createColorClasses(this.color, {\n        [mode]: true,\n        'in-item': inItem,\n        'range-disabled': disabled,\n        'range-pressed': pressedKnob !== undefined,\n        'range-has-pin': pin,\n        [`range-label-placement-${labelPlacement}`]: true,\n        'range-item-start-adjustment': needsStartAdjustment,\n        'range-item-end-adjustment': needsEndAdjustment\n      })\n    }, h(\"label\", {\n      class: \"range-wrapper\",\n      id: \"range-label\"\n    }, h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': !hasLabel\n      },\n      part: \"label\"\n    }, label !== undefined ? h(\"div\", {\n      class: \"label-text\"\n    }, label) : h(\"slot\", {\n      name: \"label\"\n    })), h(\"div\", {\n      class: \"native-wrapper\"\n    }, h(\"slot\", {\n      name: \"start\"\n    }), this.renderRangeSlider(), h(\"slot\", {\n      name: \"end\"\n    }))));\n  }\n  get hasLabel() {\n    return this.label !== undefined || this.el.querySelector('[slot=\"label\"]') !== null;\n  }\n  renderRangeSlider() {\n    var _a;\n    const {\n      min,\n      max,\n      step,\n      el,\n      handleKeyboard,\n      pressedKnob,\n      disabled,\n      pin,\n      ratioLower,\n      ratioUpper,\n      inheritedAttributes,\n      rangeId,\n      pinFormatter\n    } = this;\n    /**\n     * Look for external label, ion-label, or aria-labelledby.\n     * If none, see if user placed an aria-label on the host\n     * and use that instead.\n     */\n    let {\n      labelText\n    } = getAriaLabel(el, rangeId);\n    if (labelText === undefined || labelText === null) {\n      labelText = inheritedAttributes['aria-label'];\n    }\n    let barStart = `${ratioLower * 100}%`;\n    let barEnd = `${100 - ratioUpper * 100}%`;\n    const rtl = isRTL(this.el);\n    const start = rtl ? 'right' : 'left';\n    const end = rtl ? 'left' : 'right';\n    const tickStyle = tick => {\n      return {\n        [start]: tick[start]\n      };\n    };\n    if (this.dualKnobs === false) {\n      /**\n       * When the value is less than the activeBarStart or the min value,\n       * the knob will display at the start of the active bar.\n       */\n      if (this.valA < ((_a = this.activeBarStart) !== null && _a !== void 0 ? _a : this.min)) {\n        /**\n         * Sets the bar positions relative to the upper and lower limits.\n         * Converts the ratio values into percentages, used as offsets for left/right styles.\n         *\n         * The ratioUpper refers to the knob position on the bar.\n         * The ratioLower refers to the end position of the active bar (the value).\n         */\n        barStart = `${ratioUpper * 100}%`;\n        barEnd = `${100 - ratioLower * 100}%`;\n      } else {\n        /**\n         * Otherwise, the knob will display at the end of the active bar.\n         *\n         * The ratioLower refers to the start position of the active bar (the value).\n         * The ratioUpper refers to the knob position on the bar.\n         */\n        barStart = `${ratioLower * 100}%`;\n        barEnd = `${100 - ratioUpper * 100}%`;\n      }\n    }\n    const barStyle = {\n      [start]: barStart,\n      [end]: barEnd\n    };\n    const ticks = [];\n    if (this.snaps && this.ticks) {\n      for (let value = min; value <= max; value += step) {\n        const ratio = valueToRatio(value, min, max);\n        const ratioMin = Math.min(ratioLower, ratioUpper);\n        const ratioMax = Math.max(ratioLower, ratioUpper);\n        const tick = {\n          ratio,\n          /**\n           * Sets the tick mark as active when the tick is between the min bounds and the knob.\n           * When using activeBarStart, the tick mark will be active between the knob and activeBarStart.\n           */\n          active: ratio >= ratioMin && ratio <= ratioMax\n        };\n        tick[start] = `${ratio * 100}%`;\n        ticks.push(tick);\n      }\n    }\n    let labelledBy;\n    if (!this.legacyFormController.hasLegacyControl() && this.hasLabel) {\n      labelledBy = 'range-label';\n    }\n    return h(\"div\", {\n      class: \"range-slider\",\n      ref: rangeEl => this.rangeSlider = rangeEl,\n      /**\n       * Since the gesture has a threshold, the value\n       * won't change until the user has dragged past\n       * the threshold. This is to prevent the range\n       * from moving when the user is scrolling.\n       *\n       * This results in the value not being updated\n       * and the event emitters not being triggered\n       * if the user taps on the range. This is why\n       * we need to listen for the \"pointerUp\" event.\n       */\n      onPointerUp: ev => {\n        /**\n         * If the user drags the knob on the web\n         * version (does not occur on mobile),\n         * the \"pointerUp\" event will be triggered\n         * along with the gesture's events.\n         * This leads to duplicate events.\n         *\n         * By checking if the pressedKnob is undefined,\n         * we can determine if the \"pointerUp\" event was\n         * triggered by a tap or a drag. If it was\n         * dragged, the pressedKnob will be defined.\n         */\n        if (this.pressedKnob === undefined) {\n          this.onStart();\n          this.onEnd(ev);\n        }\n      }\n    }, ticks.map(tick => h(\"div\", {\n      style: tickStyle(tick),\n      role: \"presentation\",\n      class: {\n        'range-tick': true,\n        'range-tick-active': tick.active\n      },\n      part: tick.active ? 'tick-active' : 'tick'\n    })), h(\"div\", {\n      class: \"range-bar-container\"\n    }, h(\"div\", {\n      class: \"range-bar\",\n      role: \"presentation\",\n      part: \"bar\"\n    }), h(\"div\", {\n      class: {\n        'range-bar': true,\n        'range-bar-active': true,\n        'has-ticks': ticks.length > 0\n      },\n      role: \"presentation\",\n      style: barStyle,\n      part: \"bar-active\"\n    })), renderKnob(rtl, {\n      knob: 'A',\n      pressed: pressedKnob === 'A',\n      value: this.valA,\n      ratio: this.ratioA,\n      pin,\n      pinFormatter,\n      disabled,\n      handleKeyboard,\n      min,\n      max,\n      labelText,\n      labelledBy\n    }), this.dualKnobs && renderKnob(rtl, {\n      knob: 'B',\n      pressed: pressedKnob === 'B',\n      value: this.valB,\n      ratio: this.ratioB,\n      pin,\n      pinFormatter,\n      disabled,\n      handleKeyboard,\n      min,\n      max,\n      labelText,\n      labelledBy\n    }));\n  }\n  render() {\n    const {\n      legacyFormController\n    } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacyRange() : this.renderRange();\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"debounce\": [\"debounceChanged\"],\n      \"min\": [\"minChanged\"],\n      \"max\": [\"maxChanged\"],\n      \"activeBarStart\": [\"activeBarStartChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"value\": [\"valueChanged\"]\n    };\n  }\n};\nconst renderKnob = (rtl, {\n  knob,\n  value,\n  ratio,\n  min,\n  max,\n  disabled,\n  pressed,\n  pin,\n  handleKeyboard,\n  labelText,\n  labelledBy,\n  pinFormatter\n}) => {\n  const start = rtl ? 'right' : 'left';\n  const knobStyle = () => {\n    const style = {};\n    style[start] = `${ratio * 100}%`;\n    return style;\n  };\n  return h(\"div\", {\n    onKeyDown: ev => {\n      const key = ev.key;\n      if (key === 'ArrowLeft' || key === 'ArrowDown') {\n        handleKeyboard(knob, false);\n        ev.preventDefault();\n        ev.stopPropagation();\n      } else if (key === 'ArrowRight' || key === 'ArrowUp') {\n        handleKeyboard(knob, true);\n        ev.preventDefault();\n        ev.stopPropagation();\n      }\n    },\n    class: {\n      'range-knob-handle': true,\n      'range-knob-a': knob === 'A',\n      'range-knob-b': knob === 'B',\n      'range-knob-pressed': pressed,\n      'range-knob-min': value === min,\n      'range-knob-max': value === max,\n      'ion-activatable': true,\n      'ion-focusable': true\n    },\n    style: knobStyle(),\n    role: \"slider\",\n    tabindex: disabled ? -1 : 0,\n    \"aria-label\": labelledBy === undefined ? labelText : null,\n    \"aria-labelledby\": labelledBy !== undefined ? labelledBy : null,\n    \"aria-valuemin\": min,\n    \"aria-valuemax\": max,\n    \"aria-disabled\": disabled ? 'true' : null,\n    \"aria-valuenow\": value\n  }, pin && h(\"div\", {\n    class: \"range-pin\",\n    role: \"presentation\",\n    part: \"pin\"\n  }, pinFormatter(value)), h(\"div\", {\n    class: \"range-knob\",\n    role: \"presentation\",\n    part: \"knob\"\n  }));\n};\nconst ratioToValue = (ratio, min, max, step) => {\n  let value = (max - min) * ratio;\n  if (step > 0) {\n    // round to nearest multiple of step, then add min\n    value = Math.round(value / step) * step + min;\n  }\n  const clampedValue = clamp(min, value, max);\n  return roundToMaxDecimalPlaces(clampedValue, min, max, step);\n};\nconst valueToRatio = (value, min, max) => {\n  return clamp(0, (value - min) / (max - min), 1);\n};\nlet rangeIds = 0;\nRange.style = {\n  ios: IonRangeIosStyle0,\n  md: IonRangeMdStyle0\n};\nexport { Range as ion_range };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "findClosestIonContent", "disableContentScrollY", "resetContentScrollY", "c", "createLegacyFormController", "l", "clamp", "j", "debounceEvent", "i", "inheritAriaAttributes", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "isRTL", "createColorClasses", "hostContext", "b", "getIonMode", "getDecimalPlaces", "n", "toString", "split", "length", "roundToMaxDecimalPlaces", "references", "maxPlaces", "Math", "max", "map", "Number", "toFixed", "rangeIosCss", "IonRangeIosStyle0", "rangeMdCss", "IonRangeMdStyle0", "Range", "constructor", "hostRef", "_this", "ionChange", "ionInput", "ionStyle", "ionFocus", "ionBlur", "ionKnobMoveStart", "ionKnobMoveEnd", "rangeId", "rangeIds", "didLoad", "noUpdate", "hasFocus", "inheritedAttributes", "contentEl", "initialContentScrollY", "hasLoggedDeprecationWarning", "clampBounds", "value", "min", "ensureValueInBounds", "dualKnobs", "lower", "upper", "setupGesture", "_asyncToGenerator", "rangeSlider", "gesture", "createGesture", "el", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "onStart", "onMove", "ev", "onEnd", "enable", "disabled", "handleKeyboard", "knob", "isIncrease", "step", "ratioA", "ratioB", "emit", "updateValue", "emitValueChange", "onBlur", "emitStyle", "onFocus", "pressedKnob", "undefined", "color", "debounce", "name", "label", "pin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "round", "snaps", "ticks", "activeBarStart", "labelPlacement", "legacy", "debounce<PERSON><PERSON>ed", "originalIonInput", "minC<PERSON>ed", "updateRatio", "max<PERSON><PERSON>ed", "activeBarStartChanged", "disabled<PERSON><PERSON>ed", "valueChanged", "componentWillLoad", "hasAttribute", "getAttribute", "componentDidLoad", "connectedCallback", "legacyFormController", "disconnectedCallback", "destroy", "getValue", "_a", "hasLegacyControl", "interactive", "detail", "currentX", "setPressedKnob", "update", "clientX", "rect", "ratio", "left", "width", "valueToRatio", "ratioToValue", "getBoundingClientRect", "abs", "setFocus", "valA", "valB", "ratioLower", "ratioUpper", "shadowRoot", "knobEl", "querySelector", "focus", "renderLegacyRange", "mode", "JSON", "stringify", "onFocusin", "onFocusout", "id", "class", "renderRangeSlider", "hasStartSlotContent", "hasEndSlotContent", "renderRange", "<PERSON><PERSON><PERSON><PERSON>", "inItem", "hasStartContent", "needsStartAdjustment", "hasEndContent", "needsEndAdjustment", "part", "labelText", "barStart", "barEnd", "rtl", "start", "end", "tickStyle", "tick", "barStyle", "ratioMin", "ratioMax", "active", "push", "labelledBy", "ref", "rangeEl", "onPointerUp", "style", "role", "renderKnob", "pressed", "render", "watchers", "knobStyle", "onKeyDown", "key", "preventDefault", "stopPropagation", "tabindex", "clampedValue", "ios", "md", "ion_range"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/ion-range.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { f as findClosestIonContent, d as disableContentScrollY, r as resetContentScrollY } from './index-f3946ac1.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { l as clamp, j as debounceEvent, i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { i as isRTL } from './dir-babeabeb.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nfunction getDecimalPlaces(n) {\n    if (n % 1 === 0)\n        return 0;\n    return n.toString().split('.')[1].length;\n}\n/**\n * Fixes floating point rounding errors in a result by rounding\n * to the same specificity, or number of decimal places (*not*\n * significant figures) as provided reference numbers. If multiple\n * references are provided, the highest number of decimal places\n * between them will be used.\n *\n * The main use case is when numbers x and y are added to produce n,\n * but x and y are floats, so n may have rounding errors (such as\n * 3.1000000004 instead of 3.1). As long as only addition/subtraction\n * occurs between x and y, the specificity of the result will never\n * increase, so x and y should be passed in as the references.\n *\n * If multiplication, division, or other operations were used to\n * calculate n, the rounded result may have less specificity than\n * desired. For example, 1 / 3 = 0.33333(...), but\n * roundToMaxDecimalPlaces((1 / 3), 1, 3) will return 0, since both\n * 1 and 3 are whole numbers.\n *\n * Note that extremely precise reference numbers may lead to rounding\n * errors not being trimmed, due to the error result having the same or\n * fewer decimal places as the reference(s). This is acceptable as we\n * would not be able to tell the difference between a rounding error\n * and correct value in this case, but it does mean there is an implicit\n * precision limit. If precision that high is needed, it is recommended\n * to use a third party data type designed to handle floating point\n * errors instead.\n *\n * @param n The number to round.\n * @param references Number(s) used to calculate n, or that should otherwise\n * be used as a reference for the desired specificity.\n */\nfunction roundToMaxDecimalPlaces(n, ...references) {\n    const maxPlaces = Math.max(...references.map((r) => getDecimalPlaces(r)));\n    return Number(n.toFixed(maxPlaces));\n}\n\nconst rangeIosCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}@supports (inset-inline-start: 0){.range-knob-handle{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-knob-handle{left:0}:host-context([dir=rtl]) .range-knob-handle{left:unset;right:unset;right:0}[dir=rtl] .range-knob-handle{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}@supports (inset-inline-start: 0){.range-bar-container{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-bar-container{left:0}:host-context([dir=rtl]) .range-bar-container{left:unset;right:unset;right:0}[dir=rtl] .range-bar-container{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}@supports (inset-inline-start: 0){.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}}@supports not (inset-inline-start: 0){.range-knob{left:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}[dir=rtl] .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}}}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:#ffffff;--knob-box-shadow:0px 0.5px 4px rgba(0, 0, 0, 0.12), 0px 6px 13px rgba(0, 0, 0, 0.12);--knob-size:26px;--bar-height:4px;--bar-background:var(--ion-color-step-900, #e6e6e6);--bar-background-active:var(--ion-color-primary, #3880ff);--bar-border-radius:2px;--height:42px}:host(.legacy-range){-webkit-padding-start:16px;padding-inline-start:16px;-webkit-padding-end:16px;padding-inline-end:16px;padding-top:8px;padding-bottom:8px}:host(.range-item-start-adjustment){-webkit-padding-start:24px;padding-inline-start:24px}:host(.range-item-end-adjustment){-webkit-padding-end:24px;padding-inline-end:24px}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-tick-active{background:var(--ion-color-base)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:calc(8px + 0.75rem)}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:calc(8px + 0.75rem)}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-bar-active.has-ticks{border-radius:0;-webkit-margin-start:-2px;margin-inline-start:-2px;-webkit-margin-end:-2px;margin-inline-end:-2px}.range-tick{-webkit-margin-start:-2px;margin-inline-start:-2px;border-radius:0;position:absolute;top:17px;width:4px;height:8px;background:var(--ion-color-step-900, #e6e6e6);pointer-events:none}.range-tick-active{background:var(--bar-background-active)}.range-pin{-webkit-transform:translate3d(0,  100%,  0) scale(0.01);transform:translate3d(0,  100%,  0) scale(0.01);-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:8px;padding-bottom:8px;min-width:28px;-webkit-transition:-webkit-transform 120ms ease;transition:-webkit-transform 120ms ease;transition:transform 120ms ease;transition:transform 120ms ease, -webkit-transform 120ms ease;background:transparent;color:var(--ion-text-color, #000);font-size:0.75rem;text-align:center}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 11px), 0) scale(1);transform:translate3d(0, calc(-100% + 11px), 0) scale(1)}:host(.range-disabled){opacity:0.3}\";\nconst IonRangeIosStyle0 = rangeIosCss;\n\nconst rangeMdCss = \":host{--knob-handle-size:calc(var(--knob-size) * 2);display:-ms-flexbox;display:flex;position:relative;-ms-flex:3;flex:3;-ms-flex-align:center;align-items:center;font-family:var(--ion-font-family, inherit);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.range-disabled){pointer-events:none}::slotted(ion-label){-ms-flex:initial;flex:initial}::slotted(ion-icon[slot]){font-size:24px}.range-slider{position:relative;-ms-flex:1;flex:1;width:100%;height:var(--height);contain:size layout style;cursor:-webkit-grab;cursor:grab;-ms-touch-action:pan-y;touch-action:pan-y}:host(.range-pressed) .range-slider{cursor:-webkit-grabbing;cursor:grabbing}.range-pin{position:absolute;background:var(--ion-color-base);color:var(--ion-color-contrast);text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box}.range-knob-handle{top:calc((var(--height) - var(--knob-handle-size)) / 2);-webkit-margin-start:calc(0px - var(--knob-handle-size) / 2);margin-inline-start:calc(0px - var(--knob-handle-size) / 2);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-pack:center;justify-content:center;width:var(--knob-handle-size);height:var(--knob-handle-size);text-align:center}@supports (inset-inline-start: 0){.range-knob-handle{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-knob-handle{left:0}:host-context([dir=rtl]) .range-knob-handle{left:unset;right:unset;right:0}[dir=rtl] .range-knob-handle{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-knob-handle{left:unset}[dir=rtl] .range-knob-handle{left:unset}@supports selector(:dir(rtl)){.range-knob-handle:dir(rtl){left:unset}}.range-knob-handle:active,.range-knob-handle:focus{outline:none}.range-bar-container{border-radius:var(--bar-border-radius);top:calc((var(--height) - var(--bar-height)) / 2);position:absolute;width:100%;height:var(--bar-height)}@supports (inset-inline-start: 0){.range-bar-container{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-bar-container{left:0}:host-context([dir=rtl]) .range-bar-container{left:unset;right:unset;right:0}[dir=rtl] .range-bar-container{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset;right:unset;right:0}}}:host-context([dir=rtl]) .range-bar-container{left:unset}[dir=rtl] .range-bar-container{left:unset}@supports selector(:dir(rtl)){.range-bar-container:dir(rtl){left:unset}}.range-bar{border-radius:var(--bar-border-radius);position:absolute;width:100%;height:var(--bar-height);background:var(--bar-background);pointer-events:none}.range-knob{border-radius:var(--knob-border-radius);top:calc(50% - var(--knob-size) / 2);position:absolute;width:var(--knob-size);height:var(--knob-size);background:var(--knob-background);-webkit-box-shadow:var(--knob-box-shadow);box-shadow:var(--knob-box-shadow);z-index:2;pointer-events:none}@supports (inset-inline-start: 0){.range-knob{inset-inline-start:calc(50% - var(--knob-size) / 2)}}@supports not (inset-inline-start: 0){.range-knob{left:calc(50% - var(--knob-size) / 2)}:host-context([dir=rtl]) .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}[dir=rtl] .range-knob{left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset;right:unset;right:calc(50% - var(--knob-size) / 2)}}}:host-context([dir=rtl]) .range-knob{left:unset}[dir=rtl] .range-knob{left:unset}@supports selector(:dir(rtl)){.range-knob:dir(rtl){left:unset}}:host(.range-pressed) .range-bar-active{will-change:left, right}:host(.in-item){width:100%}:host([slot=start]),:host([slot=end]){width:auto}:host(.in-item) ::slotted(ion-label){-ms-flex-item-align:center;align-self:center}.range-wrapper{display:-ms-flexbox;display:flex;position:relative;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit}::slotted([slot=label]){max-width:200px;text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.label-text-wrapper-hidden{display:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center}:host(.range-label-placement-start) .range-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.range-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-end) .range-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.range-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:0;margin-bottom:0}:host(.range-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.range-label-placement-stacked) .range-wrapper{-ms-flex-direction:column;flex-direction:column;-ms-flex-align:stretch;align-items:stretch}:host(.range-label-placement-stacked) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top;-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host-context([dir=rtl]):host(.range-label-placement-stacked) .label-text-wrapper,:host-context([dir=rtl]).range-label-placement-stacked .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.range-label-placement-stacked:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.in-item.range-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.range-label-placement-stacked) .native-wrapper{margin-bottom:0px}:host{--knob-border-radius:50%;--knob-background:var(--bar-background-active);--knob-box-shadow:none;--knob-size:18px;--bar-height:2px;--bar-background:rgba(var(--ion-color-primary-rgb, 56, 128, 255), 0.26);--bar-background-active:var(--ion-color-primary, #3880ff);--bar-border-radius:0;--height:42px;--pin-background:var(--ion-color-primary, #3880ff);--pin-color:var(--ion-color-primary-contrast, #fff)}:host(.legacy-range) ::slotted([slot=label]){font-size:initial}:host(:not(.legacy-range)) ::slotted(:not(ion-icon)[slot=start]),:host(:not(.legacy-range)) ::slotted(:not(ion-icon)[slot=end]),:host(:not(.legacy-range)) .native-wrapper{font-size:0.75rem}:host(.legacy-range){-webkit-padding-start:14px;padding-inline-start:14px;-webkit-padding-end:14px;padding-inline-end:14px;padding-top:8px;padding-bottom:8px;font-size:0.75rem}:host(.range-item-start-adjustment){-webkit-padding-start:18px;padding-inline-start:18px}:host(.range-item-end-adjustment){-webkit-padding-end:18px;padding-inline-end:18px}:host(.ion-color) .range-bar{background:rgba(var(--ion-color-base-rgb), 0.26)}:host(.ion-color) .range-bar-active,:host(.ion-color) .range-knob,:host(.ion-color) .range-knob::before,:host(.ion-color) .range-pin,:host(.ion-color) .range-pin::before,:host(.ion-color) .range-tick{background:var(--ion-color-base);color:var(--ion-color-contrast)}::slotted([slot=start]){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0}::slotted([slot=end]){-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:0;margin-inline-end:0;margin-top:0;margin-bottom:0}:host(.range-has-pin:not(.range-label-placement-stacked)){padding-top:1.75rem}:host(.range-has-pin.range-label-placement-stacked) .label-text-wrapper{margin-bottom:1.75rem}.range-bar-active{bottom:0;width:auto;background:var(--bar-background-active)}.range-knob{-webkit-transform:scale(0.67);transform:scale(0.67);-webkit-transition-duration:120ms;transition-duration:120ms;-webkit-transition-property:background-color, border, -webkit-transform;transition-property:background-color, border, -webkit-transform;transition-property:transform, background-color, border;transition-property:transform, background-color, border, -webkit-transform;-webkit-transition-timing-function:ease;transition-timing-function:ease;z-index:2}.range-knob::before{border-radius:50%;position:absolute;width:var(--knob-size);height:var(--knob-size);-webkit-transform:scale(1);transform:scale(1);-webkit-transition:0.267s cubic-bezier(0, 0, 0.58, 1);transition:0.267s cubic-bezier(0, 0, 0.58, 1);background:var(--knob-background);content:\\\"\\\";opacity:0.13;pointer-events:none}@supports (inset-inline-start: 0){.range-knob::before{inset-inline-start:0}}@supports not (inset-inline-start: 0){.range-knob::before{left:0}:host-context([dir=rtl]) .range-knob::before{left:unset;right:unset;right:0}[dir=rtl] .range-knob::before{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){.range-knob::before:dir(rtl){left:unset;right:unset;right:0}}}.range-tick{position:absolute;top:calc((var(--height) - var(--bar-height)) / 2);width:var(--bar-height);height:var(--bar-height);background:var(--bar-background-active);z-index:1;pointer-events:none}.range-tick-active{background:transparent}.range-pin{padding-left:0;padding-right:0;padding-top:8px;padding-bottom:8px;border-radius:50%;-webkit-transform:translate3d(0,  0,  0) scale(0.01);transform:translate3d(0,  0,  0) scale(0.01);display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:1.75rem;height:1.75rem;-webkit-transition:background 120ms ease, -webkit-transform 120ms ease;transition:background 120ms ease, -webkit-transform 120ms ease;transition:transform 120ms ease, background 120ms ease;transition:transform 120ms ease, background 120ms ease, -webkit-transform 120ms ease;background:var(--pin-background);color:var(--pin-color)}.range-pin::before{bottom:-1px;-webkit-margin-start:-13px;margin-inline-start:-13px;border-radius:50% 50% 50% 0;position:absolute;width:26px;height:26px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg);-webkit-transition:background 120ms ease;transition:background 120ms ease;background:var(--pin-background);content:\\\"\\\";z-index:-1}@supports (inset-inline-start: 0){.range-pin::before{inset-inline-start:50%}}@supports not (inset-inline-start: 0){.range-pin::before{left:50%}:host-context([dir=rtl]) .range-pin::before{left:unset;right:unset;right:50%}[dir=rtl] .range-pin::before{left:unset;right:unset;right:50%}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset;right:unset;right:50%}}}:host-context([dir=rtl]) .range-pin::before{left:unset}[dir=rtl] .range-pin::before{left:unset}@supports selector(:dir(rtl)){.range-pin::before:dir(rtl){left:unset}}.range-knob-pressed .range-pin,.range-knob-handle.ion-focused .range-pin{-webkit-transform:translate3d(0, calc(-100% + 4px), 0) scale(1);transform:translate3d(0, calc(-100% + 4px), 0) scale(1)}@media (any-hover: hover){.range-knob-handle:hover .range-knob:before{-webkit-transform:scale(2);transform:scale(2);opacity:0.13}}.range-knob-handle.ion-activated .range-knob:before,.range-knob-handle.ion-focused .range-knob:before,.range-knob-handle.range-knob-pressed .range-knob:before{-webkit-transform:scale(2);transform:scale(2)}.range-knob-handle.ion-focused .range-knob::before{opacity:0.13}.range-knob-handle.ion-activated .range-knob::before,.range-knob-handle.range-knob-pressed .range-knob::before{opacity:0.25}:host(:not(.range-has-pin)) .range-knob-pressed .range-knob,:host(:not(.range-has-pin)) .range-knob-handle.ion-focused .range-knob{-webkit-transform:scale(1);transform:scale(1)}:host(.range-disabled) .range-bar-active,:host(.range-disabled) .range-bar,:host(.range-disabled) .range-tick{background-color:var(--ion-color-step-250, #bfbfbf)}:host(.range-disabled) .range-knob{-webkit-transform:scale(0.55);transform:scale(0.55);outline:5px solid #fff;background-color:var(--ion-color-step-250, #bfbfbf)}:host(.range-disabled) .label-text-wrapper,:host(.range-disabled) ::slotted([slot=start]),:host(.range-disabled) ::slotted([slot=end]){opacity:0.38}\";\nconst IonRangeMdStyle0 = rangeMdCss;\n\nconst Range = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionInput = createEvent(this, \"ionInput\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionKnobMoveStart = createEvent(this, \"ionKnobMoveStart\", 7);\n        this.ionKnobMoveEnd = createEvent(this, \"ionKnobMoveEnd\", 7);\n        this.rangeId = `ion-r-${rangeIds++}`;\n        this.didLoad = false;\n        this.noUpdate = false;\n        this.hasFocus = false;\n        this.inheritedAttributes = {};\n        this.contentEl = null;\n        this.initialContentScrollY = true;\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        this.clampBounds = (value) => {\n            return clamp(this.min, value, this.max);\n        };\n        this.ensureValueInBounds = (value) => {\n            if (this.dualKnobs) {\n                return {\n                    lower: this.clampBounds(value.lower),\n                    upper: this.clampBounds(value.upper),\n                };\n            }\n            else {\n                return this.clampBounds(value);\n            }\n        };\n        this.setupGesture = async () => {\n            const rangeSlider = this.rangeSlider;\n            if (rangeSlider) {\n                this.gesture = (await import('./index-2cf77112.js')).createGesture({\n                    el: rangeSlider,\n                    gestureName: 'range',\n                    gesturePriority: 100,\n                    /**\n                     * Provide a threshold since the drag movement\n                     * might be a user scrolling the view.\n                     * If this is true, then the range\n                     * should not move.\n                     */\n                    threshold: 10,\n                    onStart: () => this.onStart(),\n                    onMove: (ev) => this.onMove(ev),\n                    onEnd: (ev) => this.onEnd(ev),\n                });\n                this.gesture.enable(!this.disabled);\n            }\n        };\n        this.handleKeyboard = (knob, isIncrease) => {\n            const { ensureValueInBounds } = this;\n            let step = this.step;\n            step = step > 0 ? step : 1;\n            step = step / (this.max - this.min);\n            if (!isIncrease) {\n                step *= -1;\n            }\n            if (knob === 'A') {\n                this.ratioA = clamp(0, this.ratioA + step, 1);\n            }\n            else {\n                this.ratioB = clamp(0, this.ratioB + step, 1);\n            }\n            this.ionKnobMoveStart.emit({ value: ensureValueInBounds(this.value) });\n            this.updateValue();\n            this.emitValueChange();\n            this.ionKnobMoveEnd.emit({ value: ensureValueInBounds(this.value) });\n        };\n        this.onBlur = () => {\n            if (this.hasFocus) {\n                this.hasFocus = false;\n                this.ionBlur.emit();\n                this.emitStyle();\n            }\n        };\n        this.onFocus = () => {\n            if (!this.hasFocus) {\n                this.hasFocus = true;\n                this.ionFocus.emit();\n                this.emitStyle();\n            }\n        };\n        this.ratioA = 0;\n        this.ratioB = 0;\n        this.pressedKnob = undefined;\n        this.color = undefined;\n        this.debounce = undefined;\n        this.name = this.rangeId;\n        this.label = undefined;\n        this.dualKnobs = false;\n        this.min = 0;\n        this.max = 100;\n        this.pin = false;\n        this.pinFormatter = (value) => Math.round(value);\n        this.snaps = false;\n        this.step = 1;\n        this.ticks = true;\n        this.activeBarStart = undefined;\n        this.disabled = false;\n        this.value = 0;\n        this.labelPlacement = 'start';\n        this.legacy = undefined;\n    }\n    debounceChanged() {\n        const { ionInput, debounce, originalIonInput } = this;\n        /**\n         * If debounce is undefined, we have to manually revert the ionInput emitter in case\n         * debounce used to be set to a number. Otherwise, the event would stay debounced.\n         */\n        this.ionInput = debounce === undefined ? originalIonInput !== null && originalIonInput !== void 0 ? originalIonInput : ionInput : debounceEvent(ionInput, debounce);\n    }\n    minChanged() {\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    maxChanged() {\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    activeBarStartChanged() {\n        const { activeBarStart } = this;\n        if (activeBarStart !== undefined) {\n            if (activeBarStart > this.max) {\n                printIonWarning(`Range: The value of activeBarStart (${activeBarStart}) is greater than the max (${this.max}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.max;\n            }\n            else if (activeBarStart < this.min) {\n                printIonWarning(`Range: The value of activeBarStart (${activeBarStart}) is less than the min (${this.min}). Valid values are greater than or equal to the min value and less than or equal to the max value.`, this.el);\n                this.activeBarStart = this.min;\n            }\n        }\n    }\n    disabledChanged() {\n        if (this.gesture) {\n            this.gesture.enable(!this.disabled);\n        }\n        this.emitStyle();\n    }\n    valueChanged() {\n        if (!this.noUpdate) {\n            this.updateRatio();\n        }\n    }\n    componentWillLoad() {\n        /**\n         * If user has custom ID set then we should\n         * not assign the default incrementing ID.\n         */\n        if (this.el.hasAttribute('id')) {\n            this.rangeId = this.el.getAttribute('id');\n        }\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.originalIonInput = this.ionInput;\n        this.setupGesture();\n        this.updateRatio();\n        this.didLoad = true;\n    }\n    connectedCallback() {\n        const { el } = this;\n        this.legacyFormController = createLegacyFormController(el);\n        this.updateRatio();\n        this.debounceChanged();\n        this.disabledChanged();\n        this.activeBarStartChanged();\n        /**\n         * If we have not yet rendered\n         * ion-range, then rangeSlider is not defined.\n         * But if we are moving ion-range via appendChild,\n         * then rangeSlider will be defined.\n         */\n        if (this.didLoad) {\n            this.setupGesture();\n        }\n        this.contentEl = findClosestIonContent(this.el);\n    }\n    disconnectedCallback() {\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n    }\n    getValue() {\n        var _a;\n        const value = (_a = this.value) !== null && _a !== void 0 ? _a : 0;\n        if (this.dualKnobs) {\n            if (typeof value === 'object') {\n                return value;\n            }\n            return {\n                lower: 0,\n                upper: value,\n            };\n        }\n        else {\n            if (typeof value === 'object') {\n                return value.upper;\n            }\n            return value;\n        }\n    }\n    // TODO FW-2997 remove this\n    emitStyle() {\n        if (this.legacyFormController.hasLegacyControl()) {\n            this.ionStyle.emit({\n                interactive: true,\n                'interactive-disabled': this.disabled,\n                // TODO(FW-2997): remove this\n                legacy: !!this.legacy,\n            });\n        }\n    }\n    /**\n     * Emits an `ionChange` event.\n     *\n     * This API should be called for user committed changes.\n     * This API should not be used for external value changes.\n     */\n    emitValueChange() {\n        this.value = this.ensureValueInBounds(this.value);\n        this.ionChange.emit({ value: this.value });\n    }\n    /**\n     * The value should be updated on touch end or\n     * when the component is being dragged.\n     * This follows the native behavior of mobile devices.\n     *\n     * For example: When the user lifts their finger from the\n     * screen after tapping the bar or dragging the bar or knob.\n     */\n    onStart() {\n        this.ionKnobMoveStart.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    /**\n     * The value should be updated while dragging the\n     * bar or knob.\n     *\n     * While the user is dragging, the view\n     * should not scroll. This is to prevent the user from\n     * feeling disoriented while dragging.\n     *\n     * The user can scroll on the view if the knob or\n     * bar is not being dragged.\n     *\n     * @param detail The details of the gesture event.\n     */\n    onMove(detail) {\n        const { contentEl, pressedKnob } = this;\n        const currentX = detail.currentX;\n        /**\n         * Since the user is dragging on the bar or knob, the view should not scroll.\n         *\n         * This only needs to be done once.\n         */\n        if (contentEl && this.initialContentScrollY === undefined) {\n            this.initialContentScrollY = disableContentScrollY(contentEl);\n        }\n        /**\n         * The `pressedKnob` can be undefined if the user just\n         * started dragging the knob.\n         *\n         * This is necessary to determine which knob the user is dragging,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         *\n         * This only needs to be done once since the knob won't change\n         * while the user is dragging.\n         */\n        if (pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        this.update(currentX);\n    }\n    /**\n     * The value should be updated on touch end:\n     * - When the user lifts their finger from the screen after\n     * tapping the bar.\n     *\n     * @param detail The details of the gesture or mouse event.\n     */\n    onEnd(detail) {\n        const { contentEl, initialContentScrollY } = this;\n        const currentX = detail.currentX || detail.clientX;\n        /**\n         * The `pressedKnob` can be undefined if the user never\n         * dragged the knob. They just tapped on the bar.\n         *\n         * This is necessary to determine which knob the user is changing,\n         * especially when it's a dual knob.\n         * Plus, it determines when to apply certain styles.\n         */\n        if (this.pressedKnob === undefined) {\n            this.setPressedKnob(currentX);\n        }\n        /**\n         * The user is no longer dragging the bar or\n         * knob (if they were dragging it).\n         *\n         * The user can now scroll on the view in the next gesture event.\n         */\n        if (contentEl && initialContentScrollY !== undefined) {\n            resetContentScrollY(contentEl, initialContentScrollY);\n        }\n        // update the active knob's position\n        this.update(currentX);\n        /**\n         * Reset the pressed knob to undefined since the user\n         * may start dragging a different knob in the next gesture event.\n         */\n        this.pressedKnob = undefined;\n        this.emitValueChange();\n        this.ionKnobMoveEnd.emit({ value: this.ensureValueInBounds(this.value) });\n    }\n    update(currentX) {\n        // figure out where the pointer is currently at\n        // update the knob being interacted with\n        const rect = this.rect;\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        if (this.snaps) {\n            // snaps the ratio to the current value\n            ratio = valueToRatio(ratioToValue(ratio, this.min, this.max, this.step), this.min, this.max);\n        }\n        // update which knob is pressed\n        if (this.pressedKnob === 'A') {\n            this.ratioA = ratio;\n        }\n        else {\n            this.ratioB = ratio;\n        }\n        // Update input value\n        this.updateValue();\n    }\n    setPressedKnob(currentX) {\n        const rect = (this.rect = this.rangeSlider.getBoundingClientRect());\n        // figure out which knob they started closer to\n        let ratio = clamp(0, (currentX - rect.left) / rect.width, 1);\n        if (isRTL(this.el)) {\n            ratio = 1 - ratio;\n        }\n        this.pressedKnob = !this.dualKnobs || Math.abs(this.ratioA - ratio) < Math.abs(this.ratioB - ratio) ? 'A' : 'B';\n        this.setFocus(this.pressedKnob);\n    }\n    get valA() {\n        return ratioToValue(this.ratioA, this.min, this.max, this.step);\n    }\n    get valB() {\n        return ratioToValue(this.ratioB, this.min, this.max, this.step);\n    }\n    get ratioLower() {\n        if (this.dualKnobs) {\n            return Math.min(this.ratioA, this.ratioB);\n        }\n        const { activeBarStart } = this;\n        if (activeBarStart == null) {\n            return 0;\n        }\n        return valueToRatio(activeBarStart, this.min, this.max);\n    }\n    get ratioUpper() {\n        if (this.dualKnobs) {\n            return Math.max(this.ratioA, this.ratioB);\n        }\n        return this.ratioA;\n    }\n    updateRatio() {\n        const value = this.getValue();\n        const { min, max } = this;\n        if (this.dualKnobs) {\n            this.ratioA = valueToRatio(value.lower, min, max);\n            this.ratioB = valueToRatio(value.upper, min, max);\n        }\n        else {\n            this.ratioA = valueToRatio(value, min, max);\n        }\n    }\n    updateValue() {\n        this.noUpdate = true;\n        const { valA, valB } = this;\n        this.value = !this.dualKnobs\n            ? valA\n            : {\n                lower: Math.min(valA, valB),\n                upper: Math.max(valA, valB),\n            };\n        this.ionInput.emit({ value: this.value });\n        this.noUpdate = false;\n    }\n    setFocus(knob) {\n        if (this.el.shadowRoot) {\n            const knobEl = this.el.shadowRoot.querySelector(knob === 'A' ? '.range-knob-a' : '.range-knob-b');\n            if (knobEl) {\n                knobEl.focus();\n            }\n        }\n    }\n    // TODO FW-2997 remove this\n    renderLegacyRange() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-range now requires providing a label with either the label slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-range><div slot=\"label\">Volume</div></ion-range>\nExample with aria-label: <ion-range aria-label=\"Volume\"></ion-range>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-range is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\n\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new range syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const { el, pressedKnob, disabled, pin, rangeId } = this;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n        return (h(Host, { onFocusin: this.onFocus, onFocusout: this.onBlur, id: rangeId, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'range-disabled': disabled,\n                'range-pressed': pressedKnob !== undefined,\n                'range-has-pin': pin,\n                'legacy-range': true,\n            }) }, h(\"slot\", { name: \"start\" }), this.renderRangeSlider(), h(\"slot\", { name: \"end\" })));\n    }\n    /**\n     * Returns true if content was passed to the \"start\" slot\n     */\n    get hasStartSlotContent() {\n        return this.el.querySelector('[slot=\"start\"]') !== null;\n    }\n    /**\n     * Returns true if content was passed to the \"end\" slot\n     */\n    get hasEndSlotContent() {\n        return this.el.querySelector('[slot=\"end\"]') !== null;\n    }\n    renderRange() {\n        const { disabled, el, hasLabel, rangeId, pin, pressedKnob, labelPlacement, label } = this;\n        const inItem = hostContext('ion-item', el);\n        /**\n         * If there is no start content then the knob at\n         * the min value will be cut off by the item margin.\n         */\n        const hasStartContent = (hasLabel && (labelPlacement === 'start' || labelPlacement === 'fixed')) || this.hasStartSlotContent;\n        const needsStartAdjustment = inItem && !hasStartContent;\n        /**\n         * If there is no end content then the knob at\n         * the max value will be cut off by the item margin.\n         */\n        const hasEndContent = (hasLabel && labelPlacement === 'end') || this.hasEndSlotContent;\n        const needsEndAdjustment = inItem && !hasEndContent;\n        const mode = getIonMode(this);\n        renderHiddenInput(true, el, this.name, JSON.stringify(this.getValue()), disabled);\n        return (h(Host, { onFocusin: this.onFocus, onFocusout: this.onBlur, id: rangeId, class: createColorClasses(this.color, {\n                [mode]: true,\n                'in-item': inItem,\n                'range-disabled': disabled,\n                'range-pressed': pressedKnob !== undefined,\n                'range-has-pin': pin,\n                [`range-label-placement-${labelPlacement}`]: true,\n                'range-item-start-adjustment': needsStartAdjustment,\n                'range-item-end-adjustment': needsEndAdjustment,\n            }) }, h(\"label\", { class: \"range-wrapper\", id: \"range-label\" }, h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': !hasLabel,\n            }, part: \"label\" }, label !== undefined ? h(\"div\", { class: \"label-text\" }, label) : h(\"slot\", { name: \"label\" })), h(\"div\", { class: \"native-wrapper\" }, h(\"slot\", { name: \"start\" }), this.renderRangeSlider(), h(\"slot\", { name: \"end\" })))));\n    }\n    get hasLabel() {\n        return this.label !== undefined || this.el.querySelector('[slot=\"label\"]') !== null;\n    }\n    renderRangeSlider() {\n        var _a;\n        const { min, max, step, el, handleKeyboard, pressedKnob, disabled, pin, ratioLower, ratioUpper, inheritedAttributes, rangeId, pinFormatter, } = this;\n        /**\n         * Look for external label, ion-label, or aria-labelledby.\n         * If none, see if user placed an aria-label on the host\n         * and use that instead.\n         */\n        let { labelText } = getAriaLabel(el, rangeId);\n        if (labelText === undefined || labelText === null) {\n            labelText = inheritedAttributes['aria-label'];\n        }\n        let barStart = `${ratioLower * 100}%`;\n        let barEnd = `${100 - ratioUpper * 100}%`;\n        const rtl = isRTL(this.el);\n        const start = rtl ? 'right' : 'left';\n        const end = rtl ? 'left' : 'right';\n        const tickStyle = (tick) => {\n            return {\n                [start]: tick[start],\n            };\n        };\n        if (this.dualKnobs === false) {\n            /**\n             * When the value is less than the activeBarStart or the min value,\n             * the knob will display at the start of the active bar.\n             */\n            if (this.valA < ((_a = this.activeBarStart) !== null && _a !== void 0 ? _a : this.min)) {\n                /**\n                 * Sets the bar positions relative to the upper and lower limits.\n                 * Converts the ratio values into percentages, used as offsets for left/right styles.\n                 *\n                 * The ratioUpper refers to the knob position on the bar.\n                 * The ratioLower refers to the end position of the active bar (the value).\n                 */\n                barStart = `${ratioUpper * 100}%`;\n                barEnd = `${100 - ratioLower * 100}%`;\n            }\n            else {\n                /**\n                 * Otherwise, the knob will display at the end of the active bar.\n                 *\n                 * The ratioLower refers to the start position of the active bar (the value).\n                 * The ratioUpper refers to the knob position on the bar.\n                 */\n                barStart = `${ratioLower * 100}%`;\n                barEnd = `${100 - ratioUpper * 100}%`;\n            }\n        }\n        const barStyle = {\n            [start]: barStart,\n            [end]: barEnd,\n        };\n        const ticks = [];\n        if (this.snaps && this.ticks) {\n            for (let value = min; value <= max; value += step) {\n                const ratio = valueToRatio(value, min, max);\n                const ratioMin = Math.min(ratioLower, ratioUpper);\n                const ratioMax = Math.max(ratioLower, ratioUpper);\n                const tick = {\n                    ratio,\n                    /**\n                     * Sets the tick mark as active when the tick is between the min bounds and the knob.\n                     * When using activeBarStart, the tick mark will be active between the knob and activeBarStart.\n                     */\n                    active: ratio >= ratioMin && ratio <= ratioMax,\n                };\n                tick[start] = `${ratio * 100}%`;\n                ticks.push(tick);\n            }\n        }\n        let labelledBy;\n        if (!this.legacyFormController.hasLegacyControl() && this.hasLabel) {\n            labelledBy = 'range-label';\n        }\n        return (h(\"div\", { class: \"range-slider\", ref: (rangeEl) => (this.rangeSlider = rangeEl),\n            /**\n             * Since the gesture has a threshold, the value\n             * won't change until the user has dragged past\n             * the threshold. This is to prevent the range\n             * from moving when the user is scrolling.\n             *\n             * This results in the value not being updated\n             * and the event emitters not being triggered\n             * if the user taps on the range. This is why\n             * we need to listen for the \"pointerUp\" event.\n             */\n            onPointerUp: (ev) => {\n                /**\n                 * If the user drags the knob on the web\n                 * version (does not occur on mobile),\n                 * the \"pointerUp\" event will be triggered\n                 * along with the gesture's events.\n                 * This leads to duplicate events.\n                 *\n                 * By checking if the pressedKnob is undefined,\n                 * we can determine if the \"pointerUp\" event was\n                 * triggered by a tap or a drag. If it was\n                 * dragged, the pressedKnob will be defined.\n                 */\n                if (this.pressedKnob === undefined) {\n                    this.onStart();\n                    this.onEnd(ev);\n                }\n            } }, ticks.map((tick) => (h(\"div\", { style: tickStyle(tick), role: \"presentation\", class: {\n                'range-tick': true,\n                'range-tick-active': tick.active,\n            }, part: tick.active ? 'tick-active' : 'tick' }))), h(\"div\", { class: \"range-bar-container\" }, h(\"div\", { class: \"range-bar\", role: \"presentation\", part: \"bar\" }), h(\"div\", { class: {\n                'range-bar': true,\n                'range-bar-active': true,\n                'has-ticks': ticks.length > 0,\n            }, role: \"presentation\", style: barStyle, part: \"bar-active\" })), renderKnob(rtl, {\n            knob: 'A',\n            pressed: pressedKnob === 'A',\n            value: this.valA,\n            ratio: this.ratioA,\n            pin,\n            pinFormatter,\n            disabled,\n            handleKeyboard,\n            min,\n            max,\n            labelText,\n            labelledBy,\n        }), this.dualKnobs &&\n            renderKnob(rtl, {\n                knob: 'B',\n                pressed: pressedKnob === 'B',\n                value: this.valB,\n                ratio: this.ratioB,\n                pin,\n                pinFormatter,\n                disabled,\n                handleKeyboard,\n                min,\n                max,\n                labelText,\n                labelledBy,\n            })));\n    }\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacyRange() : this.renderRange();\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"debounce\": [\"debounceChanged\"],\n        \"min\": [\"minChanged\"],\n        \"max\": [\"maxChanged\"],\n        \"activeBarStart\": [\"activeBarStartChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"value\": [\"valueChanged\"]\n    }; }\n};\nconst renderKnob = (rtl, { knob, value, ratio, min, max, disabled, pressed, pin, handleKeyboard, labelText, labelledBy, pinFormatter, }) => {\n    const start = rtl ? 'right' : 'left';\n    const knobStyle = () => {\n        const style = {};\n        style[start] = `${ratio * 100}%`;\n        return style;\n    };\n    return (h(\"div\", { onKeyDown: (ev) => {\n            const key = ev.key;\n            if (key === 'ArrowLeft' || key === 'ArrowDown') {\n                handleKeyboard(knob, false);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n            else if (key === 'ArrowRight' || key === 'ArrowUp') {\n                handleKeyboard(knob, true);\n                ev.preventDefault();\n                ev.stopPropagation();\n            }\n        }, class: {\n            'range-knob-handle': true,\n            'range-knob-a': knob === 'A',\n            'range-knob-b': knob === 'B',\n            'range-knob-pressed': pressed,\n            'range-knob-min': value === min,\n            'range-knob-max': value === max,\n            'ion-activatable': true,\n            'ion-focusable': true,\n        }, style: knobStyle(), role: \"slider\", tabindex: disabled ? -1 : 0, \"aria-label\": labelledBy === undefined ? labelText : null, \"aria-labelledby\": labelledBy !== undefined ? labelledBy : null, \"aria-valuemin\": min, \"aria-valuemax\": max, \"aria-disabled\": disabled ? 'true' : null, \"aria-valuenow\": value }, pin && (h(\"div\", { class: \"range-pin\", role: \"presentation\", part: \"pin\" }, pinFormatter(value))), h(\"div\", { class: \"range-knob\", role: \"presentation\", part: \"knob\" })));\n};\nconst ratioToValue = (ratio, min, max, step) => {\n    let value = (max - min) * ratio;\n    if (step > 0) {\n        // round to nearest multiple of step, then add min\n        value = Math.round(value / step) * step + min;\n    }\n    const clampedValue = clamp(min, value, max);\n    return roundToMaxDecimalPlaces(clampedValue, min, max, step);\n};\nconst valueToRatio = (value, min, max) => {\n    return clamp(0, (value - min) / (max - min), 1);\n};\nlet rangeIds = 0;\nRange.style = {\n    ios: IonRangeIosStyle0,\n    md: IonRangeMdStyle0\n};\n\nexport { Range as ion_range };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASD,CAAC,IAAIE,qBAAqB,EAAEP,CAAC,IAAIQ,qBAAqB,EAAEV,CAAC,IAAIW,mBAAmB,QAAQ,qBAAqB;AACtH,SAASC,CAAC,IAAIC,0BAA0B,QAAQ,+BAA+B;AAC/E,SAASC,CAAC,IAAIC,KAAK,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,qBAAqB,EAAEjB,CAAC,IAAIkB,iBAAiB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAC7I,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASN,CAAC,IAAIO,KAAK,QAAQ,mBAAmB;AAC9C,SAASb,CAAC,IAAIc,kBAAkB,EAAEtB,CAAC,IAAIuB,WAAW,QAAQ,qBAAqB;AAC/E,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,SAASC,gBAAgBA,CAACC,CAAC,EAAE;EACzB,IAAIA,CAAC,GAAG,CAAC,KAAK,CAAC,EACX,OAAO,CAAC;EACZ,OAAOA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,MAAM;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,uBAAuBA,CAACJ,CAAC,EAAE,GAAGK,UAAU,EAAE;EAC/C,MAAMC,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGH,UAAU,CAACI,GAAG,CAAExC,CAAC,IAAK8B,gBAAgB,CAAC9B,CAAC,CAAC,CAAC,CAAC;EACzE,OAAOyC,MAAM,CAACV,CAAC,CAACW,OAAO,CAACL,SAAS,CAAC,CAAC;AACvC;AAEA,MAAMM,WAAW,GAAG,o2QAAo2Q;AACx3Q,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,s/XAAs/X;AACzgY,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBC,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IACjBjD,gBAAgB,CAAC,IAAI,EAAEgD,OAAO,CAAC;IAC/B,IAAI,CAACE,SAAS,GAAGhD,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAACiD,QAAQ,GAAGjD,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACkD,QAAQ,GAAGlD,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACmD,QAAQ,GAAGnD,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAACoD,OAAO,GAAGpD,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAACqD,gBAAgB,GAAGrD,WAAW,CAAC,IAAI,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAChE,IAAI,CAACsD,cAAc,GAAGtD,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAACuD,OAAO,GAAG,SAASC,QAAQ,EAAE,EAAE;IACpC,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC,IAAI,CAACC,WAAW,GAAIC,KAAK,IAAK;MAC1B,OAAOrD,KAAK,CAAC,IAAI,CAACsD,GAAG,EAAED,KAAK,EAAE,IAAI,CAAC7B,GAAG,CAAC;IAC3C,CAAC;IACD,IAAI,CAAC+B,mBAAmB,GAAIF,KAAK,IAAK;MAClC,IAAI,IAAI,CAACG,SAAS,EAAE;QAChB,OAAO;UACHC,KAAK,EAAE,IAAI,CAACL,WAAW,CAACC,KAAK,CAACI,KAAK,CAAC;UACpCC,KAAK,EAAE,IAAI,CAACN,WAAW,CAACC,KAAK,CAACK,KAAK;QACvC,CAAC;MACL,CAAC,MACI;QACD,OAAO,IAAI,CAACN,WAAW,CAACC,KAAK,CAAC;MAClC;IACJ,CAAC;IACD,IAAI,CAACM,YAAY,gBAAAC,iBAAA,CAAG,aAAY;MAC5B,MAAMC,WAAW,GAAG1B,KAAI,CAAC0B,WAAW;MACpC,IAAIA,WAAW,EAAE;QACb1B,KAAI,CAAC2B,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEC,aAAa,CAAC;UAC/DC,EAAE,EAAEH,WAAW;UACfI,WAAW,EAAE,OAAO;UACpBC,eAAe,EAAE,GAAG;UACpB;AACpB;AACA;AACA;AACA;AACA;UACoBC,SAAS,EAAE,EAAE;UACbC,OAAO,EAAEA,CAAA,KAAMjC,KAAI,CAACiC,OAAO,CAAC,CAAC;UAC7BC,MAAM,EAAGC,EAAE,IAAKnC,KAAI,CAACkC,MAAM,CAACC,EAAE,CAAC;UAC/BC,KAAK,EAAGD,EAAE,IAAKnC,KAAI,CAACoC,KAAK,CAACD,EAAE;QAChC,CAAC,CAAC;QACFnC,KAAI,CAAC2B,OAAO,CAACU,MAAM,CAAC,CAACrC,KAAI,CAACsC,QAAQ,CAAC;MACvC;IACJ,CAAC;IACD,IAAI,CAACC,cAAc,GAAG,CAACC,IAAI,EAAEC,UAAU,KAAK;MACxC,MAAM;QAAErB;MAAoB,CAAC,GAAG,IAAI;MACpC,IAAIsB,IAAI,GAAG,IAAI,CAACA,IAAI;MACpBA,IAAI,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC;MAC1BA,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACrD,GAAG,GAAG,IAAI,CAAC8B,GAAG,CAAC;MACnC,IAAI,CAACsB,UAAU,EAAE;QACbC,IAAI,IAAI,CAAC,CAAC;MACd;MACA,IAAIF,IAAI,KAAK,GAAG,EAAE;QACd,IAAI,CAACG,MAAM,GAAG9E,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC8E,MAAM,GAAGD,IAAI,EAAE,CAAC,CAAC;MACjD,CAAC,MACI;QACD,IAAI,CAACE,MAAM,GAAG/E,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC+E,MAAM,GAAGF,IAAI,EAAE,CAAC,CAAC;MACjD;MACA,IAAI,CAACpC,gBAAgB,CAACuC,IAAI,CAAC;QAAE3B,KAAK,EAAEE,mBAAmB,CAAC,IAAI,CAACF,KAAK;MAAE,CAAC,CAAC;MACtE,IAAI,CAAC4B,WAAW,CAAC,CAAC;MAClB,IAAI,CAACC,eAAe,CAAC,CAAC;MACtB,IAAI,CAACxC,cAAc,CAACsC,IAAI,CAAC;QAAE3B,KAAK,EAAEE,mBAAmB,CAAC,IAAI,CAACF,KAAK;MAAE,CAAC,CAAC;IACxE,CAAC;IACD,IAAI,CAAC8B,MAAM,GAAG,MAAM;MAChB,IAAI,IAAI,CAACpC,QAAQ,EAAE;QACf,IAAI,CAACA,QAAQ,GAAG,KAAK;QACrB,IAAI,CAACP,OAAO,CAACwC,IAAI,CAAC,CAAC;QACnB,IAAI,CAACI,SAAS,CAAC,CAAC;MACpB;IACJ,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAC,IAAI,CAACtC,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAG,IAAI;QACpB,IAAI,CAACR,QAAQ,CAACyC,IAAI,CAAC,CAAC;QACpB,IAAI,CAACI,SAAS,CAAC,CAAC;MACpB;IACJ,CAAC;IACD,IAAI,CAACN,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACO,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACC,KAAK,GAAGD,SAAS;IACtB,IAAI,CAACE,QAAQ,GAAGF,SAAS;IACzB,IAAI,CAACG,IAAI,GAAG,IAAI,CAAC/C,OAAO;IACxB,IAAI,CAACgD,KAAK,GAAGJ,SAAS;IACtB,IAAI,CAAC/B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACF,GAAG,GAAG,CAAC;IACZ,IAAI,CAAC9B,GAAG,GAAG,GAAG;IACd,IAAI,CAACoE,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,YAAY,GAAIxC,KAAK,IAAK9B,IAAI,CAACuE,KAAK,CAACzC,KAAK,CAAC;IAChD,IAAI,CAAC0C,KAAK,GAAG,KAAK;IAClB,IAAI,CAAClB,IAAI,GAAG,CAAC;IACb,IAAI,CAACmB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACC,cAAc,GAAGV,SAAS;IAC/B,IAAI,CAACd,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACpB,KAAK,GAAG,CAAC;IACd,IAAI,CAAC6C,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,MAAM,GAAGZ,SAAS;EAC3B;EACAa,eAAeA,CAAA,EAAG;IACd,MAAM;MAAE/D,QAAQ;MAAEoD,QAAQ;MAAEY;IAAiB,CAAC,GAAG,IAAI;IACrD;AACR;AACA;AACA;IACQ,IAAI,CAAChE,QAAQ,GAAGoD,QAAQ,KAAKF,SAAS,GAAGc,gBAAgB,KAAK,IAAI,IAAIA,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAGhE,QAAQ,GAAGnC,aAAa,CAACmC,QAAQ,EAAEoD,QAAQ,CAAC;EACvK;EACAa,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACxD,QAAQ,EAAE;MAChB,IAAI,CAACyD,WAAW,CAAC,CAAC;IACtB;EACJ;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAC1D,QAAQ,EAAE;MAChB,IAAI,CAACyD,WAAW,CAAC,CAAC;IACtB;EACJ;EACAE,qBAAqBA,CAAA,EAAG;IACpB,MAAM;MAAER;IAAe,CAAC,GAAG,IAAI;IAC/B,IAAIA,cAAc,KAAKV,SAAS,EAAE;MAC9B,IAAIU,cAAc,GAAG,IAAI,CAACzE,GAAG,EAAE;QAC3Bf,eAAe,CAAC,uCAAuCwF,cAAc,8BAA8B,IAAI,CAACzE,GAAG,qGAAqG,EAAE,IAAI,CAACwC,EAAE,CAAC;QAC1N,IAAI,CAACiC,cAAc,GAAG,IAAI,CAACzE,GAAG;MAClC,CAAC,MACI,IAAIyE,cAAc,GAAG,IAAI,CAAC3C,GAAG,EAAE;QAChC7C,eAAe,CAAC,uCAAuCwF,cAAc,2BAA2B,IAAI,CAAC3C,GAAG,qGAAqG,EAAE,IAAI,CAACU,EAAE,CAAC;QACvN,IAAI,CAACiC,cAAc,GAAG,IAAI,CAAC3C,GAAG;MAClC;IACJ;EACJ;EACAoD,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC5C,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACU,MAAM,CAAC,CAAC,IAAI,CAACC,QAAQ,CAAC;IACvC;IACA,IAAI,CAACW,SAAS,CAAC,CAAC;EACpB;EACAuB,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC7D,QAAQ,EAAE;MAChB,IAAI,CAACyD,WAAW,CAAC,CAAC;IACtB;EACJ;EACAK,iBAAiBA,CAAA,EAAG;IAChB;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC5C,EAAE,CAAC6C,YAAY,CAAC,IAAI,CAAC,EAAE;MAC5B,IAAI,CAAClE,OAAO,GAAG,IAAI,CAACqB,EAAE,CAAC8C,YAAY,CAAC,IAAI,CAAC;IAC7C;IACA,IAAI,CAAC9D,mBAAmB,GAAG5C,qBAAqB,CAAC,IAAI,CAAC4D,EAAE,CAAC;EAC7D;EACA+C,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACV,gBAAgB,GAAG,IAAI,CAAChE,QAAQ;IACrC,IAAI,CAACsB,YAAY,CAAC,CAAC;IACnB,IAAI,CAAC4C,WAAW,CAAC,CAAC;IAClB,IAAI,CAAC1D,OAAO,GAAG,IAAI;EACvB;EACAmE,iBAAiBA,CAAA,EAAG;IAChB,MAAM;MAAEhD;IAAG,CAAC,GAAG,IAAI;IACnB,IAAI,CAACiD,oBAAoB,GAAGnH,0BAA0B,CAACkE,EAAE,CAAC;IAC1D,IAAI,CAACuC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACH,eAAe,CAAC,CAAC;IACtB,IAAI,CAACM,eAAe,CAAC,CAAC;IACtB,IAAI,CAACD,qBAAqB,CAAC,CAAC;IAC5B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC5D,OAAO,EAAE;MACd,IAAI,CAACc,YAAY,CAAC,CAAC;IACvB;IACA,IAAI,CAACV,SAAS,GAAGvD,qBAAqB,CAAC,IAAI,CAACsE,EAAE,CAAC;EACnD;EACAkD,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACpD,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACqD,OAAO,CAAC,CAAC;MACtB,IAAI,CAACrD,OAAO,GAAGyB,SAAS;IAC5B;EACJ;EACA6B,QAAQA,CAAA,EAAG;IACP,IAAIC,EAAE;IACN,MAAMhE,KAAK,GAAG,CAACgE,EAAE,GAAG,IAAI,CAAChE,KAAK,MAAM,IAAI,IAAIgE,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC;IAClE,IAAI,IAAI,CAAC7D,SAAS,EAAE;MAChB,IAAI,OAAOH,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOA,KAAK;MAChB;MACA,OAAO;QACHI,KAAK,EAAE,CAAC;QACRC,KAAK,EAAEL;MACX,CAAC;IACL,CAAC,MACI;MACD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAOA,KAAK,CAACK,KAAK;MACtB;MACA,OAAOL,KAAK;IAChB;EACJ;EACA;EACA+B,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC6B,oBAAoB,CAACK,gBAAgB,CAAC,CAAC,EAAE;MAC9C,IAAI,CAAChF,QAAQ,CAAC0C,IAAI,CAAC;QACfuC,WAAW,EAAE,IAAI;QACjB,sBAAsB,EAAE,IAAI,CAAC9C,QAAQ;QACrC;QACA0B,MAAM,EAAE,CAAC,CAAC,IAAI,CAACA;MACnB,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIjB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC7B,KAAK,GAAG,IAAI,CAACE,mBAAmB,CAAC,IAAI,CAACF,KAAK,CAAC;IACjD,IAAI,CAACjB,SAAS,CAAC4C,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIe,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC3B,gBAAgB,CAACuC,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACE,mBAAmB,CAAC,IAAI,CAACF,KAAK;IAAE,CAAC,CAAC;EAC/E;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgB,MAAMA,CAACmD,MAAM,EAAE;IACX,MAAM;MAAEvE,SAAS;MAAEqC;IAAY,CAAC,GAAG,IAAI;IACvC,MAAMmC,QAAQ,GAAGD,MAAM,CAACC,QAAQ;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAIxE,SAAS,IAAI,IAAI,CAACC,qBAAqB,KAAKqC,SAAS,EAAE;MACvD,IAAI,CAACrC,qBAAqB,GAAGvD,qBAAqB,CAACsD,SAAS,CAAC;IACjE;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIqC,WAAW,KAAKC,SAAS,EAAE;MAC3B,IAAI,CAACmC,cAAc,CAACD,QAAQ,CAAC;IACjC;IACA,IAAI,CAACE,MAAM,CAACF,QAAQ,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIlD,KAAKA,CAACiD,MAAM,EAAE;IACV,MAAM;MAAEvE,SAAS;MAAEC;IAAsB,CAAC,GAAG,IAAI;IACjD,MAAMuE,QAAQ,GAAGD,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACI,OAAO;IAClD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAACtC,WAAW,KAAKC,SAAS,EAAE;MAChC,IAAI,CAACmC,cAAc,CAACD,QAAQ,CAAC;IACjC;IACA;AACR;AACA;AACA;AACA;AACA;IACQ,IAAIxE,SAAS,IAAIC,qBAAqB,KAAKqC,SAAS,EAAE;MAClD3F,mBAAmB,CAACqD,SAAS,EAAEC,qBAAqB,CAAC;IACzD;IACA;IACA,IAAI,CAACyE,MAAM,CAACF,QAAQ,CAAC;IACrB;AACR;AACA;AACA;IACQ,IAAI,CAACnC,WAAW,GAAGC,SAAS;IAC5B,IAAI,CAACL,eAAe,CAAC,CAAC;IACtB,IAAI,CAACxC,cAAc,CAACsC,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACE,mBAAmB,CAAC,IAAI,CAACF,KAAK;IAAE,CAAC,CAAC;EAC7E;EACAsE,MAAMA,CAACF,QAAQ,EAAE;IACb;IACA;IACA,MAAMI,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,IAAIC,KAAK,GAAG9H,KAAK,CAAC,CAAC,EAAE,CAACyH,QAAQ,GAAGI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAItH,KAAK,CAAC,IAAI,CAACsD,EAAE,CAAC,EAAE;MAChB8D,KAAK,GAAG,CAAC,GAAGA,KAAK;IACrB;IACA,IAAI,IAAI,CAAC/B,KAAK,EAAE;MACZ;MACA+B,KAAK,GAAGG,YAAY,CAACC,YAAY,CAACJ,KAAK,EAAE,IAAI,CAACxE,GAAG,EAAE,IAAI,CAAC9B,GAAG,EAAE,IAAI,CAACqD,IAAI,CAAC,EAAE,IAAI,CAACvB,GAAG,EAAE,IAAI,CAAC9B,GAAG,CAAC;IAChG;IACA;IACA,IAAI,IAAI,CAAC8D,WAAW,KAAK,GAAG,EAAE;MAC1B,IAAI,CAACR,MAAM,GAAGgD,KAAK;IACvB,CAAC,MACI;MACD,IAAI,CAAC/C,MAAM,GAAG+C,KAAK;IACvB;IACA;IACA,IAAI,CAAC7C,WAAW,CAAC,CAAC;EACtB;EACAyC,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAMI,IAAI,GAAI,IAAI,CAACA,IAAI,GAAG,IAAI,CAAChE,WAAW,CAACsE,qBAAqB,CAAC,CAAE;IACnE;IACA,IAAIL,KAAK,GAAG9H,KAAK,CAAC,CAAC,EAAE,CAACyH,QAAQ,GAAGI,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,EAAE,CAAC,CAAC;IAC5D,IAAItH,KAAK,CAAC,IAAI,CAACsD,EAAE,CAAC,EAAE;MAChB8D,KAAK,GAAG,CAAC,GAAGA,KAAK;IACrB;IACA,IAAI,CAACxC,WAAW,GAAG,CAAC,IAAI,CAAC9B,SAAS,IAAIjC,IAAI,CAAC6G,GAAG,CAAC,IAAI,CAACtD,MAAM,GAAGgD,KAAK,CAAC,GAAGvG,IAAI,CAAC6G,GAAG,CAAC,IAAI,CAACrD,MAAM,GAAG+C,KAAK,CAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,IAAI,CAACO,QAAQ,CAAC,IAAI,CAAC/C,WAAW,CAAC;EACnC;EACA,IAAIgD,IAAIA,CAAA,EAAG;IACP,OAAOJ,YAAY,CAAC,IAAI,CAACpD,MAAM,EAAE,IAAI,CAACxB,GAAG,EAAE,IAAI,CAAC9B,GAAG,EAAE,IAAI,CAACqD,IAAI,CAAC;EACnE;EACA,IAAI0D,IAAIA,CAAA,EAAG;IACP,OAAOL,YAAY,CAAC,IAAI,CAACnD,MAAM,EAAE,IAAI,CAACzB,GAAG,EAAE,IAAI,CAAC9B,GAAG,EAAE,IAAI,CAACqD,IAAI,CAAC;EACnE;EACA,IAAI2D,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAAChF,SAAS,EAAE;MAChB,OAAOjC,IAAI,CAAC+B,GAAG,CAAC,IAAI,CAACwB,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;IAC7C;IACA,MAAM;MAAEkB;IAAe,CAAC,GAAG,IAAI;IAC/B,IAAIA,cAAc,IAAI,IAAI,EAAE;MACxB,OAAO,CAAC;IACZ;IACA,OAAOgC,YAAY,CAAChC,cAAc,EAAE,IAAI,CAAC3C,GAAG,EAAE,IAAI,CAAC9B,GAAG,CAAC;EAC3D;EACA,IAAIiH,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAACjF,SAAS,EAAE;MAChB,OAAOjC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACsD,MAAM,EAAE,IAAI,CAACC,MAAM,CAAC;IAC7C;IACA,OAAO,IAAI,CAACD,MAAM;EACtB;EACAyB,WAAWA,CAAA,EAAG;IACV,MAAMlD,KAAK,GAAG,IAAI,CAAC+D,QAAQ,CAAC,CAAC;IAC7B,MAAM;MAAE9D,GAAG;MAAE9B;IAAI,CAAC,GAAG,IAAI;IACzB,IAAI,IAAI,CAACgC,SAAS,EAAE;MAChB,IAAI,CAACsB,MAAM,GAAGmD,YAAY,CAAC5E,KAAK,CAACI,KAAK,EAAEH,GAAG,EAAE9B,GAAG,CAAC;MACjD,IAAI,CAACuD,MAAM,GAAGkD,YAAY,CAAC5E,KAAK,CAACK,KAAK,EAAEJ,GAAG,EAAE9B,GAAG,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACsD,MAAM,GAAGmD,YAAY,CAAC5E,KAAK,EAAEC,GAAG,EAAE9B,GAAG,CAAC;IAC/C;EACJ;EACAyD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnC,QAAQ,GAAG,IAAI;IACpB,MAAM;MAAEwF,IAAI;MAAEC;IAAK,CAAC,GAAG,IAAI;IAC3B,IAAI,CAAClF,KAAK,GAAG,CAAC,IAAI,CAACG,SAAS,GACtB8E,IAAI,GACJ;MACE7E,KAAK,EAAElC,IAAI,CAAC+B,GAAG,CAACgF,IAAI,EAAEC,IAAI,CAAC;MAC3B7E,KAAK,EAAEnC,IAAI,CAACC,GAAG,CAAC8G,IAAI,EAAEC,IAAI;IAC9B,CAAC;IACL,IAAI,CAAClG,QAAQ,CAAC2C,IAAI,CAAC;MAAE3B,KAAK,EAAE,IAAI,CAACA;IAAM,CAAC,CAAC;IACzC,IAAI,CAACP,QAAQ,GAAG,KAAK;EACzB;EACAuF,QAAQA,CAAC1D,IAAI,EAAE;IACX,IAAI,IAAI,CAACX,EAAE,CAAC0E,UAAU,EAAE;MACpB,MAAMC,MAAM,GAAG,IAAI,CAAC3E,EAAE,CAAC0E,UAAU,CAACE,aAAa,CAACjE,IAAI,KAAK,GAAG,GAAG,eAAe,GAAG,eAAe,CAAC;MACjG,IAAIgE,MAAM,EAAE;QACRA,MAAM,CAACE,KAAK,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC3F,2BAA2B,EAAE;MACnC1C,eAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAACuD,EAAE,CAAC;MAC9M,IAAI,IAAI,CAACmC,MAAM,EAAE;QACb1F,eAAe,CAAC;AAChC;AACA,qHAAqH,EAAE,IAAI,CAACuD,EAAE,CAAC;MACnH;MACA,IAAI,CAACb,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAM;MAAEa,EAAE;MAAEsB,WAAW;MAAEb,QAAQ;MAAEmB,GAAG;MAAEjD;IAAQ,CAAC,GAAG,IAAI;IACxD,MAAMoG,IAAI,GAAGjI,UAAU,CAAC,IAAI,CAAC;IAC7BT,iBAAiB,CAAC,IAAI,EAAE2D,EAAE,EAAE,IAAI,CAAC0B,IAAI,EAAEsD,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAAC,CAAC,EAAE3C,QAAQ,CAAC;IACjF,OAAQpF,CAAC,CAACE,IAAI,EAAE;MAAE2J,SAAS,EAAE,IAAI,CAAC7D,OAAO;MAAE8D,UAAU,EAAE,IAAI,CAAChE,MAAM;MAAEiE,EAAE,EAAEzG,OAAO;MAAE0G,KAAK,EAAE1I,kBAAkB,CAAC,IAAI,CAAC6E,KAAK,EAAE;QAC/G,CAACuD,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEnI,WAAW,CAAC,UAAU,EAAEoD,EAAE,CAAC;QACtC,gBAAgB,EAAES,QAAQ;QAC1B,eAAe,EAAEa,WAAW,KAAKC,SAAS;QAC1C,eAAe,EAAEK,GAAG;QACpB,cAAc,EAAE;MACpB,CAAC;IAAE,CAAC,EAAEvG,CAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE,IAAI,CAAC4D,iBAAiB,CAAC,CAAC,EAAEjK,CAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC;EACjG;EACA;AACJ;AACA;EACI,IAAI6D,mBAAmBA,CAAA,EAAG;IACtB,OAAO,IAAI,CAACvF,EAAE,CAAC4E,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;EAC3D;EACA;AACJ;AACA;EACI,IAAIY,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACxF,EAAE,CAAC4E,aAAa,CAAC,cAAc,CAAC,KAAK,IAAI;EACzD;EACAa,WAAWA,CAAA,EAAG;IACV,MAAM;MAAEhF,QAAQ;MAAET,EAAE;MAAE0F,QAAQ;MAAE/G,OAAO;MAAEiD,GAAG;MAAEN,WAAW;MAAEY,cAAc;MAAEP;IAAM,CAAC,GAAG,IAAI;IACzF,MAAMgE,MAAM,GAAG/I,WAAW,CAAC,UAAU,EAAEoD,EAAE,CAAC;IAC1C;AACR;AACA;AACA;IACQ,MAAM4F,eAAe,GAAIF,QAAQ,KAAKxD,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,OAAO,CAAC,IAAK,IAAI,CAACqD,mBAAmB;IAC5H,MAAMM,oBAAoB,GAAGF,MAAM,IAAI,CAACC,eAAe;IACvD;AACR;AACA;AACA;IACQ,MAAME,aAAa,GAAIJ,QAAQ,IAAIxD,cAAc,KAAK,KAAK,IAAK,IAAI,CAACsD,iBAAiB;IACtF,MAAMO,kBAAkB,GAAGJ,MAAM,IAAI,CAACG,aAAa;IACnD,MAAMf,IAAI,GAAGjI,UAAU,CAAC,IAAI,CAAC;IAC7BT,iBAAiB,CAAC,IAAI,EAAE2D,EAAE,EAAE,IAAI,CAAC0B,IAAI,EAAEsD,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC7B,QAAQ,CAAC,CAAC,CAAC,EAAE3C,QAAQ,CAAC;IACjF,OAAQpF,CAAC,CAACE,IAAI,EAAE;MAAE2J,SAAS,EAAE,IAAI,CAAC7D,OAAO;MAAE8D,UAAU,EAAE,IAAI,CAAChE,MAAM;MAAEiE,EAAE,EAAEzG,OAAO;MAAE0G,KAAK,EAAE1I,kBAAkB,CAAC,IAAI,CAAC6E,KAAK,EAAE;QAC/G,CAACuD,IAAI,GAAG,IAAI;QACZ,SAAS,EAAEY,MAAM;QACjB,gBAAgB,EAAElF,QAAQ;QAC1B,eAAe,EAAEa,WAAW,KAAKC,SAAS;QAC1C,eAAe,EAAEK,GAAG;QACpB,CAAC,yBAAyBM,cAAc,EAAE,GAAG,IAAI;QACjD,6BAA6B,EAAE2D,oBAAoB;QACnD,2BAA2B,EAAEE;MACjC,CAAC;IAAE,CAAC,EAAE1K,CAAC,CAAC,OAAO,EAAE;MAAEgK,KAAK,EAAE,eAAe;MAAED,EAAE,EAAE;IAAc,CAAC,EAAE/J,CAAC,CAAC,KAAK,EAAE;MAAEgK,KAAK,EAAE;QAC9E,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAE,CAACK;MAClC,CAAC;MAAEM,IAAI,EAAE;IAAQ,CAAC,EAAErE,KAAK,KAAKJ,SAAS,GAAGlG,CAAC,CAAC,KAAK,EAAE;MAAEgK,KAAK,EAAE;IAAa,CAAC,EAAE1D,KAAK,CAAC,GAAGtG,CAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC,EAAErG,CAAC,CAAC,KAAK,EAAE;MAAEgK,KAAK,EAAE;IAAiB,CAAC,EAAEhK,CAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAE,IAAI,CAAC4D,iBAAiB,CAAC,CAAC,EAAEjK,CAAC,CAAC,MAAM,EAAE;MAAEqG,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,CAAC,CAAC;EACvP;EACA,IAAIgE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC/D,KAAK,KAAKJ,SAAS,IAAI,IAAI,CAACvB,EAAE,CAAC4E,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI;EACvF;EACAU,iBAAiBA,CAAA,EAAG;IAChB,IAAIjC,EAAE;IACN,MAAM;MAAE/D,GAAG;MAAE9B,GAAG;MAAEqD,IAAI;MAAEb,EAAE;MAAEU,cAAc;MAAEY,WAAW;MAAEb,QAAQ;MAAEmB,GAAG;MAAE4C,UAAU;MAAEC,UAAU;MAAEzF,mBAAmB;MAAEL,OAAO;MAAEkD;IAAc,CAAC,GAAG,IAAI;IACpJ;AACR;AACA;AACA;AACA;IACQ,IAAI;MAAEoE;IAAU,CAAC,GAAG1J,YAAY,CAACyD,EAAE,EAAErB,OAAO,CAAC;IAC7C,IAAIsH,SAAS,KAAK1E,SAAS,IAAI0E,SAAS,KAAK,IAAI,EAAE;MAC/CA,SAAS,GAAGjH,mBAAmB,CAAC,YAAY,CAAC;IACjD;IACA,IAAIkH,QAAQ,GAAG,GAAG1B,UAAU,GAAG,GAAG,GAAG;IACrC,IAAI2B,MAAM,GAAG,GAAG,GAAG,GAAG1B,UAAU,GAAG,GAAG,GAAG;IACzC,MAAM2B,GAAG,GAAG1J,KAAK,CAAC,IAAI,CAACsD,EAAE,CAAC;IAC1B,MAAMqG,KAAK,GAAGD,GAAG,GAAG,OAAO,GAAG,MAAM;IACpC,MAAME,GAAG,GAAGF,GAAG,GAAG,MAAM,GAAG,OAAO;IAClC,MAAMG,SAAS,GAAIC,IAAI,IAAK;MACxB,OAAO;QACH,CAACH,KAAK,GAAGG,IAAI,CAACH,KAAK;MACvB,CAAC;IACL,CAAC;IACD,IAAI,IAAI,CAAC7G,SAAS,KAAK,KAAK,EAAE;MAC1B;AACZ;AACA;AACA;MACY,IAAI,IAAI,CAAC8E,IAAI,IAAI,CAACjB,EAAE,GAAG,IAAI,CAACpB,cAAc,MAAM,IAAI,IAAIoB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI,CAAC/D,GAAG,CAAC,EAAE;QACpF;AAChB;AACA;AACA;AACA;AACA;AACA;QACgB4G,QAAQ,GAAG,GAAGzB,UAAU,GAAG,GAAG,GAAG;QACjC0B,MAAM,GAAG,GAAG,GAAG,GAAG3B,UAAU,GAAG,GAAG,GAAG;MACzC,CAAC,MACI;QACD;AAChB;AACA;AACA;AACA;AACA;QACgB0B,QAAQ,GAAG,GAAG1B,UAAU,GAAG,GAAG,GAAG;QACjC2B,MAAM,GAAG,GAAG,GAAG,GAAG1B,UAAU,GAAG,GAAG,GAAG;MACzC;IACJ;IACA,MAAMgC,QAAQ,GAAG;MACb,CAACJ,KAAK,GAAGH,QAAQ;MACjB,CAACI,GAAG,GAAGH;IACX,CAAC;IACD,MAAMnE,KAAK,GAAG,EAAE;IAChB,IAAI,IAAI,CAACD,KAAK,IAAI,IAAI,CAACC,KAAK,EAAE;MAC1B,KAAK,IAAI3C,KAAK,GAAGC,GAAG,EAAED,KAAK,IAAI7B,GAAG,EAAE6B,KAAK,IAAIwB,IAAI,EAAE;QAC/C,MAAMiD,KAAK,GAAGG,YAAY,CAAC5E,KAAK,EAAEC,GAAG,EAAE9B,GAAG,CAAC;QAC3C,MAAMkJ,QAAQ,GAAGnJ,IAAI,CAAC+B,GAAG,CAACkF,UAAU,EAAEC,UAAU,CAAC;QACjD,MAAMkC,QAAQ,GAAGpJ,IAAI,CAACC,GAAG,CAACgH,UAAU,EAAEC,UAAU,CAAC;QACjD,MAAM+B,IAAI,GAAG;UACT1C,KAAK;UACL;AACpB;AACA;AACA;UACoB8C,MAAM,EAAE9C,KAAK,IAAI4C,QAAQ,IAAI5C,KAAK,IAAI6C;QAC1C,CAAC;QACDH,IAAI,CAACH,KAAK,CAAC,GAAG,GAAGvC,KAAK,GAAG,GAAG,GAAG;QAC/B9B,KAAK,CAAC6E,IAAI,CAACL,IAAI,CAAC;MACpB;IACJ;IACA,IAAIM,UAAU;IACd,IAAI,CAAC,IAAI,CAAC7D,oBAAoB,CAACK,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACoC,QAAQ,EAAE;MAChEoB,UAAU,GAAG,aAAa;IAC9B;IACA,OAAQzL,CAAC,CAAC,KAAK,EAAE;MAAEgK,KAAK,EAAE,cAAc;MAAE0B,GAAG,EAAGC,OAAO,IAAM,IAAI,CAACnH,WAAW,GAAGmH,OAAQ;MACpF;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACYC,WAAW,EAAG3G,EAAE,IAAK;QACjB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACgB,IAAI,IAAI,CAACgB,WAAW,KAAKC,SAAS,EAAE;UAChC,IAAI,CAACnB,OAAO,CAAC,CAAC;UACd,IAAI,CAACG,KAAK,CAACD,EAAE,CAAC;QAClB;MACJ;IAAE,CAAC,EAAE0B,KAAK,CAACvE,GAAG,CAAE+I,IAAI,IAAMnL,CAAC,CAAC,KAAK,EAAE;MAAE6L,KAAK,EAAEX,SAAS,CAACC,IAAI,CAAC;MAAEW,IAAI,EAAE,cAAc;MAAE9B,KAAK,EAAE;QACtF,YAAY,EAAE,IAAI;QAClB,mBAAmB,EAAEmB,IAAI,CAACI;MAC9B,CAAC;MAAEZ,IAAI,EAAEQ,IAAI,CAACI,MAAM,GAAG,aAAa,GAAG;IAAO,CAAC,CAAE,CAAC,EAAEvL,CAAC,CAAC,KAAK,EAAE;MAAEgK,KAAK,EAAE;IAAsB,CAAC,EAAEhK,CAAC,CAAC,KAAK,EAAE;MAAEgK,KAAK,EAAE,WAAW;MAAE8B,IAAI,EAAE,cAAc;MAAEnB,IAAI,EAAE;IAAM,CAAC,CAAC,EAAE3K,CAAC,CAAC,KAAK,EAAE;MAAEgK,KAAK,EAAE;QAClL,WAAW,EAAE,IAAI;QACjB,kBAAkB,EAAE,IAAI;QACxB,WAAW,EAAErD,KAAK,CAAC7E,MAAM,GAAG;MAChC,CAAC;MAAEgK,IAAI,EAAE,cAAc;MAAED,KAAK,EAAET,QAAQ;MAAET,IAAI,EAAE;IAAa,CAAC,CAAC,CAAC,EAAEoB,UAAU,CAAChB,GAAG,EAAE;MAClFzF,IAAI,EAAE,GAAG;MACT0G,OAAO,EAAE/F,WAAW,KAAK,GAAG;MAC5BjC,KAAK,EAAE,IAAI,CAACiF,IAAI;MAChBR,KAAK,EAAE,IAAI,CAAChD,MAAM;MAClBc,GAAG;MACHC,YAAY;MACZpB,QAAQ;MACRC,cAAc;MACdpB,GAAG;MACH9B,GAAG;MACHyI,SAAS;MACTa;IACJ,CAAC,CAAC,EAAE,IAAI,CAACtH,SAAS,IACd4H,UAAU,CAAChB,GAAG,EAAE;MACZzF,IAAI,EAAE,GAAG;MACT0G,OAAO,EAAE/F,WAAW,KAAK,GAAG;MAC5BjC,KAAK,EAAE,IAAI,CAACkF,IAAI;MAChBT,KAAK,EAAE,IAAI,CAAC/C,MAAM;MAClBa,GAAG;MACHC,YAAY;MACZpB,QAAQ;MACRC,cAAc;MACdpB,GAAG;MACH9B,GAAG;MACHyI,SAAS;MACTa;IACJ,CAAC,CAAC,CAAC;EACX;EACAQ,MAAMA,CAAA,EAAG;IACL,MAAM;MAAErE;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAACK,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACwB,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACW,WAAW,CAAC,CAAC;EAClG;EACA,IAAIzF,EAAEA,CAAA,EAAG;IAAE,OAAOvE,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAW8L,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,KAAK,EAAE,CAAC,YAAY,CAAC;MACrB,gBAAgB,EAAE,CAAC,uBAAuB,CAAC;MAC3C,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,OAAO,EAAE,CAAC,cAAc;IAC5B,CAAC;EAAE;AACP,CAAC;AACD,MAAMH,UAAU,GAAGA,CAAChB,GAAG,EAAE;EAAEzF,IAAI;EAAEtB,KAAK;EAAEyE,KAAK;EAAExE,GAAG;EAAE9B,GAAG;EAAEiD,QAAQ;EAAE4G,OAAO;EAAEzF,GAAG;EAAElB,cAAc;EAAEuF,SAAS;EAAEa,UAAU;EAAEjF;AAAc,CAAC,KAAK;EACxI,MAAMwE,KAAK,GAAGD,GAAG,GAAG,OAAO,GAAG,MAAM;EACpC,MAAMoB,SAAS,GAAGA,CAAA,KAAM;IACpB,MAAMN,KAAK,GAAG,CAAC,CAAC;IAChBA,KAAK,CAACb,KAAK,CAAC,GAAG,GAAGvC,KAAK,GAAG,GAAG,GAAG;IAChC,OAAOoD,KAAK;EAChB,CAAC;EACD,OAAQ7L,CAAC,CAAC,KAAK,EAAE;IAAEoM,SAAS,EAAGnH,EAAE,IAAK;MAC9B,MAAMoH,GAAG,GAAGpH,EAAE,CAACoH,GAAG;MAClB,IAAIA,GAAG,KAAK,WAAW,IAAIA,GAAG,KAAK,WAAW,EAAE;QAC5ChH,cAAc,CAACC,IAAI,EAAE,KAAK,CAAC;QAC3BL,EAAE,CAACqH,cAAc,CAAC,CAAC;QACnBrH,EAAE,CAACsH,eAAe,CAAC,CAAC;MACxB,CAAC,MACI,IAAIF,GAAG,KAAK,YAAY,IAAIA,GAAG,KAAK,SAAS,EAAE;QAChDhH,cAAc,CAACC,IAAI,EAAE,IAAI,CAAC;QAC1BL,EAAE,CAACqH,cAAc,CAAC,CAAC;QACnBrH,EAAE,CAACsH,eAAe,CAAC,CAAC;MACxB;IACJ,CAAC;IAAEvC,KAAK,EAAE;MACN,mBAAmB,EAAE,IAAI;MACzB,cAAc,EAAE1E,IAAI,KAAK,GAAG;MAC5B,cAAc,EAAEA,IAAI,KAAK,GAAG;MAC5B,oBAAoB,EAAE0G,OAAO;MAC7B,gBAAgB,EAAEhI,KAAK,KAAKC,GAAG;MAC/B,gBAAgB,EAAED,KAAK,KAAK7B,GAAG;MAC/B,iBAAiB,EAAE,IAAI;MACvB,eAAe,EAAE;IACrB,CAAC;IAAE0J,KAAK,EAAEM,SAAS,CAAC,CAAC;IAAEL,IAAI,EAAE,QAAQ;IAAEU,QAAQ,EAAEpH,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;IAAE,YAAY,EAAEqG,UAAU,KAAKvF,SAAS,GAAG0E,SAAS,GAAG,IAAI;IAAE,iBAAiB,EAAEa,UAAU,KAAKvF,SAAS,GAAGuF,UAAU,GAAG,IAAI;IAAE,eAAe,EAAExH,GAAG;IAAE,eAAe,EAAE9B,GAAG;IAAE,eAAe,EAAEiD,QAAQ,GAAG,MAAM,GAAG,IAAI;IAAE,eAAe,EAAEpB;EAAM,CAAC,EAAEuC,GAAG,IAAKvG,CAAC,CAAC,KAAK,EAAE;IAAEgK,KAAK,EAAE,WAAW;IAAE8B,IAAI,EAAE,cAAc;IAAEnB,IAAI,EAAE;EAAM,CAAC,EAAEnE,YAAY,CAACxC,KAAK,CAAC,CAAE,EAAEhE,CAAC,CAAC,KAAK,EAAE;IAAEgK,KAAK,EAAE,YAAY;IAAE8B,IAAI,EAAE,cAAc;IAAEnB,IAAI,EAAE;EAAO,CAAC,CAAC,CAAC;AACle,CAAC;AACD,MAAM9B,YAAY,GAAGA,CAACJ,KAAK,EAAExE,GAAG,EAAE9B,GAAG,EAAEqD,IAAI,KAAK;EAC5C,IAAIxB,KAAK,GAAG,CAAC7B,GAAG,GAAG8B,GAAG,IAAIwE,KAAK;EAC/B,IAAIjD,IAAI,GAAG,CAAC,EAAE;IACV;IACAxB,KAAK,GAAG9B,IAAI,CAACuE,KAAK,CAACzC,KAAK,GAAGwB,IAAI,CAAC,GAAGA,IAAI,GAAGvB,GAAG;EACjD;EACA,MAAMwI,YAAY,GAAG9L,KAAK,CAACsD,GAAG,EAAED,KAAK,EAAE7B,GAAG,CAAC;EAC3C,OAAOJ,uBAAuB,CAAC0K,YAAY,EAAExI,GAAG,EAAE9B,GAAG,EAAEqD,IAAI,CAAC;AAChE,CAAC;AACD,MAAMoD,YAAY,GAAGA,CAAC5E,KAAK,EAAEC,GAAG,EAAE9B,GAAG,KAAK;EACtC,OAAOxB,KAAK,CAAC,CAAC,EAAE,CAACqD,KAAK,GAAGC,GAAG,KAAK9B,GAAG,GAAG8B,GAAG,CAAC,EAAE,CAAC,CAAC;AACnD,CAAC;AACD,IAAIV,QAAQ,GAAG,CAAC;AAChBZ,KAAK,CAACkJ,KAAK,GAAG;EACVa,GAAG,EAAElK,iBAAiB;EACtBmK,EAAE,EAAEjK;AACR,CAAC;AAED,SAASC,KAAK,IAAIiK,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}