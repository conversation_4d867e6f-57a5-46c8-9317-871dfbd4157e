/* App Component Styles */
ion-app {
  font-family: 'Roboto', sans-serif;
}

/* Global Material Design Styles */
.mat-toolbar {
  background: var(--ion-color-primary);
  color: white;
}

.mat-card {
  margin: 16px;
  border-radius: 8px;
}

.mat-raised-button {
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 500;
}

/* Custom App Styles */
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow: auto;
}
