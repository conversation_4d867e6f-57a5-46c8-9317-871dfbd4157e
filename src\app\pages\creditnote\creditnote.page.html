<ion-header [translucent]="true" class="material-header">
  <mat-toolbar class="material-toolbar">
    <button mat-icon-button (click)="goBack()" aria-label="Back">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <span class="toolbar-title">Credit Notes</span>
    <span class="spacer"></span>
    <button mat-icon-button (click)="refreshData()" aria-label="Refresh" [disabled]="isLoading">
      <mat-icon [class.spinning]="isLoading">refresh</mat-icon>
    </button>
  </mat-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="material-content">
  <div class="creditnote-container">
    
    <!-- Summary Cards -->
    <div class="summary-section">
      <div class="summary-cards">
        <mat-card class="summary-card total">
          <mat-card-content>
            <div class="card-content">
              <mat-icon class="card-icon">account_balance</mat-icon>
              <div class="card-info">
                <h3>Total Amount</h3>
                <p class="amount">₹{{ getTotalAmount() | number:'1.2-2' }}</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="summary-card approved">
          <mat-card-content>
            <div class="card-content">
              <mat-icon class="card-icon">check_circle</mat-icon>
              <div class="card-info">
                <h3>Approved</h3>
                <p class="amount">₹{{ getApprovedAmount() | number:'1.2-2' }}</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>

        <mat-card class="summary-card pending">
          <mat-card-content>
            <div class="card-content">
              <mat-icon class="card-icon">schedule</mat-icon>
              <div class="card-info">
                <h3>Pending</h3>
                <p class="amount">₹{{ getPendingAmount() | number:'1.2-2' }}</p>
              </div>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </div>

    <!-- Search Section -->
    <mat-card class="search-card">
      <mat-card-header>
        <mat-card-title class="search-title">
          <mat-icon class="title-icon">search</mat-icon>
          Search Credit Notes
        </mat-card-title>
        <mat-card-subtitle>Filter credit notes by ID, invoice number, or customer</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
          <div class="search-row">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search</mat-label>
              <input 
                matInput 
                formControlName="searchTerm" 
                placeholder="Enter credit note ID, invoice number, or customer name">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
            
            <mat-form-field appearance="outline" class="status-field">
              <mat-label>Status</mat-label>
              <mat-select formControlName="status">
                <mat-option value="">All Status</mat-option>
                <mat-option value="approved">Approved</mat-option>
                <mat-option value="pending">Pending</mat-option>
                <mat-option value="processing">Processing</mat-option>
                <mat-option value="rejected">Rejected</mat-option>
              </mat-select>
            </mat-form-field>
            
            <button 
              mat-raised-button 
              color="primary" 
              type="submit"
              [disabled]="isLoading"
              class="search-button">
              <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
              <mat-icon *ngIf="!isLoading">search</mat-icon>
              Search
            </button>
            
            <button 
              mat-button 
              type="button"
              (click)="clearSearch()"
              class="clear-button">
              <mat-icon>clear</mat-icon>
              Clear
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Results Section -->
    <mat-card class="results-card">
      <mat-card-header>
        <mat-card-title class="results-title">
          <mat-icon class="title-icon">receipt</mat-icon>
          Credit Notes
          <span class="results-count" matBadge="{{ dataSource.filteredData.length }}" matBadgeColor="primary">
          </span>
        </mat-card-title>
        <mat-card-subtitle>Click on any credit note to view detailed information</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content class="table-content">
        <!-- Loading Spinner -->
        <div *ngIf="isLoading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading credit notes...</p>
        </div>

        <!-- Desktop Table View -->
        <div class="table-container desktop-view" *ngIf="!isLoading">
          <table mat-table [dataSource]="dataSource" matSort class="creditnotes-table">
            
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Credit Note ID</th>
              <td mat-cell *matCellDef="let creditNote" class="id-cell">
                <span class="creditnote-id">{{ creditNote.id }}</span>
              </td>
            </ng-container>

            <!-- Invoice Number Column -->
            <ng-container matColumnDef="invoiceNumber">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Invoice Number</th>
              <td mat-cell *matCellDef="let creditNote">{{ creditNote.invoiceNumber }}</td>
            </ng-container>

            <!-- Amount Column -->
            <ng-container matColumnDef="amount">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>
              <td mat-cell *matCellDef="let creditNote" class="amount-cell">
                <span class="amount">₹{{ creditNote.amount | number:'1.2-2' }}</span>
              </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
              <td mat-cell *matCellDef="let creditNote">
                <ion-badge [color]="getStatusColor(creditNote.status)" class="status-badge">
                  {{ creditNote.status }}
                </ion-badge>
              </td>
            </ng-container>

            <!-- Date Issued Column -->
            <ng-container matColumnDef="dateIssued">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Date Issued</th>
              <td mat-cell *matCellDef="let creditNote">{{ creditNote.dateIssued | date:'short' }}</td>
            </ng-container>

            <!-- Due Date Column -->
            <ng-container matColumnDef="dueDate">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Due Date</th>
              <td mat-cell *matCellDef="let creditNote">{{ creditNote.dueDate | date:'short' }}</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let creditNote">
                <button 
                  mat-icon-button 
                  color="primary" 
                  (click)="viewDetails(creditNote)"
                  matTooltip="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button 
                  mat-icon-button 
                  color="accent" 
                  (click)="downloadPDF(creditNote)"
                  matTooltip="Download PDF">
                  <mat-icon>download</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
                class="creditnote-row"
                (click)="viewDetails(row)"></tr>
          </table>

          <mat-paginator 
            [pageSizeOptions]="[5, 10, 20]" 
            showFirstLastButtons
            class="table-paginator">
          </mat-paginator>
        </div>

        <!-- Mobile Card View -->
        <div class="mobile-view" *ngIf="!isLoading">
          <mat-expansion-panel 
            *ngFor="let creditNote of dataSource.filteredData" 
            class="creditnote-card">
            
            <mat-expansion-panel-header>
              <mat-panel-title>
                <div class="card-title">
                  <span class="creditnote-id">{{ creditNote.id }}</span>
                  <ion-badge [color]="getStatusColor(creditNote.status)" class="status-badge">
                    {{ creditNote.status }}
                  </ion-badge>
                </div>
              </mat-panel-title>
              <mat-panel-description>
                <div class="card-description">
                  <span class="amount">₹{{ creditNote.amount | number:'1.2-2' }}</span>
                </div>
              </mat-panel-description>
            </mat-expansion-panel-header>

            <div class="card-content">
              <div class="info-row">
                <span class="label">Invoice Number:</span>
                <span class="value">{{ creditNote.invoiceNumber }}</span>
              </div>
              <div class="info-row">
                <span class="label">Customer:</span>
                <span class="value">{{ creditNote.customerName }}</span>
              </div>
              <div class="info-row">
                <span class="label">Date Issued:</span>
                <span class="value">{{ creditNote.dateIssued | date:'short' }}</span>
              </div>
              <div class="info-row">
                <span class="label">Due Date:</span>
                <span class="value">{{ creditNote.dueDate | date:'short' }}</span>
              </div>
              <div class="info-row">
                <span class="label">Reason:</span>
                <span class="value">{{ creditNote.reason }}</span>
              </div>
              
              <mat-divider class="card-divider"></mat-divider>
              
              <div class="card-actions">
                <button 
                  mat-raised-button 
                  color="primary" 
                  (click)="viewDetails(creditNote)">
                  <mat-icon>visibility</mat-icon>
                  View Details
                </button>
                <button 
                  mat-stroked-button 
                  color="accent" 
                  (click)="downloadPDF(creditNote)">
                  <mat-icon>download</mat-icon>
                  Download PDF
                </button>
              </div>
            </div>
          </mat-expansion-panel>
        </div>

        <!-- No Data Message -->
        <div *ngIf="!isLoading && dataSource.filteredData.length === 0" class="no-data">
          <mat-icon class="no-data-icon">receipt_long</mat-icon>
          <h3>No credit notes found</h3>
          <p>Try adjusting your search criteria or check back later.</p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</ion-content>
