import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';

export interface CreditNoteData {
  id: string;
  invoiceNumber: string;
  amount: number;
  currency: string;
  status: string;
  dateIssued: Date;
  dueDate: Date;
  reason: string;
  customerName: string;
}

@Component({
  selector: 'app-creditnote',
  templateUrl: './creditnote.page.html',
  styleUrls: ['./creditnote.page.scss'],
})
export class CreditnotePage implements OnInit, AfterViewInit {

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  searchForm!: FormGroup;
  displayedColumns: string[] = ['id', 'invoiceNumber', 'amount', 'status', 'dateIssued', 'dueDate', 'actions'];
  dataSource = new MatTableDataSource<CreditNoteData>();
  isLoading = false;
  selectedTabIndex = 0;

  // Sample data
  creditNotes: CreditNoteData[] = [
    {
      id: 'CN-2024-001',
      invoiceNumber: 'INV-2024-001',
      amount: 15000.00,
      currency: 'INR',
      status: 'Approved',
      dateIssued: new Date('2024-01-15'),
      dueDate: new Date('2024-02-15'),
      reason: 'Product quality issue - glass panels damaged',
      customerName: 'ABC Construction Ltd.'
    },
    {
      id: 'CN-2024-002',
      invoiceNumber: 'INV-2024-002',
      amount: 8500.00,
      currency: 'INR',
      status: 'Pending',
      dateIssued: new Date('2024-01-18'),
      dueDate: new Date('2024-02-18'),
      reason: 'Delivery delay compensation',
      customerName: 'XYZ Builders Pvt. Ltd.'
    },
    {
      id: 'CN-2024-003',
      invoiceNumber: 'INV-2024-003',
      amount: 22000.00,
      currency: 'INR',
      status: 'Processing',
      dateIssued: new Date('2024-01-20'),
      dueDate: new Date('2024-02-20'),
      reason: 'Incorrect specifications delivered',
      customerName: 'Modern Glass Solutions'
    },
    {
      id: 'CN-2024-004',
      invoiceNumber: 'INV-2024-004',
      amount: 5200.00,
      currency: 'INR',
      status: 'Rejected',
      dateIssued: new Date('2024-01-12'),
      dueDate: new Date('2024-02-12'),
      reason: 'Installation service issues',
      customerName: 'Premium Interiors'
    }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router
  ) {
    this.searchForm = this.formBuilder.group({
      searchTerm: [''],
      dateFrom: [''],
      dateTo: [''],
      status: ['']
    });
  }

  ngOnInit() {
    this.dataSource.data = this.creditNotes;
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  onSearch() {
    const searchTerm = this.searchForm.get('searchTerm')?.value;
    const status = this.searchForm.get('status')?.value;
    
    this.isLoading = true;
    
    // Simulate search delay
    setTimeout(() => {
      let filteredData = this.creditNotes;
      
      if (searchTerm) {
        filteredData = filteredData.filter(item => 
          item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.customerName.toLowerCase().includes(searchTerm.toLowerCase())
        );
      }
      
      if (status) {
        filteredData = filteredData.filter(item => 
          item.status.toLowerCase() === status.toLowerCase()
        );
      }
      
      this.dataSource.data = filteredData;
      this.isLoading = false;
    }, 1000);
  }

  clearSearch() {
    this.searchForm.reset();
    this.dataSource.data = this.creditNotes;
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'success';
      case 'processing':
        return 'primary';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'danger';
      default:
        return 'medium';
    }
  }

  getTotalAmount(): number {
    return this.dataSource.filteredData.reduce((total, item) => total + item.amount, 0);
  }

  getApprovedAmount(): number {
    return this.dataSource.filteredData
      .filter(item => item.status.toLowerCase() === 'approved')
      .reduce((total, item) => total + item.amount, 0);
  }

  getPendingAmount(): number {
    return this.dataSource.filteredData
      .filter(item => item.status.toLowerCase() === 'pending')
      .reduce((total, item) => total + item.amount, 0);
  }

  viewDetails(creditNote: CreditNoteData) {
    // Navigate to credit note details or open modal
    console.log('View details for:', creditNote);
  }

  downloadPDF(creditNote: CreditNoteData) {
    // Implement PDF download functionality
    console.log('Download PDF for:', creditNote);
  }

  goBack() {
    this.router.navigate(['/home']);
  }

  refreshData() {
    this.isLoading = true;
    
    // Simulate data refresh
    setTimeout(() => {
      this.dataSource.data = [...this.creditNotes];
      this.isLoading = false;
    }, 1500);
  }
}
