import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LoadingController, ToastController } from '@ionic/angular';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-feedback',
  templateUrl: './feedback.page.html',
  styleUrls: ['./feedback.page.scss'],
})
export class FeedbackPage implements OnInit {

  feedbackForm: FormGroup;
  isLoading = false;
  selectedRating = 5;

  feedbackCategories = [
    { value: 'service', label: 'Customer Service', icon: 'people' },
    { value: 'product', label: 'Product Quality', icon: 'star' },
    { value: 'delivery', label: 'Delivery Experience', icon: 'car' },
    { value: 'website', label: 'Website/App', icon: 'phone-portrait' },
    { value: 'support', label: 'Technical Support', icon: 'help-circle' },
    { value: 'other', label: 'Other', icon: 'ellipsis-horizontal' }
  ];

  ratingLabels = [
    { value: 1, label: 'Very Poor', color: '#f44336', icon: 'sad' },
    { value: 2, label: 'Poor', color: '#ff9800', icon: 'sad' },
    { value: 3, label: 'Average', color: '#ffc107', icon: 'remove-circle' },
    { value: 4, label: 'Good', color: '#4caf50', icon: 'happy' },
    { value: 5, label: 'Excellent', color: '#2196f3', icon: 'happy' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private loadingController: LoadingController,
    private toastController: ToastController,
    private snackBar: MatSnackBar
  ) {
    this.createForm();
  }

  ngOnInit() {}

  createForm() {
    this.feedbackForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.pattern(/^\d{10}$/)]],
      category: ['', Validators.required],
      rating: [5, [Validators.required, Validators.min(1), Validators.max(5)]],
      subject: ['', [Validators.required, Validators.minLength(5)]],
      message: ['', [Validators.required, Validators.minLength(10)]],
      wouldRecommend: [true],
      allowContact: [false]
    });
  }

  onRatingChange(rating: number) {
    this.selectedRating = rating;
    this.feedbackForm.patchValue({ rating });
  }

  getRatingInfo() {
    return this.ratingLabels.find(r => r.value === this.selectedRating) || this.ratingLabels[4];
  }

  async onSubmit() {
    if (this.feedbackForm.valid) {
      this.isLoading = true;
      
      const loading = await this.loadingController.create({
        message: 'Submitting feedback...',
        duration: 3000
      });
      
      await loading.present();
      
      // Simulate submission process
      setTimeout(async () => {
        this.isLoading = false;
        await loading.dismiss();
        
        // Show success message
        this.snackBar.open(
          'Thank you for your feedback! We appreciate your input.',
          'Close',
          {
            duration: 5000,
            panelClass: ['success-snackbar'],
            horizontalPosition: 'center',
            verticalPosition: 'top'
          }
        );
        
        // Reset form
        this.feedbackForm.reset();
        this.selectedRating = 5;
        this.feedbackForm.patchValue({ 
          rating: 5, 
          wouldRecommend: true, 
          allowContact: false 
        });
        
        // Navigate back to home
        setTimeout(() => {
          this.router.navigate(['/home']);
        }, 2000);
      }, 3000);
    } else {
      const toast = await this.toastController.create({
        message: 'Please fill in all required fields correctly.',
        duration: 3000,
        color: 'danger',
        position: 'top'
      });
      await toast.present();
    }
  }

  getErrorMessage(field: string): string {
    const control = this.feedbackForm.get(field);
    if (control?.hasError('required')) {
      return `${this.getFieldLabel(field)} is required`;
    }
    if (control?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength'].requiredLength;
      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;
    }
    if (control?.hasError('pattern')) {
      return 'Please enter a valid phone number (10 digits)';
    }
    return '';
  }

  private getFieldLabel(field: string): string {
    const labels: { [key: string]: string } = {
      name: 'Name',
      email: 'Email',
      phone: 'Phone',
      category: 'Category',
      subject: 'Subject',
      message: 'Message'
    };
    return labels[field] || field;
  }

  goBack() {
    this.router.navigate(['/home']);
  }
}
