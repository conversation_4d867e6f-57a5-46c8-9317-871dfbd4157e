.register-content {
  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-section {
    text-align: center;
    margin-bottom: 32px;

    .header-content {
      h1 {
        color: #1976d2;
        font-size: 28px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      p {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .progress-section {
    margin-bottom: 40px;

    .progress-steps {
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 600px;
      margin: 0 auto;

      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        .step-number {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #e0e0e0;
          color: #999;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 16px;
          margin-bottom: 8px;
          transition: all 0.3s ease;

          &.active {
            background: #1976d2;
            color: white;
          }

          &.completed {
            background: #4caf50;
            color: white;
          }
        }

        .step-label {
          font-size: 12px;
          color: #666;
          text-align: center;
          font-weight: 500;
        }

        &.active .step-number {
          background: #1976d2;
          color: white;
        }

        &.completed .step-number {
          background: #4caf50;
          color: white;
        }
      }

      .step-connector {
        flex: 1;
        height: 2px;
        background: #e0e0e0;
        margin: 0 16px;
        margin-bottom: 32px;
        transition: all 0.3s ease;

        &.completed {
          background: #4caf50;
        }
      }
    }
  }

  .step-content {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
  }

  .form-section {
    h3 {
      color: #1976d2;
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .section-description {
      color: #666;
      font-size: 14px;
      margin: 0 0 32px 0;
    }
  }

  // Complaint Type Cards
  .complaint-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 32px;

    .complaint-type-card {
      border: 2px solid #e0e0e0;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: white;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        border-color: #1976d2;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
      }

      &.selected {
        border-color: #1976d2;
        background: #e3f2fd;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
      }

      .card-icon {
        ion-icon {
          font-size: 32px;
          color: #1976d2;
        }
      }

      .card-content {
        flex: 1;

        h4 {
          color: #333;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 4px 0;
        }

        p {
          color: #666;
          font-size: 14px;
          margin: 0;
          line-height: 1.4;
        }
      }

      .card-radio {
        ion-radio {
          --color: #1976d2;
          --color-checked: #1976d2;
        }
      }
    }
  }

  // Invoice Search
  .search-section {
    margin-bottom: 32px;

    .search-field {
      width: 100%;
      margin-bottom: 24px;
    }

    .search-results {
      h4 {
        color: #333;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 16px 0;
      }

      .invoice-list {
        .invoice-item {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          background: white;

          &:hover {
            border-color: #1976d2;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
          }

          .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            strong {
              color: #1976d2;
              font-size: 16px;
            }

            .invoice-date {
              color: #666;
              font-size: 14px;
            }
          }

          .invoice-customer {
            color: #333;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .invoice-zone {
            color: #666;
            font-size: 14px;
          }
        }
      }
    }

    .no-results {
      text-align: center;
      padding: 32px;
      color: #666;

      p {
        margin: 8px 0;
        font-size: 16px;

        &.search-hint {
          font-size: 14px;
          color: #999;
          font-style: italic;
        }
      }
    }
  }

  .selected-invoice {
    margin-top: 32px;

    h4 {
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
    }

    .invoice-details-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .invoice-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        margin-bottom: 16px;

        .detail-item {
          label {
            display: block;
            color: #666;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
          }

          span {
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }

      .invoice-actions {
        border-top: 1px solid #e0e0e0;
        padding-top: 16px;
        text-align: right;
      }
    }
  }

  // Complaint Details
  .selected-type-display {
    margin-bottom: 32px;

    h4 {
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
    }

    .type-display-card {
      background: #e3f2fd;
      border: 1px solid #1976d2;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      align-items: center;
      gap: 16px;

      ion-icon {
        font-size: 24px;
        color: #1976d2;
      }

      div {
        strong {
          color: #1976d2;
          font-size: 16px;
          display: block;
          margin-bottom: 4px;
        }

        p {
          color: #666;
          font-size: 14px;
          margin: 0;
        }
      }
    }
  }

  .form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0;
    }

    .half-width {
      flex: 1;
    }

    .full-width {
      width: 100%;
    }
  }

  mat-form-field {
    margin-bottom: 16px;

    &.search-field {
      width: 100%;
    }

    .mat-form-field-outline {
      border-radius: 8px;
    }

    .mat-form-field-label {
      color: #666;
    }

    &.mat-focused {
      .mat-form-field-label {
        color: #1976d2;
      }
    }

    mat-icon {
      color: #666;
    }
  }

  .complaint-letters-section {
    margin: 24px 0;

    mat-checkbox {
      .mat-checkbox-label {
        font-size: 16px;
        color: #333;
      }
    }
  }

  .file-upload-section {
    margin: 24px 0;
    padding: 20px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    background: #fafafa;

    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-weight: 500;
    }

    .upload-button {
      margin-bottom: 12px;

      mat-icon {
        margin-right: 8px;
      }
    }

    .upload-hint {
      color: #666;
      font-size: 12px;
      margin: 8px 0 0 0;
    }

    .selected-files {
      margin-top: 16px;

      h5 {
        color: #333;
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 12px 0;
      }

      .file-list {
        .file-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 12px;
          background: white;
          border-radius: 6px;
          margin-bottom: 8px;
          border: 1px solid #e0e0e0;

          mat-icon {
            color: #666;
          }

          .file-name {
            flex: 1;
            font-size: 14px;
            color: #333;
          }

          .file-size {
            font-size: 12px;
            color: #666;
          }

          .remove-file {
            width: 32px;
            height: 32px;

            mat-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
              color: #f44336;
            }
          }
        }
      }
    }
  }

  .step-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;

    button {
      min-width: 140px;
      height: 44px;
      border-radius: 8px;
      font-weight: 500;

      mat-icon {
        margin-left: 8px;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      &:first-child mat-icon {
        margin-left: 0;
        margin-right: 8px;
      }

      mat-spinner {
        margin-right: 8px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .register-content {
    .container {
      padding: 16px;
    }

    .step-content {
      padding: 24px 16px;
    }

    .complaint-types-grid {
      grid-template-columns: 1fr;
    }

    .progress-section .progress-steps {
      .step-label {
        font-size: 10px;
      }

      .step-connector {
        margin: 0 8px;
      }
    }

    .invoice-details-grid {
      grid-template-columns: 1fr !important;
    }
  }
}
