/* Material Design Register Page Styles */

.material-header {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
              0px 4px 5px 0px rgba(0, 0, 0, 0.14),
              0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.material-toolbar {
  height: 64px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  
  .toolbar-title {
    font-size: 20px;
    font-weight: 500;
    margin-left: 16px;
  }
  
  .spacer {
    flex: 1 1 auto;
  }
}

.material-content {
  --background: #f5f5f5;
  padding: 20px;
}

.register-container {
  max-width: 800px;
  margin: 0 auto;
}

/* Register Card */
.register-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .mat-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px;
    
    .register-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 500;
      color: #333;
      margin: 0;
      
      .title-icon {
        margin-right: 12px;
        font-size: 28px;
        color: #1976d2;
      }
    }
    
    .mat-card-subtitle {
      color: #666;
      font-size: 14px;
      margin-top: 8px;
    }
  }
  
  .mat-card-content {
    padding: 0;
  }
}

/* Stepper Styles */
.register-stepper {
  .mat-step-header {
    padding: 24px;
    
    .mat-step-icon {
      background-color: #1976d2;
      color: white;
    }
    
    .mat-step-icon-selected {
      background-color: #1976d2;
    }
    
    .mat-step-icon-state-done {
      background-color: #4caf50;
    }
    
    .mat-step-label {
      font-weight: 500;
      color: #333;
    }
  }
  
  .mat-step-content {
    padding: 0 24px 24px 24px;
  }
}

/* Form Styles */
.step-form {
  margin-bottom: 24px;
}

.form-row {
  display: flex;
  gap: 16px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 0;
  }
}

.full-width {
  width: 100%;
}

.half-width {
  flex: 1;
  
  @media (max-width: 768px) {
    width: 100%;
  }
}

.mat-form-field {
  margin-bottom: 16px;
  
  .mat-form-field-outline {
    border-radius: 8px;
  }
  
  &.mat-focused .mat-form-field-outline-thick {
    border-color: #1976d2;
  }
  
  .mat-form-field-label {
    color: #666;
  }
  
  .mat-input-element,
  .mat-select-value {
    color: #333;
  }
}

/* Step Actions */
.step-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e0e0e0;
  
  button {
    border-radius: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    
    &.submit-button {
      background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
      color: white;
      
      .spinning {
        animation: spin 1s linear infinite;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File Upload Section */
.file-upload-section {
  margin: 24px 0;
  
  h4 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0 0 16px 0;
  }
  
  .upload-button {
    border-radius: 8px;
    border: 2px dashed #1976d2;
    padding: 12px 24px;
    
    &:hover {
      background-color: rgba(25, 118, 210, 0.04);
    }
  }
  
  .selected-files {
    margin-top: 16px;
    
    .file-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      background: #f5f5f5;
      border-radius: 8px;
      margin-bottom: 8px;
      
      mat-icon:first-child {
        margin-right: 8px;
        color: #666;
      }
      
      span {
        flex: 1;
        font-size: 14px;
        color: #333;
      }
      
      button {
        margin-left: 8px;
      }
    }
  }
}

/* Terms Section */
.terms-section {
  margin: 24px 0;
  
  .mat-checkbox {
    .mat-checkbox-label {
      font-size: 14px;
      color: #666;
    }
  }
  
  .terms-link {
    color: #1976d2;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

/* Review Section */
.review-section {
  h4 {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin: 0 0 24px 0;
  }
  
  .review-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 16px;
    
    h5 {
      font-size: 16px;
      font-weight: 500;
      color: #1976d2;
      margin: 0 0 12px 0;
    }
    
    p {
      margin: 8px 0;
      font-size: 14px;
      color: #333;
      
      strong {
        color: #666;
        font-weight: 500;
      }
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .material-content {
    padding: 12px;
  }
  
  .register-card {
    border-radius: 8px;
    
    .mat-card-header {
      padding: 16px;
      
      .register-title {
        font-size: 20px;
        
        .title-icon {
          font-size: 24px;
        }
      }
    }
  }
  
  .register-stepper {
    .mat-step-header {
      padding: 16px;
    }
    
    .mat-step-content {
      padding: 0 16px 16px 16px;
    }
  }
  
  .step-actions {
    flex-direction: column;
    gap: 12px;
    
    button {
      width: 100%;
    }
  }
}

@media (max-width: 480px) {
  .material-toolbar .toolbar-title {
    font-size: 18px;
  }
  
  .register-card .mat-card-header {
    padding: 12px;
    
    .register-title {
      font-size: 18px;
      flex-direction: column;
      text-align: center;
      
      .title-icon {
        margin-right: 0;
        margin-bottom: 8px;
      }
    }
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
