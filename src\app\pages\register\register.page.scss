// Ultra-Modern Compact Register Complaint Page
.ultra-modern-toolbar {
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  
  .toolbar-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    
    .toolbar-title {
      font-size: 18px;
      font-weight: 500;
    }
    
    .mini-progress {
      width: 100px;
      height: 2px;
      background: rgba(255,255,255,0.3);
      border-radius: 1px;
      overflow: hidden;
      
      .progress-bar {
        height: 100%;
        background: #fff;
        transition: width 0.3s ease;
      }
    }
  }
}

.ultra-compact-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 16px;
  min-height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

// Inline Stepper
.inline-stepper {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  
  .step-progress {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    
    .step-dot {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #e0e0e0;
      color: #666;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.3s ease;
      
      &.active {
        background: #1976d2;
        color: white;
        transform: scale(1.1);
      }
      
      &.completed {
        background: #4caf50;
        color: white;
      }
      
      mat-icon {
        font-size: 18px;
      }
    }
    
    .step-line {
      flex: 1;
      height: 2px;
      background: #e0e0e0;
      margin: 0 12px;
      transition: all 0.3s ease;
      
      &.active {
        background: #4caf50;
      }
    }
  }
  
  .step-labels {
    display: flex;
    justify-content: space-between;
    
    span {
      font-size: 12px;
      color: #666;
      font-weight: 500;
      text-align: center;
      flex: 1;
      
      &.active {
        color: #1976d2;
        font-weight: 600;
      }
    }
  }
}

// Breadcrumb Summary
.breadcrumb-summary {
  margin-bottom: 16px;
  
  .mat-mdc-chip-set {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
  }
}

// Ultra-Compact Step Content
.ultra-compact-step {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.step-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  .step-header {
    text-align: center;
    margin-bottom: 24px;
    
    h2 {
      font-size: 24px;
      font-weight: 400;
      color: #333;
      margin: 0 0 8px 0;
    }
    
    p {
      font-size: 14px;
      color: #666;
      margin: 0;
    }
  }
}

// Compact Grid for Type Selection
.compact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
  
  .type-option {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }
    
    &.selected {
      border-color: #1976d2;
      background: #e3f2fd;
    }
    
    .option-layout {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 8px 0;
      
      .option-icon {
        font-size: 28px;
        color: #666;
      }
      
      .option-content {
        flex: 1;
        
        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
        
        p {
          margin: 0;
          font-size: 13px;
          color: #666;
          line-height: 1.3;
        }
      }
    }
    
    &.selected {
      .option-icon {
        color: #1976d2;
      }
      
      .option-content h3 {
        color: #1976d2;
      }
    }
  }
}

// Compact List for Description Selection
.compact-list {
  .description-option {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    margin-bottom: 12px;
    
    &:hover {
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    &.selected {
      border-color: #1976d2;
      background: #e3f2fd;
    }
    
    .radio-layout {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      padding: 8px 0;
      
      .radio-content {
        flex: 1;
        
        h4 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
        
        p {
          margin: 0;
          font-size: 13px;
          color: #666;
          line-height: 1.3;
        }
      }
    }
    
    &.selected {
      .radio-content h4 {
        color: #1976d2;
      }
    }
  }
}

// Search Field
.search-field {
  width: 100%;
  margin-bottom: 16px;
}

// Invoice List
.invoice-list {
  .results-info {
    font-size: 12px;
    color: #666;
    margin-bottom: 12px;
    text-align: center;
  }
  
  .compact-invoice-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
    
    .invoice-option {
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }
      
      &.selected {
        border-color: #1976d2;
        background: #e3f2fd;
      }
      
      .invoice-layout {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 0;
        
        .invoice-main {
          .invoice-number {
            font-size: 14px;
            font-weight: 600;
            color: #1976d2;
            margin-bottom: 2px;
          }
          
          .invoice-date {
            font-size: 11px;
            color: #666;
          }
        }
        
        .invoice-details {
          flex: 1;
          
          .customer {
            font-size: 13px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
          }
          
          .location {
            font-size: 11px;
            color: #666;
          }
        }
        
        .select-icon {
          color: #ccc;
          font-size: 20px;
          
          &.mat-icon {
            color: #1976d2;
          }
        }
      }
      
      &.selected .select-icon {
        color: #1976d2;
      }
    }
  }
}

// Selected Preview
.selected-preview {
  margin-top: 16px;
  background: #e8f5e8;
  border: 2px solid #4caf50;
  
  .mat-mdc-card-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2e7d32;
    font-size: 16px;
  }
  
  .preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 8px;
    margin-bottom: 16px;
    font-size: 13px;
    
    div {
      background: white;
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #c8e6c9;
    }
  }
}

// Final Summary
.final-summary {
  margin-bottom: 16px;
  background: #f3e5f5;
  border: 2px solid #9c27b0;
  
  .summary-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    color: #7b1fa2;
    font-weight: 500;
  }
  
  .summary-chips {
    .mat-mdc-chip-set {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}

// Contact Form
.contact-form {
  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
    
    @media (max-width: 600px) {
      grid-template-columns: 1fr;
    }
  }
  
  .full-width {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .upload-section {
    .upload-area {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
      margin-top: 12px;
      
      .upload-hint {
        font-size: 11px;
        color: #666;
      }
      
      .file-chips {
        width: 100%;
        margin-top: 8px;
        
        .mat-mdc-chip-set {
          display: flex;
          flex-wrap: wrap;
          gap: 6px;
        }
      }
    }
  }
}

// Fixed Bottom Navigation
.bottom-navigation {
  position: sticky;
  bottom: 0;
  background: white;
  border-top: 1px solid #e0e0e0;
  padding: 16px;
  margin: 16px -16px -16px -16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
  
  .spacer {
    flex: 1;
  }
  
  .nav-button {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
  }
}

// Success State
.success-state {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .success-card {
    max-width: 400px;
    margin: 20px;
    text-align: center;
    
    .success-icon {
      font-size: 64px;
      color: #4caf50;
      margin-bottom: 16px;
    }
    
    h2 {
      color: #333;
      margin: 0 0 12px 0;
      font-size: 20px;
      font-weight: 500;
    }
    
    p {
      color: #666;
      margin: 0 0 24px 0;
      font-size: 14px;
    }
    
    button {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0 auto;
    }
  }
}

// No Results
.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #666;
  
  mat-icon {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 16px;
  }
  
  p {
    margin: 4px 0;
    
    &.hint {
      font-size: 12px;
      color: #999;
    }
  }
}

// Responsive Design
@media (max-width: 600px) {
  .ultra-compact-container {
    padding: 12px;
  }
  
  .compact-grid {
    grid-template-columns: 1fr;
  }
  
  .compact-invoice-grid {
    grid-template-columns: 1fr;
  }
  
  .inline-stepper {
    padding: 12px;
    
    .step-progress .step-dot {
      width: 28px;
      height: 28px;
      font-size: 12px;
    }
    
    .step-labels span {
      font-size: 10px;
    }
  }
  
  .step-header {
    h2 {
      font-size: 20px;
    }
  }
}
