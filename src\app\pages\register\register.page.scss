.register-content {
  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  .container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-section {
    text-align: center;
    margin-bottom: 32px;

    .header-content {
      h1 {
        color: #1976d2;
        font-size: 28px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      p {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .progress-section {
    margin-bottom: 40px;

    .progress-steps {
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 600px;
      margin: 0 auto;

      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;

        .step-number {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #e0e0e0;
          color: #999;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          font-size: 16px;
          margin-bottom: 8px;
          transition: all 0.3s ease;

          &.active {
            background: #1976d2;
            color: white;
          }

          &.completed {
            background: #4caf50;
            color: white;
          }
        }

        .step-label {
          font-size: 12px;
          color: #666;
          text-align: center;
          font-weight: 500;
        }

        &.active .step-number {
          background: #1976d2;
          color: white;
        }

        &.completed .step-number {
          background: #4caf50;
          color: white;
        }
      }

      .step-connector {
        flex: 1;
        height: 2px;
        background: #e0e0e0;
        margin: 0 16px;
        margin-bottom: 32px;
        transition: all 0.3s ease;

        &.completed {
          background: #4caf50;
        }
      }
    }
  }

  .step-content {
    background: white;
    border-radius: 12px;
    padding: 32px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 24px;
  }

  .form-section {
    h3 {
      color: #1976d2;
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
    }

    .section-description {
      color: #666;
      font-size: 14px;
      margin: 0 0 32px 0;
    }
  }

  // Complaint Type Cards
  .complaint-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 32px;

    .complaint-type-card {
      border: 2px solid #e0e0e0;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      background: white;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        border-color: #1976d2;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);
      }

      &.selected {
        border-color: #1976d2;
        background: #e3f2fd;
        box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
      }

      .card-icon {
        ion-icon {
          font-size: 32px;
          color: #1976d2;
        }
      }

      .card-content {
        flex: 1;

        h4 {
          color: #333;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 4px 0;
        }

        p {
          color: #666;
          font-size: 14px;
          margin: 0;
          line-height: 1.4;
        }
      }

      .card-radio {
        ion-radio {
          --color: #1976d2;
          --color-checked: #1976d2;
        }
      }
    }
  }

  // Complaint Description Selection
  .complaint-descriptions-section {
    margin-bottom: 32px;

    h4 {
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 20px 0;
    }

    .description-options {
      .description-radio-group {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .description-option {
          border: 2px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          background: white;
          transition: all 0.3s ease;
          margin: 0;

          &:hover {
            border-color: #ff9800;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);
          }

          &.mat-radio-checked {
            border-color: #ff9800;
            background: #fff3e0;
            box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);
          }

          .description-content {
            margin-left: 32px;

            strong {
              color: #333;
              font-size: 16px;
              display: block;
              margin-bottom: 4px;
            }

            p {
              color: #666;
              font-size: 14px;
              margin: 0;
              line-height: 1.4;
            }
          }

          ::ng-deep .mat-radio-container {
            .mat-radio-outer-circle {
              border-color: #ff9800;
            }

            .mat-radio-inner-circle {
              background-color: #ff9800;
            }
          }

          ::ng-deep .mat-radio-checked .mat-radio-container {
            .mat-radio-outer-circle {
              border-color: #ff9800;
            }

            .mat-radio-inner-circle {
              background-color: #ff9800;
            }
          }
        }
      }
    }
  }

  // Complaint Summary Display
  .complaint-summary-display {
    margin-bottom: 32px;

    h4 {
      color: #7b1fa2;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
    }

    .summary-card {
      background: #f3e5f5;
      border: 2px solid #9c27b0;
      border-radius: 8px;
      padding: 16px;

      .summary-item {
        margin-bottom: 8px;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }

        strong {
          color: #7b1fa2;
          margin-right: 8px;
        }
      }
    }
  }

  // Complete Summary Display
  .complete-summary-display {
    margin-bottom: 32px;

    h4 {
      color: #1565c0;
      font-size: 20px;
      font-weight: 600;
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #1565c0;
        font-size: 22px;
      }

      .final-review-badge {
        background: #1565c0;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-left: auto;
      }
    }

    .summary-sections {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;

      .summary-section {
        background: #e3f2fd;
        border: 2px solid #1976d2;
        border-radius: 8px;
        padding: 16px;

        h5 {
          color: #1565c0;
          font-size: 16px;
          font-weight: 600;
          margin: 0 0 12px 0;
        }

        .summary-item {
          margin-bottom: 8px;
          font-size: 14px;

          &:last-child {
            margin-bottom: 0;
          }

          strong {
            color: #1565c0;
            margin-right: 8px;
          }
        }

        // Readonly Invoice Details in Step 4
        .invoice-details-readonly {
          background: #f1f8e9;
          border: 2px solid #4caf50;
          border-radius: 12px;
          padding: 20px;
          margin-top: 16px;
          position: relative;

          &::before {
            content: 'READ ONLY';
            position: absolute;
            top: -10px;
            right: 20px;
            background: #4caf50;
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            letter-spacing: 0.5px;
          }

          .section-header-readonly {
            margin: 16px 0 12px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #c8e6c9;

            h6 {
              margin: 0;
              color: #2e7d32;
              font-weight: 600;
              font-size: 16px;
              display: flex;
              align-items: center;
              gap: 8px;

              mat-icon {
                color: #4caf50;
                font-size: 18px;
              }
            }
          }

          .readonly-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 16px;

            .readonly-item {
              &.full-width {
                grid-column: 1 / -1;
              }

              label {
                display: block;
                color: #2e7d32;
                font-size: 11px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 4px;
              }

              span {
                display: block;
                background: white;
                border: 1px solid #c8e6c9;
                border-radius: 4px;
                padding: 8px;
                color: #1b5e20;
                font-size: 13px;
                font-weight: 500;
              }
            }
          }

          .readonly-items-container {
            .readonly-item-card {
              background: white;
              border: 1px solid #c8e6c9;
              border-radius: 8px;
              padding: 16px;
              margin-bottom: 12px;

              .readonly-item-header {
                color: #2e7d32;
                font-weight: 600;
                font-size: 14px;
                margin-bottom: 8px;
                padding-bottom: 4px;
                border-bottom: 1px solid #e8f5e8;
              }

              .readonly-item-description {
                color: #1b5e20;
                font-weight: 500;
                font-size: 13px;
                margin-bottom: 12px;
                background: #f1f8e9;
                padding: 6px;
                border-radius: 4px;
              }

              .readonly-specs-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
                gap: 8px;
                margin-bottom: 12px;

                .readonly-spec {
                  display: flex;
                  flex-direction: column;
                  gap: 2px;

                  label {
                    color: #2e7d32;
                    font-size: 10px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                  }

                  span {
                    background: #e8f5e8;
                    border: 1px solid #c8e6c9;
                    border-radius: 3px;
                    padding: 4px 6px;
                    color: #1b5e20;
                    font-size: 12px;
                    font-weight: 600;
                    text-align: center;
                  }
                }
              }

              .defect-selection-readonly {
                margin-top: 12px;
                padding-top: 12px;
                border-top: 1px solid #c8e6c9;

                label {
                  color: #d32f2f;
                  font-size: 11px;
                  font-weight: 600;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  margin-bottom: 4px;
                  display: block;
                }

                .defect-value {
                  background: #ffebee;
                  border: 1px solid #f8bbd9;
                  border-radius: 4px;
                  padding: 6px 8px;
                  color: #c62828;
                  font-size: 12px;
                  font-weight: 600;
                  display: inline-block;
                }
              }
            }
          }
        }
      }
    }
  }

  // Contact Form Section
  .contact-form-section {
    h4 {
      color: #d32f2f;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #d32f2f;
        font-size: 20px;
      }
    }

    .contact-form-card {
      background: #fff3e0;
      border: 2px solid #ff9800;
      border-radius: 12px;
      padding: 24px;
      position: relative;

      &::before {
        content: 'CONTACT DETAILS';
        position: absolute;
        top: -10px;
        right: 20px;
        background: #ff9800;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
    }
  }

  // Invoice Search
  .search-section {
    margin-bottom: 32px;

    .search-field {
      width: 100%;
      margin-bottom: 24px;
    }

    .search-results {
      h4 {
        color: #333;
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 16px 0;
      }

      .invoice-list {
        .invoice-item {
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          background: white;

          &:hover {
            border-color: #1976d2;
            box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);
          }

          .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            strong {
              color: #1976d2;
              font-size: 16px;
            }

            .invoice-date {
              color: #666;
              font-size: 14px;
            }
          }

          .invoice-customer {
            color: #333;
            font-weight: 500;
            margin-bottom: 4px;
          }

          .invoice-zone {
            color: #666;
            font-size: 14px;
          }
        }
      }
    }

    .no-results {
      text-align: center;
      padding: 32px;
      color: #666;

      p {
        margin: 8px 0;
        font-size: 16px;

        &.search-hint {
          font-size: 14px;
          color: #999;
          font-style: italic;
        }
      }
    }
  }

  .selected-invoice {
    margin-top: 32px;

    h4 {
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
    }

    .invoice-details-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .section-header {
        margin: 20px 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #e0e0e0;

        h5 {
          margin: 0;
          color: #333;
          font-weight: 600;
          display: flex;
          align-items: center;
          gap: 8px;

          mat-icon {
            color: #1976d2;
            font-size: 20px;
          }
        }
      }

      .invoice-details-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 16px;
        margin-bottom: 16px;

        .detail-item {
          &.full-width {
            grid-column: 1 / -1;
          }

          label {
            display: block;
            color: #666;
            font-size: 12px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
          }

          span {
            color: #333;
            font-size: 14px;
            font-weight: 500;
          }
        }
      }

      .items-container {
        margin-bottom: 20px;

        .item-card {
          margin-bottom: 16px;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
          overflow: hidden;

          .mat-mdc-card-header {
            background-color: #f5f5f5;
            padding: 12px 16px;

            .item-code {
              font-size: 1rem;
              font-weight: 600;
              color: #1976d2;
              margin: 0;
            }
          }

          .item-description {
            font-weight: 500;
            color: #333;
            margin-bottom: 16px;
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 4px;
          }

          .item-specs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 16px;

            .spec-item {
              display: flex;
              flex-direction: column;
              gap: 4px;

              label {
                font-weight: 500;
                color: #666;
                font-size: 0.8rem;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }

              .spec-value {
                font-weight: 600;
                color: #333;
                font-size: 0.9rem;
                padding: 6px 8px;
                background-color: #e3f2fd;
                border-radius: 4px;
                text-align: center;
                border: 1px solid #bbdefb;
              }
            }
          }

          .defect-selection {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #e0e0e0;

            .defect-field {
              width: 100%;
            }
          }
        }
      }

      .invoice-actions {
        border-top: 1px solid #e0e0e0;
        padding-top: 16px;
        text-align: right;
      }
    }
  }

  // Complaint Details
  .selected-type-display {
    margin-bottom: 32px;

    h4 {
      color: #333;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 16px 0;
    }

    .type-display-card {
      background: #e3f2fd;
      border: 1px solid #1976d2;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      align-items: center;
      gap: 16px;

      ion-icon {
        font-size: 24px;
        color: #1976d2;
      }

      div {
        strong {
          color: #1976d2;
          font-size: 16px;
          display: block;
          margin-bottom: 4px;
        }

        p {
          color: #666;
          font-size: 14px;
          margin: 0;
        }
      }
    }
  }

  // Invoice Details Section (Read-Only)
  .invoice-details-section {
    margin-bottom: 40px;

    h4 {
      color: #2e7d32;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #2e7d32;
        font-size: 20px;
      }
    }

    .readonly-invoice-card {
      background: #f1f8e9;
      border: 2px solid #4caf50;
      border-radius: 12px;
      padding: 24px;
      position: relative;

      &::before {
        content: 'READ ONLY';
        position: absolute;
        top: -10px;
        right: 20px;
        background: #4caf50;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }

      .invoice-details-grid {
        display: grid;
        gap: 20px;

        .detail-group {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 16px;

          @media (max-width: 768px) {
            grid-template-columns: 1fr;
          }

          .detail-item {
            &.full-width {
              grid-column: 1 / -1;
            }

            label {
              display: block;
              color: #2e7d32;
              font-size: 12px;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              margin-bottom: 8px;
            }

            .readonly-field {
              display: flex;
              align-items: center;
              gap: 8px;
              background: white;
              border: 1px solid #c8e6c9;
              border-radius: 6px;
              padding: 12px;

              mat-icon {
                color: #4caf50;
                font-size: 18px;
                width: 18px;
                height: 18px;
              }

              span {
                color: #1b5e20;
                font-size: 14px;
                font-weight: 500;
                flex: 1;
              }
            }
          }
        }
      }
    }
  }

  // Complaint Form Section (Editable)
  .complaint-form-section {
    h4 {
      color: #d32f2f;
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #d32f2f;
        font-size: 20px;
      }
    }

    .editable-form-card {
      background: #fff3e0;
      border: 2px solid #ff9800;
      border-radius: 12px;
      padding: 24px;
      position: relative;

      &::before {
        content: 'EDITABLE';
        position: absolute;
        top: -10px;
        right: 20px;
        background: #ff9800;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 600;
        letter-spacing: 0.5px;
      }
    }
  }

  .form-row {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0;
    }

    .half-width {
      flex: 1;
    }

    .full-width {
      width: 100%;
    }
  }

  mat-form-field {
    margin-bottom: 16px;

    &.search-field {
      width: 100%;
    }

    .mat-form-field-outline {
      border-radius: 8px;
    }

    .mat-form-field-label {
      color: #666;
    }

    &.mat-focused {
      .mat-form-field-label {
        color: #1976d2;
      }
    }

    mat-icon {
      color: #666;
    }
  }

  .complaint-letters-section {
    margin: 24px 0;
    padding: 16px;
    background: #f3e5f5;
    border: 1px solid #9c27b0;
    border-radius: 8px;

    mat-checkbox {
      .mat-checkbox-label {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }
    }

    .checkbox-hint {
      margin: 8px 0 0 32px;
      color: #666;
      font-size: 13px;
      font-style: italic;
      line-height: 1.4;
    }
  }

  .file-upload-section {
    margin: 24px 0 0 0;

    h5 {
      color: #7b1fa2;
      font-size: 16px;
      font-weight: 600;
      margin: 0 0 16px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      mat-icon {
        color: #7b1fa2;
        font-size: 18px;
      }
    }

    .upload-area {
      padding: 20px;
      border: 2px dashed #9c27b0;
      border-radius: 8px;
      background: #fce4ec;
      text-align: center;
      margin-bottom: 16px;

      .upload-button {
        margin-bottom: 12px;
        border-color: #9c27b0;
        color: #7b1fa2;

        &:hover {
          background-color: rgba(156, 39, 176, 0.04);
        }

        mat-icon {
          margin-right: 8px;
        }
      }

      .upload-hint {
        color: #666;
        font-size: 12px;
        margin: 8px 0 0 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;

        mat-icon {
          font-size: 14px;
          width: 14px;
          height: 14px;
        }
      }
    }

    .selected-files {
      h6 {
        color: #7b1fa2;
        font-size: 14px;
        font-weight: 600;
        margin: 0 0 12px 0;
      }

      .file-list {
        .file-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          background: white;
          border-radius: 8px;
          margin-bottom: 8px;
          border: 1px solid #e1bee7;
          transition: all 0.3s ease;

          &:hover {
            border-color: #9c27b0;
            box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);
          }

          .file-icon {
            color: #7b1fa2;
            font-size: 20px;
          }

          .file-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;

            .file-name {
              font-size: 14px;
              color: #333;
              font-weight: 500;
            }

            .file-size {
              font-size: 12px;
              color: #666;
            }
          }

          .remove-file {
            width: 36px;
            height: 36px;

            mat-icon {
              font-size: 18px;
              width: 18px;
              height: 18px;
            }
          }
        }
      }
    }
  }

  .step-actions {
    display: flex;
    gap: 16px;
    justify-content: space-between;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 2px solid #e0e0e0;

    .back-button {
      color: #666;
      border-color: #ccc;

      &:hover {
        background-color: #f5f5f5;
        border-color: #999;
      }
    }

    .submit-button {
      background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
      color: white;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);

      &:hover:not(:disabled) {
        background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
        box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
        transform: translateY(-1px);
      }

      &:disabled {
        background: #ccc;
        color: #999;
        box-shadow: none;
      }
    }

    button {
      min-width: 140px;
      height: 44px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;

      mat-icon {
        margin-left: 8px;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }

      &:first-child mat-icon {
        margin-left: 0;
        margin-right: 8px;
      }

      mat-spinner {
        margin-right: 8px;
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .register-content {
    .container {
      padding: 16px;
    }

    .step-content {
      padding: 24px 16px;
    }

    .complaint-types-grid {
      grid-template-columns: 1fr;
    }

    .progress-section .progress-steps {
      .step-label {
        font-size: 10px;
      }

      .step-connector {
        margin: 0 8px;
      }
    }

    .invoice-details-grid {
      grid-template-columns: 1fr !important;
    }
  }
}
