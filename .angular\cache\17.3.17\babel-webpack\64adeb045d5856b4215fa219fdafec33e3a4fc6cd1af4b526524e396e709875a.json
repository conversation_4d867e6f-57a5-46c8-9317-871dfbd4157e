{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Optional, Inject, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { MatRipple, _MatInternalFormField, MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst _c0 = [\"input\"];\nconst _c1 = [\"label\"];\nconst _c2 = [\"*\"];\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n  providedIn: 'root',\n  factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n  return {\n    color: 'accent',\n    clickAction: 'check-indeterminate'\n  };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n  /** The initial state of the component before any user interaction. */\n  TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n  /** The state representing the component when it's becoming checked. */\n  TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n  /** The state representing the component when it's becoming unchecked. */\n  TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n  /** The state representing the component when it's becoming indeterminate. */\n  TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatCheckbox),\n  multi: true\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {}\n// Increasing integer for generating unique ids for checkbox components.\nlet nextUniqueId = 0;\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n  /** Focuses the checkbox. */\n  focus() {\n    this._inputElement.nativeElement.focus();\n  }\n  /** Creates the change event that will be emitted by the checkbox. */\n  _createChangeEvent(isChecked) {\n    const event = new MatCheckboxChange();\n    event.source = this;\n    event.checked = isChecked;\n    return event;\n  }\n  /** Gets the element on which to add the animation CSS classes. */\n  _getAnimationTargetElement() {\n    return this._inputElement?.nativeElement;\n  }\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  constructor(_elementRef, _changeDetectorRef, _ngZone, tabIndex, _animationMode, _options) {\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._ngZone = _ngZone;\n    this._animationMode = _animationMode;\n    this._options = _options;\n    /** CSS classes to add when transitioning between the different checkbox states. */\n    this._animationClasses = {\n      uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n      uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n      checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n      checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n      indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n      indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked'\n    };\n    /**\n     * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n     * take precedence so this may be omitted.\n     */\n    this.ariaLabel = '';\n    /**\n     * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n     */\n    this.ariaLabelledby = null;\n    /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n    this.labelPosition = 'after';\n    /** Name value will be applied to the input element if present */\n    this.name = null;\n    /** Event emitted when the checkbox's `checked` value changes. */\n    this.change = new EventEmitter();\n    /** Event emitted when the checkbox's `indeterminate` value changes. */\n    this.indeterminateChange = new EventEmitter();\n    /**\n     * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n     * @docs-private\n     */\n    this._onTouched = () => {};\n    this._currentAnimationClass = '';\n    this._currentCheckState = TransitionCheckState.Init;\n    this._controlValueAccessorChangeFn = () => {};\n    this._validatorChangeFn = () => {};\n    this._checked = false;\n    this._disabled = false;\n    this._indeterminate = false;\n    this._options = this._options || defaults;\n    this.color = this._options.color || defaults.color;\n    this.tabIndex = parseInt(tabIndex) || 0;\n    this.id = this._uniqueId = `mat-mdc-checkbox-${++nextUniqueId}`;\n  }\n  ngOnChanges(changes) {\n    if (changes['required']) {\n      this._validatorChangeFn();\n    }\n  }\n  ngAfterViewInit() {\n    this._syncIndeterminate(this._indeterminate);\n  }\n  /** Whether the checkbox is checked. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    if (value != this.checked) {\n      this._checked = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /** Whether the checkbox is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    if (value !== this.disabled) {\n      this._disabled = value;\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  /**\n   * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n   * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n   * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n   * set to false.\n   */\n  get indeterminate() {\n    return this._indeterminate;\n  }\n  set indeterminate(value) {\n    const changed = value != this._indeterminate;\n    this._indeterminate = value;\n    if (changed) {\n      if (this._indeterminate) {\n        this._transitionCheckState(TransitionCheckState.Indeterminate);\n      } else {\n        this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      }\n      this.indeterminateChange.emit(this._indeterminate);\n    }\n    this._syncIndeterminate(this._indeterminate);\n  }\n  _isRippleDisabled() {\n    return this.disableRipple || this.disabled;\n  }\n  /** Method being called whenever the label text changes. */\n  _onLabelTextChange() {\n    // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n    // component will be only marked for check, but no actual change detection runs automatically.\n    // Instead of going back into the zone in order to trigger a change detection which causes\n    // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n    // an explicit change detection for the checkbox view and its children.\n    this._changeDetectorRef.detectChanges();\n  }\n  // Implemented as part of ControlValueAccessor.\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnChange(fn) {\n    this._controlValueAccessorChangeFn = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  // Implemented as part of ControlValueAccessor.\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  // Implemented as a part of Validator.\n  validate(control) {\n    return this.required && control.value !== true ? {\n      'required': true\n    } : null;\n  }\n  // Implemented as a part of Validator.\n  registerOnValidatorChange(fn) {\n    this._validatorChangeFn = fn;\n  }\n  _transitionCheckState(newState) {\n    let oldState = this._currentCheckState;\n    let element = this._getAnimationTargetElement();\n    if (oldState === newState || !element) {\n      return;\n    }\n    if (this._currentAnimationClass) {\n      element.classList.remove(this._currentAnimationClass);\n    }\n    this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n    this._currentCheckState = newState;\n    if (this._currentAnimationClass.length > 0) {\n      element.classList.add(this._currentAnimationClass);\n      // Remove the animation class to avoid animation when the checkbox is moved between containers\n      const animationClass = this._currentAnimationClass;\n      this._ngZone.runOutsideAngular(() => {\n        setTimeout(() => {\n          element.classList.remove(animationClass);\n        }, 1000);\n      });\n    }\n  }\n  _emitChangeEvent() {\n    this._controlValueAccessorChangeFn(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n    // Assigning the value again here is redundant, but we have to do it in case it was\n    // changed inside the `change` listener which will cause the input to be out of sync.\n    if (this._inputElement) {\n      this._inputElement.nativeElement.checked = this.checked;\n    }\n  }\n  /** Toggles the `checked` state of the checkbox. */\n  toggle() {\n    this.checked = !this.checked;\n    this._controlValueAccessorChangeFn(this.checked);\n  }\n  _handleInputClick() {\n    const clickAction = this._options?.clickAction;\n    // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n    if (!this.disabled && clickAction !== 'noop') {\n      // When user manually click on the checkbox, `indeterminate` is set to false.\n      if (this.indeterminate && clickAction !== 'check') {\n        Promise.resolve().then(() => {\n          this._indeterminate = false;\n          this.indeterminateChange.emit(this._indeterminate);\n        });\n      }\n      this._checked = !this._checked;\n      this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n      // Emit our custom change event if the native input emitted one.\n      // It is important to only emit it, if the native input triggered one, because\n      // we don't want to trigger a change event, when the `checked` variable changes for example.\n      this._emitChangeEvent();\n    } else if (!this.disabled && clickAction === 'noop') {\n      // Reset native input when clicked with noop. The native checkbox becomes checked after\n      // click, reset it to be align with `checked` value of `mat-checkbox`.\n      this._inputElement.nativeElement.checked = this.checked;\n      this._inputElement.nativeElement.indeterminate = this.indeterminate;\n    }\n  }\n  _onInteractionEvent(event) {\n    // We always have to stop propagation on the change event.\n    // Otherwise the change event, from the input element, will bubble up and\n    // emit its event object to the `change` output.\n    event.stopPropagation();\n  }\n  _onBlur() {\n    // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n    // Angular does not expect events to be raised during change detection, so any state change\n    // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n    // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n    // telling the form control it has been touched until the next tick.\n    Promise.resolve().then(() => {\n      this._onTouched();\n      this._changeDetectorRef.markForCheck();\n    });\n  }\n  _getAnimationClassForCheckStateTransition(oldState, newState) {\n    // Don't transition if animations are disabled.\n    if (this._animationMode === 'NoopAnimations') {\n      return '';\n    }\n    switch (oldState) {\n      case TransitionCheckState.Init:\n        // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n        // [checked] bound to it.\n        if (newState === TransitionCheckState.Checked) {\n          return this._animationClasses.uncheckedToChecked;\n        } else if (newState == TransitionCheckState.Indeterminate) {\n          return this._checked ? this._animationClasses.checkedToIndeterminate : this._animationClasses.uncheckedToIndeterminate;\n        }\n        break;\n      case TransitionCheckState.Unchecked:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.uncheckedToChecked : this._animationClasses.uncheckedToIndeterminate;\n      case TransitionCheckState.Checked:\n        return newState === TransitionCheckState.Unchecked ? this._animationClasses.checkedToUnchecked : this._animationClasses.checkedToIndeterminate;\n      case TransitionCheckState.Indeterminate:\n        return newState === TransitionCheckState.Checked ? this._animationClasses.indeterminateToChecked : this._animationClasses.indeterminateToUnchecked;\n    }\n    return '';\n  }\n  /**\n   * Syncs the indeterminate value with the checkbox DOM node.\n   *\n   * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n   * property is supported on an element boils down to `if (propName in element)`. Domino's\n   * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n   * server-side rendering.\n   */\n  _syncIndeterminate(value) {\n    const nativeCheckbox = this._inputElement;\n    if (nativeCheckbox) {\n      nativeCheckbox.nativeElement.indeterminate = value;\n    }\n  }\n  _onInputClick() {\n    this._handleInputClick();\n  }\n  _onTouchTargetClick() {\n    this._handleInputClick();\n    if (!this.disabled) {\n      // Normally the input should be focused already, but if the click\n      // comes from the touch target, then we might have to focus it ourselves.\n      this._inputElement.nativeElement.focus();\n    }\n  }\n  /**\n   *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n   *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n   *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n   *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n   *  bubbles when the label is clicked.\n   */\n  _preventBubblingFromLabel(event) {\n    if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n      event.stopPropagation();\n    }\n  }\n  static {\n    this.ɵfac = function MatCheckbox_Factory(t) {\n      return new (t || MatCheckbox)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_CHECKBOX_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCheckbox,\n      selectors: [[\"mat-checkbox\"]],\n      viewQuery: function MatCheckbox_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n          i0.ɵɵviewQuery(MatRipple, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._labelElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.ripple = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-checkbox\"],\n      hostVars: 14,\n      hostBindings: function MatCheckbox_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-labelledby\", null);\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"mat-accent\");\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\")(\"mdc-checkbox--disabled\", ctx.disabled)(\"mat-mdc-checkbox-disabled\", ctx.disabled)(\"mat-mdc-checkbox-checked\", ctx.checked);\n        }\n      },\n      inputs: {\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"ariaDescribedby\"],\n        id: \"id\",\n        required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n        labelPosition: \"labelPosition\",\n        name: \"name\",\n        value: \"value\",\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? undefined : numberAttribute(value)],\n        color: \"color\",\n        checked: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"checked\", \"checked\", booleanAttribute],\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        indeterminate: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"indeterminate\", \"indeterminate\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\",\n        indeterminateChange: \"indeterminateChange\"\n      },\n      exportAs: [\"matCheckbox\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatCheckbox,\n        multi: true\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 15,\n      vars: 19,\n      consts: [[\"checkbox\", \"\"], [\"input\", \"\"], [\"label\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"click\", \"labelPosition\"], [1, \"mdc-checkbox\"], [1, \"mat-mdc-checkbox-touch-target\", 3, \"click\"], [\"type\", \"checkbox\", 1, \"mdc-checkbox__native-control\", 3, \"blur\", \"click\", \"change\", \"checked\", \"indeterminate\", \"disabled\", \"id\", \"required\", \"tabIndex\"], [1, \"mdc-checkbox__ripple\"], [1, \"mdc-checkbox__background\"], [\"focusable\", \"false\", \"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-checkbox__checkmark\"], [\"fill\", \"none\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-checkbox__checkmark-path\"], [1, \"mdc-checkbox__mixedmark\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-checkbox-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-label\", 3, \"for\"]],\n      template: function MatCheckbox_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 3);\n          i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._preventBubblingFromLabel($event));\n          });\n          i0.ɵɵelementStart(1, \"div\", 4, 0)(3, \"div\", 5);\n          i0.ɵɵlistener(\"click\", function MatCheckbox_Template_div_click_3_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onTouchTargetClick());\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"input\", 6, 1);\n          i0.ɵɵlistener(\"blur\", function MatCheckbox_Template_input_blur_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onBlur());\n          })(\"click\", function MatCheckbox_Template_input_click_4_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onInputClick());\n          })(\"change\", function MatCheckbox_Template_input_change_4_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._onInteractionEvent($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"div\", 7);\n          i0.ɵɵelementStart(7, \"div\", 8);\n          i0.ɵɵnamespaceSVG();\n          i0.ɵɵelementStart(8, \"svg\", 9);\n          i0.ɵɵelement(9, \"path\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵnamespaceHTML();\n          i0.ɵɵelement(10, \"div\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(11, \"div\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"label\", 13, 2);\n          i0.ɵɵprojection(14);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const checkbox_r2 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n          i0.ɵɵadvance(4);\n          i0.ɵɵclassProp(\"mdc-checkbox--selected\", ctx.checked);\n          i0.ɵɵproperty(\"checked\", ctx.checked)(\"indeterminate\", ctx.indeterminate)(\"disabled\", ctx.disabled)(\"id\", ctx.inputId)(\"required\", ctx.required)(\"tabIndex\", ctx.disabled ? -1 : ctx.tabIndex);\n          i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel || null)(\"aria-labelledby\", ctx.ariaLabelledby)(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-checked\", ctx.indeterminate ? \"mixed\" : null)(\"name\", ctx.name)(\"value\", ctx.value);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"matRippleTrigger\", checkbox_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"for\", ctx.inputId);\n        }\n      },\n      dependencies: [MatRipple, _MatInternalFormField],\n      styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color)}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-checkbox',\n      host: {\n        'class': 'mat-mdc-checkbox',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n        '[class.mdc-checkbox--disabled]': 'disabled',\n        '[id]': 'id',\n        // Add classes that users can use to more easily target disabled or checked checkboxes.\n        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n        '[class.mat-mdc-checkbox-checked]': 'checked',\n        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"'\n      },\n      providers: [MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatCheckbox,\n        multi: true\n      }],\n      exportAs: 'matCheckbox',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color)}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_CHECKBOX_DEFAULT_OPTIONS]\n    }]\n  }], {\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    id: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    name: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    indeterminateChange: [{\n      type: Output\n    }],\n    value: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    _inputElement: [{\n      type: ViewChild,\n      args: ['input']\n    }],\n    _labelElement: [{\n      type: ViewChild,\n      args: ['label']\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? undefined : numberAttribute(value)\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    ripple: [{\n      type: ViewChild,\n      args: [MatRipple]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    indeterminate: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatCheckboxRequiredValidator_BaseFactory;\n      return function MatCheckboxRequiredValidator_Factory(t) {\n        return (ɵMatCheckboxRequiredValidator_BaseFactory || (ɵMatCheckboxRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatCheckboxRequiredValidator)))(t || MatCheckboxRequiredValidator);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCheckboxRequiredValidator,\n      selectors: [[\"mat-checkbox\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"formControl\", \"\"], [\"mat-checkbox\", \"required\", \"\", \"ngModel\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_CHECKBOX_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n      providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR],\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatCheckboxRequiredValidatorModule {\n  static {\n    this.ɵfac = function _MatCheckboxRequiredValidatorModule_Factory(t) {\n      return new (t || _MatCheckboxRequiredValidatorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: _MatCheckboxRequiredValidatorModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatCheckboxRequiredValidatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckboxRequiredValidator],\n      exports: [MatCheckboxRequiredValidator]\n    }]\n  }], null, null);\n})();\nclass MatCheckboxModule {\n  static {\n    this.ɵfac = function MatCheckboxModule_Factory(t) {\n      return new (t || MatCheckboxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCheckboxModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCheckbox, MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCheckbox, MatCommonModule],\n      exports: [MatCheckbox, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "forwardRef", "EventEmitter", "ANIMATION_MODULE_TYPE", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Optional", "Inject", "Input", "Output", "ViewChild", "Directive", "NgModule", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "CheckboxRequiredValidator", "<PERSON><PERSON><PERSON><PERSON>", "_MatInternalFormField", "MatCommonModule", "_c0", "_c1", "_c2", "MAT_CHECKBOX_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY", "color", "clickAction", "TransitionCheckState", "MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "MatCheckbox", "multi", "MatCheckboxChange", "nextUniqueId", "defaults", "focus", "_inputElement", "nativeElement", "_createChangeEvent", "isChecked", "event", "source", "checked", "_getAnimationTargetElement", "inputId", "id", "_uniqueId", "constructor", "_elementRef", "_changeDetectorRef", "_ngZone", "tabIndex", "_animationMode", "_options", "_animationClasses", "uncheckedToChecked", "uncheckedToIndeterminate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "checkedToIndeterminate", "indeterminateToChecked", "indeterminate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelPosition", "name", "change", "indeterminateChange", "_onTouched", "_currentAnimationClass", "_currentCheckState", "Init", "_controlValueAccessorChangeFn", "_validatorChangeFn", "_checked", "_disabled", "_indeterminate", "parseInt", "ngOnChanges", "changes", "ngAfterViewInit", "_syncIndeterminate", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "indeterminate", "changed", "_transitionCheckState", "Indeterminate", "Checked", "Unchecked", "emit", "_isRippleDisabled", "disable<PERSON><PERSON><PERSON>", "_onLabelTextChange", "detectChanges", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "validate", "control", "required", "registerOnValidatorChange", "newState", "oldState", "element", "classList", "remove", "_getAnimationClassForCheckStateTransition", "length", "add", "animationClass", "runOutsideAngular", "setTimeout", "_emitChangeEvent", "toggle", "_handleInputClick", "Promise", "resolve", "then", "_onInteractionEvent", "stopPropagation", "_onBlur", "nativeCheckbox", "_onInputClick", "_onTouchTargetClick", "_preventBubblingFromLabel", "target", "_labelElement", "contains", "ɵfac", "MatCheckbox_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ChangeDetectorRef", "NgZone", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "MatCheckbox_Query", "rf", "ctx", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "ripple", "hostAttrs", "hostVars", "hostBindings", "MatCheckbox_HostBindings", "ɵɵhostProperty", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "None", "aria<PERSON><PERSON><PERSON><PERSON>", "HasDecoratorInputTransform", "undefined", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatCheckbox_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵlistener", "MatCheckbox_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "MatCheckbox_Template_div_click_3_listener", "ɵɵelementEnd", "MatCheckbox_Template_input_blur_4_listener", "MatCheckbox_Template_input_click_4_listener", "MatCheckbox_Template_input_change_4_listener", "ɵɵelement", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵprojection", "checkbox_r2", "ɵɵreference", "ɵɵproperty", "ɵɵadvance", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "providers", "OnPush", "imports", "decorators", "transform", "MAT_CHECKBOX_REQUIRED_VALIDATOR", "MatCheckboxRequiredValidator", "ɵMatCheckboxRequiredValidator_BaseFactory", "MatCheckboxRequiredValidator_Factory", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "ɵɵInheritDefinitionFeature", "_MatCheckboxRequiredValidatorModule", "_MatCheckboxRequiredValidatorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "MatCheckboxModule", "MatCheckboxModule_Factory"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@angular/material/fesm2022/checkbox.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Optional, Inject, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { MatRipple, _MatInternalFormField, MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n    providedIn: 'root',\n    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n        clickAction: 'check-indeterminate',\n    };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n    /** The initial state of the component before any user interaction. */\n    TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n    /** The state representing the component when it's becoming checked. */\n    TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n    /** The state representing the component when it's becoming unchecked. */\n    TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n    /** The state representing the component when it's becoming indeterminate. */\n    TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatCheckbox),\n    multi: true,\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n}\n// Increasing integer for generating unique ids for checkbox components.\nlet nextUniqueId = 0;\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n    /** Focuses the checkbox. */\n    focus() {\n        this._inputElement.nativeElement.focus();\n    }\n    /** Creates the change event that will be emitted by the checkbox. */\n    _createChangeEvent(isChecked) {\n        const event = new MatCheckboxChange();\n        event.source = this;\n        event.checked = isChecked;\n        return event;\n    }\n    /** Gets the element on which to add the animation CSS classes. */\n    _getAnimationTargetElement() {\n        return this._inputElement?.nativeElement;\n    }\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(_elementRef, _changeDetectorRef, _ngZone, tabIndex, _animationMode, _options) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._ngZone = _ngZone;\n        this._animationMode = _animationMode;\n        this._options = _options;\n        /** CSS classes to add when transitioning between the different checkbox states. */\n        this._animationClasses = {\n            uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n            uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n            checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n            checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n            indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n            indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked',\n        };\n        /**\n         * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n         * take precedence so this may be omitted.\n         */\n        this.ariaLabel = '';\n        /**\n         * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n         */\n        this.ariaLabelledby = null;\n        /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n        this.labelPosition = 'after';\n        /** Name value will be applied to the input element if present */\n        this.name = null;\n        /** Event emitted when the checkbox's `checked` value changes. */\n        this.change = new EventEmitter();\n        /** Event emitted when the checkbox's `indeterminate` value changes. */\n        this.indeterminateChange = new EventEmitter();\n        /**\n         * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n         * @docs-private\n         */\n        this._onTouched = () => { };\n        this._currentAnimationClass = '';\n        this._currentCheckState = TransitionCheckState.Init;\n        this._controlValueAccessorChangeFn = () => { };\n        this._validatorChangeFn = () => { };\n        this._checked = false;\n        this._disabled = false;\n        this._indeterminate = false;\n        this._options = this._options || defaults;\n        this.color = this._options.color || defaults.color;\n        this.tabIndex = parseInt(tabIndex) || 0;\n        this.id = this._uniqueId = `mat-mdc-checkbox-${++nextUniqueId}`;\n    }\n    ngOnChanges(changes) {\n        if (changes['required']) {\n            this._validatorChangeFn();\n        }\n    }\n    ngAfterViewInit() {\n        this._syncIndeterminate(this._indeterminate);\n    }\n    /** Whether the checkbox is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        if (value != this.checked) {\n            this._checked = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Whether the checkbox is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value !== this.disabled) {\n            this._disabled = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n     * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n     * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n     * set to false.\n     */\n    get indeterminate() {\n        return this._indeterminate;\n    }\n    set indeterminate(value) {\n        const changed = value != this._indeterminate;\n        this._indeterminate = value;\n        if (changed) {\n            if (this._indeterminate) {\n                this._transitionCheckState(TransitionCheckState.Indeterminate);\n            }\n            else {\n                this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            }\n            this.indeterminateChange.emit(this._indeterminate);\n        }\n        this._syncIndeterminate(this._indeterminate);\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Method being called whenever the label text changes. */\n    _onLabelTextChange() {\n        // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n        // component will be only marked for check, but no actual change detection runs automatically.\n        // Instead of going back into the zone in order to trigger a change detection which causes\n        // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n        // an explicit change detection for the checkbox view and its children.\n        this._changeDetectorRef.detectChanges();\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        this.checked = !!value;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    // Implemented as a part of Validator.\n    validate(control) {\n        return this.required && control.value !== true ? { 'required': true } : null;\n    }\n    // Implemented as a part of Validator.\n    registerOnValidatorChange(fn) {\n        this._validatorChangeFn = fn;\n    }\n    _transitionCheckState(newState) {\n        let oldState = this._currentCheckState;\n        let element = this._getAnimationTargetElement();\n        if (oldState === newState || !element) {\n            return;\n        }\n        if (this._currentAnimationClass) {\n            element.classList.remove(this._currentAnimationClass);\n        }\n        this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n        this._currentCheckState = newState;\n        if (this._currentAnimationClass.length > 0) {\n            element.classList.add(this._currentAnimationClass);\n            // Remove the animation class to avoid animation when the checkbox is moved between containers\n            const animationClass = this._currentAnimationClass;\n            this._ngZone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    element.classList.remove(animationClass);\n                }, 1000);\n            });\n        }\n    }\n    _emitChangeEvent() {\n        this._controlValueAccessorChangeFn(this.checked);\n        this.change.emit(this._createChangeEvent(this.checked));\n        // Assigning the value again here is redundant, but we have to do it in case it was\n        // changed inside the `change` listener which will cause the input to be out of sync.\n        if (this._inputElement) {\n            this._inputElement.nativeElement.checked = this.checked;\n        }\n    }\n    /** Toggles the `checked` state of the checkbox. */\n    toggle() {\n        this.checked = !this.checked;\n        this._controlValueAccessorChangeFn(this.checked);\n    }\n    _handleInputClick() {\n        const clickAction = this._options?.clickAction;\n        // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n        if (!this.disabled && clickAction !== 'noop') {\n            // When user manually click on the checkbox, `indeterminate` is set to false.\n            if (this.indeterminate && clickAction !== 'check') {\n                Promise.resolve().then(() => {\n                    this._indeterminate = false;\n                    this.indeterminateChange.emit(this._indeterminate);\n                });\n            }\n            this._checked = !this._checked;\n            this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            // Emit our custom change event if the native input emitted one.\n            // It is important to only emit it, if the native input triggered one, because\n            // we don't want to trigger a change event, when the `checked` variable changes for example.\n            this._emitChangeEvent();\n        }\n        else if (!this.disabled && clickAction === 'noop') {\n            // Reset native input when clicked with noop. The native checkbox becomes checked after\n            // click, reset it to be align with `checked` value of `mat-checkbox`.\n            this._inputElement.nativeElement.checked = this.checked;\n            this._inputElement.nativeElement.indeterminate = this.indeterminate;\n        }\n    }\n    _onInteractionEvent(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n    }\n    _onBlur() {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state change\n        // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    _getAnimationClassForCheckStateTransition(oldState, newState) {\n        // Don't transition if animations are disabled.\n        if (this._animationMode === 'NoopAnimations') {\n            return '';\n        }\n        switch (oldState) {\n            case TransitionCheckState.Init:\n                // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n                // [checked] bound to it.\n                if (newState === TransitionCheckState.Checked) {\n                    return this._animationClasses.uncheckedToChecked;\n                }\n                else if (newState == TransitionCheckState.Indeterminate) {\n                    return this._checked\n                        ? this._animationClasses.checkedToIndeterminate\n                        : this._animationClasses.uncheckedToIndeterminate;\n                }\n                break;\n            case TransitionCheckState.Unchecked:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.uncheckedToChecked\n                    : this._animationClasses.uncheckedToIndeterminate;\n            case TransitionCheckState.Checked:\n                return newState === TransitionCheckState.Unchecked\n                    ? this._animationClasses.checkedToUnchecked\n                    : this._animationClasses.checkedToIndeterminate;\n            case TransitionCheckState.Indeterminate:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.indeterminateToChecked\n                    : this._animationClasses.indeterminateToUnchecked;\n        }\n        return '';\n    }\n    /**\n     * Syncs the indeterminate value with the checkbox DOM node.\n     *\n     * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n     * property is supported on an element boils down to `if (propName in element)`. Domino's\n     * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n     * server-side rendering.\n     */\n    _syncIndeterminate(value) {\n        const nativeCheckbox = this._inputElement;\n        if (nativeCheckbox) {\n            nativeCheckbox.nativeElement.indeterminate = value;\n        }\n    }\n    _onInputClick() {\n        this._handleInputClick();\n    }\n    _onTouchTargetClick() {\n        this._handleInputClick();\n        if (!this.disabled) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement.nativeElement.focus();\n        }\n    }\n    /**\n     *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n     *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n     *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n     *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n     *  bubbles when the label is clicked.\n     */\n    _preventBubblingFromLabel(event) {\n        if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n            event.stopPropagation();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckbox, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: 'tabindex', attribute: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_CHECKBOX_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatCheckbox, isStandalone: true, selector: \"mat-checkbox\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], id: \"id\", required: [\"required\", \"required\", booleanAttribute], labelPosition: \"labelPosition\", name: \"name\", value: \"value\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? undefined : numberAttribute(value))], color: \"color\", checked: [\"checked\", \"checked\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], indeterminate: [\"indeterminate\", \"indeterminate\", booleanAttribute] }, outputs: { change: \"change\", indeterminateChange: \"indeterminateChange\" }, host: { properties: { \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"class._mat-animation-noopable\": \"_animationMode === 'NoopAnimations'\", \"class.mdc-checkbox--disabled\": \"disabled\", \"id\": \"id\", \"class.mat-mdc-checkbox-disabled\": \"disabled\", \"class.mat-mdc-checkbox-checked\": \"checked\", \"class\": \"color ? \\\"mat-\\\" + color : \\\"mat-accent\\\"\" }, classAttribute: \"mat-mdc-checkbox\" }, providers: [\n            MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n            {\n                provide: NG_VALIDATORS,\n                useExisting: MatCheckbox,\n                multi: true,\n            },\n        ], viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"_labelElement\", first: true, predicate: [\"label\"], descendants: true }, { propertyName: \"ripple\", first: true, predicate: MatRipple, descendants: true }], exportAs: [\"matCheckbox\"], usesOnChanges: true, ngImport: i0, template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color)}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: _MatInternalFormField, selector: \"div[mat-internal-form-field]\", inputs: [\"labelPosition\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-checkbox', host: {\n                        'class': 'mat-mdc-checkbox',\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n                        '[class.mdc-checkbox--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Add classes that users can use to more easily target disabled or checked checkboxes.\n                        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n                        '[class.mat-mdc-checkbox-checked]': 'checked',\n                        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"',\n                    }, providers: [\n                        MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n                        {\n                            provide: NG_VALIDATORS,\n                            useExisting: MatCheckbox,\n                            multi: true,\n                        },\n                    ], exportAs: 'matCheckbox', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, imports: [MatRipple, _MatInternalFormField], template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color)}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_CHECKBOX_DEFAULT_OPTIONS]\n                }] }], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], id: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], labelPosition: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], indeterminateChange: [{\n                type: Output\n            }], value: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }], _labelElement: [{\n                type: ViewChild,\n                args: ['label']\n            }], tabIndex: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? undefined : numberAttribute(value)) }]\n            }], color: [{\n                type: Input\n            }], ripple: [{\n                type: ViewChild,\n                args: [MatRipple]\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], indeterminate: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n    multi: true,\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxRequiredValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCheckboxRequiredValidator, isStandalone: true, selector: \"mat-checkbox[required][formControlName],\\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]\", providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxRequiredValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n                    providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR],\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatCheckboxRequiredValidatorModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, imports: [MatCheckboxRequiredValidator], exports: [MatCheckboxRequiredValidator] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCheckboxRequiredValidator],\n                    exports: [MatCheckboxRequiredValidator],\n                }]\n        }] });\nclass MatCheckboxModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule], exports: [MatCheckbox, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCheckbox, MatCommonModule],\n                    exports: [MatCheckbox, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACrQ,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,yBAAyB,QAAQ,gBAAgB;AAC5F,SAASC,SAAS,EAAEC,qBAAqB,EAAEC,eAAe,QAAQ,wBAAwB;;AAE1F;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,4BAA4B,GAAG,IAAI1B,cAAc,CAAC,8BAA8B,EAAE;EACpF2B,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO;IACHC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACjB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7B;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/D;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;AACrF,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG;EACxCC,OAAO,EAAEjB,iBAAiB;EAC1BkB,WAAW,EAAElC,UAAU,CAAC,MAAMmC,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,iBAAiB,CAAC;AAExB;AACA,IAAIC,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,QAAQ,GAAGX,oCAAoC,CAAC,CAAC;AACvD,MAAMO,WAAW,CAAC;EACd;EACAK,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;EAC5C;EACA;EACAG,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,MAAMC,KAAK,GAAG,IAAIR,iBAAiB,CAAC,CAAC;IACrCQ,KAAK,CAACC,MAAM,GAAG,IAAI;IACnBD,KAAK,CAACE,OAAO,GAAGH,SAAS;IACzB,OAAOC,KAAK;EAChB;EACA;EACAG,0BAA0BA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACP,aAAa,EAAEC,aAAa;EAC5C;EACA;EACA,IAAIO,OAAOA,CAAA,EAAG;IACV,OAAO,GAAG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,SAAS,QAAQ;EAC/C;EACAC,WAAWA,CAACC,WAAW,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IACtF,IAAI,CAACL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAACC,iBAAiB,GAAG;MACrBC,kBAAkB,EAAE,sCAAsC;MAC1DC,wBAAwB,EAAE,4CAA4C;MACtEC,kBAAkB,EAAE,sCAAsC;MAC1DC,sBAAsB,EAAE,0CAA0C;MAClEC,sBAAsB,EAAE,0CAA0C;MAClEC,wBAAwB,EAAE;IAC9B,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;AACR;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,aAAa,GAAG,OAAO;IAC5B;IACA,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,MAAM,GAAG,IAAIrE,YAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACsE,mBAAmB,GAAG,IAAItE,YAAY,CAAC,CAAC;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAACuE,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,kBAAkB,GAAG3C,oBAAoB,CAAC4C,IAAI;IACnD,IAAI,CAACC,6BAA6B,GAAG,MAAM,CAAE,CAAC;IAC9C,IAAI,CAACC,kBAAkB,GAAG,MAAM,CAAE,CAAC;IACnC,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACtB,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAInB,QAAQ;IACzC,IAAI,CAACV,KAAK,GAAG,IAAI,CAAC6B,QAAQ,CAAC7B,KAAK,IAAIU,QAAQ,CAACV,KAAK;IAClD,IAAI,CAAC2B,QAAQ,GAAGyB,QAAQ,CAACzB,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI,CAACN,EAAE,GAAG,IAAI,CAACC,SAAS,GAAG,oBAAoB,EAAEb,YAAY,EAAE;EACnE;EACA4C,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACrB,IAAI,CAACN,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAO,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACL,cAAc,CAAC;EAChD;EACA;EACA,IAAIjC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC+B,QAAQ;EACxB;EACA,IAAI/B,OAAOA,CAACuC,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,CAACvC,OAAO,EAAE;MACvB,IAAI,CAAC+B,QAAQ,GAAGQ,KAAK;MACrB,IAAI,CAAChC,kBAAkB,CAACiC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACT,SAAS;EACzB;EACA,IAAIS,QAAQA,CAACF,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACE,QAAQ,EAAE;MACzB,IAAI,CAACT,SAAS,GAAGO,KAAK;MACtB,IAAI,CAAChC,kBAAkB,CAACiC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACT,cAAc;EAC9B;EACA,IAAIS,aAAaA,CAACH,KAAK,EAAE;IACrB,MAAMI,OAAO,GAAGJ,KAAK,IAAI,IAAI,CAACN,cAAc;IAC5C,IAAI,CAACA,cAAc,GAAGM,KAAK;IAC3B,IAAII,OAAO,EAAE;MACT,IAAI,IAAI,CAACV,cAAc,EAAE;QACrB,IAAI,CAACW,qBAAqB,CAAC5D,oBAAoB,CAAC6D,aAAa,CAAC;MAClE,CAAC,MACI;QACD,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAAC5C,OAAO,GAAGhB,oBAAoB,CAAC8D,OAAO,GAAG9D,oBAAoB,CAAC+D,SAAS,CAAC;MAC5G;MACA,IAAI,CAACvB,mBAAmB,CAACwB,IAAI,CAAC,IAAI,CAACf,cAAc,CAAC;IACtD;IACA,IAAI,CAACK,kBAAkB,CAAC,IAAI,CAACL,cAAc,CAAC;EAChD;EACAgB,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,aAAa,IAAI,IAAI,CAACT,QAAQ;EAC9C;EACA;EACAU,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC5C,kBAAkB,CAAC6C,aAAa,CAAC,CAAC;EAC3C;EACA;EACAC,UAAUA,CAACd,KAAK,EAAE;IACd,IAAI,CAACvC,OAAO,GAAG,CAAC,CAACuC,KAAK;EAC1B;EACA;EACAe,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC1B,6BAA6B,GAAG0B,EAAE;EAC3C;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAC9B,UAAU,GAAG8B,EAAE;EACxB;EACA;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACjB,QAAQ,GAAGiB,UAAU;EAC9B;EACA;EACAC,QAAQA,CAACC,OAAO,EAAE;IACd,OAAO,IAAI,CAACC,QAAQ,IAAID,OAAO,CAACrB,KAAK,KAAK,IAAI,GAAG;MAAE,UAAU,EAAE;IAAK,CAAC,GAAG,IAAI;EAChF;EACA;EACAuB,yBAAyBA,CAACP,EAAE,EAAE;IAC1B,IAAI,CAACzB,kBAAkB,GAAGyB,EAAE;EAChC;EACAX,qBAAqBA,CAACmB,QAAQ,EAAE;IAC5B,IAAIC,QAAQ,GAAG,IAAI,CAACrC,kBAAkB;IACtC,IAAIsC,OAAO,GAAG,IAAI,CAAChE,0BAA0B,CAAC,CAAC;IAC/C,IAAI+D,QAAQ,KAAKD,QAAQ,IAAI,CAACE,OAAO,EAAE;MACnC;IACJ;IACA,IAAI,IAAI,CAACvC,sBAAsB,EAAE;MAC7BuC,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,IAAI,CAACzC,sBAAsB,CAAC;IACzD;IACA,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAAC0C,yCAAyC,CAACJ,QAAQ,EAAED,QAAQ,CAAC;IAChG,IAAI,CAACpC,kBAAkB,GAAGoC,QAAQ;IAClC,IAAI,IAAI,CAACrC,sBAAsB,CAAC2C,MAAM,GAAG,CAAC,EAAE;MACxCJ,OAAO,CAACC,SAAS,CAACI,GAAG,CAAC,IAAI,CAAC5C,sBAAsB,CAAC;MAClD;MACA,MAAM6C,cAAc,GAAG,IAAI,CAAC7C,sBAAsB;MAClD,IAAI,CAAClB,OAAO,CAACgE,iBAAiB,CAAC,MAAM;QACjCC,UAAU,CAAC,MAAM;UACbR,OAAO,CAACC,SAAS,CAACC,MAAM,CAACI,cAAc,CAAC;QAC5C,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN;EACJ;EACAG,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC7C,6BAA6B,CAAC,IAAI,CAAC7B,OAAO,CAAC;IAChD,IAAI,CAACuB,MAAM,CAACyB,IAAI,CAAC,IAAI,CAACpD,kBAAkB,CAAC,IAAI,CAACI,OAAO,CAAC,CAAC;IACvD;IACA;IACA,IAAI,IAAI,CAACN,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO;IAC3D;EACJ;EACA;EACA2E,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC3E,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAAC6B,6BAA6B,CAAC,IAAI,CAAC7B,OAAO,CAAC;EACpD;EACA4E,iBAAiBA,CAAA,EAAG;IAChB,MAAM7F,WAAW,GAAG,IAAI,CAAC4B,QAAQ,EAAE5B,WAAW;IAC9C;IACA,IAAI,CAAC,IAAI,CAAC0D,QAAQ,IAAI1D,WAAW,KAAK,MAAM,EAAE;MAC1C;MACA,IAAI,IAAI,CAAC2D,aAAa,IAAI3D,WAAW,KAAK,OAAO,EAAE;QAC/C8F,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAAC9C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACT,mBAAmB,CAACwB,IAAI,CAAC,IAAI,CAACf,cAAc,CAAC;QACtD,CAAC,CAAC;MACN;MACA,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACa,qBAAqB,CAAC,IAAI,CAACb,QAAQ,GAAG/C,oBAAoB,CAAC8D,OAAO,GAAG9D,oBAAoB,CAAC+D,SAAS,CAAC;MACzG;MACA;MACA;MACA,IAAI,CAAC2B,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI,IAAI,CAAC,IAAI,CAACjC,QAAQ,IAAI1D,WAAW,KAAK,MAAM,EAAE;MAC/C;MACA;MACA,IAAI,CAACW,aAAa,CAACC,aAAa,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO;MACvD,IAAI,CAACN,aAAa,CAACC,aAAa,CAAC+C,aAAa,GAAG,IAAI,CAACA,aAAa;IACvE;EACJ;EACAsC,mBAAmBA,CAAClF,KAAK,EAAE;IACvB;IACA;IACA;IACAA,KAAK,CAACmF,eAAe,CAAC,CAAC;EAC3B;EACAC,OAAOA,CAAA,EAAG;IACN;IACA;IACA;IACA;IACA;IACAL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACtD,UAAU,CAAC,CAAC;MACjB,IAAI,CAAClB,kBAAkB,CAACiC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA4B,yCAAyCA,CAACJ,QAAQ,EAAED,QAAQ,EAAE;IAC1D;IACA,IAAI,IAAI,CAACrD,cAAc,KAAK,gBAAgB,EAAE;MAC1C,OAAO,EAAE;IACb;IACA,QAAQsD,QAAQ;MACZ,KAAKhF,oBAAoB,CAAC4C,IAAI;QAC1B;QACA;QACA,IAAImC,QAAQ,KAAK/E,oBAAoB,CAAC8D,OAAO,EAAE;UAC3C,OAAO,IAAI,CAAClC,iBAAiB,CAACC,kBAAkB;QACpD,CAAC,MACI,IAAIkD,QAAQ,IAAI/E,oBAAoB,CAAC6D,aAAa,EAAE;UACrD,OAAO,IAAI,CAACd,QAAQ,GACd,IAAI,CAACnB,iBAAiB,CAACI,sBAAsB,GAC7C,IAAI,CAACJ,iBAAiB,CAACE,wBAAwB;QACzD;QACA;MACJ,KAAK9B,oBAAoB,CAAC+D,SAAS;QAC/B,OAAOgB,QAAQ,KAAK/E,oBAAoB,CAAC8D,OAAO,GAC1C,IAAI,CAAClC,iBAAiB,CAACC,kBAAkB,GACzC,IAAI,CAACD,iBAAiB,CAACE,wBAAwB;MACzD,KAAK9B,oBAAoB,CAAC8D,OAAO;QAC7B,OAAOiB,QAAQ,KAAK/E,oBAAoB,CAAC+D,SAAS,GAC5C,IAAI,CAACnC,iBAAiB,CAACG,kBAAkB,GACzC,IAAI,CAACH,iBAAiB,CAACI,sBAAsB;MACvD,KAAKhC,oBAAoB,CAAC6D,aAAa;QACnC,OAAOkB,QAAQ,KAAK/E,oBAAoB,CAAC8D,OAAO,GAC1C,IAAI,CAAClC,iBAAiB,CAACK,sBAAsB,GAC7C,IAAI,CAACL,iBAAiB,CAACM,wBAAwB;IAC7D;IACA,OAAO,EAAE;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIoB,kBAAkBA,CAACC,KAAK,EAAE;IACtB,MAAM4C,cAAc,GAAG,IAAI,CAACzF,aAAa;IACzC,IAAIyF,cAAc,EAAE;MAChBA,cAAc,CAACxF,aAAa,CAAC+C,aAAa,GAAGH,KAAK;IACtD;EACJ;EACA6C,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACR,iBAAiB,CAAC,CAAC;EAC5B;EACAS,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACT,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAACnC,QAAQ,EAAE;MAChB;MACA;MACA,IAAI,CAAC/C,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI6F,yBAAyBA,CAACxF,KAAK,EAAE;IAC7B,IAAI,CAAC,CAACA,KAAK,CAACyF,MAAM,IAAI,IAAI,CAACC,aAAa,CAAC7F,aAAa,CAAC8F,QAAQ,CAAC3F,KAAK,CAACyF,MAAM,CAAC,EAAE;MAC3EzF,KAAK,CAACmF,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA;IAAS,IAAI,CAACS,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFxG,WAAW,EAArBrC,EAAE,CAAA8I,iBAAA,CAAqC9I,EAAE,CAAC+I,UAAU,GAApD/I,EAAE,CAAA8I,iBAAA,CAA+D9I,EAAE,CAACgJ,iBAAiB,GAArFhJ,EAAE,CAAA8I,iBAAA,CAAgG9I,EAAE,CAACiJ,MAAM,GAA3GjJ,EAAE,CAAAkJ,iBAAA,CAAsH,UAAU,GAAlIlJ,EAAE,CAAA8I,iBAAA,CAA8J1I,qBAAqB,MAArLJ,EAAE,CAAA8I,iBAAA,CAAgNnH,4BAA4B;IAAA,CAA4D;EAAE;EAC5Y;IAAS,IAAI,CAACwH,IAAI,kBAD8EnJ,EAAE,CAAAoJ,iBAAA;MAAAC,IAAA,EACJhH,WAAW;MAAAiH,SAAA;MAAAC,SAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADTzJ,EAAE,CAAA2J,WAAA,CAAAnI,GAAA;UAAFxB,EAAE,CAAA2J,WAAA,CAAAlI,GAAA;UAAFzB,EAAE,CAAA2J,WAAA,CAQuJtI,SAAS;QAAA;QAAA,IAAAoI,EAAA;UAAA,IAAAG,EAAA;UARlK5J,EAAE,CAAA6J,cAAA,CAAAD,EAAA,GAAF5J,EAAE,CAAA8J,WAAA,QAAAJ,GAAA,CAAA/G,aAAA,GAAAiH,EAAA,CAAAG,KAAA;UAAF/J,EAAE,CAAA6J,cAAA,CAAAD,EAAA,GAAF5J,EAAE,CAAA8J,WAAA,QAAAJ,GAAA,CAAAjB,aAAA,GAAAmB,EAAA,CAAAG,KAAA;UAAF/J,EAAE,CAAA6J,cAAA,CAAAD,EAAA,GAAF5J,EAAE,CAAA8J,WAAA,QAAAJ,GAAA,CAAAM,MAAA,GAAAJ,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAE,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAX,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzJ,EAAE,CAAAqK,cAAA,OAAAX,GAAA,CAAAtG,EACM,CAAC;UADTpD,EAAE,CAAAsK,WAAA,aACJ,IAAI,gBAAJ,IAAI,qBAAJ,IAAI;UADFtK,EAAE,CAAAuK,UAAA,CAAAb,GAAA,CAAA3H,KAAA,GACI,MAAM,GAAA2H,GAAA,CAAA3H,KAAA,GAAW,YAAf,CAAC;UADT/B,EAAE,CAAAwK,WAAA,4BAAAd,GAAA,CAAA/F,cAAA,KACe,gBAAT,CAAC,2BAAA+F,GAAA,CAAAhE,QAAD,CAAC,8BAAAgE,GAAA,CAAAhE,QAAD,CAAC,6BAAAgE,GAAA,CAAAzG,OAAD,CAAC;QAAA;MAAA;MAAAwH,MAAA;QAAArG,SAAA,GADTpE,EAAE,CAAA0K,YAAA,CAAAC,IAAA;QAAAtG,cAAA,GAAFrE,EAAE,CAAA0K,YAAA,CAAAC,IAAA;QAAAC,eAAA,GAAF5K,EAAE,CAAA0K,YAAA,CAAAC,IAAA;QAAAvH,EAAA;QAAA0D,QAAA,GAAF9G,EAAE,CAAA0K,YAAA,CAAAG,0BAAA,0BACuQxK,gBAAgB;QAAAiE,aAAA;QAAAC,IAAA;QAAAiB,KAAA;QAAAW,aAAA,GADzRnG,EAAE,CAAA0K,YAAA,CAAAG,0BAAA,oCAC0YxK,gBAAgB;QAAAqD,QAAA,GAD5Z1D,EAAE,CAAA0K,YAAA,CAAAG,0BAAA,0BACicrF,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAGsF,SAAS,GAAGxK,eAAe,CAACkF,KAAK,CAAE;QAAAzD,KAAA;QAAAkB,OAAA,GADjgBjD,EAAE,CAAA0K,YAAA,CAAAG,0BAAA,wBACkjBxK,gBAAgB;QAAAqF,QAAA,GADpkB1F,EAAE,CAAA0K,YAAA,CAAAG,0BAAA,0BACwmBxK,gBAAgB;QAAAsF,aAAA,GAD1nB3F,EAAE,CAAA0K,YAAA,CAAAG,0BAAA,oCAC6qBxK,gBAAgB;MAAA;MAAA0K,OAAA;QAAAvG,MAAA;QAAAC,mBAAA;MAAA;MAAAuG,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAD/rBlL,EAAE,CAAAmL,kBAAA,CAC4rC,CACtxCjJ,mCAAmC,EACnC;QACIC,OAAO,EAAEhB,aAAa;QACtBiB,WAAW,EAAEC,WAAW;QACxBC,KAAK,EAAE;MACX,CAAC,CACJ,GAR2FtC,EAAE,CAAAoL,wBAAA,EAAFpL,EAAE,CAAAqL,oBAAA,EAAFrL,EAAE,CAAAsL,mBAAA;MAAAC,kBAAA,EAAA7J,GAAA;MAAA8J,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qBAAAnC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAoC,GAAA,GAAF7L,EAAE,CAAA8L,gBAAA;UAAF9L,EAAE,CAAA+L,eAAA;UAAF/L,EAAE,CAAAgM,cAAA,YAQ6W,CAAC;UARhXhM,EAAE,CAAAiM,UAAA,mBAAAC,0CAAAC,MAAA;YAAFnM,EAAE,CAAAoM,aAAA,CAAAP,GAAA;YAAA,OAAF7L,EAAE,CAAAqM,WAAA,CAQ0U3C,GAAA,CAAAnB,yBAAA,CAAA4D,MAAgC,CAAC;UAAA,CAAC,CAAC;UAR/WnM,EAAE,CAAAgM,cAAA,eAQuZ,CAAC,YAAoJ,CAAC;UAR/iBhM,EAAE,CAAAiM,UAAA,mBAAAK,0CAAA;YAAFtM,EAAE,CAAAoM,aAAA,CAAAP,GAAA;YAAA,OAAF7L,EAAE,CAAAqM,WAAA,CAQqhB3C,GAAA,CAAApB,mBAAA,CAAoB,CAAC;UAAA,CAAC,CAAC;UAR9iBtI,EAAE,CAAAuM,YAAA,CAQkjB,CAAC;UARrjBvM,EAAE,CAAAgM,cAAA,iBAQ+1C,CAAC;UARl2ChM,EAAE,CAAAiM,UAAA,kBAAAO,2CAAA;YAAFxM,EAAE,CAAAoM,aAAA,CAAAP,GAAA;YAAA,OAAF7L,EAAE,CAAAqM,WAAA,CAQsvC3C,GAAA,CAAAvB,OAAA,CAAQ,CAAC;UAAA,CAAC,CAAC,mBAAAsE,4CAAA;YARnwCzM,EAAE,CAAAoM,aAAA,CAAAP,GAAA;YAAA,OAAF7L,EAAE,CAAAqM,WAAA,CAQwxC3C,GAAA,CAAArB,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC,oBAAAqE,6CAAAP,MAAA;YAR3yCnM,EAAE,CAAAoM,aAAA,CAAAP,GAAA;YAAA,OAAF7L,EAAE,CAAAqM,WAAA,CAQi0C3C,GAAA,CAAAzB,mBAAA,CAAAkE,MAA0B,CAAC;UAAA,CAAC,CAAC;UARh2CnM,EAAE,CAAAuM,YAAA,CAQ+1C,CAAC;UARl2CvM,EAAE,CAAA2M,SAAA,YAQ+4C,CAAC;UARl5C3M,EAAE,CAAAgM,cAAA,YAQ67C,CAAC;UARh8ChM,EAAE,CAAA4M,cAAA;UAAF5M,EAAE,CAAAgM,cAAA,YAQ+kD,CAAC;UARllDhM,EAAE,CAAA2M,SAAA,cAQ0tD,CAAC;UAR7tD3M,EAAE,CAAAuM,YAAA,CAQwuD,CAAC;UAR3uDvM,EAAE,CAAA6M,eAAA;UAAF7M,EAAE,CAAA2M,SAAA,cAQ6xD,CAAC;UARhyD3M,EAAE,CAAAuM,YAAA,CAQyyD,CAAC;UAR5yDvM,EAAE,CAAA2M,SAAA,cAQmgE,CAAC;UARtgE3M,EAAE,CAAAuM,YAAA,CAQ6gE,CAAC;UARhhEvM,EAAE,CAAAgM,cAAA,mBAQ01E,CAAC;UAR71EhM,EAAE,CAAA8M,YAAA,GAQy3E,CAAC;UAR53E9M,EAAE,CAAAuM,YAAA,CAQq4E,CAAC,CAAO,CAAC;QAAA;QAAA,IAAA9C,EAAA;UAAA,MAAAsD,WAAA,GARh5E/M,EAAE,CAAAgN,WAAA;UAAFhN,EAAE,CAAAiN,UAAA,kBAAAvD,GAAA,CAAApF,aAQ8T,CAAC;UARjUtE,EAAE,CAAAkN,SAAA,EAQ6sB,CAAC;UARhtBlN,EAAE,CAAAwK,WAAA,2BAAAd,GAAA,CAAAzG,OAQ6sB,CAAC;UARhtBjD,EAAE,CAAAiN,UAAA,YAAAvD,GAAA,CAAAzG,OAQwhC,CAAC,kBAAAyG,GAAA,CAAA/D,aAA6C,CAAC,aAAA+D,GAAA,CAAAhE,QAAmC,CAAC,OAAAgE,GAAA,CAAAvG,OAA4B,CAAC,aAAAuG,GAAA,CAAA5C,QAAmC,CAAC,aAAA4C,GAAA,CAAAhE,QAAA,QAAAgE,GAAA,CAAAhG,QAAmD,CAAC;UARluC1D,EAAE,CAAAsK,WAAA,eAAAZ,GAAA,CAAAtF,SAAA,6BAAAsF,GAAA,CAAArF,cAAA,sBAAAqF,GAAA,CAAAkB,eAAA,kBAAAlB,GAAA,CAAA/D,aAAA,2BAAA+D,GAAA,CAAAnF,IAAA,WAAAmF,GAAA,CAAAlE,KAAA;UAAFxF,EAAE,CAAAkN,SAAA,EAQ+5D,CAAC;UARl6DlN,EAAE,CAAAiN,UAAA,qBAAAF,WAQ+5D,CAAC,sBAAArD,GAAA,CAAAvD,aAAA,IAAAuD,GAAA,CAAAhE,QAAwD,CAAC,0BAAmC,CAAC;UAR//D1F,EAAE,CAAAkN,SAAA,CAQy1E,CAAC;UAR51ElN,EAAE,CAAAiN,UAAA,QAAAvD,GAAA,CAAAvG,OAQy1E,CAAC;QAAA;MAAA;MAAAgK,YAAA,GAA44nB9L,SAAS,EAAwPC,qBAAqB;MAAA8L,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAyK;EAAE;AAC7wtB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAVoGvN,EAAE,CAAAwN,iBAAA,CAUXnL,WAAW,EAAc,CAAC;IACzGgH,IAAI,EAAE9I,SAAS;IACfkN,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEC,IAAI,EAAE;QAC7B,OAAO,EAAE,kBAAkB;QAC3B,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,iCAAiC,EAAE,qCAAqC;QACxE,gCAAgC,EAAE,UAAU;QAC5C,MAAM,EAAE,IAAI;QACZ;QACA,mCAAmC,EAAE,UAAU;QAC/C,kCAAkC,EAAE,SAAS;QAC7C,SAAS,EAAE;MACf,CAAC;MAAEC,SAAS,EAAE,CACV1L,mCAAmC,EACnC;QACIC,OAAO,EAAEhB,aAAa;QACtBiB,WAAW,EAAEC,WAAW;QACxBC,KAAK,EAAE;MACX,CAAC,CACJ;MAAE0I,QAAQ,EAAE,aAAa;MAAEqC,aAAa,EAAE7M,iBAAiB,CAACmK,IAAI;MAAE2C,eAAe,EAAE7M,uBAAuB,CAACoN,MAAM;MAAE5C,UAAU,EAAE,IAAI;MAAE6C,OAAO,EAAE,CAACzM,SAAS,EAAEC,qBAAqB,CAAC;MAAEqK,QAAQ,EAAE,ipEAAipE;MAAEyB,MAAM,EAAE,CAAC,6xnBAA6xnB;IAAE,CAAC;EACrosB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/D,IAAI,EAAErJ,EAAE,CAAC+I;EAAW,CAAC,EAAE;IAAEM,IAAI,EAAErJ,EAAE,CAACgJ;EAAkB,CAAC,EAAE;IAAEK,IAAI,EAAErJ,EAAE,CAACiJ;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAEyB,SAAS;IAAEiD,UAAU,EAAE,CAAC;MAC7H1E,IAAI,EAAE3I,SAAS;MACf+M,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAEpE,IAAI,EAAEyB,SAAS;IAAEiD,UAAU,EAAE,CAAC;MAClC1E,IAAI,EAAE1I;IACV,CAAC,EAAE;MACC0I,IAAI,EAAEzI,MAAM;MACZ6M,IAAI,EAAE,CAACrN,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEiJ,IAAI,EAAEyB,SAAS;IAAEiD,UAAU,EAAE,CAAC;MAClC1E,IAAI,EAAE1I;IACV,CAAC,EAAE;MACC0I,IAAI,EAAEzI,MAAM;MACZ6M,IAAI,EAAE,CAAC9L,4BAA4B;IACvC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEyC,SAAS,EAAE,CAAC;MACrCiF,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEpJ,cAAc,EAAE,CAAC;MACjBgF,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE7C,eAAe,EAAE,CAAC;MAClBvB,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAErK,EAAE,EAAE,CAAC;MACLiG,IAAI,EAAExI;IACV,CAAC,CAAC;IAAEiG,QAAQ,EAAE,CAAC;MACXuC,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE3N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiE,aAAa,EAAE,CAAC;MAChB+E,IAAI,EAAExI;IACV,CAAC,CAAC;IAAE0D,IAAI,EAAE,CAAC;MACP8E,IAAI,EAAExI;IACV,CAAC,CAAC;IAAE2D,MAAM,EAAE,CAAC;MACT6E,IAAI,EAAEvI;IACV,CAAC,CAAC;IAAE2D,mBAAmB,EAAE,CAAC;MACtB4E,IAAI,EAAEvI;IACV,CAAC,CAAC;IAAE0E,KAAK,EAAE,CAAC;MACR6D,IAAI,EAAExI;IACV,CAAC,CAAC;IAAEsF,aAAa,EAAE,CAAC;MAChBkD,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE3N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsC,aAAa,EAAE,CAAC;MAChB0G,IAAI,EAAEtI,SAAS;MACf0M,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAEhF,aAAa,EAAE,CAAC;MAChBY,IAAI,EAAEtI,SAAS;MACf0M,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE/J,QAAQ,EAAE,CAAC;MACX2F,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAGxI,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAGsF,SAAS,GAAGxK,eAAe,CAACkF,KAAK;MAAG,CAAC;IACzF,CAAC,CAAC;IAAEzD,KAAK,EAAE,CAAC;MACRsH,IAAI,EAAExI;IACV,CAAC,CAAC;IAAEmJ,MAAM,EAAE,CAAC;MACTX,IAAI,EAAEtI,SAAS;MACf0M,IAAI,EAAE,CAACpM,SAAS;IACpB,CAAC,CAAC;IAAE4B,OAAO,EAAE,CAAC;MACVoG,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE3N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEqF,QAAQ,EAAE,CAAC;MACX2D,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE3N;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsF,aAAa,EAAE,CAAC;MAChB0D,IAAI,EAAExI,KAAK;MACX4M,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAE3N;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM4N,+BAA+B,GAAG;EACpC9L,OAAO,EAAEhB,aAAa;EACtBiB,WAAW,EAAElC,UAAU,CAAC,MAAMgO,4BAA4B,CAAC;EAC3D5L,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4L,4BAA4B,SAAS9M,yBAAyB,CAAC;EACjE;IAAS,IAAI,CAACuH,IAAI;MAAA,IAAAwF,yCAAA;MAAA,gBAAAC,qCAAAvF,CAAA;QAAA,QAAAsF,yCAAA,KAAAA,yCAAA,GAnH8EnO,EAAE,CAAAqO,qBAAA,CAmHQH,4BAA4B,IAAArF,CAAA,IAA5BqF,4BAA4B;MAAA;IAAA,IAAqD;EAAE;EAC7L;IAAS,IAAI,CAACI,IAAI,kBApH8EtO,EAAE,CAAAuO,iBAAA;MAAAlF,IAAA,EAoHJ6E,4BAA4B;MAAA5E,SAAA;MAAA2B,UAAA;MAAAC,QAAA,GApH1BlL,EAAE,CAAAmL,kBAAA,CAoHkM,CAAC8C,+BAA+B,CAAC,GApHrOjO,EAAE,CAAAwO,0BAAA;IAAA,EAoH2Q;EAAE;AACnX;AACA;EAAA,QAAAjB,SAAA,oBAAAA,SAAA,KAtHoGvN,EAAE,CAAAwN,iBAAA,CAsHXU,4BAA4B,EAAc,CAAC;IAC1H7E,IAAI,EAAErI,SAAS;IACfyM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;AAC9B,kFAAkF;MAC9DE,SAAS,EAAE,CAACK,+BAA+B,CAAC;MAC5ChD,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMwD,mCAAmC,CAAC;EACtC;IAAS,IAAI,CAAC9F,IAAI,YAAA+F,4CAAA7F,CAAA;MAAA,YAAAA,CAAA,IAAwF4F,mCAAmC;IAAA,CAAkD;EAAE;EACjM;IAAS,IAAI,CAACE,IAAI,kBAtI8E3O,EAAE,CAAA4O,gBAAA;MAAAvF,IAAA,EAsISoF;IAAmC,EAAqF;EAAE;EACrO;IAAS,IAAI,CAACI,IAAI,kBAvI8E7O,EAAE,CAAA8O,gBAAA,IAuI+C;EAAE;AACvJ;AACA;EAAA,QAAAvB,SAAA,oBAAAA,SAAA,KAzIoGvN,EAAE,CAAAwN,iBAAA,CAyIXiB,mCAAmC,EAAc,CAAC;IACjIpF,IAAI,EAAEpI,QAAQ;IACdwM,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAACI,4BAA4B,CAAC;MACvCa,OAAO,EAAE,CAACb,4BAA4B;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMc,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACrG,IAAI,YAAAsG,0BAAApG,CAAA;MAAA,YAAAA,CAAA,IAAwFmG,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACL,IAAI,kBAlJ8E3O,EAAE,CAAA4O,gBAAA;MAAAvF,IAAA,EAkJS2F;IAAiB,EAAqF;EAAE;EACnN;IAAS,IAAI,CAACH,IAAI,kBAnJ8E7O,EAAE,CAAA8O,gBAAA;MAAAhB,OAAA,GAmJsCzL,WAAW,EAAEd,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC/L;AACA;EAAA,QAAAgM,SAAA,oBAAAA,SAAA,KArJoGvN,EAAE,CAAAwN,iBAAA,CAqJXwB,iBAAiB,EAAc,CAAC;IAC/G3F,IAAI,EAAEpI,QAAQ;IACdwM,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAACzL,WAAW,EAAEd,eAAe,CAAC;MACvCwN,OAAO,EAAE,CAAC1M,WAAW,EAAEd,eAAe;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASW,mCAAmC,EAAEP,4BAA4B,EAAEG,oCAAoC,EAAEmM,+BAA+B,EAAE5L,WAAW,EAAEE,iBAAiB,EAAEyM,iBAAiB,EAAEd,4BAA4B,EAAEjM,oBAAoB,EAAEwM,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}