/* Material Design Login Page Styles */

.login-content {
  --background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
}

.login-container {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* Logo Section */
.logo-section {
  text-align: center;
  margin-bottom: 32px;
  color: white;
  
  .logo-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px auto;
    border: 2px solid rgba(255, 255, 255, 0.3);
    
    .logo-icon {
      font-size: 40px;
      color: white;
    }
  }
  
  .app-title {
    font-size: 28px;
    font-weight: 300;
    margin: 0 0 8px 0;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  
  .app-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
  }
}

/* Login Card */
.login-card {
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  
  .mat-card-header {
    padding: 24px 24px 16px 24px;
    text-align: center;
    
    .login-title {
      font-size: 24px;
      font-weight: 500;
      color: #333;
      margin: 0;
    }
    
    .mat-card-subtitle {
      color: #666;
      font-size: 14px;
      margin-top: 4px;
    }
  }
  
  .mat-card-content {
    padding: 0 24px 16px 24px;
  }
  
  .mat-card-actions {
    padding: 0 24px 24px 24px;
    justify-content: center;
  }
}

/* Form Styles */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.mat-form-field {
  .mat-form-field-outline {
    border-radius: 8px;
  }
  
  &.mat-focused .mat-form-field-outline-thick {
    border-color: #1976d2;
  }
  
  .mat-form-field-label {
    color: #666;
  }
  
  .mat-input-element {
    color: #333;
  }
}

.login-button {
  height: 48px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 8px;
  
  &:disabled {
    background-color: #ccc;
    color: #999;
  }
  
  .spinning {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Login Actions */
.login-actions {
  .forgot-password {
    font-size: 14px;
    text-transform: none;
    letter-spacing: normal;
  }
}

/* Footer */
.login-footer {
  text-align: center;
  margin-top: 24px;
  color: white;
  
  .footer-text {
    font-size: 14px;
    margin: 0 0 8px 0;
    
    .register-link {
      font-size: 14px;
      text-transform: none;
      letter-spacing: normal;
      color: white;
      text-decoration: underline;
    }
  }
  
  .version-text {
    font-size: 12px;
    opacity: 0.7;
    margin: 0;
  }
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-content {
    padding: 16px;
  }
  
  .login-container {
    max-width: 100%;
  }
  
  .logo-section {
    margin-bottom: 24px;
    
    .logo-circle {
      width: 60px;
      height: 60px;
      
      .logo-icon {
        font-size: 30px;
      }
    }
    
    .app-title {
      font-size: 24px;
    }
    
    .app-subtitle {
      font-size: 14px;
    }
  }
  
  .login-card {
    border-radius: 12px;
    
    .mat-card-header,
    .mat-card-content,
    .mat-card-actions {
      padding-left: 16px;
      padding-right: 16px;
    }
    
    .login-title {
      font-size: 20px;
    }
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(30, 30, 30, 0.95);
    
    .login-title {
      color: #fff;
    }
    
    .mat-card-subtitle {
      color: #ccc;
    }
  }
  
  .mat-form-field {
    .mat-form-field-label {
      color: #ccc;
    }
    
    .mat-input-element {
      color: #fff;
    }
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
