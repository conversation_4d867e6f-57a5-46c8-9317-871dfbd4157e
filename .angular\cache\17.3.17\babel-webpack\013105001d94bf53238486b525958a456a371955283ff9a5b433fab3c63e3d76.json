{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/stepper\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/card\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/checkbox\";\nimport * as i14 from \"@angular/material/toolbar\";\nfunction RegisterPage_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Personal Information\");\n  }\n}\nfunction RegisterPage_mat_error_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.personalInfoForm, \"firstName\"), \" \");\n  }\n}\nfunction RegisterPage_mat_error_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.personalInfoForm, \"lastName\"), \" \");\n  }\n}\nfunction RegisterPage_mat_error_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.personalInfoForm, \"email\"), \" \");\n  }\n}\nfunction RegisterPage_mat_error_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.personalInfoForm, \"phone\"), \" \");\n  }\n}\nfunction RegisterPage_mat_error_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.personalInfoForm, \"company\"), \" \");\n  }\n}\nfunction RegisterPage_mat_error_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.personalInfoForm, \"address\"), \" \");\n  }\n}\nfunction RegisterPage_ng_template_73_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Complaint Details\");\n  }\n}\nfunction RegisterPage_mat_error_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.complaintForm, \"invoiceNumber\"), \" \");\n  }\n}\nfunction RegisterPage_mat_option_86_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r3.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.label, \" \");\n  }\n}\nfunction RegisterPage_mat_error_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.complaintForm, \"complaintType\"), \" \");\n  }\n}\nfunction RegisterPage_mat_error_111_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getErrorMessage(ctx_r1.complaintForm, \"description\"), \" \");\n  }\n}\nfunction RegisterPage_div_121_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"insert_drive_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 56);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_121_div_1_Template_button_click_5_listener() {\n      const i_r6 = i0.ɵɵrestoreView(_r5).index;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.removeFile(i_r6));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"close\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r7 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(file_r7.name);\n  }\n}\nfunction RegisterPage_div_121_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 53);\n    i0.ɵɵtemplate(1, RegisterPage_div_121_div_1_Template, 8, 1, \"div\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.selectedFiles);\n  }\n}\nfunction RegisterPage_ng_template_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \"Review & Submit\");\n  }\n}\nfunction RegisterPage_p_179_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\")(1, \"strong\");\n    i0.ɵɵtext(2, \"Attachments:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.selectedFiles.length, \" file(s)\");\n  }\n}\nfunction RegisterPage_mat_icon_186_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 57);\n    i0.ɵɵtext(1, \"refresh\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_187_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_188_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Submit Complaint\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_189_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Submitting...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterPage {\n  constructor(formBuilder, router, loadingController, toastController) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.isLoading = false;\n    this.selectedFiles = [];\n    this.selectedInvoice = null;\n    this.invoiceSearchResults = [];\n    this.showInvoiceResults = false;\n    this.complaintTypes = [{\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    }, {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    }, {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    }, {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    }, {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    }, {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }];\n    // Sample invoice data for demonstration\n    this.sampleInvoices = [{\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    }, {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    }, {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    }];\n    this.createForms();\n  }\n  ngOnInit() {}\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: ['', [Validators.required, Validators.minLength(3)]]\n    });\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      complaintDescription: ['', [Validators.required, Validators.minLength(10)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.personalInfoForm.valid && _this.complaintForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Registering complaint...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate registration process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          const toast = yield _this.toastController.create({\n            message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n            duration: 4000,\n            color: 'success',\n            position: 'top'\n          });\n          yield toast.present();\n          // Navigate to track page\n          _this.router.navigate(['/track']);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please fill in all required fields correctly.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files) {\n      this.selectedFiles = Array.from(files);\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n  }\n  getErrorMessage(form, field) {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      firstName: 'First Name',\n      lastName: 'Last Name',\n      email: 'Email',\n      phone: 'Phone',\n      company: 'Company',\n      address: 'Address',\n      invoiceNumber: 'Invoice Number',\n      complaintType: 'Complaint Type',\n      description: 'Description',\n      urgency: 'Urgency'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function RegisterPage_Factory(t) {\n      return new (t || RegisterPage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.LoadingController), i0.ɵɵdirectiveInject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterPage,\n      selectors: [[\"app-register\"]],\n      decls: 190,\n      vars: 34,\n      consts: [[\"stepper\", \"\"], [\"fileInput\", \"\"], [1, \"material-header\", 3, \"translucent\"], [1, \"material-toolbar\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Back\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [1, \"material-content\", 3, \"fullscreen\"], [1, \"register-container\"], [1, \"register-card\"], [1, \"register-title\"], [1, \"title-icon\"], [\"orientation\", \"vertical\", \"linear\", \"true\", 1, \"register-stepper\"], [\"label\", \"Personal Information\", \"state\", \"person\", 3, \"stepControl\"], [\"matStepLabel\", \"\"], [1, \"step-form\", 3, \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"firstName\", \"placeholder\", \"Enter first name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"lastName\", \"placeholder\", \"Enter last name\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Enter email address\", \"type\", \"email\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"placeholder\", \"Enter 10-digit phone number\", \"type\", \"tel\"], [\"matInput\", \"\", \"formControlName\", \"company\", \"placeholder\", \"Enter company name\"], [\"matInput\", \"\", \"formControlName\", \"address\", \"placeholder\", \"Enter complete address\", \"rows\", \"3\"], [1, \"step-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"matStepperNext\", \"\", 3, \"disabled\"], [\"label\", \"Complaint Details\", \"state\", \"assignment\", 3, \"stepControl\"], [\"matInput\", \"\", \"formControlName\", \"invoiceNumber\", \"placeholder\", \"Enter invoice number\"], [\"formControlName\", \"complaintType\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"urgency\"], [\"value\", \"low\"], [\"value\", \"medium\"], [\"value\", \"high\"], [\"value\", \"critical\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"placeholder\", \"Describe your complaint in detail\", \"rows\", \"4\"], [1, \"file-upload-section\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \"image/*,.pdf,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [\"class\", \"selected-files\", 4, \"ngIf\"], [1, \"terms-section\"], [\"formControlName\", \"agreedToTerms\", \"color\", \"primary\"], [\"href\", \"#\", 1, \"terms-link\"], [\"mat-button\", \"\", \"matStepperPrevious\", \"\"], [\"label\", \"Review & Submit\", \"state\", \"done\"], [1, \"review-section\"], [1, \"review-card\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"class\", \"spinning\", 4, \"ngIf\"], [3, \"value\"], [1, \"selected-files\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"spinning\"]],\n      template: function RegisterPage_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"ion-header\", 2)(1, \"mat-toolbar\", 3)(2, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_button_click_2_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.goBack());\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"span\", 5);\n          i0.ɵɵtext(6, \"Register Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"ion-content\", 7)(9, \"div\", 8)(10, \"mat-card\", 9)(11, \"mat-card-header\")(12, \"mat-card-title\", 10)(13, \"mat-icon\", 11);\n          i0.ɵɵtext(14, \"assignment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(15, \" New Complaint Registration \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"mat-card-subtitle\");\n          i0.ɵɵtext(17, \"Please fill in all the required information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"mat-card-content\")(19, \"mat-stepper\", 12, 0)(21, \"mat-step\", 13);\n          i0.ɵɵtemplate(22, RegisterPage_ng_template_22_Template, 1, 0, \"ng-template\", 14);\n          i0.ɵɵelementStart(23, \"form\", 15)(24, \"div\", 16)(25, \"mat-form-field\", 17)(26, \"mat-label\");\n          i0.ɵɵtext(27, \"First Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(28, \"input\", 18);\n          i0.ɵɵelementStart(29, \"mat-icon\", 19);\n          i0.ɵɵtext(30, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(31, RegisterPage_mat_error_31_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"mat-form-field\", 17)(33, \"mat-label\");\n          i0.ɵɵtext(34, \"Last Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(35, \"input\", 21);\n          i0.ɵɵelementStart(36, \"mat-icon\", 19);\n          i0.ɵɵtext(37, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(38, RegisterPage_mat_error_38_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(39, \"mat-form-field\", 22)(40, \"mat-label\");\n          i0.ɵɵtext(41, \"Email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(42, \"input\", 23);\n          i0.ɵɵelementStart(43, \"mat-icon\", 19);\n          i0.ɵɵtext(44, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(45, RegisterPage_mat_error_45_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"mat-form-field\", 22)(47, \"mat-label\");\n          i0.ɵɵtext(48, \"Phone Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(49, \"input\", 24);\n          i0.ɵɵelementStart(50, \"mat-icon\", 19);\n          i0.ɵɵtext(51, \"phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(52, RegisterPage_mat_error_52_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"mat-form-field\", 22)(54, \"mat-label\");\n          i0.ɵɵtext(55, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(56, \"input\", 25);\n          i0.ɵɵelementStart(57, \"mat-icon\", 19);\n          i0.ɵɵtext(58, \"business\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(59, RegisterPage_mat_error_59_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"mat-form-field\", 22)(61, \"mat-label\");\n          i0.ɵɵtext(62, \"Address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(63, \"textarea\", 26);\n          i0.ɵɵelementStart(64, \"mat-icon\", 19);\n          i0.ɵɵtext(65, \"location_on\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(66, RegisterPage_mat_error_66_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 27)(68, \"button\", 28);\n          i0.ɵɵtext(69, \" Next \");\n          i0.ɵɵelementStart(70, \"mat-icon\");\n          i0.ɵɵtext(71, \"arrow_forward\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(72, \"mat-step\", 29);\n          i0.ɵɵtemplate(73, RegisterPage_ng_template_73_Template, 1, 0, \"ng-template\", 14);\n          i0.ɵɵelementStart(74, \"form\", 15)(75, \"mat-form-field\", 22)(76, \"mat-label\");\n          i0.ɵɵtext(77, \"Invoice Number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(78, \"input\", 30);\n          i0.ɵɵelementStart(79, \"mat-icon\", 19);\n          i0.ɵɵtext(80, \"receipt\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(81, RegisterPage_mat_error_81_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"mat-form-field\", 22)(83, \"mat-label\");\n          i0.ɵɵtext(84, \"Complaint Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"mat-select\", 31);\n          i0.ɵɵtemplate(86, RegisterPage_mat_option_86_Template, 2, 2, \"mat-option\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(87, \"mat-icon\", 19);\n          i0.ɵɵtext(88, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(89, RegisterPage_mat_error_89_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"mat-form-field\", 22)(91, \"mat-label\");\n          i0.ɵɵtext(92, \"Urgency Level\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"mat-select\", 33)(94, \"mat-option\", 34);\n          i0.ɵɵtext(95, \"Low\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(96, \"mat-option\", 35);\n          i0.ɵɵtext(97, \"Medium\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"mat-option\", 36);\n          i0.ɵɵtext(99, \"High\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(100, \"mat-option\", 37);\n          i0.ɵɵtext(101, \"Critical\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(102, \"mat-icon\", 19);\n          i0.ɵɵtext(103, \"priority_high\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(104, \"mat-form-field\", 22)(105, \"mat-label\");\n          i0.ɵɵtext(106, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(107, \"textarea\", 38);\n          i0.ɵɵtext(108, \"                \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(109, \"mat-icon\", 19);\n          i0.ɵɵtext(110, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(111, RegisterPage_mat_error_111_Template, 2, 1, \"mat-error\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"div\", 39)(113, \"h4\");\n          i0.ɵɵtext(114, \"Attachments (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(115, \"input\", 40, 1);\n          i0.ɵɵlistener(\"change\", function RegisterPage_Template_input_change_115_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onFileSelected($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"button\", 41);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_button_click_117_listener() {\n            i0.ɵɵrestoreView(_r1);\n            const fileInput_r4 = i0.ɵɵreference(116);\n            return i0.ɵɵresetView(fileInput_r4.click());\n          });\n          i0.ɵɵelementStart(118, \"mat-icon\");\n          i0.ɵɵtext(119, \"attach_file\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(120, \" Choose Files \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(121, RegisterPage_div_121_Template, 2, 1, \"div\", 42);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"div\", 43)(123, \"mat-checkbox\", 44);\n          i0.ɵɵtext(124, \" I agree to the \");\n          i0.ɵɵelementStart(125, \"a\", 45);\n          i0.ɵɵtext(126, \"Terms and Conditions\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(127, \"div\", 27)(128, \"button\", 46)(129, \"mat-icon\");\n          i0.ɵɵtext(130, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(131, \" Back \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(132, \"button\", 28);\n          i0.ɵɵtext(133, \" Next \");\n          i0.ɵɵelementStart(134, \"mat-icon\");\n          i0.ɵɵtext(135, \"arrow_forward\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(136, \"mat-step\", 47);\n          i0.ɵɵtemplate(137, RegisterPage_ng_template_137_Template, 1, 0, \"ng-template\", 14);\n          i0.ɵɵelementStart(138, \"div\", 48)(139, \"h4\");\n          i0.ɵɵtext(140, \"Review Your Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(141, \"div\", 49)(142, \"h5\");\n          i0.ɵɵtext(143, \"Personal Information\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"p\")(145, \"strong\");\n          i0.ɵɵtext(146, \"Name:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(147);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(148, \"p\")(149, \"strong\");\n          i0.ɵɵtext(150, \"Email:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(151);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(152, \"p\")(153, \"strong\");\n          i0.ɵɵtext(154, \"Phone:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(155);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(156, \"p\")(157, \"strong\");\n          i0.ɵɵtext(158, \"Company:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(159);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(160, \"div\", 49)(161, \"h5\");\n          i0.ɵɵtext(162, \"Complaint Details\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(163, \"p\")(164, \"strong\");\n          i0.ɵɵtext(165, \"Invoice Number:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(166);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(167, \"p\")(168, \"strong\");\n          i0.ɵɵtext(169, \"Type:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(170);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(171, \"p\")(172, \"strong\");\n          i0.ɵɵtext(173, \"Urgency:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(174);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(175, \"p\")(176, \"strong\");\n          i0.ɵɵtext(177, \"Description:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(178);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(179, RegisterPage_p_179_Template, 4, 1, \"p\", 20);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(180, \"div\", 27)(181, \"button\", 46)(182, \"mat-icon\");\n          i0.ɵɵtext(183, \"arrow_back\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(184, \" Back \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(185, \"button\", 50);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_button_click_185_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSubmit());\n          });\n          i0.ɵɵtemplate(186, RegisterPage_mat_icon_186_Template, 2, 0, \"mat-icon\", 51)(187, RegisterPage_mat_icon_187_Template, 2, 0, \"mat-icon\", 20)(188, RegisterPage_span_188_Template, 2, 0, \"span\", 20)(189, RegisterPage_span_189_Template, 2, 0, \"span\", 20);\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_6_0;\n          let tmp_7_0;\n          let tmp_8_0;\n          let tmp_9_0;\n          let tmp_10_0;\n          let tmp_11_0;\n          let tmp_15_0;\n          let tmp_17_0;\n          let tmp_18_0;\n          let tmp_21_0;\n          let tmp_22_0;\n          let tmp_23_0;\n          let tmp_24_0;\n          let tmp_25_0;\n          let tmp_26_0;\n          let tmp_27_0;\n          let tmp_28_0;\n          i0.ɵɵproperty(\"translucent\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"fullscreen\", true);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"stepControl\", ctx.personalInfoForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.personalInfoForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx.personalInfoForm.get(\"firstName\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx.personalInfoForm.get(\"firstName\")) == null ? null : tmp_6_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.personalInfoForm.get(\"lastName\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.personalInfoForm.get(\"lastName\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx.personalInfoForm.get(\"email\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx.personalInfoForm.get(\"email\")) == null ? null : tmp_8_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx.personalInfoForm.get(\"phone\")) == null ? null : tmp_9_0.invalid) && ((tmp_9_0 = ctx.personalInfoForm.get(\"phone\")) == null ? null : tmp_9_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx.personalInfoForm.get(\"company\")) == null ? null : tmp_10_0.invalid) && ((tmp_10_0 = ctx.personalInfoForm.get(\"company\")) == null ? null : tmp_10_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx.personalInfoForm.get(\"address\")) == null ? null : tmp_11_0.invalid) && ((tmp_11_0 = ctx.personalInfoForm.get(\"address\")) == null ? null : tmp_11_0.touched));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.personalInfoForm.invalid);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"stepControl\", ctx.complaintForm);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.complaintForm);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx.complaintForm.get(\"invoiceNumber\")) == null ? null : tmp_15_0.invalid) && ((tmp_15_0 = ctx.complaintForm.get(\"invoiceNumber\")) == null ? null : tmp_15_0.touched));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.complaintTypes);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx.complaintForm.get(\"complaintType\")) == null ? null : tmp_17_0.invalid) && ((tmp_17_0 = ctx.complaintForm.get(\"complaintType\")) == null ? null : tmp_17_0.touched));\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_18_0 = ctx.complaintForm.get(\"description\")) == null ? null : tmp_18_0.invalid) && ((tmp_18_0 = ctx.complaintForm.get(\"description\")) == null ? null : tmp_18_0.touched));\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFiles.length > 0);\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"disabled\", ctx.complaintForm.invalid);\n          i0.ɵɵadvance(15);\n          i0.ɵɵtextInterpolate2(\" \", (tmp_21_0 = ctx.personalInfoForm.get(\"firstName\")) == null ? null : tmp_21_0.value, \" \", (tmp_21_0 = ctx.personalInfoForm.get(\"lastName\")) == null ? null : tmp_21_0.value, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_22_0 = ctx.personalInfoForm.get(\"email\")) == null ? null : tmp_22_0.value, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_23_0 = ctx.personalInfoForm.get(\"phone\")) == null ? null : tmp_23_0.value, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_24_0 = ctx.personalInfoForm.get(\"company\")) == null ? null : tmp_24_0.value, \"\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_25_0 = ctx.complaintForm.get(\"invoiceNumber\")) == null ? null : tmp_25_0.value, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_26_0 = ctx.complaintForm.get(\"complaintType\")) == null ? null : tmp_26_0.value, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_27_0 = ctx.complaintForm.get(\"urgency\")) == null ? null : tmp_27_0.value, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" \", (tmp_28_0 = ctx.complaintForm.get(\"description\")) == null ? null : tmp_28_0.value, \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.selectedFiles.length > 0);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.personalInfoForm.invalid || ctx.complaintForm.invalid);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.IonContent, i3.IonHeader, i5.MatStep, i5.MatStepLabel, i5.MatStepper, i5.MatStepperNext, i5.MatStepperPrevious, i6.MatFormField, i6.MatLabel, i6.MatError, i6.MatSuffix, i7.MatInput, i8.MatButton, i8.MatIconButton, i9.MatCard, i9.MatCardContent, i9.MatCardHeader, i9.MatCardSubtitle, i9.MatCardTitle, i10.MatIcon, i11.MatSelect, i12.MatOption, i13.MatCheckbox, i14.MatToolbar],\n      styles: [\"\\n\\n.material-header[_ngcontent-%COMP%] {\\n  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);\\n}\\n\\n.material-toolbar[_ngcontent-%COMP%] {\\n  height: 64px;\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\\n  color: white;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin-left: 16px;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.material-content[_ngcontent-%COMP%] {\\n  --background: #f5f5f5;\\n  padding: 20px;\\n}\\n\\n.register-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.register-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  overflow: hidden;\\n}\\n.register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 24px;\\n}\\n.register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .register-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 24px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n}\\n.register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .register-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 28px;\\n  color: #1976d2;\\n}\\n.register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin-top: 8px;\\n}\\n.register-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n\\n\\n.register-stepper[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.register-stepper[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%]   .mat-step-icon[_ngcontent-%COMP%] {\\n  background-color: #1976d2;\\n  color: white;\\n}\\n.register-stepper[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%]   .mat-step-icon-selected[_ngcontent-%COMP%] {\\n  background-color: #1976d2;\\n}\\n.register-stepper[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%]   .mat-step-icon-state-done[_ngcontent-%COMP%] {\\n  background-color: #4caf50;\\n}\\n.register-stepper[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%]   .mat-step-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n.register-stepper[_ngcontent-%COMP%]   .mat-step-content[_ngcontent-%COMP%] {\\n  padding: 0 24px 24px 24px;\\n}\\n\\n\\n\\n.step-form[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n@media (max-width: 768px) {\\n  .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n.mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.mat-form-field[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n.mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  border-color: #1976d2;\\n}\\n.mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.mat-form-field[_ngcontent-%COMP%]   .mat-input-element[_ngcontent-%COMP%], .mat-form-field[_ngcontent-%COMP%]   .mat-select-value[_ngcontent-%COMP%] {\\n  color: #333;\\n}\\n\\n\\n\\n.step-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 24px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.step-actions[_ngcontent-%COMP%]   button.submit-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n}\\n.step-actions[_ngcontent-%COMP%]   button.submit-button[_ngcontent-%COMP%]   .spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n.file-upload-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.file-upload-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0 0 16px 0;\\n}\\n.file-upload-section[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  border: 2px dashed #1976d2;\\n  padding: 12px 24px;\\n}\\n.file-upload-section[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(25, 118, 210, 0.04);\\n}\\n.file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n.file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  padding: 8px 12px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n}\\n.file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%]:first-child {\\n  margin-right: 8px;\\n  color: #666;\\n}\\n.file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  font-size: 14px;\\n  color: #333;\\n}\\n.file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n.terms-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n.terms-section[_ngcontent-%COMP%]   .mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n}\\n.terms-section[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  text-decoration: none;\\n}\\n.terms-section[_ngcontent-%COMP%]   .terms-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n.review-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0 0 24px 0;\\n}\\n.review-section[_ngcontent-%COMP%]   .review-card[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 16px;\\n}\\n.review-section[_ngcontent-%COMP%]   .review-card[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #1976d2;\\n  margin: 0 0 12px 0;\\n}\\n.review-section[_ngcontent-%COMP%]   .review-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 14px;\\n  color: #333;\\n}\\n.review-section[_ngcontent-%COMP%]   .review-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-weight: 500;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .material-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .register-card[_ngcontent-%COMP%] {\\n    border-radius: 8px;\\n  }\\n  .register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .register-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .register-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .register-stepper[_ngcontent-%COMP%]   .mat-step-header[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-stepper[_ngcontent-%COMP%]   .mat-step-content[_ngcontent-%COMP%] {\\n    padding: 0 16px 16px 16px;\\n  }\\n  .step-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n  .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .register-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .register-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .register-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n    margin-bottom: 8px;\\n  }\\n}\\n\\n\\n.fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.slide-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideUp 0.4s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    transform: translateY(30px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵtext", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "getErrorMessage", "personalInfoForm", "complaintForm", "ɵɵproperty", "type_r3", "value", "label", "ɵɵlistener", "RegisterPage_div_121_div_1_Template_button_click_5_listener", "i_r6", "ɵɵrestoreView", "_r5", "index", "ɵɵnextContext", "ɵɵresetView", "removeFile", "ɵɵtextInterpolate", "file_r7", "name", "ɵɵtemplate", "RegisterPage_div_121_div_1_Template", "selectedFiles", "length", "RegisterPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "isLoading", "selectedInvoice", "invoiceSearchResults", "showInvoiceResults", "complaintTypes", "icon", "description", "sampleInvoices", "invoiceNumber", "invoiceDate", "Date", "customerName", "customerAddress", "zone", "operatingUnit", "organization", "billToLocation", "shipToLocation", "createForms", "ngOnInit", "complaintTypeForm", "group", "selectedType", "required", "invoiceSearchForm", "searchTerm", "<PERSON><PERSON><PERSON><PERSON>", "complaintDetailsForm", "contactPersonName", "contactNumber", "pattern", "complaintDescription", "comments", "hasComplaintLetters", "attachedFile", "onSubmit", "_this", "_asyncToGenerator", "valid", "loading", "create", "message", "duration", "present", "setTimeout", "dismiss", "toast", "now", "color", "position", "navigate", "onFileSelected", "event", "files", "target", "Array", "from", "splice", "form", "field", "control", "get", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "firstName", "lastName", "email", "phone", "company", "address", "complaintType", "urgency", "goBack", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "LoadingController", "ToastController", "selectors", "decls", "vars", "consts", "template", "RegisterPage_Template", "rf", "ctx", "RegisterPage_Template_button_click_2_listener", "_r1", "ɵɵelement", "RegisterPage_ng_template_22_Template", "RegisterPage_mat_error_31_Template", "RegisterPage_mat_error_38_Template", "RegisterPage_mat_error_45_Template", "RegisterPage_mat_error_52_Template", "RegisterPage_mat_error_59_Template", "RegisterPage_mat_error_66_Template", "RegisterPage_ng_template_73_Template", "RegisterPage_mat_error_81_Template", "RegisterPage_mat_option_86_Template", "RegisterPage_mat_error_89_Template", "RegisterPage_mat_error_111_Template", "RegisterPage_Template_input_change_115_listener", "$event", "RegisterPage_Template_button_click_117_listener", "fileInput_r4", "ɵɵreference", "click", "RegisterPage_div_121_Template", "RegisterPage_ng_template_137_Template", "RegisterPage_p_179_Template", "RegisterPage_Template_button_click_185_listener", "RegisterPage_mat_icon_186_Template", "RegisterPage_mat_icon_187_Template", "RegisterPage_span_188_Template", "RegisterPage_span_189_Template", "tmp_6_0", "invalid", "touched", "tmp_7_0", "tmp_8_0", "tmp_9_0", "tmp_10_0", "tmp_11_0", "tmp_15_0", "tmp_17_0", "tmp_18_0", "ɵɵtextInterpolate2", "tmp_21_0", "tmp_22_0", "tmp_23_0", "tmp_24_0", "tmp_25_0", "tmp_26_0", "tmp_27_0", "tmp_28_0"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\n\nexport interface InvoiceData {\n  invoiceNumber: string;\n  invoiceDate: Date;\n  customerName: string;\n  customerAddress: string;\n  zone: string;\n  operatingUnit: string;\n  organization: string;\n  billToLocation: string;\n  shipToLocation: string;\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss'],\n})\nexport class RegisterPage implements OnInit {\n\n  complaintTypeForm!: FormGroup;\n  invoiceSearchForm!: FormGroup;\n  complaintDetailsForm!: FormGroup;\n  isLoading = false;\n  selectedFiles: File[] = [];\n  selectedInvoice: InvoiceData | null = null;\n  invoiceSearchResults: InvoiceData[] = [];\n  showInvoiceResults = false;\n\n  complaintTypes = [\n    {\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    },\n    {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    },\n    {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    },\n    {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    },\n    {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    },\n    {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }\n  ];\n\n  // Sample invoice data for demonstration\n  sampleInvoices: InvoiceData[] = [\n    {\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    },\n    {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    },\n    {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController\n  ) {\n    this.createForms();\n  }\n\n  ngOnInit() {}\n\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: ['', [Validators.required, Validators.minLength(3)]]\n    });\n\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      complaintDescription: ['', [Validators.required, Validators.minLength(10)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n\n  async onSubmit() {\n    if (this.personalInfoForm.valid && this.complaintForm.valid) {\n      this.isLoading = true;\n      \n      const loading = await this.loadingController.create({\n        message: 'Registering complaint...',\n        duration: 3000\n      });\n      \n      await loading.present();\n      \n      // Simulate registration process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n        \n        const toast = await this.toastController.create({\n          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n          duration: 4000,\n          color: 'success',\n          position: 'top'\n        });\n        await toast.present();\n        \n        // Navigate to track page\n        this.router.navigate(['/track']);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please fill in all required fields correctly.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files) {\n      this.selectedFiles = Array.from(files);\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n  }\n\n  getErrorMessage(form: FormGroup, field: string): string {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      firstName: 'First Name',\n      lastName: 'Last Name',\n      email: 'Email',\n      phone: 'Phone',\n      company: 'Company',\n      address: 'Address',\n      invoiceNumber: 'Invoice Number',\n      complaintType: 'Complaint Type',\n      description: 'Description',\n      urgency: 'Urgency'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n}\n", "<ion-header [translucent]=\"true\" class=\"material-header\">\n  <mat-toolbar class=\"material-toolbar\">\n    <button mat-icon-button (click)=\"goBack()\" aria-label=\"Back\">\n      <mat-icon>arrow_back</mat-icon>\n    </button>\n    <span class=\"toolbar-title\">Register Complaint</span>\n    <span class=\"spacer\"></span>\n  </mat-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\" class=\"material-content\">\n  <div class=\"register-container\">\n    <mat-card class=\"register-card\">\n      <mat-card-header>\n        <mat-card-title class=\"register-title\">\n          <mat-icon class=\"title-icon\">assignment</mat-icon>\n          New Complaint Registration\n        </mat-card-title>\n        <mat-card-subtitle>Please fill in all the required information</mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <mat-stepper #stepper orientation=\"vertical\" linear=\"true\" class=\"register-stepper\">\n          \n          <!-- Step 1: Personal Information -->\n          <mat-step [stepControl]=\"personalInfoForm\" label=\"Personal Information\" state=\"person\">\n            <ng-template matStepLabel>Personal Information</ng-template>\n            \n            <form [formGroup]=\"personalInfoForm\" class=\"step-form\">\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>First Name</mat-label>\n                  <input matInput formControlName=\"firstName\" placeholder=\"Enter first name\">\n                  <mat-icon matSuffix>person</mat-icon>\n                  <mat-error *ngIf=\"personalInfoForm.get('firstName')?.invalid && personalInfoForm.get('firstName')?.touched\">\n                    {{ getErrorMessage(personalInfoForm, 'firstName') }}\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Last Name</mat-label>\n                  <input matInput formControlName=\"lastName\" placeholder=\"Enter last name\">\n                  <mat-icon matSuffix>person</mat-icon>\n                  <mat-error *ngIf=\"personalInfoForm.get('lastName')?.invalid && personalInfoForm.get('lastName')?.touched\">\n                    {{ getErrorMessage(personalInfoForm, 'lastName') }}\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Email</mat-label>\n                <input matInput formControlName=\"email\" placeholder=\"Enter email address\" type=\"email\">\n                <mat-icon matSuffix>email</mat-icon>\n                <mat-error *ngIf=\"personalInfoForm.get('email')?.invalid && personalInfoForm.get('email')?.touched\">\n                  {{ getErrorMessage(personalInfoForm, 'email') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Phone Number</mat-label>\n                <input matInput formControlName=\"phone\" placeholder=\"Enter 10-digit phone number\" type=\"tel\">\n                <mat-icon matSuffix>phone</mat-icon>\n                <mat-error *ngIf=\"personalInfoForm.get('phone')?.invalid && personalInfoForm.get('phone')?.touched\">\n                  {{ getErrorMessage(personalInfoForm, 'phone') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Company</mat-label>\n                <input matInput formControlName=\"company\" placeholder=\"Enter company name\">\n                <mat-icon matSuffix>business</mat-icon>\n                <mat-error *ngIf=\"personalInfoForm.get('company')?.invalid && personalInfoForm.get('company')?.touched\">\n                  {{ getErrorMessage(personalInfoForm, 'company') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Address</mat-label>\n                <textarea matInput formControlName=\"address\" placeholder=\"Enter complete address\" rows=\"3\"></textarea>\n                <mat-icon matSuffix>location_on</mat-icon>\n                <mat-error *ngIf=\"personalInfoForm.get('address')?.invalid && personalInfoForm.get('address')?.touched\">\n                  {{ getErrorMessage(personalInfoForm, 'address') }}\n                </mat-error>\n              </mat-form-field>\n            </form>\n\n            <div class=\"step-actions\">\n              <button mat-raised-button color=\"primary\" matStepperNext [disabled]=\"personalInfoForm.invalid\">\n                Next\n                <mat-icon>arrow_forward</mat-icon>\n              </button>\n            </div>\n          </mat-step>\n\n          <!-- Step 2: Complaint Details -->\n          <mat-step [stepControl]=\"complaintForm\" label=\"Complaint Details\" state=\"assignment\">\n            <ng-template matStepLabel>Complaint Details</ng-template>\n            \n            <form [formGroup]=\"complaintForm\" class=\"step-form\">\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Invoice Number</mat-label>\n                <input matInput formControlName=\"invoiceNumber\" placeholder=\"Enter invoice number\">\n                <mat-icon matSuffix>receipt</mat-icon>\n                <mat-error *ngIf=\"complaintForm.get('invoiceNumber')?.invalid && complaintForm.get('invoiceNumber')?.touched\">\n                  {{ getErrorMessage(complaintForm, 'invoiceNumber') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Complaint Type</mat-label>\n                <mat-select formControlName=\"complaintType\">\n                  <mat-option *ngFor=\"let type of complaintTypes\" [value]=\"type.value\">\n                    {{ type.label }}\n                  </mat-option>\n                </mat-select>\n                <mat-icon matSuffix>category</mat-icon>\n                <mat-error *ngIf=\"complaintForm.get('complaintType')?.invalid && complaintForm.get('complaintType')?.touched\">\n                  {{ getErrorMessage(complaintForm, 'complaintType') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Urgency Level</mat-label>\n                <mat-select formControlName=\"urgency\">\n                  <mat-option value=\"low\">Low</mat-option>\n                  <mat-option value=\"medium\">Medium</mat-option>\n                  <mat-option value=\"high\">High</mat-option>\n                  <mat-option value=\"critical\">Critical</mat-option>\n                </mat-select>\n                <mat-icon matSuffix>priority_high</mat-icon>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Description</mat-label>\n                <textarea \n                  matInput \n                  formControlName=\"description\" \n                  placeholder=\"Describe your complaint in detail\" \n                  rows=\"4\">\n                </textarea>\n                <mat-icon matSuffix>description</mat-icon>\n                <mat-error *ngIf=\"complaintForm.get('description')?.invalid && complaintForm.get('description')?.touched\">\n                  {{ getErrorMessage(complaintForm, 'description') }}\n                </mat-error>\n              </mat-form-field>\n\n              <!-- File Upload -->\n              <div class=\"file-upload-section\">\n                <h4>Attachments (Optional)</h4>\n                <input \n                  type=\"file\" \n                  #fileInput \n                  (change)=\"onFileSelected($event)\" \n                  multiple \n                  accept=\"image/*,.pdf,.doc,.docx\"\n                  style=\"display: none;\">\n                \n                <button \n                  mat-stroked-button \n                  color=\"primary\" \n                  (click)=\"fileInput.click()\"\n                  class=\"upload-button\">\n                  <mat-icon>attach_file</mat-icon>\n                  Choose Files\n                </button>\n\n                <div *ngIf=\"selectedFiles.length > 0\" class=\"selected-files\">\n                  <div *ngFor=\"let file of selectedFiles; let i = index\" class=\"file-item\">\n                    <mat-icon>insert_drive_file</mat-icon>\n                    <span>{{ file.name }}</span>\n                    <button mat-icon-button (click)=\"removeFile(i)\" color=\"warn\">\n                      <mat-icon>close</mat-icon>\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              <!-- Terms and Conditions -->\n              <div class=\"terms-section\">\n                <mat-checkbox formControlName=\"agreedToTerms\" color=\"primary\">\n                  I agree to the <a href=\"#\" class=\"terms-link\">Terms and Conditions</a>\n                </mat-checkbox>\n              </div>\n            </form>\n\n            <div class=\"step-actions\">\n              <button mat-button matStepperPrevious>\n                <mat-icon>arrow_back</mat-icon>\n                Back\n              </button>\n              <button mat-raised-button color=\"primary\" matStepperNext [disabled]=\"complaintForm.invalid\">\n                Next\n                <mat-icon>arrow_forward</mat-icon>\n              </button>\n            </div>\n          </mat-step>\n\n          <!-- Step 3: Review and Submit -->\n          <mat-step label=\"Review & Submit\" state=\"done\">\n            <ng-template matStepLabel>Review & Submit</ng-template>\n            \n            <div class=\"review-section\">\n              <h4>Review Your Information</h4>\n              \n              <div class=\"review-card\">\n                <h5>Personal Information</h5>\n                <p><strong>Name:</strong> {{ personalInfoForm.get('firstName')?.value }} {{ personalInfoForm.get('lastName')?.value }}</p>\n                <p><strong>Email:</strong> {{ personalInfoForm.get('email')?.value }}</p>\n                <p><strong>Phone:</strong> {{ personalInfoForm.get('phone')?.value }}</p>\n                <p><strong>Company:</strong> {{ personalInfoForm.get('company')?.value }}</p>\n              </div>\n\n              <div class=\"review-card\">\n                <h5>Complaint Details</h5>\n                <p><strong>Invoice Number:</strong> {{ complaintForm.get('invoiceNumber')?.value }}</p>\n                <p><strong>Type:</strong> {{ complaintForm.get('complaintType')?.value }}</p>\n                <p><strong>Urgency:</strong> {{ complaintForm.get('urgency')?.value }}</p>\n                <p><strong>Description:</strong> {{ complaintForm.get('description')?.value }}</p>\n                <p *ngIf=\"selectedFiles.length > 0\"><strong>Attachments:</strong> {{ selectedFiles.length }} file(s)</p>\n              </div>\n            </div>\n\n            <div class=\"step-actions\">\n              <button mat-button matStepperPrevious>\n                <mat-icon>arrow_back</mat-icon>\n                Back\n              </button>\n              <button \n                mat-raised-button \n                color=\"primary\" \n                (click)=\"onSubmit()\"\n                [disabled]=\"isLoading || personalInfoForm.invalid || complaintForm.invalid\"\n                class=\"submit-button\">\n                <mat-icon *ngIf=\"isLoading\" class=\"spinning\">refresh</mat-icon>\n                <mat-icon *ngIf=\"!isLoading\">send</mat-icon>\n                <span *ngIf=\"!isLoading\">Submit Complaint</span>\n                <span *ngIf=\"isLoading\">Submitting...</span>\n              </button>\n            </div>\n          </mat-step>\n        </mat-stepper>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</ion-content>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;ICyB7BC,EAAA,CAAAC,MAAA,2BAAoB;;;;;IAQxCD,EAAA,CAAAE,cAAA,gBAA4G;IAC1GF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,gBAAA,oBACF;;;;;IAOAR,EAAA,CAAAE,cAAA,gBAA0G;IACxGF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,gBAAA,mBACF;;;;;IAQFR,EAAA,CAAAE,cAAA,gBAAoG;IAClGF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,gBAAA,gBACF;;;;;IAOAR,EAAA,CAAAE,cAAA,gBAAoG;IAClGF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,gBAAA,gBACF;;;;;IAOAR,EAAA,CAAAE,cAAA,gBAAwG;IACtGF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,gBAAA,kBACF;;;;;IAOAR,EAAA,CAAAE,cAAA,gBAAwG;IACtGF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAE,gBAAA,kBACF;;;;;IAcsBR,EAAA,CAAAC,MAAA,wBAAiB;;;;;IAOvCD,EAAA,CAAAE,cAAA,gBAA8G;IAC5GF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAG,aAAA,wBACF;;;;;IAMET,EAAA,CAAAE,cAAA,qBAAqE;IACnEF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAa;;;;IAFmCH,EAAA,CAAAU,UAAA,UAAAC,OAAA,CAAAC,KAAA,CAAoB;IAClEZ,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAM,OAAA,CAAAE,KAAA,MACF;;;;;IAGFb,EAAA,CAAAE,cAAA,gBAA8G;IAC5GF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAG,aAAA,wBACF;;;;;IAuBAT,EAAA,CAAAE,cAAA,gBAA0G;IACxGF,EAAA,CAAAC,MAAA,GACF;IAAAD,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,CAAAD,MAAA,CAAAG,aAAA,sBACF;;;;;;IAyBIT,EADF,CAAAE,cAAA,cAAyE,eAC7D;IAAAF,EAAA,CAAAC,MAAA,wBAAiB;IAAAD,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAC,MAAA,GAAe;IAAAD,EAAA,CAAAG,YAAA,EAAO;IAC5BH,EAAA,CAAAE,cAAA,iBAA6D;IAArCF,EAAA,CAAAc,UAAA,mBAAAC,4DAAA;MAAA,MAAAC,IAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA,EAAAC,KAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAgB,UAAA,CAAAN,IAAA,CAAa;IAAA,EAAC;IAC7ChB,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAC,MAAA,YAAK;IAEnBD,EAFmB,CAAAG,YAAA,EAAW,EACnB,EACL;;;;IAJEH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAuB,iBAAA,CAAAC,OAAA,CAAAC,IAAA,CAAe;;;;;IAHzBzB,EAAA,CAAAE,cAAA,cAA6D;IAC3DF,EAAA,CAAA0B,UAAA,IAAAC,mCAAA,kBAAyE;IAO3E3B,EAAA,CAAAG,YAAA,EAAM;;;;IAPkBH,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAU,UAAA,YAAAJ,MAAA,CAAAsB,aAAA,CAAkB;;;;;IAgCpB5B,EAAA,CAAAC,MAAA,sBAAe;;;;;IAmBDD,EAApC,CAAAE,cAAA,QAAoC,aAAQ;IAAAF,EAAA,CAAAC,MAAA,mBAAY;IAAAD,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAC,MAAA,GAAkC;IAAAD,EAAA,CAAAG,YAAA,EAAI;;;;IAAtCH,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAsB,aAAA,CAAAC,MAAA,aAAkC;;;;;IAepG7B,EAAA,CAAAE,cAAA,mBAA6C;IAAAF,EAAA,CAAAC,MAAA,cAAO;IAAAD,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/DH,EAAA,CAAAE,cAAA,eAA6B;IAAAF,EAAA,CAAAC,MAAA,WAAI;IAAAD,EAAA,CAAAG,YAAA,EAAW;;;;;IAC5CH,EAAA,CAAAE,cAAA,WAAyB;IAAAF,EAAA,CAAAC,MAAA,uBAAgB;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;;;IAChDH,EAAA,CAAAE,cAAA,WAAwB;IAAAF,EAAA,CAAAC,MAAA,oBAAa;IAAAD,EAAA,CAAAG,YAAA,EAAO;;;ADtN5D,OAAM,MAAO2B,YAAY;EAuFvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IAtFzB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAR,aAAa,GAAW,EAAE;IAC1B,KAAAS,eAAe,GAAuB,IAAI;IAC1C,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,kBAAkB,GAAG,KAAK;IAE1B,KAAAC,cAAc,GAAG,CACf;MACE5B,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,sBAAsB;MAC7B4B,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE;KACd,EACD;MACE9B,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,uBAAuB;MAC9B4B,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE;KACd,EACD;MACE9B,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,2BAA2B;MAClC4B,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE;KACd,EACD;MACE9B,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,oBAAoB;MAC3B4B,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE;KACd,EACD;MACE9B,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,iBAAiB;MACxB4B,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE;KACd,EACD;MACE9B,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,yBAAyB;MAChC4B,IAAI,EAAE,uBAAuB;MAC7BC,WAAW,EAAE;KACd,CACF;IAED;IACA,KAAAC,cAAc,GAAkB,CAC9B;MACEC,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,uBAAuB;MACrCC,eAAe,EAAE,yDAAyD;MAC1EC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,uBAAuB;MACvCC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,wBAAwB;MACtCC,eAAe,EAAE,mDAAmD;MACpEC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,uBAAuB;MACtCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,8BAA8B;MAC9CC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,wBAAwB;MACtCC,eAAe,EAAE,oDAAoD;MACrEC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE;KACjB,CACF;IAQC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA,GAAI;EAEZD,WAAWA,CAAA;IACT,IAAI,CAACE,iBAAiB,GAAG,IAAI,CAACxB,WAAW,CAACyB,KAAK,CAAC;MAC9CC,YAAY,EAAE,CAAC,EAAE,EAAE3D,UAAU,CAAC4D,QAAQ;KACvC,CAAC;IAEF,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAAC5B,WAAW,CAACyB,KAAK,CAAC;MAC9CI,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC9D,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAAC+D,SAAS,CAAC,CAAC,CAAC,CAAC;KAChE,CAAC;IAEF,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAAC/B,WAAW,CAACyB,KAAK,CAAC;MACjDO,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACjE,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAAC+D,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEG,aAAa,EAAE,CAAC,EAAE,EAAE,CAAClE,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAACmE,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAC7EC,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAACpE,UAAU,CAAC4D,QAAQ,EAAE5D,UAAU,CAAC+D,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3EM,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,YAAY,EAAE,CAAC,IAAI;KACpB,CAAC;EACJ;EAEMC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAID,KAAI,CAAChE,gBAAgB,CAACkE,KAAK,IAAIF,KAAI,CAAC/D,aAAa,CAACiE,KAAK,EAAE;QAC3DF,KAAI,CAACpC,SAAS,GAAG,IAAI;QAErB,MAAMuC,OAAO,SAASH,KAAI,CAACtC,iBAAiB,CAAC0C,MAAM,CAAC;UAClDC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB;QACAC,UAAU,cAAAP,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAACpC,SAAS,GAAG,KAAK;UACtB,MAAMuC,OAAO,CAACM,OAAO,EAAE;UAEvB,MAAMC,KAAK,SAASV,KAAI,CAACrC,eAAe,CAACyC,MAAM,CAAC;YAC9CC,OAAO,EAAE,uDAAuD,GAAG/B,IAAI,CAACqC,GAAG,EAAE;YAC7EL,QAAQ,EAAE,IAAI;YACdM,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,MAAMH,KAAK,CAACH,OAAO,EAAE;UAErB;UACAP,KAAI,CAACvC,MAAM,CAACqD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMJ,KAAK,SAASV,KAAI,CAACrC,eAAe,CAACyC,MAAM,CAAC;UAC9CC,OAAO,EAAE,+CAA+C;UACxDC,QAAQ,EAAE,IAAI;UACdM,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;SACX,CAAC;QACF,MAAMH,KAAK,CAACH,OAAO,EAAE;;IACtB;EACH;EAEAQ,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACT,IAAI,CAAC7D,aAAa,GAAG+D,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;;EAE1C;EAEAnE,UAAUA,CAACH,KAAa;IACtB,IAAI,CAACS,aAAa,CAACiE,MAAM,CAAC1E,KAAK,EAAE,CAAC,CAAC;EACrC;EAEAZ,eAAeA,CAACuF,IAAe,EAAEC,KAAa;IAC5C,MAAMC,OAAO,GAAGF,IAAI,CAACG,GAAG,CAACF,KAAK,CAAC;IAC/B,IAAIC,OAAO,EAAEE,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACJ,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,oCAAoC;;IAE7C,IAAIF,OAAO,EAAEE,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMpC,SAAS,GAAGkC,OAAO,CAACI,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACJ,KAAK,CAAC,qBAAqBjC,SAAS,aAAa;;IAEhF,IAAIkC,OAAO,EAAEE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACJ,KAAa;IACjC,MAAMO,MAAM,GAA8B;MACxCC,SAAS,EAAE,YAAY;MACvBC,QAAQ,EAAE,WAAW;MACrBC,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE,SAAS;MAClBhE,aAAa,EAAE,gBAAgB;MAC/BiE,aAAa,EAAE,gBAAgB;MAC/BnE,WAAW,EAAE,aAAa;MAC1BoE,OAAO,EAAE;KACV;IACD,OAAOR,MAAM,CAACP,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAgB,MAAMA,CAAA;IACJ,IAAI,CAAC9E,MAAM,CAACqD,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBA1MWxD,YAAY,EAAA9B,EAAA,CAAAgH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlH,EAAA,CAAAgH,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApH,EAAA,CAAAgH,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAtH,EAAA,CAAAgH,iBAAA,CAAAK,EAAA,CAAAE,eAAA;IAAA;EAAA;;;YAAZzF,YAAY;MAAA0F,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCpBrB9H,EAFJ,CAAAE,cAAA,oBAAyD,qBACjB,gBACyB;UAArCF,EAAA,CAAAc,UAAA,mBAAAkH,8CAAA;YAAAhI,EAAA,CAAAiB,aAAA,CAAAgH,GAAA;YAAA,OAAAjI,EAAA,CAAAqB,WAAA,CAAS0G,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UACxC/G,EAAA,CAAAE,cAAA,eAAU;UAAAF,EAAA,CAAAC,MAAA,iBAAU;UACtBD,EADsB,CAAAG,YAAA,EAAW,EACxB;UACTH,EAAA,CAAAE,cAAA,cAA4B;UAAAF,EAAA,CAAAC,MAAA,yBAAkB;UAAAD,EAAA,CAAAG,YAAA,EAAO;UACrDH,EAAA,CAAAkI,SAAA,cAA4B;UAEhClI,EADE,CAAAG,YAAA,EAAc,EACH;UAOHH,EALV,CAAAE,cAAA,qBAA0D,aACxB,mBACE,uBACb,0BACwB,oBACR;UAAAF,EAAA,CAAAC,MAAA,kBAAU;UAAAD,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,MAAA,oCACF;UAAAD,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAE,cAAA,yBAAmB;UAAAF,EAAA,CAAAC,MAAA,mDAA2C;UAChED,EADgE,CAAAG,YAAA,EAAoB,EAClE;UAMdH,EAJJ,CAAAE,cAAA,wBAAkB,0BACoE,oBAGK;UACrFF,EAAA,CAAA0B,UAAA,KAAAyG,oCAAA,0BAA0B;UAKpBnI,EAHN,CAAAE,cAAA,gBAAuD,eAC/B,0BACoC,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,kBAAU;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAkI,SAAA,iBAA2E;UAC3ElI,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA0B,UAAA,KAAA0G,kCAAA,wBAA4G;UAG9GpI,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAE,cAAA,0BAAwD,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,iBAAS;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAkI,SAAA,iBAAyE;UACzElI,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA0B,UAAA,KAAA2G,kCAAA,wBAA0G;UAI9GrI,EADE,CAAAG,YAAA,EAAiB,EACb;UAGJH,EADF,CAAAE,cAAA,0BAAwD,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAkI,SAAA,iBAAuF;UACvFlI,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA0B,UAAA,KAAA4G,kCAAA,wBAAoG;UAGtGtI,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAE,cAAA,0BAAwD,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,oBAAY;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAkI,SAAA,iBAA6F;UAC7FlI,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA0B,UAAA,KAAA6G,kCAAA,wBAAoG;UAGtGvI,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAE,cAAA,0BAAwD,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAkI,SAAA,iBAA2E;UAC3ElI,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,gBAAQ;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACvCH,EAAA,CAAA0B,UAAA,KAAA8G,kCAAA,wBAAwG;UAG1GxI,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAE,cAAA,0BAAwD,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAC9BH,EAAA,CAAAkI,SAAA,oBAAsG;UACtGlI,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,mBAAW;UAAAD,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA0B,UAAA,KAAA+G,kCAAA,wBAAwG;UAI5GzI,EADE,CAAAG,YAAA,EAAiB,EACZ;UAGLH,EADF,CAAAE,cAAA,eAA0B,kBACuE;UAC7FF,EAAA,CAAAC,MAAA,cACA;UAAAD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAC,MAAA,qBAAa;UAG7BD,EAH6B,CAAAG,YAAA,EAAW,EAC3B,EACL,EACG;UAGXH,EAAA,CAAAE,cAAA,oBAAqF;UACnFF,EAAA,CAAA0B,UAAA,KAAAgH,oCAAA,0BAA0B;UAItB1I,EAFJ,CAAAE,cAAA,gBAAoD,0BACM,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,sBAAc;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAkI,SAAA,iBAAmF;UACnFlI,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACtCH,EAAA,CAAA0B,UAAA,KAAAiH,kCAAA,wBAA8G;UAGhH3I,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAE,cAAA,0BAAwD,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,sBAAc;UAAAD,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAE,cAAA,sBAA4C;UAC1CF,EAAA,CAAA0B,UAAA,KAAAkH,mCAAA,yBAAqE;UAGvE5I,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAE,cAAA,oBAAoB;UAAAF,EAAA,CAAAC,MAAA,gBAAQ;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACvCH,EAAA,CAAA0B,UAAA,KAAAmH,kCAAA,wBAA8G;UAGhH7I,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAE,cAAA,0BAAwD,iBAC3C;UAAAF,EAAA,CAAAC,MAAA,qBAAa;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAElCH,EADF,CAAAE,cAAA,sBAAsC,sBACZ;UAAAF,EAAA,CAAAC,MAAA,WAAG;UAAAD,EAAA,CAAAG,YAAA,EAAa;UACxCH,EAAA,CAAAE,cAAA,sBAA2B;UAAAF,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAG,YAAA,EAAa;UAC9CH,EAAA,CAAAE,cAAA,sBAAyB;UAAAF,EAAA,CAAAC,MAAA,YAAI;UAAAD,EAAA,CAAAG,YAAA,EAAa;UAC1CH,EAAA,CAAAE,cAAA,uBAA6B;UAAAF,EAAA,CAAAC,MAAA,iBAAQ;UACvCD,EADuC,CAAAG,YAAA,EAAa,EACvC;UACbH,EAAA,CAAAE,cAAA,qBAAoB;UAAAF,EAAA,CAAAC,MAAA,sBAAa;UACnCD,EADmC,CAAAG,YAAA,EAAW,EAC7B;UAGfH,EADF,CAAAE,cAAA,2BAAwD,kBAC3C;UAAAF,EAAA,CAAAC,MAAA,oBAAW;UAAAD,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAE,cAAA,qBAIW;UACXF,EAAA,CAAAC,MAAA;UAAAD,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAE,cAAA,qBAAoB;UAAAF,EAAA,CAAAC,MAAA,oBAAW;UAAAD,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA0B,UAAA,MAAAoH,mCAAA,wBAA0G;UAG5G9I,EAAA,CAAAG,YAAA,EAAiB;UAIfH,EADF,CAAAE,cAAA,gBAAiC,WAC3B;UAAAF,EAAA,CAAAC,MAAA,+BAAsB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UAC/BH,EAAA,CAAAE,cAAA,qBAMyB;UAHvBF,EAAA,CAAAc,UAAA,oBAAAiI,gDAAAC,MAAA;YAAAhJ,EAAA,CAAAiB,aAAA,CAAAgH,GAAA;YAAA,OAAAjI,EAAA,CAAAqB,WAAA,CAAU0G,GAAA,CAAAxC,cAAA,CAAAyD,MAAA,CAAsB;UAAA,EAAC;UAHnChJ,EAAA,CAAAG,YAAA,EAMyB;UAEzBH,EAAA,CAAAE,cAAA,mBAIwB;UADtBF,EAAA,CAAAc,UAAA,mBAAAmI,gDAAA;YAAAjJ,EAAA,CAAAiB,aAAA,CAAAgH,GAAA;YAAA,MAAAiB,YAAA,GAAAlJ,EAAA,CAAAmJ,WAAA;YAAA,OAAAnJ,EAAA,CAAAqB,WAAA,CAAS6H,YAAA,CAAAE,KAAA,EAAiB;UAAA,EAAC;UAE3BpJ,EAAA,CAAAE,cAAA,iBAAU;UAAAF,EAAA,CAAAC,MAAA,oBAAW;UAAAD,EAAA,CAAAG,YAAA,EAAW;UAChCH,EAAA,CAAAC,MAAA,uBACF;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAA0B,UAAA,MAAA2H,6BAAA,kBAA6D;UAS/DrJ,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAE,cAAA,gBAA2B,yBACqC;UAC5DF,EAAA,CAAAC,MAAA,yBAAe;UAAAD,EAAA,CAAAE,cAAA,cAA+B;UAAAF,EAAA,CAAAC,MAAA,6BAAoB;UAGxED,EAHwE,CAAAG,YAAA,EAAI,EACzD,EACX,EACD;UAIHH,EAFJ,CAAAE,cAAA,gBAA0B,mBACc,iBAC1B;UAAAF,EAAA,CAAAC,MAAA,mBAAU;UAAAD,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,MAAA,eACF;UAAAD,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAE,cAAA,mBAA4F;UAC1FF,EAAA,CAAAC,MAAA,eACA;UAAAD,EAAA,CAAAE,cAAA,iBAAU;UAAAF,EAAA,CAAAC,MAAA,sBAAa;UAG7BD,EAH6B,CAAAG,YAAA,EAAW,EAC3B,EACL,EACG;UAGXH,EAAA,CAAAE,cAAA,qBAA+C;UAC7CF,EAAA,CAAA0B,UAAA,MAAA4H,qCAAA,0BAA0B;UAGxBtJ,EADF,CAAAE,cAAA,gBAA4B,WACtB;UAAAF,EAAA,CAAAC,MAAA,gCAAuB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UAG9BH,EADF,CAAAE,cAAA,gBAAyB,WACnB;UAAAF,EAAA,CAAAC,MAAA,6BAAoB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,cAAK;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAA4F;UAAAD,EAAA,CAAAG,YAAA,EAAI;UACvHH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAA0C;UAAAD,EAAA,CAAAG,YAAA,EAAI;UACtEH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,eAAM;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAA0C;UAAAD,EAAA,CAAAG,YAAA,EAAI;UACtEH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,iBAAQ;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAA4C;UAC3ED,EAD2E,CAAAG,YAAA,EAAI,EACzE;UAGJH,EADF,CAAAE,cAAA,gBAAyB,WACnB;UAAAF,EAAA,CAAAC,MAAA,0BAAiB;UAAAD,EAAA,CAAAG,YAAA,EAAK;UACvBH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,wBAAe;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAA+C;UAAAD,EAAA,CAAAG,YAAA,EAAI;UACpFH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,cAAK;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAA+C;UAAAD,EAAA,CAAAG,YAAA,EAAI;UAC1EH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,iBAAQ;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAAyC;UAAAD,EAAA,CAAAG,YAAA,EAAI;UACvEH,EAAH,CAAAE,cAAA,UAAG,eAAQ;UAAAF,EAAA,CAAAC,MAAA,qBAAY;UAAAD,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,MAAA,KAA6C;UAAAD,EAAA,CAAAG,YAAA,EAAI;UAClFH,EAAA,CAAA0B,UAAA,MAAA6H,2BAAA,gBAAoC;UAExCvJ,EADE,CAAAG,YAAA,EAAM,EACF;UAIFH,EAFJ,CAAAE,cAAA,gBAA0B,mBACc,iBAC1B;UAAAF,EAAA,CAAAC,MAAA,mBAAU;UAAAD,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAC,MAAA,eACF;UAAAD,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAE,cAAA,mBAKwB;UAFtBF,EAAA,CAAAc,UAAA,mBAAA0I,gDAAA;YAAAxJ,EAAA,CAAAiB,aAAA,CAAAgH,GAAA;YAAA,OAAAjI,EAAA,CAAAqB,WAAA,CAAS0G,GAAA,CAAAxD,QAAA,EAAU;UAAA,EAAC;UAMpBvE,EAHA,CAAA0B,UAAA,MAAA+H,kCAAA,uBAA6C,MAAAC,kCAAA,uBAChB,MAAAC,8BAAA,mBACJ,MAAAC,8BAAA,mBACD;UAQxC5J,EAPc,CAAAG,YAAA,EAAS,EACL,EACG,EACC,EACG,EACV,EACP,EACM;;;;;;;;;;;;;;;;;;;;UApPFH,EAAA,CAAAU,UAAA,qBAAoB;UAUnBV,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAU,UAAA,oBAAmB;UAeZV,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAU,UAAA,gBAAAqH,GAAA,CAAAvH,gBAAA,CAAgC;UAGlCR,EAAA,CAAAI,SAAA,GAA8B;UAA9BJ,EAAA,CAAAU,UAAA,cAAAqH,GAAA,CAAAvH,gBAAA,CAA8B;UAMlBR,EAAA,CAAAI,SAAA,GAA8F;UAA9FJ,EAAA,CAAAU,UAAA,WAAAmJ,OAAA,GAAA9B,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,gCAAA4D,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAA9B,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,gCAAA4D,OAAA,CAAAE,OAAA,EAA8F;UAS9F/J,EAAA,CAAAI,SAAA,GAA4F;UAA5FJ,EAAA,CAAAU,UAAA,WAAAsJ,OAAA,GAAAjC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,+BAAA+D,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAjC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,+BAAA+D,OAAA,CAAAD,OAAA,EAA4F;UAU9F/J,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAU,UAAA,WAAAuJ,OAAA,GAAAlC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,4BAAAgE,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAlC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,4BAAAgE,OAAA,CAAAF,OAAA,EAAsF;UAStF/J,EAAA,CAAAI,SAAA,GAAsF;UAAtFJ,EAAA,CAAAU,UAAA,WAAAwJ,OAAA,GAAAnC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,4BAAAiE,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAnC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,4BAAAiE,OAAA,CAAAH,OAAA,EAAsF;UAStF/J,EAAA,CAAAI,SAAA,GAA0F;UAA1FJ,EAAA,CAAAU,UAAA,WAAAyJ,QAAA,GAAApC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,8BAAAkE,QAAA,CAAAL,OAAA,OAAAK,QAAA,GAAApC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,8BAAAkE,QAAA,CAAAJ,OAAA,EAA0F;UAS1F/J,EAAA,CAAAI,SAAA,GAA0F;UAA1FJ,EAAA,CAAAU,UAAA,WAAA0J,QAAA,GAAArC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,8BAAAmE,QAAA,CAAAN,OAAA,OAAAM,QAAA,GAAArC,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,8BAAAmE,QAAA,CAAAL,OAAA,EAA0F;UAO/C/J,EAAA,CAAAI,SAAA,GAAqC;UAArCJ,EAAA,CAAAU,UAAA,aAAAqH,GAAA,CAAAvH,gBAAA,CAAAsJ,OAAA,CAAqC;UAQxF9J,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAU,UAAA,gBAAAqH,GAAA,CAAAtH,aAAA,CAA6B;UAG/BT,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAU,UAAA,cAAAqH,GAAA,CAAAtH,aAAA,CAA2B;UAKjBT,EAAA,CAAAI,SAAA,GAAgG;UAAhGJ,EAAA,CAAAU,UAAA,WAAA2J,QAAA,GAAAtC,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,oCAAAoE,QAAA,CAAAP,OAAA,OAAAO,QAAA,GAAAtC,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,oCAAAoE,QAAA,CAAAN,OAAA,EAAgG;UAQ7E/J,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAU,UAAA,YAAAqH,GAAA,CAAAvF,cAAA,CAAiB;UAKpCxC,EAAA,CAAAI,SAAA,GAAgG;UAAhGJ,EAAA,CAAAU,UAAA,WAAA4J,QAAA,GAAAvC,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,oCAAAqE,QAAA,CAAAR,OAAA,OAAAQ,QAAA,GAAAvC,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,oCAAAqE,QAAA,CAAAP,OAAA,EAAgG;UAyBhG/J,EAAA,CAAAI,SAAA,IAA4F;UAA5FJ,EAAA,CAAAU,UAAA,WAAA6J,QAAA,GAAAxC,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,kCAAAsE,QAAA,CAAAT,OAAA,OAAAS,QAAA,GAAAxC,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,kCAAAsE,QAAA,CAAAR,OAAA,EAA4F;UAyBlG/J,EAAA,CAAAI,SAAA,IAA8B;UAA9BJ,EAAA,CAAAU,UAAA,SAAAqH,GAAA,CAAAnG,aAAA,CAAAC,MAAA,KAA8B;UAwBmB7B,EAAA,CAAAI,SAAA,IAAkC;UAAlCJ,EAAA,CAAAU,UAAA,aAAAqH,GAAA,CAAAtH,aAAA,CAAAqJ,OAAA,CAAkC;UAgB/D9J,EAAA,CAAAI,SAAA,IAA4F;UAA5FJ,EAAA,CAAAwK,kBAAA,OAAAC,QAAA,GAAA1C,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,gCAAAwE,QAAA,CAAA7J,KAAA,QAAA6J,QAAA,GAAA1C,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,+BAAAwE,QAAA,CAAA7J,KAAA,KAA4F;UAC3FZ,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAK,kBAAA,OAAAqK,QAAA,GAAA3C,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,4BAAAyE,QAAA,CAAA9J,KAAA,KAA0C;UAC1CZ,EAAA,CAAAI,SAAA,GAA0C;UAA1CJ,EAAA,CAAAK,kBAAA,OAAAsK,QAAA,GAAA5C,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,4BAAA0E,QAAA,CAAA/J,KAAA,KAA0C;UACxCZ,EAAA,CAAAI,SAAA,GAA4C;UAA5CJ,EAAA,CAAAK,kBAAA,OAAAuK,QAAA,GAAA7C,GAAA,CAAAvH,gBAAA,CAAAyF,GAAA,8BAAA2E,QAAA,CAAAhK,KAAA,KAA4C;UAKrCZ,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAK,kBAAA,OAAAwK,QAAA,GAAA9C,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,oCAAA4E,QAAA,CAAAjK,KAAA,KAA+C;UACzDZ,EAAA,CAAAI,SAAA,GAA+C;UAA/CJ,EAAA,CAAAK,kBAAA,OAAAyK,QAAA,GAAA/C,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,oCAAA6E,QAAA,CAAAlK,KAAA,KAA+C;UAC5CZ,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAK,kBAAA,OAAA0K,QAAA,GAAAhD,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,8BAAA8E,QAAA,CAAAnK,KAAA,KAAyC;UACrCZ,EAAA,CAAAI,SAAA,GAA6C;UAA7CJ,EAAA,CAAAK,kBAAA,OAAA2K,QAAA,GAAAjD,GAAA,CAAAtH,aAAA,CAAAwF,GAAA,kCAAA+E,QAAA,CAAApK,KAAA,KAA6C;UAC1EZ,EAAA,CAAAI,SAAA,EAA8B;UAA9BJ,EAAA,CAAAU,UAAA,SAAAqH,GAAA,CAAAnG,aAAA,CAAAC,MAAA,KAA8B;UAalC7B,EAAA,CAAAI,SAAA,GAA2E;UAA3EJ,EAAA,CAAAU,UAAA,aAAAqH,GAAA,CAAA3F,SAAA,IAAA2F,GAAA,CAAAvH,gBAAA,CAAAsJ,OAAA,IAAA/B,GAAA,CAAAtH,aAAA,CAAAqJ,OAAA,CAA2E;UAEhE9J,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAU,UAAA,SAAAqH,GAAA,CAAA3F,SAAA,CAAe;UACfpC,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAU,UAAA,UAAAqH,GAAA,CAAA3F,SAAA,CAAgB;UACpBpC,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAU,UAAA,UAAAqH,GAAA,CAAA3F,SAAA,CAAgB;UAChBpC,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAU,UAAA,SAAAqH,GAAA,CAAA3F,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}