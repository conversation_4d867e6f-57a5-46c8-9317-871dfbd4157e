{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport { r as raf } from './helpers-be245865.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-b874c3c3.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\nimport './index-9b0d46f4.js';\n\n/**\n * iOS Loading Enter Animation\n */\nconst iosEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'scale(1.1)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Loading Leave Animation\n */\nconst iosLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: 'scale(1)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.9)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Enter Animation\n */\nconst mdEnterAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 0.01, 'var(--backdrop-opacity)').beforeStyles({\n    'pointer-events': 'none'\n  }).afterClearStyles(['pointer-events']);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.01,\n    transform: 'scale(1.1)'\n  }, {\n    offset: 1,\n    opacity: 1,\n    transform: 'scale(1)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Leave Animation\n */\nconst mdLeaveAnimation = baseEl => {\n  const baseAnimation = createAnimation();\n  const backdropAnimation = createAnimation();\n  const wrapperAnimation = createAnimation();\n  backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n  wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([{\n    offset: 0,\n    opacity: 0.99,\n    transform: 'scale(1)'\n  }, {\n    offset: 1,\n    opacity: 0,\n    transform: 'scale(0.9)'\n  }]);\n  return baseAnimation.addElement(baseEl).easing('ease-in-out').duration(200).addAnimation([backdropAnimation, wrapperAnimation]);\n};\nconst loadingIosCss = \".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, #f9f9f9));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, #666666);--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst IonLoadingIosStyle0 = loadingIosCss;\nconst loadingMdCss = \".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, #f2f2f2);--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #3880ff);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, #262626);font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst IonLoadingMdStyle0 = loadingMdCss;\nconst Loading = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.didPresent = createEvent(this, \"ionLoadingDidPresent\", 7);\n    this.willPresent = createEvent(this, \"ionLoadingWillPresent\", 7);\n    this.willDismiss = createEvent(this, \"ionLoadingWillDismiss\", 7);\n    this.didDismiss = createEvent(this, \"ionLoadingDidDismiss\", 7);\n    this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n    this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n    this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n    this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n    this.delegateController = createDelegateController(this);\n    this.lockController = createLockController();\n    this.triggerController = createTriggerController();\n    this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n    this.presented = false;\n    this.onBackdropTap = () => {\n      this.dismiss(undefined, BACKDROP);\n    };\n    this.overlayIndex = undefined;\n    this.delegate = undefined;\n    this.hasController = false;\n    this.keyboardClose = true;\n    this.enterAnimation = undefined;\n    this.leaveAnimation = undefined;\n    this.message = undefined;\n    this.cssClass = undefined;\n    this.duration = 0;\n    this.backdropDismiss = false;\n    this.showBackdrop = true;\n    this.spinner = undefined;\n    this.translucent = false;\n    this.animated = true;\n    this.htmlAttributes = undefined;\n    this.isOpen = false;\n    this.trigger = undefined;\n  }\n  onIsOpenChange(newValue, oldValue) {\n    if (newValue === true && oldValue === false) {\n      this.present();\n    } else if (newValue === false && oldValue === true) {\n      this.dismiss();\n    }\n  }\n  triggerChanged() {\n    const {\n      trigger,\n      el,\n      triggerController\n    } = this;\n    if (trigger) {\n      triggerController.addClickListener(el, trigger);\n    }\n  }\n  connectedCallback() {\n    prepareOverlay(this.el);\n    this.triggerChanged();\n  }\n  componentWillLoad() {\n    if (this.spinner === undefined) {\n      const mode = getIonMode(this);\n      this.spinner = config.get('loadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n    }\n    setOverlayId(this.el);\n  }\n  componentDidLoad() {\n    /**\n     * If loading indicator was rendered with isOpen=\"true\"\n     * then we should open loading indicator immediately.\n     */\n    if (this.isOpen === true) {\n      raf(() => this.present());\n    }\n    /**\n     * When binding values in frameworks such as Angular\n     * it is possible for the value to be set after the Web Component\n     * initializes but before the value watcher is set up in Stencil.\n     * As a result, the watcher callback may not be fired.\n     * We work around this by manually calling the watcher\n     * callback when the component has loaded and the watcher\n     * is configured.\n     */\n    this.triggerChanged();\n  }\n  disconnectedCallback() {\n    this.triggerController.removeClickListener();\n  }\n  /**\n   * Present the loading overlay after it has been created.\n   */\n  present() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this.lockController.lock();\n      yield _this.delegateController.attachViewToDom();\n      yield present(_this, 'loadingEnter', iosEnterAnimation, mdEnterAnimation);\n      if (_this.duration > 0) {\n        _this.durationTimeout = setTimeout(() => _this.dismiss(), _this.duration + 10);\n      }\n      unlock();\n    })();\n  }\n  /**\n   * Dismiss the loading overlay after it has been presented.\n   *\n   * @param data Any data to emit in the dismiss events.\n   * @param role The role of the element that is dismissing the loading.\n   * This can be useful in a button handler for determining which button was\n   * clicked to dismiss the loading.\n   * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n   *\n   * This is a no-op if the overlay has not been presented yet. If you want\n   * to remove an overlay from the DOM that was never presented, use the\n   * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n   */\n  dismiss(data, role) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const unlock = yield _this2.lockController.lock();\n      if (_this2.durationTimeout) {\n        clearTimeout(_this2.durationTimeout);\n      }\n      const dismissed = yield dismiss(_this2, data, role, 'loadingLeave', iosLeaveAnimation, mdLeaveAnimation);\n      if (dismissed) {\n        _this2.delegateController.removeViewFromDom();\n      }\n      unlock();\n      return dismissed;\n    })();\n  }\n  /**\n   * Returns a promise that resolves when the loading did dismiss.\n   */\n  onDidDismiss() {\n    return eventMethod(this.el, 'ionLoadingDidDismiss');\n  }\n  /**\n   * Returns a promise that resolves when the loading will dismiss.\n   */\n  onWillDismiss() {\n    return eventMethod(this.el, 'ionLoadingWillDismiss');\n  }\n  renderLoadingMessage(msgId) {\n    const {\n      customHTMLEnabled,\n      message\n    } = this;\n    if (customHTMLEnabled) {\n      return h(\"div\", {\n        class: \"loading-content\",\n        id: msgId,\n        innerHTML: sanitizeDOMString(message)\n      });\n    }\n    return h(\"div\", {\n      class: \"loading-content\",\n      id: msgId\n    }, message);\n  }\n  render() {\n    const {\n      message,\n      spinner,\n      htmlAttributes,\n      overlayIndex\n    } = this;\n    const mode = getIonMode(this);\n    const msgId = `loading-${overlayIndex}-msg`;\n    /**\n     * If the message is defined, use that as the label.\n     * Otherwise, don't set aria-labelledby.\n     */\n    const ariaLabelledBy = message !== undefined ? msgId : null;\n    return h(Host, Object.assign({\n      key: 'e780853dc67b7b4ebd8dd65cadab648e4238c6ee',\n      role: \"dialog\",\n      \"aria-modal\": \"true\",\n      \"aria-labelledby\": ariaLabelledBy,\n      tabindex: \"-1\"\n    }, htmlAttributes, {\n      style: {\n        zIndex: `${40000 + this.overlayIndex}`\n      },\n      onIonBackdropTap: this.onBackdropTap,\n      class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), {\n        [mode]: true,\n        'overlay-hidden': true,\n        'loading-translucent': this.translucent\n      })\n    }), h(\"ion-backdrop\", {\n      key: '8cd59ca7bc97b981fd578a526dfe859847e4d392',\n      visible: this.showBackdrop,\n      tappable: this.backdropDismiss\n    }), h(\"div\", {\n      key: 'ef392aaf2cb7f6f9cecc685525cce3abc333e800',\n      tabindex: \"0\"\n    }), h(\"div\", {\n      key: 'f1f6df21a7fa6565fe33acb4a5f355b5ec3e65b2',\n      class: \"loading-wrapper ion-overlay-wrapper\"\n    }, spinner && h(\"div\", {\n      key: '725cf5a206152885e31ab061b0c466fe1ead0225',\n      class: \"loading-spinner\"\n    }, h(\"ion-spinner\", {\n      key: '5891dc39fa133b71576aec219f552386b202e163',\n      name: spinner,\n      \"aria-hidden\": \"true\"\n    })), message !== undefined && this.renderLoadingMessage(msgId)), h(\"div\", {\n      key: '8103269f1181325a507ed1c681f5ef15e40fbc34',\n      tabindex: \"0\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"isOpen\": [\"onIsOpenChange\"],\n      \"trigger\": [\"triggerChanged\"]\n    };\n  }\n};\nLoading.style = {\n  ios: IonLoadingIosStyle0,\n  md: IonLoadingMdStyle0\n};\nexport { Loading as ion_loading };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "E", "ENABLE_HTML_CONTENT_DEFAULT", "a", "sanitizeDOMString", "raf", "c", "createLockController", "createDelegateController", "e", "createTriggerController", "B", "BACKDROP", "j", "prepareOverlay", "k", "setOverlayId", "present", "g", "dismiss", "eventMethod", "getClassMap", "config", "b", "getIonMode", "createAnimation", "iosEnterAnimation", "baseEl", "baseAnimation", "backdropAnimation", "wrapperAnimation", "addElement", "querySelector", "fromTo", "beforeStyles", "afterClearStyles", "keyframes", "offset", "opacity", "transform", "easing", "duration", "addAnimation", "iosLeaveAnimation", "mdEnterAnimation", "mdLeaveAnimation", "loadingIosCss", "IonLoadingIosStyle0", "loadingMdCss", "IonLoadingMdStyle0", "Loading", "constructor", "hostRef", "didPresent", "willPresent", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "didPresentShorthand", "willPresentShorthand", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "delegate<PERSON><PERSON>roller", "lockController", "triggerController", "customHTMLEnabled", "get", "presented", "onBackdropTap", "undefined", "overlayIndex", "delegate", "hasController", "keyboardClose", "enterAnimation", "leaveAnimation", "message", "cssClass", "<PERSON><PERSON><PERSON><PERSON>", "showBackdrop", "spinner", "translucent", "animated", "htmlAttributes", "isOpen", "trigger", "onIsOpenChange", "newValue", "oldValue", "triggerChanged", "el", "addClickListener", "connectedCallback", "componentWillLoad", "mode", "componentDidLoad", "disconnectedCallback", "removeClickListener", "_this", "_asyncToGenerator", "unlock", "lock", "attachViewToDom", "durationTimeout", "setTimeout", "data", "role", "_this2", "clearTimeout", "dismissed", "removeViewFromDom", "onDid<PERSON><PERSON><PERSON>", "on<PERSON>ill<PERSON><PERSON>iss", "renderLoadingMessage", "msgId", "class", "id", "innerHTML", "render", "ariaLabelledBy", "Object", "assign", "key", "tabindex", "style", "zIndex", "onIonBackdropTap", "visible", "tappable", "name", "watchers", "ios", "md", "ion_loading"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/ion-loading.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { E as ENABLE_HTML_CONTENT_DEFAULT, a as sanitizeDOMString } from './config-49c88215.js';\nimport { r as raf } from './helpers-be245865.js';\nimport { c as createLockController } from './lock-controller-316928be.js';\nimport { d as createDelegateController, e as createTriggerController, B as BACKDROP, j as prepareOverlay, k as setOverlayId, f as present, g as dismiss, h as eventMethod } from './overlays-b874c3c3.js';\nimport { g as getClassMap } from './theme-01f3f29c.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createAnimation } from './animation-6a0c5338.js';\nimport './index-a5d50daf.js';\nimport './hardware-back-button-6107a37c.js';\nimport './framework-delegate-ed4ba327.js';\nimport './index-9b0d46f4.js';\n\n/**\n * iOS Loading Enter Animation\n */\nconst iosEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.01, transform: 'scale(1.1)' },\n        { offset: 1, opacity: 1, transform: 'scale(1)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * iOS Loading Leave Animation\n */\nconst iosLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.99, transform: 'scale(1)' },\n        { offset: 1, opacity: 0, transform: 'scale(0.9)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Enter Animation\n */\nconst mdEnterAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation\n        .addElement(baseEl.querySelector('ion-backdrop'))\n        .fromTo('opacity', 0.01, 'var(--backdrop-opacity)')\n        .beforeStyles({\n        'pointer-events': 'none',\n    })\n        .afterClearStyles(['pointer-events']);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.01, transform: 'scale(1.1)' },\n        { offset: 1, opacity: 1, transform: 'scale(1)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\n/**\n * Md Loading Leave Animation\n */\nconst mdLeaveAnimation = (baseEl) => {\n    const baseAnimation = createAnimation();\n    const backdropAnimation = createAnimation();\n    const wrapperAnimation = createAnimation();\n    backdropAnimation.addElement(baseEl.querySelector('ion-backdrop')).fromTo('opacity', 'var(--backdrop-opacity)', 0);\n    wrapperAnimation.addElement(baseEl.querySelector('.loading-wrapper')).keyframes([\n        { offset: 0, opacity: 0.99, transform: 'scale(1)' },\n        { offset: 1, opacity: 0, transform: 'scale(0.9)' },\n    ]);\n    return baseAnimation\n        .addElement(baseEl)\n        .easing('ease-in-out')\n        .duration(200)\n        .addAnimation([backdropAnimation, wrapperAnimation]);\n};\n\nconst loadingIosCss = \".sc-ion-loading-ios-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-ios-h{display:none}.loading-wrapper.sc-ion-loading-ios{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-ios{color:var(--spinner-color)}.sc-ion-loading-ios-h{--background:var(--ion-overlay-background-color, var(--ion-color-step-100, #f9f9f9));--max-width:270px;--max-height:90%;--spinner-color:var(--ion-color-step-600, #666666);--backdrop-opacity:var(--ion-backdrop-opacity, 0.3);color:var(--ion-text-color, #000);font-size:0.875rem}.loading-wrapper.sc-ion-loading-ios{border-radius:8px;-webkit-padding-start:34px;padding-inline-start:34px;-webkit-padding-end:34px;padding-inline-end:34px;padding-top:24px;padding-bottom:24px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.loading-translucent.sc-ion-loading-ios-h .loading-wrapper.sc-ion-loading-ios{background-color:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}.loading-content.sc-ion-loading-ios{font-weight:bold}.loading-spinner.sc-ion-loading-ios+.loading-content.sc-ion-loading-ios{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst IonLoadingIosStyle0 = loadingIosCss;\n\nconst loadingMdCss = \".sc-ion-loading-md-h{--min-width:auto;--width:auto;--min-height:auto;--height:auto;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;font-family:var(--ion-font-family, inherit);contain:strict;-ms-touch-action:none;touch-action:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:1001}.overlay-hidden.sc-ion-loading-md-h{display:none}.loading-wrapper.sc-ion-loading-md{display:-ms-flexbox;display:flex;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);opacity:0;z-index:10}ion-spinner.sc-ion-loading-md{color:var(--spinner-color)}.sc-ion-loading-md-h{--background:var(--ion-color-step-50, #f2f2f2);--max-width:280px;--max-height:90%;--spinner-color:var(--ion-color-primary, #3880ff);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32);color:var(--ion-color-step-850, #262626);font-size:0.875rem}.loading-wrapper.sc-ion-loading-md{border-radius:2px;-webkit-padding-start:24px;padding-inline-start:24px;-webkit-padding-end:24px;padding-inline-end:24px;padding-top:24px;padding-bottom:24px;-webkit-box-shadow:0 16px 20px rgba(0, 0, 0, 0.4);box-shadow:0 16px 20px rgba(0, 0, 0, 0.4)}.loading-spinner.sc-ion-loading-md+.loading-content.sc-ion-loading-md{-webkit-margin-start:16px;margin-inline-start:16px}\";\nconst IonLoadingMdStyle0 = loadingMdCss;\n\nconst Loading = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.didPresent = createEvent(this, \"ionLoadingDidPresent\", 7);\n        this.willPresent = createEvent(this, \"ionLoadingWillPresent\", 7);\n        this.willDismiss = createEvent(this, \"ionLoadingWillDismiss\", 7);\n        this.didDismiss = createEvent(this, \"ionLoadingDidDismiss\", 7);\n        this.didPresentShorthand = createEvent(this, \"didPresent\", 7);\n        this.willPresentShorthand = createEvent(this, \"willPresent\", 7);\n        this.willDismissShorthand = createEvent(this, \"willDismiss\", 7);\n        this.didDismissShorthand = createEvent(this, \"didDismiss\", 7);\n        this.delegateController = createDelegateController(this);\n        this.lockController = createLockController();\n        this.triggerController = createTriggerController();\n        this.customHTMLEnabled = config.get('innerHTMLTemplatesEnabled', ENABLE_HTML_CONTENT_DEFAULT);\n        this.presented = false;\n        this.onBackdropTap = () => {\n            this.dismiss(undefined, BACKDROP);\n        };\n        this.overlayIndex = undefined;\n        this.delegate = undefined;\n        this.hasController = false;\n        this.keyboardClose = true;\n        this.enterAnimation = undefined;\n        this.leaveAnimation = undefined;\n        this.message = undefined;\n        this.cssClass = undefined;\n        this.duration = 0;\n        this.backdropDismiss = false;\n        this.showBackdrop = true;\n        this.spinner = undefined;\n        this.translucent = false;\n        this.animated = true;\n        this.htmlAttributes = undefined;\n        this.isOpen = false;\n        this.trigger = undefined;\n    }\n    onIsOpenChange(newValue, oldValue) {\n        if (newValue === true && oldValue === false) {\n            this.present();\n        }\n        else if (newValue === false && oldValue === true) {\n            this.dismiss();\n        }\n    }\n    triggerChanged() {\n        const { trigger, el, triggerController } = this;\n        if (trigger) {\n            triggerController.addClickListener(el, trigger);\n        }\n    }\n    connectedCallback() {\n        prepareOverlay(this.el);\n        this.triggerChanged();\n    }\n    componentWillLoad() {\n        if (this.spinner === undefined) {\n            const mode = getIonMode(this);\n            this.spinner = config.get('loadingSpinner', config.get('spinner', mode === 'ios' ? 'lines' : 'crescent'));\n        }\n        setOverlayId(this.el);\n    }\n    componentDidLoad() {\n        /**\n         * If loading indicator was rendered with isOpen=\"true\"\n         * then we should open loading indicator immediately.\n         */\n        if (this.isOpen === true) {\n            raf(() => this.present());\n        }\n        /**\n         * When binding values in frameworks such as Angular\n         * it is possible for the value to be set after the Web Component\n         * initializes but before the value watcher is set up in Stencil.\n         * As a result, the watcher callback may not be fired.\n         * We work around this by manually calling the watcher\n         * callback when the component has loaded and the watcher\n         * is configured.\n         */\n        this.triggerChanged();\n    }\n    disconnectedCallback() {\n        this.triggerController.removeClickListener();\n    }\n    /**\n     * Present the loading overlay after it has been created.\n     */\n    async present() {\n        const unlock = await this.lockController.lock();\n        await this.delegateController.attachViewToDom();\n        await present(this, 'loadingEnter', iosEnterAnimation, mdEnterAnimation);\n        if (this.duration > 0) {\n            this.durationTimeout = setTimeout(() => this.dismiss(), this.duration + 10);\n        }\n        unlock();\n    }\n    /**\n     * Dismiss the loading overlay after it has been presented.\n     *\n     * @param data Any data to emit in the dismiss events.\n     * @param role The role of the element that is dismissing the loading.\n     * This can be useful in a button handler for determining which button was\n     * clicked to dismiss the loading.\n     * Some examples include: ``\"cancel\"`, `\"destructive\"`, \"selected\"`, and `\"backdrop\"`.\n     *\n     * This is a no-op if the overlay has not been presented yet. If you want\n     * to remove an overlay from the DOM that was never presented, use the\n     * [remove](https://developer.mozilla.org/en-US/docs/Web/API/Element/remove) method.\n     */\n    async dismiss(data, role) {\n        const unlock = await this.lockController.lock();\n        if (this.durationTimeout) {\n            clearTimeout(this.durationTimeout);\n        }\n        const dismissed = await dismiss(this, data, role, 'loadingLeave', iosLeaveAnimation, mdLeaveAnimation);\n        if (dismissed) {\n            this.delegateController.removeViewFromDom();\n        }\n        unlock();\n        return dismissed;\n    }\n    /**\n     * Returns a promise that resolves when the loading did dismiss.\n     */\n    onDidDismiss() {\n        return eventMethod(this.el, 'ionLoadingDidDismiss');\n    }\n    /**\n     * Returns a promise that resolves when the loading will dismiss.\n     */\n    onWillDismiss() {\n        return eventMethod(this.el, 'ionLoadingWillDismiss');\n    }\n    renderLoadingMessage(msgId) {\n        const { customHTMLEnabled, message } = this;\n        if (customHTMLEnabled) {\n            return h(\"div\", { class: \"loading-content\", id: msgId, innerHTML: sanitizeDOMString(message) });\n        }\n        return (h(\"div\", { class: \"loading-content\", id: msgId }, message));\n    }\n    render() {\n        const { message, spinner, htmlAttributes, overlayIndex } = this;\n        const mode = getIonMode(this);\n        const msgId = `loading-${overlayIndex}-msg`;\n        /**\n         * If the message is defined, use that as the label.\n         * Otherwise, don't set aria-labelledby.\n         */\n        const ariaLabelledBy = message !== undefined ? msgId : null;\n        return (h(Host, Object.assign({ key: 'e780853dc67b7b4ebd8dd65cadab648e4238c6ee', role: \"dialog\", \"aria-modal\": \"true\", \"aria-labelledby\": ariaLabelledBy, tabindex: \"-1\" }, htmlAttributes, { style: {\n                zIndex: `${40000 + this.overlayIndex}`,\n            }, onIonBackdropTap: this.onBackdropTap, class: Object.assign(Object.assign({}, getClassMap(this.cssClass)), { [mode]: true, 'overlay-hidden': true, 'loading-translucent': this.translucent }) }), h(\"ion-backdrop\", { key: '8cd59ca7bc97b981fd578a526dfe859847e4d392', visible: this.showBackdrop, tappable: this.backdropDismiss }), h(\"div\", { key: 'ef392aaf2cb7f6f9cecc685525cce3abc333e800', tabindex: \"0\" }), h(\"div\", { key: 'f1f6df21a7fa6565fe33acb4a5f355b5ec3e65b2', class: \"loading-wrapper ion-overlay-wrapper\" }, spinner && (h(\"div\", { key: '725cf5a206152885e31ab061b0c466fe1ead0225', class: \"loading-spinner\" }, h(\"ion-spinner\", { key: '5891dc39fa133b71576aec219f552386b202e163', name: spinner, \"aria-hidden\": \"true\" }))), message !== undefined && this.renderLoadingMessage(msgId)), h(\"div\", { key: '8103269f1181325a507ed1c681f5ef15e40fbc34', tabindex: \"0\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"isOpen\": [\"onIsOpenChange\"],\n        \"trigger\": [\"triggerChanged\"]\n    }; }\n};\nLoading.style = {\n    ios: IonLoadingIosStyle0,\n    md: IonLoadingMdStyle0\n};\n\nexport { Loading as ion_loading };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASC,CAAC,IAAIC,2BAA2B,EAAEC,CAAC,IAAIC,iBAAiB,QAAQ,sBAAsB;AAC/F,SAASZ,CAAC,IAAIa,GAAG,QAAQ,uBAAuB;AAChD,SAASC,CAAC,IAAIC,oBAAoB,QAAQ,+BAA+B;AACzE,SAASb,CAAC,IAAIc,wBAAwB,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,YAAY,EAAEjB,CAAC,IAAIkB,OAAO,EAAEC,CAAC,IAAIC,OAAO,EAAEvB,CAAC,IAAIwB,WAAW,QAAQ,wBAAwB;AACzM,SAASF,CAAC,IAAIG,WAAW,QAAQ,qBAAqB;AACtD,SAASf,CAAC,IAAIgB,MAAM,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AACzE,SAASlB,CAAC,IAAImB,eAAe,QAAQ,yBAAyB;AAC9D,OAAO,qBAAqB;AAC5B,OAAO,oCAAoC;AAC3C,OAAO,kCAAkC;AACzC,OAAO,qBAAqB;;AAE5B;AACA;AACA;AACA,MAAMC,iBAAiB,GAAIC,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAa,CAAC,EACrD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAW,CAAC,CACnD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMa,iBAAiB,GAAIhB,MAAM,IAAK;EAClC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAW,CAAC,EACnD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CACrD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMc,gBAAgB,GAAIjB,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CACZE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAChDC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAClDC,YAAY,CAAC;IACd,gBAAgB,EAAE;EACtB,CAAC,CAAC,CACGC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,CAAC;EACzCL,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAa,CAAC,EACrD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAW,CAAC,CACnD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;;AAED;AACA;AACA;AACA,MAAMe,gBAAgB,GAAIlB,MAAM,IAAK;EACjC,MAAMC,aAAa,GAAGH,eAAe,CAAC,CAAC;EACvC,MAAMI,iBAAiB,GAAGJ,eAAe,CAAC,CAAC;EAC3C,MAAMK,gBAAgB,GAAGL,eAAe,CAAC,CAAC;EAC1CI,iBAAiB,CAACE,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,cAAc,CAAC,CAAC,CAACC,MAAM,CAAC,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;EAClHH,gBAAgB,CAACC,UAAU,CAACJ,MAAM,CAACK,aAAa,CAAC,kBAAkB,CAAC,CAAC,CAACI,SAAS,CAAC,CAC5E;IAAEC,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAW,CAAC,EACnD;IAAEF,MAAM,EAAE,CAAC;IAAEC,OAAO,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAa,CAAC,CACrD,CAAC;EACF,OAAOX,aAAa,CACfG,UAAU,CAACJ,MAAM,CAAC,CAClBa,MAAM,CAAC,aAAa,CAAC,CACrBC,QAAQ,CAAC,GAAG,CAAC,CACbC,YAAY,CAAC,CAACb,iBAAiB,EAAEC,gBAAgB,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMgB,aAAa,GAAG,k8DAAk8D;AACx9D,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,YAAY,GAAG,+nDAA+nD;AACppD,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,OAAO,GAAG,MAAM;EAClBC,WAAWA,CAACC,OAAO,EAAE;IACjB3D,gBAAgB,CAAC,IAAI,EAAE2D,OAAO,CAAC;IAC/B,IAAI,CAACC,UAAU,GAAG1D,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAAC2D,WAAW,GAAG3D,WAAW,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAChE,IAAI,CAAC4D,WAAW,GAAG5D,WAAW,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAChE,IAAI,CAAC6D,UAAU,GAAG7D,WAAW,CAAC,IAAI,EAAE,sBAAsB,EAAE,CAAC,CAAC;IAC9D,IAAI,CAAC8D,mBAAmB,GAAG9D,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAAC+D,oBAAoB,GAAG/D,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACgE,oBAAoB,GAAGhE,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACiE,mBAAmB,GAAGjE,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC7D,IAAI,CAACkE,kBAAkB,GAAGrD,wBAAwB,CAAC,IAAI,CAAC;IACxD,IAAI,CAACsD,cAAc,GAAGvD,oBAAoB,CAAC,CAAC;IAC5C,IAAI,CAACwD,iBAAiB,GAAGrD,uBAAuB,CAAC,CAAC;IAClD,IAAI,CAACsD,iBAAiB,GAAG1C,MAAM,CAAC2C,GAAG,CAAC,2BAA2B,EAAE/D,2BAA2B,CAAC;IAC7F,IAAI,CAACgE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAAChD,OAAO,CAACiD,SAAS,EAAExD,QAAQ,CAAC;IACrC,CAAC;IACD,IAAI,CAACyD,YAAY,GAAGD,SAAS;IAC7B,IAAI,CAACE,QAAQ,GAAGF,SAAS;IACzB,IAAI,CAACG,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,cAAc,GAAGL,SAAS;IAC/B,IAAI,CAACM,cAAc,GAAGN,SAAS;IAC/B,IAAI,CAACO,OAAO,GAAGP,SAAS;IACxB,IAAI,CAACQ,QAAQ,GAAGR,SAAS;IACzB,IAAI,CAAC3B,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACoC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,OAAO,GAAGX,SAAS;IACxB,IAAI,CAACY,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,cAAc,GAAGd,SAAS;IAC/B,IAAI,CAACe,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAGhB,SAAS;EAC5B;EACAiB,cAAcA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IAC/B,IAAID,QAAQ,KAAK,IAAI,IAAIC,QAAQ,KAAK,KAAK,EAAE;MACzC,IAAI,CAACtE,OAAO,CAAC,CAAC;IAClB,CAAC,MACI,IAAIqE,QAAQ,KAAK,KAAK,IAAIC,QAAQ,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACpE,OAAO,CAAC,CAAC;IAClB;EACJ;EACAqE,cAAcA,CAAA,EAAG;IACb,MAAM;MAAEJ,OAAO;MAAEK,EAAE;MAAE1B;IAAkB,CAAC,GAAG,IAAI;IAC/C,IAAIqB,OAAO,EAAE;MACTrB,iBAAiB,CAAC2B,gBAAgB,CAACD,EAAE,EAAEL,OAAO,CAAC;IACnD;EACJ;EACAO,iBAAiBA,CAAA,EAAG;IAChB7E,cAAc,CAAC,IAAI,CAAC2E,EAAE,CAAC;IACvB,IAAI,CAACD,cAAc,CAAC,CAAC;EACzB;EACAI,iBAAiBA,CAAA,EAAG;IAChB,IAAI,IAAI,CAACb,OAAO,KAAKX,SAAS,EAAE;MAC5B,MAAMyB,IAAI,GAAGrE,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,CAACuD,OAAO,GAAGzD,MAAM,CAAC2C,GAAG,CAAC,gBAAgB,EAAE3C,MAAM,CAAC2C,GAAG,CAAC,SAAS,EAAE4B,IAAI,KAAK,KAAK,GAAG,OAAO,GAAG,UAAU,CAAC,CAAC;IAC7G;IACA7E,YAAY,CAAC,IAAI,CAACyE,EAAE,CAAC;EACzB;EACAK,gBAAgBA,CAAA,EAAG;IACf;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAACX,MAAM,KAAK,IAAI,EAAE;MACtB9E,GAAG,CAAC,MAAM,IAAI,CAACY,OAAO,CAAC,CAAC,CAAC;IAC7B;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACuE,cAAc,CAAC,CAAC;EACzB;EACAO,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAChC,iBAAiB,CAACiC,mBAAmB,CAAC,CAAC;EAChD;EACA;AACJ;AACA;EACU/E,OAAOA,CAAA,EAAG;IAAA,IAAAgF,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAMC,MAAM,SAASF,KAAI,CAACnC,cAAc,CAACsC,IAAI,CAAC,CAAC;MAC/C,MAAMH,KAAI,CAACpC,kBAAkB,CAACwC,eAAe,CAAC,CAAC;MAC/C,MAAMpF,OAAO,CAACgF,KAAI,EAAE,cAAc,EAAEvE,iBAAiB,EAAEkB,gBAAgB,CAAC;MACxE,IAAIqD,KAAI,CAACxD,QAAQ,GAAG,CAAC,EAAE;QACnBwD,KAAI,CAACK,eAAe,GAAGC,UAAU,CAAC,MAAMN,KAAI,CAAC9E,OAAO,CAAC,CAAC,EAAE8E,KAAI,CAACxD,QAAQ,GAAG,EAAE,CAAC;MAC/E;MACA0D,MAAM,CAAC,CAAC;IAAC;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACUhF,OAAOA,CAACqF,IAAI,EAAEC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAR,iBAAA;MACtB,MAAMC,MAAM,SAASO,MAAI,CAAC5C,cAAc,CAACsC,IAAI,CAAC,CAAC;MAC/C,IAAIM,MAAI,CAACJ,eAAe,EAAE;QACtBK,YAAY,CAACD,MAAI,CAACJ,eAAe,CAAC;MACtC;MACA,MAAMM,SAAS,SAASzF,OAAO,CAACuF,MAAI,EAAEF,IAAI,EAAEC,IAAI,EAAE,cAAc,EAAE9D,iBAAiB,EAAEE,gBAAgB,CAAC;MACtG,IAAI+D,SAAS,EAAE;QACXF,MAAI,CAAC7C,kBAAkB,CAACgD,iBAAiB,CAAC,CAAC;MAC/C;MACAV,MAAM,CAAC,CAAC;MACR,OAAOS,SAAS;IAAC;EACrB;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAO1F,WAAW,CAAC,IAAI,CAACqE,EAAE,EAAE,sBAAsB,CAAC;EACvD;EACA;AACJ;AACA;EACIsB,aAAaA,CAAA,EAAG;IACZ,OAAO3F,WAAW,CAAC,IAAI,CAACqE,EAAE,EAAE,uBAAuB,CAAC;EACxD;EACAuB,oBAAoBA,CAACC,KAAK,EAAE;IACxB,MAAM;MAAEjD,iBAAiB;MAAEW;IAAQ,CAAC,GAAG,IAAI;IAC3C,IAAIX,iBAAiB,EAAE;MACnB,OAAOpE,CAAC,CAAC,KAAK,EAAE;QAAEsH,KAAK,EAAE,iBAAiB;QAAEC,EAAE,EAAEF,KAAK;QAAEG,SAAS,EAAEhH,iBAAiB,CAACuE,OAAO;MAAE,CAAC,CAAC;IACnG;IACA,OAAQ/E,CAAC,CAAC,KAAK,EAAE;MAAEsH,KAAK,EAAE,iBAAiB;MAAEC,EAAE,EAAEF;IAAM,CAAC,EAAEtC,OAAO,CAAC;EACtE;EACA0C,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE1C,OAAO;MAAEI,OAAO;MAAEG,cAAc;MAAEb;IAAa,CAAC,GAAG,IAAI;IAC/D,MAAMwB,IAAI,GAAGrE,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMyF,KAAK,GAAG,WAAW5C,YAAY,MAAM;IAC3C;AACR;AACA;AACA;IACQ,MAAMiD,cAAc,GAAG3C,OAAO,KAAKP,SAAS,GAAG6C,KAAK,GAAG,IAAI;IAC3D,OAAQrH,CAAC,CAACE,IAAI,EAAEyH,MAAM,CAACC,MAAM,CAAC;MAAEC,GAAG,EAAE,0CAA0C;MAAEhB,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAE,MAAM;MAAE,iBAAiB,EAAEa,cAAc;MAAEI,QAAQ,EAAE;IAAK,CAAC,EAAExC,cAAc,EAAE;MAAEyC,KAAK,EAAE;QAC7LC,MAAM,EAAE,GAAG,KAAK,GAAG,IAAI,CAACvD,YAAY;MACxC,CAAC;MAAEwD,gBAAgB,EAAE,IAAI,CAAC1D,aAAa;MAAE+C,KAAK,EAAEK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEnG,WAAW,CAAC,IAAI,CAACuD,QAAQ,CAAC,CAAC,EAAE;QAAE,CAACiB,IAAI,GAAG,IAAI;QAAE,gBAAgB,EAAE,IAAI;QAAE,qBAAqB,EAAE,IAAI,CAACb;MAAY,CAAC;IAAE,CAAC,CAAC,EAAEpF,CAAC,CAAC,cAAc,EAAE;MAAE6H,GAAG,EAAE,0CAA0C;MAAEK,OAAO,EAAE,IAAI,CAAChD,YAAY;MAAEiD,QAAQ,EAAE,IAAI,CAAClD;IAAgB,CAAC,CAAC,EAAEjF,CAAC,CAAC,KAAK,EAAE;MAAE6H,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC,EAAE9H,CAAC,CAAC,KAAK,EAAE;MAAE6H,GAAG,EAAE,0CAA0C;MAAEP,KAAK,EAAE;IAAsC,CAAC,EAAEnC,OAAO,IAAKnF,CAAC,CAAC,KAAK,EAAE;MAAE6H,GAAG,EAAE,0CAA0C;MAAEP,KAAK,EAAE;IAAkB,CAAC,EAAEtH,CAAC,CAAC,aAAa,EAAE;MAAE6H,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAEjD,OAAO;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAE,EAAEJ,OAAO,KAAKP,SAAS,IAAI,IAAI,CAAC4C,oBAAoB,CAACC,KAAK,CAAC,CAAC,EAAErH,CAAC,CAAC,KAAK,EAAE;MAAE6H,GAAG,EAAE,0CAA0C;MAAEC,QAAQ,EAAE;IAAI,CAAC,CAAC,CAAC;EACt2B;EACA,IAAIjC,EAAEA,CAAA,EAAG;IAAE,OAAOzF,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiI,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,QAAQ,EAAE,CAAC,gBAAgB,CAAC;MAC5B,SAAS,EAAE,CAAC,gBAAgB;IAChC,CAAC;EAAE;AACP,CAAC;AACD/E,OAAO,CAACyE,KAAK,GAAG;EACZO,GAAG,EAAEnF,mBAAmB;EACxBoF,EAAE,EAAElF;AACR,CAAC;AAED,SAASC,OAAO,IAAIkF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}