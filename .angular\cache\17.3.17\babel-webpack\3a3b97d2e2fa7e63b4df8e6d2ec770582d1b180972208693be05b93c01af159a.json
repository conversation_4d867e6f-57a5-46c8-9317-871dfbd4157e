{"ast": null, "code": "import { raceInit } from '../observable/race';\nimport { operate } from '../util/lift';\nimport { identity } from '../util/identity';\nexport function raceWith(...otherSources) {\n  return !otherSources.length ? identity : operate((source, subscriber) => {\n    raceInit([source, ...otherSources])(subscriber);\n  });\n}", "map": {"version": 3, "names": ["raceInit", "operate", "identity", "raceWith", "otherSources", "length", "source", "subscriber"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/rxjs/dist/esm/internal/operators/raceWith.js"], "sourcesContent": ["import { raceInit } from '../observable/race';\nimport { operate } from '../util/lift';\nimport { identity } from '../util/identity';\nexport function raceWith(...otherSources) {\n    return !otherSources.length\n        ? identity\n        : operate((source, subscriber) => {\n            raceInit([source, ...otherSources])(subscriber);\n        });\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAO,SAASC,QAAQA,CAAC,GAAGC,YAAY,EAAE;EACtC,OAAO,CAACA,YAAY,CAACC,MAAM,GACrBH,QAAQ,GACRD,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IAC9BP,QAAQ,CAAC,CAACM,MAAM,EAAE,GAAGF,YAAY,CAAC,CAAC,CAACG,UAAU,CAAC;EACnD,CAAC,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}