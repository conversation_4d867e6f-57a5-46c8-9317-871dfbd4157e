{"ast": null, "code": "import { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@ionic/angular\";\nimport * as i5 from \"@angular/material/toolbar\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/table\";\nimport * as i12 from \"@angular/material/paginator\";\nimport * as i13 from \"@angular/material/sort\";\nimport * as i14 from \"@angular/material/badge\";\nimport * as i15 from \"@angular/material/progress-spinner\";\nimport * as i16 from \"@angular/material/expansion\";\nimport * as i17 from \"@angular/material/divider\";\nimport * as i18 from \"@angular/material/core\";\nconst _c0 = () => [5, 10, 20];\nfunction CreditnotePage_mat_icon_80_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 44);\n    i0.ɵɵtext(1, \"refresh\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_mat_icon_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"search\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 45);\n    i0.ɵɵelement(1, \"mat-spinner\", 46);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Loading credit notes...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction CreditnotePage_div_98_th_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Credit Note ID\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_td_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"span\", 66);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const creditNote_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(creditNote_r1.id);\n  }\n}\nfunction CreditnotePage_div_98_th_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Invoice Number\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_td_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const creditNote_r2 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(creditNote_r2.invoiceNumber);\n  }\n}\nfunction CreditnotePage_div_98_th_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Amount\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_td_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 68)(1, \"span\", 14);\n    i0.ɵɵtext(2);\n    i0.ɵɵpipe(3, \"number\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const creditNote_r3 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(3, 1, creditNote_r3.amount, \"1.2-2\"), \"\");\n  }\n}\nfunction CreditnotePage_div_98_th_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Status\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_td_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"ion-badge\", 69);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const creditNote_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r4.getStatusColor(creditNote_r4.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", creditNote_r4.status, \" \");\n  }\n}\nfunction CreditnotePage_div_98_th_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Date Issued\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_td_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const creditNote_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, creditNote_r6.dateIssued, \"short\"));\n  }\n}\nfunction CreditnotePage_div_98_th_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Due Date\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_td_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 67);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const creditNote_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 1, creditNote_r7.dueDate, \"short\"));\n  }\n}\nfunction CreditnotePage_div_98_th_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 70);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_td_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 67)(1, \"button\", 71);\n    i0.ɵɵlistener(\"click\", function CreditnotePage_div_98_td_22_Template_button_click_1_listener() {\n      const creditNote_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.viewDetails(creditNote_r9));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"visibility\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 72);\n    i0.ɵɵlistener(\"click\", function CreditnotePage_div_98_td_22_Template_button_click_4_listener() {\n      const creditNote_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.downloadPDF(creditNote_r9));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"download\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction CreditnotePage_div_98_tr_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 73);\n  }\n}\nfunction CreditnotePage_div_98_tr_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 74);\n    i0.ɵɵlistener(\"click\", function CreditnotePage_div_98_tr_24_Template_tr_click_0_listener() {\n      const row_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.viewDetails(row_r11));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CreditnotePage_div_98_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"table\", 48);\n    i0.ɵɵelementContainerStart(2, 49);\n    i0.ɵɵtemplate(3, CreditnotePage_div_98_th_3_Template, 2, 0, \"th\", 50)(4, CreditnotePage_div_98_td_4_Template, 3, 1, \"td\", 51);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(5, 52);\n    i0.ɵɵtemplate(6, CreditnotePage_div_98_th_6_Template, 2, 0, \"th\", 50)(7, CreditnotePage_div_98_td_7_Template, 2, 1, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(8, 54);\n    i0.ɵɵtemplate(9, CreditnotePage_div_98_th_9_Template, 2, 0, \"th\", 50)(10, CreditnotePage_div_98_td_10_Template, 4, 4, \"td\", 55);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(11, 56);\n    i0.ɵɵtemplate(12, CreditnotePage_div_98_th_12_Template, 2, 0, \"th\", 50)(13, CreditnotePage_div_98_td_13_Template, 3, 2, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(14, 57);\n    i0.ɵɵtemplate(15, CreditnotePage_div_98_th_15_Template, 2, 0, \"th\", 50)(16, CreditnotePage_div_98_td_16_Template, 3, 4, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(17, 58);\n    i0.ɵɵtemplate(18, CreditnotePage_div_98_th_18_Template, 2, 0, \"th\", 50)(19, CreditnotePage_div_98_td_19_Template, 3, 4, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementContainerStart(20, 59);\n    i0.ɵɵtemplate(21, CreditnotePage_div_98_th_21_Template, 2, 0, \"th\", 60)(22, CreditnotePage_div_98_td_22_Template, 7, 0, \"td\", 53);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(23, CreditnotePage_div_98_tr_23_Template, 1, 0, \"tr\", 61)(24, CreditnotePage_div_98_tr_24_Template, 1, 0, \"tr\", 62);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"mat-paginator\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"dataSource\", ctx_r4.dataSource);\n    i0.ɵɵadvance(22);\n    i0.ɵɵproperty(\"matHeaderRowDef\", ctx_r4.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matRowDefColumns\", ctx_r4.displayedColumns);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"pageSizeOptions\", i0.ɵɵpureFunction0(4, _c0));\n  }\n}\nfunction CreditnotePage_div_99_mat_expansion_panel_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 77)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"div\", 78)(4, \"span\", 66);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ion-badge\", 69);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"mat-panel-description\")(9, \"div\", 79)(10, \"span\", 14);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"number\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 80)(15, \"span\", 81);\n    i0.ɵɵtext(16, \"Invoice Number:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 82);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 80)(20, \"span\", 81);\n    i0.ɵɵtext(21, \"Customer:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 82);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 80)(25, \"span\", 81);\n    i0.ɵɵtext(26, \"Date Issued:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 82);\n    i0.ɵɵtext(28);\n    i0.ɵɵpipe(29, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"div\", 80)(31, \"span\", 81);\n    i0.ɵɵtext(32, \"Due Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"span\", 82);\n    i0.ɵɵtext(34);\n    i0.ɵɵpipe(35, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 80)(37, \"span\", 81);\n    i0.ɵɵtext(38, \"Reason:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 82);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(41, \"mat-divider\", 83);\n    i0.ɵɵelementStart(42, \"div\", 84)(43, \"button\", 85);\n    i0.ɵɵlistener(\"click\", function CreditnotePage_div_99_mat_expansion_panel_1_Template_button_click_43_listener() {\n      const creditNote_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.viewDetails(creditNote_r13));\n    });\n    i0.ɵɵelementStart(44, \"mat-icon\");\n    i0.ɵɵtext(45, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(46, \" View Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function CreditnotePage_div_99_mat_expansion_panel_1_Template_button_click_47_listener() {\n      const creditNote_r13 = i0.ɵɵrestoreView(_r12).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.downloadPDF(creditNote_r13));\n    });\n    i0.ɵɵelementStart(48, \"mat-icon\");\n    i0.ɵɵtext(49, \"download\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(50, \" Download PDF \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const creditNote_r13 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(creditNote_r13.id);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r4.getStatusColor(creditNote_r13.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", creditNote_r13.status, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(12, 9, creditNote_r13.amount, \"1.2-2\"), \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(creditNote_r13.invoiceNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(creditNote_r13.customerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(29, 12, creditNote_r13.dateIssued, \"short\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(35, 15, creditNote_r13.dueDate, \"short\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(creditNote_r13.reason);\n  }\n}\nfunction CreditnotePage_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 75);\n    i0.ɵɵtemplate(1, CreditnotePage_div_99_mat_expansion_panel_1_Template, 51, 18, \"mat-expansion-panel\", 76);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.dataSource.filteredData);\n  }\n}\nfunction CreditnotePage_div_100_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"mat-icon\", 88);\n    i0.ɵɵtext(2, \"receipt_long\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"No credit notes found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Try adjusting your search criteria or check back later.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class CreditnotePage {\n  constructor(formBuilder, router) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.displayedColumns = ['id', 'invoiceNumber', 'amount', 'status', 'dateIssued', 'dueDate', 'actions'];\n    this.dataSource = new MatTableDataSource();\n    this.isLoading = false;\n    this.selectedTabIndex = 0;\n    // Sample data\n    this.creditNotes = [{\n      id: 'CN-2024-001',\n      invoiceNumber: 'INV-2024-001',\n      amount: 15000.00,\n      currency: 'INR',\n      status: 'Approved',\n      dateIssued: new Date('2024-01-15'),\n      dueDate: new Date('2024-02-15'),\n      reason: 'Product quality issue - glass panels damaged',\n      customerName: 'ABC Construction Ltd.'\n    }, {\n      id: 'CN-2024-002',\n      invoiceNumber: 'INV-2024-002',\n      amount: 8500.00,\n      currency: 'INR',\n      status: 'Pending',\n      dateIssued: new Date('2024-01-18'),\n      dueDate: new Date('2024-02-18'),\n      reason: 'Delivery delay compensation',\n      customerName: 'XYZ Builders Pvt. Ltd.'\n    }, {\n      id: 'CN-2024-003',\n      invoiceNumber: 'INV-2024-003',\n      amount: 22000.00,\n      currency: 'INR',\n      status: 'Processing',\n      dateIssued: new Date('2024-01-20'),\n      dueDate: new Date('2024-02-20'),\n      reason: 'Incorrect specifications delivered',\n      customerName: 'Modern Glass Solutions'\n    }, {\n      id: 'CN-2024-004',\n      invoiceNumber: 'INV-2024-004',\n      amount: 5200.00,\n      currency: 'INR',\n      status: 'Rejected',\n      dateIssued: new Date('2024-01-12'),\n      dueDate: new Date('2024-02-12'),\n      reason: 'Installation service issues',\n      customerName: 'Premium Interiors'\n    }];\n    this.searchForm = this.formBuilder.group({\n      searchTerm: [''],\n      dateFrom: [''],\n      dateTo: [''],\n      status: ['']\n    });\n  }\n  ngOnInit() {\n    this.dataSource.data = this.creditNotes;\n  }\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n  onSearch() {\n    const searchTerm = this.searchForm.get('searchTerm')?.value;\n    const status = this.searchForm.get('status')?.value;\n    this.isLoading = true;\n    // Simulate search delay\n    setTimeout(() => {\n      let filteredData = this.creditNotes;\n      if (searchTerm) {\n        filteredData = filteredData.filter(item => item.id.toLowerCase().includes(searchTerm.toLowerCase()) || item.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || item.customerName.toLowerCase().includes(searchTerm.toLowerCase()));\n      }\n      if (status) {\n        filteredData = filteredData.filter(item => item.status.toLowerCase() === status.toLowerCase());\n      }\n      this.dataSource.data = filteredData;\n      this.isLoading = false;\n    }, 1000);\n  }\n  clearSearch() {\n    this.searchForm.reset();\n    this.dataSource.data = this.creditNotes;\n  }\n  getStatusColor(status) {\n    switch (status.toLowerCase()) {\n      case 'approved':\n        return 'success';\n      case 'processing':\n        return 'primary';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'danger';\n      default:\n        return 'medium';\n    }\n  }\n  getTotalAmount() {\n    return this.dataSource.filteredData.reduce((total, item) => total + item.amount, 0);\n  }\n  getApprovedAmount() {\n    return this.dataSource.filteredData.filter(item => item.status.toLowerCase() === 'approved').reduce((total, item) => total + item.amount, 0);\n  }\n  getPendingAmount() {\n    return this.dataSource.filteredData.filter(item => item.status.toLowerCase() === 'pending').reduce((total, item) => total + item.amount, 0);\n  }\n  viewDetails(creditNote) {\n    // Navigate to credit note details or open modal\n    console.log('View details for:', creditNote);\n  }\n  downloadPDF(creditNote) {\n    // Implement PDF download functionality\n    console.log('Download PDF for:', creditNote);\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  refreshData() {\n    this.isLoading = true;\n    // Simulate data refresh\n    setTimeout(() => {\n      this.dataSource.data = [...this.creditNotes];\n      this.isLoading = false;\n    }, 1500);\n  }\n  static {\n    this.ɵfac = function CreditnotePage_Factory(t) {\n      return new (t || CreditnotePage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: CreditnotePage,\n      selectors: [[\"app-creditnote\"]],\n      viewQuery: function CreditnotePage_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatPaginator, 5);\n          i0.ɵɵviewQuery(MatSort, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        }\n      },\n      decls: 101,\n      vars: 26,\n      consts: [[1, \"material-header\", 3, \"translucent\"], [1, \"material-toolbar\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Back\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Refresh\", 3, \"click\", \"disabled\"], [1, \"material-content\", 3, \"fullscreen\"], [1, \"creditnote-container\"], [1, \"summary-section\"], [1, \"summary-cards\"], [1, \"summary-card\", \"total\"], [1, \"card-content\"], [1, \"card-icon\"], [1, \"card-info\"], [1, \"amount\"], [1, \"summary-card\", \"approved\"], [1, \"summary-card\", \"pending\"], [1, \"search-card\"], [1, \"search-title\"], [1, \"title-icon\"], [1, \"search-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"search-row\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"formControlName\", \"searchTerm\", \"placeholder\", \"Enter credit note ID, invoice number, or customer name\"], [\"matSuffix\", \"\"], [\"appearance\", \"outline\", 1, \"status-field\"], [\"formControlName\", \"status\"], [\"value\", \"\"], [\"value\", \"approved\"], [\"value\", \"pending\"], [\"value\", \"processing\"], [\"value\", \"rejected\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"search-button\", 3, \"disabled\"], [\"class\", \"spinning\", 4, \"ngIf\"], [4, \"ngIf\"], [\"mat-button\", \"\", \"type\", \"button\", 1, \"clear-button\", 3, \"click\"], [1, \"results-card\"], [1, \"results-title\"], [\"matBadgeColor\", \"primary\", 1, \"results-count\", 3, \"matBadge\"], [1, \"table-content\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [\"class\", \"table-container desktop-view\", 4, \"ngIf\"], [\"class\", \"mobile-view\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [1, \"spinning\"], [1, \"loading-container\"], [\"diameter\", \"50\"], [1, \"table-container\", \"desktop-view\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"creditnotes-table\", 3, \"dataSource\"], [\"matColumnDef\", \"id\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", \"class\", \"id-cell\", 4, \"matCellDef\"], [\"matColumnDef\", \"invoiceNumber\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"amount\"], [\"mat-cell\", \"\", \"class\", \"amount-cell\", 4, \"matCellDef\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"dateIssued\"], [\"matColumnDef\", \"dueDate\"], [\"matColumnDef\", \"actions\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", \"class\", \"creditnote-row\", 3, \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"showFirstLastButtons\", \"\", 1, \"table-paginator\", 3, \"pageSizeOptions\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\", 1, \"id-cell\"], [1, \"creditnote-id\"], [\"mat-cell\", \"\"], [\"mat-cell\", \"\", 1, \"amount-cell\"], [1, \"status-badge\", 3, \"color\"], [\"mat-header-cell\", \"\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"View Details\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Download PDF\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 1, \"creditnote-row\", 3, \"click\"], [1, \"mobile-view\"], [\"class\", \"creditnote-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"creditnote-card\"], [1, \"card-title\"], [1, \"card-description\"], [1, \"info-row\"], [1, \"label\"], [1, \"value\"], [1, \"card-divider\"], [1, \"card-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 3, \"click\"], [1, \"no-data\"], [1, \"no-data-icon\"]],\n      template: function CreditnotePage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"mat-toolbar\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function CreditnotePage_Template_button_click_2_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"span\", 3);\n          i0.ɵɵtext(6, \"Credit Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 4);\n          i0.ɵɵelementStart(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function CreditnotePage_Template_button_click_8_listener() {\n            return ctx.refreshData();\n          });\n          i0.ɵɵelementStart(9, \"mat-icon\");\n          i0.ɵɵtext(10, \"refresh\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(11, \"ion-content\", 6)(12, \"div\", 7)(13, \"div\", 8)(14, \"div\", 9)(15, \"mat-card\", 10)(16, \"mat-card-content\")(17, \"div\", 11)(18, \"mat-icon\", 12);\n          i0.ɵɵtext(19, \"account_balance\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 13)(21, \"h3\");\n          i0.ɵɵtext(22, \"Total Amount\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 14);\n          i0.ɵɵtext(24);\n          i0.ɵɵpipe(25, \"number\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(26, \"mat-card\", 15)(27, \"mat-card-content\")(28, \"div\", 11)(29, \"mat-icon\", 12);\n          i0.ɵɵtext(30, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"h3\");\n          i0.ɵɵtext(33, \"Approved\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\", 14);\n          i0.ɵɵtext(35);\n          i0.ɵɵpipe(36, \"number\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(37, \"mat-card\", 16)(38, \"mat-card-content\")(39, \"div\", 11)(40, \"mat-icon\", 12);\n          i0.ɵɵtext(41, \"schedule\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 13)(43, \"h3\");\n          i0.ɵɵtext(44, \"Pending\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"p\", 14);\n          i0.ɵɵtext(46);\n          i0.ɵɵpipe(47, \"number\");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(48, \"mat-card\", 17)(49, \"mat-card-header\")(50, \"mat-card-title\", 18)(51, \"mat-icon\", 19);\n          i0.ɵɵtext(52, \"search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Search Credit Notes \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"mat-card-subtitle\");\n          i0.ɵɵtext(55, \"Filter credit notes by ID, invoice number, or customer\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"mat-card-content\")(57, \"form\", 20);\n          i0.ɵɵlistener(\"ngSubmit\", function CreditnotePage_Template_form_ngSubmit_57_listener() {\n            return ctx.onSearch();\n          });\n          i0.ɵɵelementStart(58, \"div\", 21)(59, \"mat-form-field\", 22)(60, \"mat-label\");\n          i0.ɵɵtext(61, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(62, \"input\", 23);\n          i0.ɵɵelementStart(63, \"mat-icon\", 24);\n          i0.ɵɵtext(64, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"mat-form-field\", 25)(66, \"mat-label\");\n          i0.ɵɵtext(67, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"mat-select\", 26)(69, \"mat-option\", 27);\n          i0.ɵɵtext(70, \"All Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"mat-option\", 28);\n          i0.ɵɵtext(72, \"Approved\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"mat-option\", 29);\n          i0.ɵɵtext(74, \"Pending\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"mat-option\", 30);\n          i0.ɵɵtext(76, \"Processing\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"mat-option\", 31);\n          i0.ɵɵtext(78, \"Rejected\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(79, \"button\", 32);\n          i0.ɵɵtemplate(80, CreditnotePage_mat_icon_80_Template, 2, 0, \"mat-icon\", 33)(81, CreditnotePage_mat_icon_81_Template, 2, 0, \"mat-icon\", 34);\n          i0.ɵɵtext(82, \" Search \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"button\", 35);\n          i0.ɵɵlistener(\"click\", function CreditnotePage_Template_button_click_83_listener() {\n            return ctx.clearSearch();\n          });\n          i0.ɵɵelementStart(84, \"mat-icon\");\n          i0.ɵɵtext(85, \"clear\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(86, \" Clear \");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(87, \"mat-card\", 36)(88, \"mat-card-header\")(89, \"mat-card-title\", 37)(90, \"mat-icon\", 19);\n          i0.ɵɵtext(91, \"receipt\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(92, \" Credit Notes \");\n          i0.ɵɵelement(93, \"span\", 38);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(94, \"mat-card-subtitle\");\n          i0.ɵɵtext(95, \"Click on any credit note to view detailed information\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(96, \"mat-card-content\", 39);\n          i0.ɵɵtemplate(97, CreditnotePage_div_97_Template, 4, 0, \"div\", 40)(98, CreditnotePage_div_98_Template, 26, 5, \"div\", 41)(99, CreditnotePage_div_99_Template, 2, 1, \"div\", 42)(100, CreditnotePage_div_100_Template, 7, 0, \"div\", 43);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"translucent\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"spinning\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"fullscreen\", true);\n          i0.ɵɵadvance(13);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(25, 17, ctx.getTotalAmount(), \"1.2-2\"), \"\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(36, 20, ctx.getApprovedAmount(), \"1.2-2\"), \"\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(47, 23, ctx.getPendingAmount(), \"1.2-2\"), \"\");\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.searchForm);\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance(12);\n          i0.ɵɵpropertyInterpolate(\"matBadge\", ctx.dataSource.filteredData.length);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.dataSource.filteredData.length === 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i4.IonBadge, i4.IonContent, i4.IonHeader, i5.MatToolbar, i6.MatButton, i6.MatIconButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardSubtitle, i7.MatCardTitle, i8.MatIcon, i9.MatFormField, i9.MatLabel, i9.MatSuffix, i10.MatInput, i11.MatTable, i11.MatHeaderCellDef, i11.MatHeaderRowDef, i11.MatColumnDef, i11.MatCellDef, i11.MatRowDef, i11.MatHeaderCell, i11.MatCell, i11.MatHeaderRow, i11.MatRow, i12.MatPaginator, i13.MatSort, i13.MatSortHeader, i14.MatBadge, i15.MatProgressSpinner, i16.MatExpansionPanel, i16.MatExpansionPanelHeader, i16.MatExpansionPanelTitle, i16.MatExpansionPanelDescription, i17.MatDivider, i18.MatOption, i3.DecimalPipe, i3.DatePipe],\n      styles: [\"\\n\\n.material-header[_ngcontent-%COMP%] {\\n  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);\\n}\\n\\n.material-toolbar[_ngcontent-%COMP%] {\\n  height: 64px;\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\\n  color: white;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin-left: 16px;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n.material-content[_ngcontent-%COMP%] {\\n  --background: #f5f5f5;\\n  padding: 20px;\\n}\\n\\n.creditnote-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.summary-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.summary-cards[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.summary-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n.summary-card.total[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\\n  color: white;\\n}\\n.summary-card.total[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.summary-card.approved[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n}\\n.summary-card.approved[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.summary-card.pending[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);\\n  color: white;\\n}\\n.summary-card.pending[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n.summary-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n.summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  margin-right: 16px;\\n}\\n.summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  font-weight: 500;\\n  margin: 0 0 4px 0;\\n  opacity: 0.9;\\n}\\n.summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .amount[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n\\n\\n.search-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n.search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 20px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n}\\n.search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n\\n.search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr auto auto auto;\\n  gap: 16px;\\n  align-items: center;\\n}\\n@media (max-width: 768px) {\\n  .search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n}\\n.search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  min-width: 300px;\\n}\\n@media (max-width: 768px) {\\n  .search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n}\\n.search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .status-field[_ngcontent-%COMP%] {\\n  min-width: 150px;\\n}\\n@media (max-width: 768px) {\\n  .search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .status-field[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .search-button[_ngcontent-%COMP%], .search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .clear-button[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n@media (max-width: 768px) {\\n  .search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .search-button[_ngcontent-%COMP%], .search-form[_ngcontent-%COMP%]   .search-row[_ngcontent-%COMP%]   .clear-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n\\n\\n.results-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n.results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n}\\n.results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 20px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n}\\n.results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n.results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%]   .results-count[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n.results-card[_ngcontent-%COMP%]   .table-content[_ngcontent-%COMP%] {\\n  padding: 0;\\n}\\n\\n\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n}\\n.loading-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  color: #666;\\n  font-size: 16px;\\n}\\n\\n\\n\\n.desktop-view[_ngcontent-%COMP%] {\\n  display: block;\\n}\\n@media (max-width: 768px) {\\n  .desktop-view[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.creditnotes-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.creditnotes-table[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #333;\\n  font-weight: 600;\\n  font-size: 14px;\\n}\\n.creditnotes-table[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n}\\n.creditnotes-table[_ngcontent-%COMP%]   .creditnote-row[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: background-color 0.3s ease;\\n}\\n.creditnotes-table[_ngcontent-%COMP%]   .creditnote-row[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(25, 118, 210, 0.04);\\n}\\n.creditnotes-table[_ngcontent-%COMP%]   .id-cell[_ngcontent-%COMP%]   .creditnote-id[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: 600;\\n  color: #1976d2;\\n}\\n.creditnotes-table[_ngcontent-%COMP%]   .amount-cell[_ngcontent-%COMP%]   .amount[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2e7d32;\\n}\\n.creditnotes-table[_ngcontent-%COMP%]   .status-badge[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.table-paginator[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e0e0e0;\\n}\\n\\n\\n\\n.mobile-view[_ngcontent-%COMP%] {\\n  display: none;\\n}\\n@media (max-width: 768px) {\\n  .mobile-view[_ngcontent-%COMP%] {\\n    display: block;\\n  }\\n}\\n\\n.creditnote-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border-radius: 8px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%]   .creditnote-id[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-weight: 600;\\n  color: #1976d2;\\n  font-size: 14px;\\n  margin-bottom: 4px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-end;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .mat-expansion-panel-header[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%]   .amount[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2e7d32;\\n  font-size: 16px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  padding: 16px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  margin-bottom: 12px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #666;\\n  font-size: 14px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .info-row[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n  text-align: right;\\n  flex: 1;\\n  margin-left: 16px;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-divider[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: center;\\n}\\n@media (max-width: 480px) {\\n  .creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n}\\n.creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n@media (max-width: 480px) {\\n  .creditnote-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n\\n\\n\\n.no-data[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  padding: 60px 20px;\\n  text-align: center;\\n}\\n.no-data[_ngcontent-%COMP%]   .no-data-icon[_ngcontent-%COMP%] {\\n  font-size: 64px;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n.no-data[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n}\\n.no-data[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .material-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .summary-cards[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 12px;\\n  }\\n  .search-card[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%] {\\n    border-radius: 8px;\\n    margin-bottom: 16px;\\n  }\\n  .search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%], .search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%], .search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n    margin-right: 12px;\\n  }\\n  .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 12px;\\n  }\\n  .summary-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   .card-info[_ngcontent-%COMP%]   .amount[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .search-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .search-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%], .results-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .results-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n    margin-bottom: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["MatTableDataSource", "MatPaginator", "MatSort", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "creditNote_r1", "id", "creditNote_r2", "invoiceNumber", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "creditNote_r3", "amount", "ɵɵproperty", "ctx_r4", "getStatusColor", "creditNote_r4", "status", "creditNote_r6", "dateIssued", "creditNote_r7", "dueDate", "ɵɵlistener", "CreditnotePage_div_98_td_22_Template_button_click_1_listener", "creditNote_r9", "ɵɵrestoreView", "_r8", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewDetails", "CreditnotePage_div_98_td_22_Template_button_click_4_listener", "downloadPDF", "CreditnotePage_div_98_tr_24_Template_tr_click_0_listener", "row_r11", "_r10", "ɵɵelementContainerStart", "ɵɵtemplate", "CreditnotePage_div_98_th_3_Template", "CreditnotePage_div_98_td_4_Template", "CreditnotePage_div_98_th_6_Template", "CreditnotePage_div_98_td_7_Template", "CreditnotePage_div_98_th_9_Template", "CreditnotePage_div_98_td_10_Template", "CreditnotePage_div_98_th_12_Template", "CreditnotePage_div_98_td_13_Template", "CreditnotePage_div_98_th_15_Template", "CreditnotePage_div_98_td_16_Template", "CreditnotePage_div_98_th_18_Template", "CreditnotePage_div_98_td_19_Template", "CreditnotePage_div_98_th_21_Template", "CreditnotePage_div_98_td_22_Template", "CreditnotePage_div_98_tr_23_Template", "CreditnotePage_div_98_tr_24_Template", "dataSource", "displayedColumns", "ɵɵpureFunction0", "_c0", "CreditnotePage_div_99_mat_expansion_panel_1_Template_button_click_43_listener", "creditNote_r13", "_r12", "CreditnotePage_div_99_mat_expansion_panel_1_Template_button_click_47_listener", "customerName", "reason", "CreditnotePage_div_99_mat_expansion_panel_1_Template", "filteredData", "CreditnotePage", "constructor", "formBuilder", "router", "isLoading", "selectedTabIndex", "creditNotes", "currency", "Date", "searchForm", "group", "searchTerm", "dateFrom", "dateTo", "ngOnInit", "data", "ngAfterViewInit", "paginator", "sort", "onSearch", "get", "value", "setTimeout", "filter", "item", "toLowerCase", "includes", "clearSearch", "reset", "getTotalAmount", "reduce", "total", "getApprovedAmount", "getPendingAmount", "creditNote", "console", "log", "goBack", "navigate", "refreshData", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "selectors", "viewQuery", "CreditnotePage_Query", "rf", "ctx", "CreditnotePage_Template_button_click_2_listener", "CreditnotePage_Template_button_click_8_listener", "CreditnotePage_Template_form_ngSubmit_57_listener", "CreditnotePage_mat_icon_80_Template", "CreditnotePage_mat_icon_81_Template", "CreditnotePage_Template_button_click_83_listener", "CreditnotePage_div_97_Template", "CreditnotePage_div_98_Template", "CreditnotePage_div_99_Template", "CreditnotePage_div_100_Template", "ɵɵclassProp", "ɵɵpropertyInterpolate", "length"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\creditnote\\creditnote.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\creditnote\\creditnote.page.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\n\nexport interface CreditNoteData {\n  id: string;\n  invoiceNumber: string;\n  amount: number;\n  currency: string;\n  status: string;\n  dateIssued: Date;\n  dueDate: Date;\n  reason: string;\n  customerName: string;\n}\n\n@Component({\n  selector: 'app-creditnote',\n  templateUrl: './creditnote.page.html',\n  styleUrls: ['./creditnote.page.scss'],\n})\nexport class CreditnotePage implements OnInit {\n\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  searchForm!: FormGroup;\n  displayedColumns: string[] = ['id', 'invoiceNumber', 'amount', 'status', 'dateIssued', 'dueDate', 'actions'];\n  dataSource = new MatTableDataSource<CreditNoteData>();\n  isLoading = false;\n  selectedTabIndex = 0;\n\n  // Sample data\n  creditNotes: CreditNoteData[] = [\n    {\n      id: 'CN-2024-001',\n      invoiceNumber: 'INV-2024-001',\n      amount: 15000.00,\n      currency: 'INR',\n      status: 'Approved',\n      dateIssued: new Date('2024-01-15'),\n      dueDate: new Date('2024-02-15'),\n      reason: 'Product quality issue - glass panels damaged',\n      customerName: 'ABC Construction Ltd.'\n    },\n    {\n      id: 'CN-2024-002',\n      invoiceNumber: 'INV-2024-002',\n      amount: 8500.00,\n      currency: 'INR',\n      status: 'Pending',\n      dateIssued: new Date('2024-01-18'),\n      dueDate: new Date('2024-02-18'),\n      reason: 'Delivery delay compensation',\n      customerName: 'XYZ Builders Pvt. Ltd.'\n    },\n    {\n      id: 'CN-2024-003',\n      invoiceNumber: 'INV-2024-003',\n      amount: 22000.00,\n      currency: 'INR',\n      status: 'Processing',\n      dateIssued: new Date('2024-01-20'),\n      dueDate: new Date('2024-02-20'),\n      reason: 'Incorrect specifications delivered',\n      customerName: 'Modern Glass Solutions'\n    },\n    {\n      id: 'CN-2024-004',\n      invoiceNumber: 'INV-2024-004',\n      amount: 5200.00,\n      currency: 'INR',\n      status: 'Rejected',\n      dateIssued: new Date('2024-01-12'),\n      dueDate: new Date('2024-02-12'),\n      reason: 'Installation service issues',\n      customerName: 'Premium Interiors'\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router\n  ) {\n    this.searchForm = this.formBuilder.group({\n      searchTerm: [''],\n      dateFrom: [''],\n      dateTo: [''],\n      status: ['']\n    });\n  }\n\n  ngOnInit() {\n    this.dataSource.data = this.creditNotes;\n  }\n\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  onSearch() {\n    const searchTerm = this.searchForm.get('searchTerm')?.value;\n    const status = this.searchForm.get('status')?.value;\n    \n    this.isLoading = true;\n    \n    // Simulate search delay\n    setTimeout(() => {\n      let filteredData = this.creditNotes;\n      \n      if (searchTerm) {\n        filteredData = filteredData.filter(item => \n          item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          item.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          item.customerName.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n      }\n      \n      if (status) {\n        filteredData = filteredData.filter(item => \n          item.status.toLowerCase() === status.toLowerCase()\n        );\n      }\n      \n      this.dataSource.data = filteredData;\n      this.isLoading = false;\n    }, 1000);\n  }\n\n  clearSearch() {\n    this.searchForm.reset();\n    this.dataSource.data = this.creditNotes;\n  }\n\n  getStatusColor(status: string): string {\n    switch (status.toLowerCase()) {\n      case 'approved':\n        return 'success';\n      case 'processing':\n        return 'primary';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'danger';\n      default:\n        return 'medium';\n    }\n  }\n\n  getTotalAmount(): number {\n    return this.dataSource.filteredData.reduce((total, item) => total + item.amount, 0);\n  }\n\n  getApprovedAmount(): number {\n    return this.dataSource.filteredData\n      .filter(item => item.status.toLowerCase() === 'approved')\n      .reduce((total, item) => total + item.amount, 0);\n  }\n\n  getPendingAmount(): number {\n    return this.dataSource.filteredData\n      .filter(item => item.status.toLowerCase() === 'pending')\n      .reduce((total, item) => total + item.amount, 0);\n  }\n\n  viewDetails(creditNote: CreditNoteData) {\n    // Navigate to credit note details or open modal\n    console.log('View details for:', creditNote);\n  }\n\n  downloadPDF(creditNote: CreditNoteData) {\n    // Implement PDF download functionality\n    console.log('Download PDF for:', creditNote);\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n\n  refreshData() {\n    this.isLoading = true;\n    \n    // Simulate data refresh\n    setTimeout(() => {\n      this.dataSource.data = [...this.creditNotes];\n      this.isLoading = false;\n    }, 1500);\n  }\n}\n", "<ion-header [translucent]=\"true\" class=\"material-header\">\n  <mat-toolbar class=\"material-toolbar\">\n    <button mat-icon-button (click)=\"goBack()\" aria-label=\"Back\">\n      <mat-icon>arrow_back</mat-icon>\n    </button>\n    <span class=\"toolbar-title\">Credit Notes</span>\n    <span class=\"spacer\"></span>\n    <button mat-icon-button (click)=\"refreshData()\" aria-label=\"Refresh\" [disabled]=\"isLoading\">\n      <mat-icon [class.spinning]=\"isLoading\">refresh</mat-icon>\n    </button>\n  </mat-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\" class=\"material-content\">\n  <div class=\"creditnote-container\">\n    \n    <!-- Summary Cards -->\n    <div class=\"summary-section\">\n      <div class=\"summary-cards\">\n        <mat-card class=\"summary-card total\">\n          <mat-card-content>\n            <div class=\"card-content\">\n              <mat-icon class=\"card-icon\">account_balance</mat-icon>\n              <div class=\"card-info\">\n                <h3>Total Amount</h3>\n                <p class=\"amount\">₹{{ getTotalAmount() | number:'1.2-2' }}</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"summary-card approved\">\n          <mat-card-content>\n            <div class=\"card-content\">\n              <mat-icon class=\"card-icon\">check_circle</mat-icon>\n              <div class=\"card-info\">\n                <h3>Approved</h3>\n                <p class=\"amount\">₹{{ getApprovedAmount() | number:'1.2-2' }}</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n\n        <mat-card class=\"summary-card pending\">\n          <mat-card-content>\n            <div class=\"card-content\">\n              <mat-icon class=\"card-icon\">schedule</mat-icon>\n              <div class=\"card-info\">\n                <h3>Pending</h3>\n                <p class=\"amount\">₹{{ getPendingAmount() | number:'1.2-2' }}</p>\n              </div>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n\n    <!-- Search Section -->\n    <mat-card class=\"search-card\">\n      <mat-card-header>\n        <mat-card-title class=\"search-title\">\n          <mat-icon class=\"title-icon\">search</mat-icon>\n          Search Credit Notes\n        </mat-card-title>\n        <mat-card-subtitle>Filter credit notes by ID, invoice number, or customer</mat-card-subtitle>\n      </mat-card-header>\n      \n      <mat-card-content>\n        <form [formGroup]=\"searchForm\" (ngSubmit)=\"onSearch()\" class=\"search-form\">\n          <div class=\"search-row\">\n            <mat-form-field appearance=\"outline\" class=\"search-field\">\n              <mat-label>Search</mat-label>\n              <input \n                matInput \n                formControlName=\"searchTerm\" \n                placeholder=\"Enter credit note ID, invoice number, or customer name\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n            \n            <mat-form-field appearance=\"outline\" class=\"status-field\">\n              <mat-label>Status</mat-label>\n              <mat-select formControlName=\"status\">\n                <mat-option value=\"\">All Status</mat-option>\n                <mat-option value=\"approved\">Approved</mat-option>\n                <mat-option value=\"pending\">Pending</mat-option>\n                <mat-option value=\"processing\">Processing</mat-option>\n                <mat-option value=\"rejected\">Rejected</mat-option>\n              </mat-select>\n            </mat-form-field>\n            \n            <button \n              mat-raised-button \n              color=\"primary\" \n              type=\"submit\"\n              [disabled]=\"isLoading\"\n              class=\"search-button\">\n              <mat-icon *ngIf=\"isLoading\" class=\"spinning\">refresh</mat-icon>\n              <mat-icon *ngIf=\"!isLoading\">search</mat-icon>\n              Search\n            </button>\n            \n            <button \n              mat-button \n              type=\"button\"\n              (click)=\"clearSearch()\"\n              class=\"clear-button\">\n              <mat-icon>clear</mat-icon>\n              Clear\n            </button>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Results Section -->\n    <mat-card class=\"results-card\">\n      <mat-card-header>\n        <mat-card-title class=\"results-title\">\n          <mat-icon class=\"title-icon\">receipt</mat-icon>\n          Credit Notes\n          <span class=\"results-count\" matBadge=\"{{ dataSource.filteredData.length }}\" matBadgeColor=\"primary\">\n          </span>\n        </mat-card-title>\n        <mat-card-subtitle>Click on any credit note to view detailed information</mat-card-subtitle>\n      </mat-card-header>\n      \n      <mat-card-content class=\"table-content\">\n        <!-- Loading Spinner -->\n        <div *ngIf=\"isLoading\" class=\"loading-container\">\n          <mat-spinner diameter=\"50\"></mat-spinner>\n          <p>Loading credit notes...</p>\n        </div>\n\n        <!-- Desktop Table View -->\n        <div class=\"table-container desktop-view\" *ngIf=\"!isLoading\">\n          <table mat-table [dataSource]=\"dataSource\" matSort class=\"creditnotes-table\">\n            \n            <!-- ID Column -->\n            <ng-container matColumnDef=\"id\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>Credit Note ID</th>\n              <td mat-cell *matCellDef=\"let creditNote\" class=\"id-cell\">\n                <span class=\"creditnote-id\">{{ creditNote.id }}</span>\n              </td>\n            </ng-container>\n\n            <!-- Invoice Number Column -->\n            <ng-container matColumnDef=\"invoiceNumber\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>Invoice Number</th>\n              <td mat-cell *matCellDef=\"let creditNote\">{{ creditNote.invoiceNumber }}</td>\n            </ng-container>\n\n            <!-- Amount Column -->\n            <ng-container matColumnDef=\"amount\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>Amount</th>\n              <td mat-cell *matCellDef=\"let creditNote\" class=\"amount-cell\">\n                <span class=\"amount\">₹{{ creditNote.amount | number:'1.2-2' }}</span>\n              </td>\n            </ng-container>\n\n            <!-- Status Column -->\n            <ng-container matColumnDef=\"status\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>\n              <td mat-cell *matCellDef=\"let creditNote\">\n                <ion-badge [color]=\"getStatusColor(creditNote.status)\" class=\"status-badge\">\n                  {{ creditNote.status }}\n                </ion-badge>\n              </td>\n            </ng-container>\n\n            <!-- Date Issued Column -->\n            <ng-container matColumnDef=\"dateIssued\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>Date Issued</th>\n              <td mat-cell *matCellDef=\"let creditNote\">{{ creditNote.dateIssued | date:'short' }}</td>\n            </ng-container>\n\n            <!-- Due Date Column -->\n            <ng-container matColumnDef=\"dueDate\">\n              <th mat-header-cell *matHeaderCellDef mat-sort-header>Due Date</th>\n              <td mat-cell *matCellDef=\"let creditNote\">{{ creditNote.dueDate | date:'short' }}</td>\n            </ng-container>\n\n            <!-- Actions Column -->\n            <ng-container matColumnDef=\"actions\">\n              <th mat-header-cell *matHeaderCellDef>Actions</th>\n              <td mat-cell *matCellDef=\"let creditNote\">\n                <button \n                  mat-icon-button \n                  color=\"primary\" \n                  (click)=\"viewDetails(creditNote)\"\n                  matTooltip=\"View Details\">\n                  <mat-icon>visibility</mat-icon>\n                </button>\n                <button \n                  mat-icon-button \n                  color=\"accent\" \n                  (click)=\"downloadPDF(creditNote)\"\n                  matTooltip=\"Download PDF\">\n                  <mat-icon>download</mat-icon>\n                </button>\n              </td>\n            </ng-container>\n\n            <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n            <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" \n                class=\"creditnote-row\"\n                (click)=\"viewDetails(row)\"></tr>\n          </table>\n\n          <mat-paginator \n            [pageSizeOptions]=\"[5, 10, 20]\" \n            showFirstLastButtons\n            class=\"table-paginator\">\n          </mat-paginator>\n        </div>\n\n        <!-- Mobile Card View -->\n        <div class=\"mobile-view\" *ngIf=\"!isLoading\">\n          <mat-expansion-panel \n            *ngFor=\"let creditNote of dataSource.filteredData\" \n            class=\"creditnote-card\">\n            \n            <mat-expansion-panel-header>\n              <mat-panel-title>\n                <div class=\"card-title\">\n                  <span class=\"creditnote-id\">{{ creditNote.id }}</span>\n                  <ion-badge [color]=\"getStatusColor(creditNote.status)\" class=\"status-badge\">\n                    {{ creditNote.status }}\n                  </ion-badge>\n                </div>\n              </mat-panel-title>\n              <mat-panel-description>\n                <div class=\"card-description\">\n                  <span class=\"amount\">₹{{ creditNote.amount | number:'1.2-2' }}</span>\n                </div>\n              </mat-panel-description>\n            </mat-expansion-panel-header>\n\n            <div class=\"card-content\">\n              <div class=\"info-row\">\n                <span class=\"label\">Invoice Number:</span>\n                <span class=\"value\">{{ creditNote.invoiceNumber }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"label\">Customer:</span>\n                <span class=\"value\">{{ creditNote.customerName }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"label\">Date Issued:</span>\n                <span class=\"value\">{{ creditNote.dateIssued | date:'short' }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"label\">Due Date:</span>\n                <span class=\"value\">{{ creditNote.dueDate | date:'short' }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"label\">Reason:</span>\n                <span class=\"value\">{{ creditNote.reason }}</span>\n              </div>\n              \n              <mat-divider class=\"card-divider\"></mat-divider>\n              \n              <div class=\"card-actions\">\n                <button \n                  mat-raised-button \n                  color=\"primary\" \n                  (click)=\"viewDetails(creditNote)\">\n                  <mat-icon>visibility</mat-icon>\n                  View Details\n                </button>\n                <button \n                  mat-stroked-button \n                  color=\"accent\" \n                  (click)=\"downloadPDF(creditNote)\">\n                  <mat-icon>download</mat-icon>\n                  Download PDF\n                </button>\n              </div>\n            </div>\n          </mat-expansion-panel>\n        </div>\n\n        <!-- No Data Message -->\n        <div *ngIf=\"!isLoading && dataSource.filteredData.length === 0\" class=\"no-data\">\n          <mat-icon class=\"no-data-icon\">receipt_long</mat-icon>\n          <h3>No credit notes found</h3>\n          <p>Try adjusting your search criteria or check back later.</p>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</ion-content>\n"], "mappings": "AAGA,SAASA,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;IC2FlCC,EAAA,CAAAC,cAAA,mBAA6C;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/DH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IA+BpDH,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAI,SAAA,sBAAyC;IACzCJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAC5BF,EAD4B,CAAAG,YAAA,EAAI,EAC1B;;;;;IAQAH,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEvEH,EADF,CAAAC,cAAA,aAA0D,eAC5B;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACjDF,EADiD,CAAAG,YAAA,EAAO,EACnD;;;;IADyBH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,aAAA,CAAAC,EAAA,CAAmB;;;;;IAMjDR,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACzEH,EAAA,CAAAC,cAAA,aAA0C;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAnCH,EAAA,CAAAK,SAAA,EAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAG,aAAA,CAAAC,aAAA,CAA8B;;;;;IAKxEV,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/DH,EADF,CAAAC,cAAA,aAA8D,eACvC;IAAAD,EAAA,CAAAE,MAAA,GAAyC;;IAChEF,EADgE,CAAAG,YAAA,EAAO,EAClE;;;;IADkBH,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAW,kBAAA,WAAAX,EAAA,CAAAY,WAAA,OAAAC,aAAA,CAAAC,MAAA,eAAyC;;;;;IAMhEd,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/DH,EADF,CAAAC,cAAA,aAA0C,oBACoC;IAC1ED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAY,EACT;;;;;IAHQH,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAe,UAAA,UAAAC,MAAA,CAAAC,cAAA,CAAAC,aAAA,CAAAC,MAAA,EAA2C;IACpDnB,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAW,kBAAA,MAAAO,aAAA,CAAAC,MAAA,MACF;;;;;IAMFnB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACtEH,EAAA,CAAAC,cAAA,aAA0C;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA/CH,EAAA,CAAAK,SAAA,EAA0C;IAA1CL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAY,WAAA,OAAAQ,aAAA,CAAAC,UAAA,WAA0C;;;;;IAKpFrB,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACnEH,EAAA,CAAAC,cAAA,aAA0C;IAAAD,EAAA,CAAAE,MAAA,GAAuC;;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA5CH,EAAA,CAAAK,SAAA,EAAuC;IAAvCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAY,WAAA,OAAAU,aAAA,CAAAC,OAAA,WAAuC;;;;;IAKjFvB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAEhDH,EADF,CAAAC,cAAA,aAA0C,iBAKZ;IAD1BD,EAAA,CAAAwB,UAAA,mBAAAC,6DAAA;MAAA,MAAAC,aAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASf,MAAA,CAAAgB,WAAA,CAAAN,aAAA,CAAuB;IAAA,EAAC;IAEjC1B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,iBAI4B;IAD1BD,EAAA,CAAAwB,UAAA,mBAAAS,6DAAA;MAAA,MAAAP,aAAA,GAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASf,MAAA,CAAAkB,WAAA,CAAAR,aAAA,CAAuB;IAAA,EAAC;IAEjC1B,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAEtBF,EAFsB,CAAAG,YAAA,EAAW,EACtB,EACN;;;;;IAGPH,EAAA,CAAAI,SAAA,aAA4D;;;;;;IAC5DJ,EAAA,CAAAC,cAAA,aAE+B;IAA3BD,EAAA,CAAAwB,UAAA,mBAAAW,yDAAA;MAAA,MAAAC,OAAA,GAAApC,EAAA,CAAA2B,aAAA,CAAAU,IAAA,EAAAR,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASf,MAAA,CAAAgB,WAAA,CAAAI,OAAA,CAAgB;IAAA,EAAC;IAACpC,EAAA,CAAAG,YAAA,EAAK;;;;;IAtEtCH,EADF,CAAAC,cAAA,cAA6D,gBACkB;IAG3ED,EAAA,CAAAsC,uBAAA,OAAgC;IAE9BtC,EADA,CAAAuC,UAAA,IAAAC,mCAAA,iBAAsD,IAAAC,mCAAA,iBACI;;IAM5DzC,EAAA,CAAAsC,uBAAA,OAA2C;IAEzCtC,EADA,CAAAuC,UAAA,IAAAG,mCAAA,iBAAsD,IAAAC,mCAAA,iBACZ;;IAI5C3C,EAAA,CAAAsC,uBAAA,OAAoC;IAElCtC,EADA,CAAAuC,UAAA,IAAAK,mCAAA,iBAAsD,KAAAC,oCAAA,iBACQ;;IAMhE7C,EAAA,CAAAsC,uBAAA,QAAoC;IAElCtC,EADA,CAAAuC,UAAA,KAAAO,oCAAA,iBAAsD,KAAAC,oCAAA,iBACZ;;IAQ5C/C,EAAA,CAAAsC,uBAAA,QAAwC;IAEtCtC,EADA,CAAAuC,UAAA,KAAAS,oCAAA,iBAAsD,KAAAC,oCAAA,iBACZ;;IAI5CjD,EAAA,CAAAsC,uBAAA,QAAqC;IAEnCtC,EADA,CAAAuC,UAAA,KAAAW,oCAAA,iBAAsD,KAAAC,oCAAA,iBACZ;;IAI5CnD,EAAA,CAAAsC,uBAAA,QAAqC;IAEnCtC,EADA,CAAAuC,UAAA,KAAAa,oCAAA,iBAAsC,KAAAC,oCAAA,iBACI;;IAmB5CrD,EADA,CAAAuC,UAAA,KAAAe,oCAAA,iBAAuD,KAAAC,oCAAA,iBAGxB;IACjCvD,EAAA,CAAAG,YAAA,EAAQ;IAERH,EAAA,CAAAI,SAAA,yBAIgB;IAClBJ,EAAA,CAAAG,YAAA,EAAM;;;;IA9EaH,EAAA,CAAAK,SAAA,EAAyB;IAAzBL,EAAA,CAAAe,UAAA,eAAAC,MAAA,CAAAwC,UAAA,CAAyB;IAmEpBxD,EAAA,CAAAK,SAAA,IAAiC;IAAjCL,EAAA,CAAAe,UAAA,oBAAAC,MAAA,CAAAyC,gBAAA,CAAiC;IACpBzD,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAe,UAAA,qBAAAC,MAAA,CAAAyC,gBAAA,CAA0B;IAM3DzD,EAAA,CAAAK,SAAA,EAA+B;IAA/BL,EAAA,CAAAe,UAAA,oBAAAf,EAAA,CAAA0D,eAAA,IAAAC,GAAA,EAA+B;;;;;;IAezB3D,EAPR,CAAAC,cAAA,8BAE0B,iCAEI,sBACT,cACS,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtDH,EAAA,CAAAC,cAAA,oBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAY,EACR,EACU;IAGdH,EAFJ,CAAAC,cAAA,4BAAuB,cACS,gBACP;IAAAD,EAAA,CAAAE,MAAA,IAAyC;;IAGpEF,EAHoE,CAAAG,YAAA,EAAO,EACjE,EACgB,EACG;IAIzBH,EAFJ,CAAAC,cAAA,eAA0B,eACF,gBACA;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;IAEJH,EADF,CAAAC,cAAA,eAAsB,gBACA;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;IAEJH,EADF,CAAAC,cAAA,eAAsB,gBACA;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA0C;;IAChEF,EADgE,CAAAG,YAAA,EAAO,EACjE;IAEJH,EADF,CAAAC,cAAA,eAAsB,gBACA;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADF,CAAAC,cAAA,eAAsB,gBACA;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;IAENH,EAAA,CAAAI,SAAA,uBAAgD;IAG9CJ,EADF,CAAAC,cAAA,eAA0B,kBAIY;IAAlCD,EAAA,CAAAwB,UAAA,mBAAAoC,8EAAA;MAAA,MAAAC,cAAA,GAAA7D,EAAA,CAAA2B,aAAA,CAAAmC,IAAA,EAAAjC,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASf,MAAA,CAAAgB,WAAA,CAAA6B,cAAA,CAAuB;IAAA,EAAC;IACjC7D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAGoC;IAAlCD,EAAA,CAAAwB,UAAA,mBAAAuC,8EAAA;MAAA,MAAAF,cAAA,GAAA7D,EAAA,CAAA2B,aAAA,CAAAmC,IAAA,EAAAjC,SAAA;MAAA,MAAAb,MAAA,GAAAhB,EAAA,CAAA8B,aAAA;MAAA,OAAA9B,EAAA,CAAA+B,WAAA,CAASf,MAAA,CAAAkB,WAAA,CAAA2B,cAAA,CAAuB;IAAA,EAAC;IACjC7D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACc;;;;;IAtDcH,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAuD,cAAA,CAAArD,EAAA,CAAmB;IACpCR,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAe,UAAA,UAAAC,MAAA,CAAAC,cAAA,CAAA4C,cAAA,CAAA1C,MAAA,EAA2C;IACpDnB,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAW,kBAAA,MAAAkD,cAAA,CAAA1C,MAAA,MACF;IAKqBnB,EAAA,CAAAK,SAAA,GAAyC;IAAzCL,EAAA,CAAAW,kBAAA,WAAAX,EAAA,CAAAY,WAAA,QAAAiD,cAAA,CAAA/C,MAAA,eAAyC;IAQ5Cd,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAM,iBAAA,CAAAuD,cAAA,CAAAnD,aAAA,CAA8B;IAI9BV,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,iBAAA,CAAAuD,cAAA,CAAAG,YAAA,CAA6B;IAI7BhE,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAY,WAAA,SAAAiD,cAAA,CAAAxC,UAAA,WAA0C;IAI1CrB,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAM,iBAAA,CAAAN,EAAA,CAAAY,WAAA,SAAAiD,cAAA,CAAAtC,OAAA,WAAuC;IAIvCvB,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAM,iBAAA,CAAAuD,cAAA,CAAAI,MAAA,CAAuB;;;;;IAxCnDjE,EAAA,CAAAC,cAAA,cAA4C;IAC1CD,EAAA,CAAAuC,UAAA,IAAA2B,oDAAA,oCAE0B;IA4D5BlE,EAAA,CAAAG,YAAA,EAAM;;;;IA7DqBH,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAe,UAAA,YAAAC,MAAA,CAAAwC,UAAA,CAAAW,YAAA,CAA0B;;;;;IAiEnDnE,EADF,CAAAC,cAAA,cAAgF,mBAC/C;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8DAAuD;IAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;;;ADtQd,OAAM,MAAOiE,cAAc;EA2DzBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAvDhB,KAAAd,gBAAgB,GAAa,CAAC,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;IAC5G,KAAAD,UAAU,GAAG,IAAI3D,kBAAkB,EAAkB;IACrD,KAAA2E,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAC,WAAW,GAAqB,CAC9B;MACElE,EAAE,EAAE,aAAa;MACjBE,aAAa,EAAE,cAAc;MAC7BI,MAAM,EAAE,QAAQ;MAChB6D,QAAQ,EAAE,KAAK;MACfxD,MAAM,EAAE,UAAU;MAClBE,UAAU,EAAE,IAAIuD,IAAI,CAAC,YAAY,CAAC;MAClCrD,OAAO,EAAE,IAAIqD,IAAI,CAAC,YAAY,CAAC;MAC/BX,MAAM,EAAE,8CAA8C;MACtDD,YAAY,EAAE;KACf,EACD;MACExD,EAAE,EAAE,aAAa;MACjBE,aAAa,EAAE,cAAc;MAC7BI,MAAM,EAAE,OAAO;MACf6D,QAAQ,EAAE,KAAK;MACfxD,MAAM,EAAE,SAAS;MACjBE,UAAU,EAAE,IAAIuD,IAAI,CAAC,YAAY,CAAC;MAClCrD,OAAO,EAAE,IAAIqD,IAAI,CAAC,YAAY,CAAC;MAC/BX,MAAM,EAAE,6BAA6B;MACrCD,YAAY,EAAE;KACf,EACD;MACExD,EAAE,EAAE,aAAa;MACjBE,aAAa,EAAE,cAAc;MAC7BI,MAAM,EAAE,QAAQ;MAChB6D,QAAQ,EAAE,KAAK;MACfxD,MAAM,EAAE,YAAY;MACpBE,UAAU,EAAE,IAAIuD,IAAI,CAAC,YAAY,CAAC;MAClCrD,OAAO,EAAE,IAAIqD,IAAI,CAAC,YAAY,CAAC;MAC/BX,MAAM,EAAE,oCAAoC;MAC5CD,YAAY,EAAE;KACf,EACD;MACExD,EAAE,EAAE,aAAa;MACjBE,aAAa,EAAE,cAAc;MAC7BI,MAAM,EAAE,OAAO;MACf6D,QAAQ,EAAE,KAAK;MACfxD,MAAM,EAAE,UAAU;MAClBE,UAAU,EAAE,IAAIuD,IAAI,CAAC,YAAY,CAAC;MAClCrD,OAAO,EAAE,IAAIqD,IAAI,CAAC,YAAY,CAAC;MAC/BX,MAAM,EAAE,6BAA6B;MACrCD,YAAY,EAAE;KACf,CACF;IAMC,IAAI,CAACa,UAAU,GAAG,IAAI,CAACP,WAAW,CAACQ,KAAK,CAAC;MACvCC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZ9D,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEA+D,QAAQA,CAAA;IACN,IAAI,CAAC1B,UAAU,CAAC2B,IAAI,GAAG,IAAI,CAACT,WAAW;EACzC;EAEAU,eAAeA,CAAA;IACb,IAAI,CAAC5B,UAAU,CAAC6B,SAAS,GAAG,IAAI,CAACA,SAAS;IAC1C,IAAI,CAAC7B,UAAU,CAAC8B,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAC,QAAQA,CAAA;IACN,MAAMR,UAAU,GAAG,IAAI,CAACF,UAAU,CAACW,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAC3D,MAAMtE,MAAM,GAAG,IAAI,CAAC0D,UAAU,CAACW,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK;IAEnD,IAAI,CAACjB,SAAS,GAAG,IAAI;IAErB;IACAkB,UAAU,CAAC,MAAK;MACd,IAAIvB,YAAY,GAAG,IAAI,CAACO,WAAW;MAEnC,IAAIK,UAAU,EAAE;QACdZ,YAAY,GAAGA,YAAY,CAACwB,MAAM,CAACC,IAAI,IACrCA,IAAI,CAACpF,EAAE,CAACqF,WAAW,EAAE,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,EAAE,CAAC,IACxDD,IAAI,CAAClF,aAAa,CAACmF,WAAW,EAAE,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,EAAE,CAAC,IACnED,IAAI,CAAC5B,YAAY,CAAC6B,WAAW,EAAE,CAACC,QAAQ,CAACf,UAAU,CAACc,WAAW,EAAE,CAAC,CACnE;;MAGH,IAAI1E,MAAM,EAAE;QACVgD,YAAY,GAAGA,YAAY,CAACwB,MAAM,CAACC,IAAI,IACrCA,IAAI,CAACzE,MAAM,CAAC0E,WAAW,EAAE,KAAK1E,MAAM,CAAC0E,WAAW,EAAE,CACnD;;MAGH,IAAI,CAACrC,UAAU,CAAC2B,IAAI,GAAGhB,YAAY;MACnC,IAAI,CAACK,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAuB,WAAWA,CAAA;IACT,IAAI,CAAClB,UAAU,CAACmB,KAAK,EAAE;IACvB,IAAI,CAACxC,UAAU,CAAC2B,IAAI,GAAG,IAAI,CAACT,WAAW;EACzC;EAEAzD,cAAcA,CAACE,MAAc;IAC3B,QAAQA,MAAM,CAAC0E,WAAW,EAAE;MAC1B,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,QAAQ;MACjB;QACE,OAAO,QAAQ;;EAErB;EAEAI,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACzC,UAAU,CAACW,YAAY,CAAC+B,MAAM,CAAC,CAACC,KAAK,EAAEP,IAAI,KAAKO,KAAK,GAAGP,IAAI,CAAC9E,MAAM,EAAE,CAAC,CAAC;EACrF;EAEAsF,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAAC5C,UAAU,CAACW,YAAY,CAChCwB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzE,MAAM,CAAC0E,WAAW,EAAE,KAAK,UAAU,CAAC,CACxDK,MAAM,CAAC,CAACC,KAAK,EAAEP,IAAI,KAAKO,KAAK,GAAGP,IAAI,CAAC9E,MAAM,EAAE,CAAC,CAAC;EACpD;EAEAuF,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAAC7C,UAAU,CAACW,YAAY,CAChCwB,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACzE,MAAM,CAAC0E,WAAW,EAAE,KAAK,SAAS,CAAC,CACvDK,MAAM,CAAC,CAACC,KAAK,EAAEP,IAAI,KAAKO,KAAK,GAAGP,IAAI,CAAC9E,MAAM,EAAE,CAAC,CAAC;EACpD;EAEAkB,WAAWA,CAACsE,UAA0B;IACpC;IACAC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,UAAU,CAAC;EAC9C;EAEApE,WAAWA,CAACoE,UAA0B;IACpC;IACAC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,UAAU,CAAC;EAC9C;EAEAG,MAAMA,CAAA;IACJ,IAAI,CAAClC,MAAM,CAACmC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnC,SAAS,GAAG,IAAI;IAErB;IACAkB,UAAU,CAAC,MAAK;MACd,IAAI,CAAClC,UAAU,CAAC2B,IAAI,GAAG,CAAC,GAAG,IAAI,CAACT,WAAW,CAAC;MAC5C,IAAI,CAACF,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;;;uBAvKWJ,cAAc,EAAApE,EAAA,CAAA4G,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9G,EAAA,CAAA4G,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAd5C,cAAc;MAAA6C,SAAA;MAAAC,SAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;yBAEdtH,YAAY;yBACZC,OAAO;;;;;;;;;;;;;UCzBhBC,EAFJ,CAAAC,cAAA,oBAAyD,qBACjB,gBACyB;UAArCD,EAAA,CAAAwB,UAAA,mBAAA8F,gDAAA;YAAA,OAASD,GAAA,CAAAZ,MAAA,EAAQ;UAAA,EAAC;UACxCzG,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UACTH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC/CH,EAAA,CAAAI,SAAA,cAA4B;UAC5BJ,EAAA,CAAAC,cAAA,gBAA4F;UAApED,EAAA,CAAAwB,UAAA,mBAAA+F,gDAAA;YAAA,OAASF,GAAA,CAAAV,WAAA,EAAa;UAAA,EAAC;UAC7C3G,EAAA,CAAAC,cAAA,eAAuC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAGpDF,EAHoD,CAAAG,YAAA,EAAW,EAClD,EACG,EACH;UAWCH,EATd,CAAAC,cAAA,sBAA0D,cACtB,cAGH,cACA,oBACY,wBACjB,eACU,oBACI;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEpDH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,aAAkB;UAAAD,EAAA,CAAAE,MAAA,IAAwC;;UAIlEF,EAJkE,CAAAG,YAAA,EAAI,EAC1D,EACF,EACW,EACV;UAKLH,EAHN,CAAAC,cAAA,oBAAwC,wBACpB,eACU,oBACI;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAEjDH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjBH,EAAA,CAAAC,cAAA,aAAkB;UAAAD,EAAA,CAAAE,MAAA,IAA2C;;UAIrEF,EAJqE,CAAAG,YAAA,EAAI,EAC7D,EACF,EACW,EACV;UAKLH,EAHN,CAAAC,cAAA,oBAAuC,wBACnB,eACU,oBACI;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE7CH,EADF,CAAAC,cAAA,eAAuB,UACjB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChBH,EAAA,CAAAC,cAAA,aAAkB;UAAAD,EAAA,CAAAE,MAAA,IAA0C;;UAMxEF,EANwE,CAAAG,YAAA,EAAI,EAC5D,EACF,EACW,EACV,EACP,EACF;UAMAH,EAHN,CAAAC,cAAA,oBAA8B,uBACX,0BACsB,oBACN;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9CH,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,8DAAsD;UAC3EF,EAD2E,CAAAG,YAAA,EAAoB,EAC7E;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,gBAC2D;UAA5CD,EAAA,CAAAwB,UAAA,sBAAAgG,kDAAA;YAAA,OAAYH,GAAA,CAAA9B,QAAA,EAAU;UAAA,EAAC;UAGhDvF,EAFJ,CAAAC,cAAA,eAAwB,0BACoC,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC7BH,EAAA,CAAAI,SAAA,iBAGuE;UACvEJ,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UAGfH,EADF,CAAAC,cAAA,0BAA0D,iBAC7C;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAE3BH,EADF,CAAAC,cAAA,sBAAqC,sBACd;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAC5CH,EAAA,CAAAC,cAAA,sBAA6B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAClDH,EAAA,CAAAC,cAAA,sBAA4B;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAa;UAChDH,EAAA,CAAAC,cAAA,sBAA+B;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAC,cAAA,sBAA6B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAEzCF,EAFyC,CAAAG,YAAA,EAAa,EACvC,EACE;UAEjBH,EAAA,CAAAC,cAAA,kBAKwB;UAEtBD,EADA,CAAAuC,UAAA,KAAAkF,mCAAA,uBAA6C,KAAAC,mCAAA,uBAChB;UAC7B1H,EAAA,CAAAE,MAAA,gBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAETH,EAAA,CAAAC,cAAA,kBAIuB;UADrBD,EAAA,CAAAwB,UAAA,mBAAAmG,iDAAA;YAAA,OAASN,GAAA,CAAAtB,WAAA,EAAa;UAAA,EAAC;UAEvB/F,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1BH,EAAA,CAAAE,MAAA,eACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACD,EACU,EACV;UAMLH,EAHN,CAAAC,cAAA,oBAA+B,uBACZ,0BACuB,oBACP;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/CH,EAAA,CAAAE,MAAA,sBACA;UAAAF,EAAA,CAAAI,SAAA,gBACO;UACTJ,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,6DAAqD;UAC1EF,EAD0E,CAAAG,YAAA,EAAoB,EAC5E;UAElBH,EAAA,CAAAC,cAAA,4BAAwC;UA4JtCD,EA1JA,CAAAuC,UAAA,KAAAqF,8BAAA,kBAAiD,KAAAC,8BAAA,mBAMY,KAAAC,8BAAA,kBAkFjB,MAAAC,+BAAA,kBAkEoC;UAQxF/H,EAHM,CAAAG,YAAA,EAAmB,EACV,EACP,EACM;;;UAlSFH,EAAA,CAAAe,UAAA,qBAAoB;UAOyCf,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAe,UAAA,aAAAsG,GAAA,CAAA7C,SAAA,CAAsB;UAC/ExE,EAAA,CAAAK,SAAA,EAA4B;UAA5BL,EAAA,CAAAgI,WAAA,aAAAX,GAAA,CAAA7C,SAAA,CAA4B;UAK/BxE,EAAA,CAAAK,SAAA,GAAmB;UAAnBL,EAAA,CAAAe,UAAA,oBAAmB;UAYEf,EAAA,CAAAK,SAAA,IAAwC;UAAxCL,EAAA,CAAAW,kBAAA,WAAAX,EAAA,CAAAY,WAAA,SAAAyG,GAAA,CAAApB,cAAA,iBAAwC;UAYxCjG,EAAA,CAAAK,SAAA,IAA2C;UAA3CL,EAAA,CAAAW,kBAAA,WAAAX,EAAA,CAAAY,WAAA,SAAAyG,GAAA,CAAAjB,iBAAA,iBAA2C;UAY3CpG,EAAA,CAAAK,SAAA,IAA0C;UAA1CL,EAAA,CAAAW,kBAAA,WAAAX,EAAA,CAAAY,WAAA,SAAAyG,GAAA,CAAAhB,gBAAA,iBAA0C;UAmB9DrG,EAAA,CAAAK,SAAA,IAAwB;UAAxBL,EAAA,CAAAe,UAAA,cAAAsG,GAAA,CAAAxC,UAAA,CAAwB;UA0BxB7E,EAAA,CAAAK,SAAA,IAAsB;UAAtBL,EAAA,CAAAe,UAAA,aAAAsG,GAAA,CAAA7C,SAAA,CAAsB;UAEXxE,EAAA,CAAAK,SAAA,EAAe;UAAfL,EAAA,CAAAe,UAAA,SAAAsG,GAAA,CAAA7C,SAAA,CAAe;UACfxE,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAe,UAAA,UAAAsG,GAAA,CAAA7C,SAAA,CAAgB;UAuBHxE,EAAA,CAAAK,SAAA,IAA+C;UAA/CL,EAAA,CAAAiI,qBAAA,aAAAZ,GAAA,CAAA7D,UAAA,CAAAW,YAAA,CAAA+D,MAAA,CAA+C;UAQvElI,EAAA,CAAAK,SAAA,GAAe;UAAfL,EAAA,CAAAe,UAAA,SAAAsG,GAAA,CAAA7C,SAAA,CAAe;UAMsBxE,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAe,UAAA,UAAAsG,GAAA,CAAA7C,SAAA,CAAgB;UAkFjCxE,EAAA,CAAAK,SAAA,EAAgB;UAAhBL,EAAA,CAAAe,UAAA,UAAAsG,GAAA,CAAA7C,SAAA,CAAgB;UAkEpCxE,EAAA,CAAAK,SAAA,EAAwD;UAAxDL,EAAA,CAAAe,UAAA,UAAAsG,GAAA,CAAA7C,SAAA,IAAA6C,GAAA,CAAA7D,UAAA,CAAAW,YAAA,CAAA+D,MAAA,OAAwD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}