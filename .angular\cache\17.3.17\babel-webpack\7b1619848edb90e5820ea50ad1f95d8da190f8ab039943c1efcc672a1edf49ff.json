{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\nlet CreditnotePage = class CreditnotePage {\n  constructor(formBuilder, router) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.displayedColumns = ['id', 'invoiceNumber', 'amount', 'status', 'dateIssued', 'dueDate', 'actions'];\n    this.dataSource = new MatTableDataSource();\n    this.isLoading = false;\n    this.selectedTabIndex = 0;\n    // Sample data\n    this.creditNotes = [{\n      id: 'CN-2024-001',\n      invoiceNumber: 'INV-2024-001',\n      amount: 15000.00,\n      currency: 'INR',\n      status: 'Approved',\n      dateIssued: new Date('2024-01-15'),\n      dueDate: new Date('2024-02-15'),\n      reason: 'Product quality issue - glass panels damaged',\n      customerName: 'ABC Construction Ltd.'\n    }, {\n      id: 'CN-2024-002',\n      invoiceNumber: 'INV-2024-002',\n      amount: 8500.00,\n      currency: 'INR',\n      status: 'Pending',\n      dateIssued: new Date('2024-01-18'),\n      dueDate: new Date('2024-02-18'),\n      reason: 'Delivery delay compensation',\n      customerName: 'XYZ Builders Pvt. Ltd.'\n    }, {\n      id: 'CN-2024-003',\n      invoiceNumber: 'INV-2024-003',\n      amount: 22000.00,\n      currency: 'INR',\n      status: 'Processing',\n      dateIssued: new Date('2024-01-20'),\n      dueDate: new Date('2024-02-20'),\n      reason: 'Incorrect specifications delivered',\n      customerName: 'Modern Glass Solutions'\n    }, {\n      id: 'CN-2024-004',\n      invoiceNumber: 'INV-2024-004',\n      amount: 5200.00,\n      currency: 'INR',\n      status: 'Rejected',\n      dateIssued: new Date('2024-01-12'),\n      dueDate: new Date('2024-02-12'),\n      reason: 'Installation service issues',\n      customerName: 'Premium Interiors'\n    }];\n    this.searchForm = this.formBuilder.group({\n      searchTerm: [''],\n      dateFrom: [''],\n      dateTo: [''],\n      status: ['']\n    });\n  }\n  ngOnInit() {\n    this.dataSource.data = this.creditNotes;\n  }\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n  onSearch() {\n    const searchTerm = this.searchForm.get('searchTerm')?.value;\n    const status = this.searchForm.get('status')?.value;\n    this.isLoading = true;\n    // Simulate search delay\n    setTimeout(() => {\n      let filteredData = this.creditNotes;\n      if (searchTerm) {\n        filteredData = filteredData.filter(item => item.id.toLowerCase().includes(searchTerm.toLowerCase()) || item.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || item.customerName.toLowerCase().includes(searchTerm.toLowerCase()));\n      }\n      if (status) {\n        filteredData = filteredData.filter(item => item.status.toLowerCase() === status.toLowerCase());\n      }\n      this.dataSource.data = filteredData;\n      this.isLoading = false;\n    }, 1000);\n  }\n  clearSearch() {\n    this.searchForm.reset();\n    this.dataSource.data = this.creditNotes;\n  }\n  getStatusColor(status) {\n    switch (status.toLowerCase()) {\n      case 'approved':\n        return 'success';\n      case 'processing':\n        return 'primary';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'danger';\n      default:\n        return 'medium';\n    }\n  }\n  getTotalAmount() {\n    return this.dataSource.filteredData.reduce((total, item) => total + item.amount, 0);\n  }\n  getApprovedAmount() {\n    return this.dataSource.filteredData.filter(item => item.status.toLowerCase() === 'approved').reduce((total, item) => total + item.amount, 0);\n  }\n  getPendingAmount() {\n    return this.dataSource.filteredData.filter(item => item.status.toLowerCase() === 'pending').reduce((total, item) => total + item.amount, 0);\n  }\n  viewDetails(creditNote) {\n    // Navigate to credit note details or open modal\n    console.log('View details for:', creditNote);\n  }\n  downloadPDF(creditNote) {\n    // Implement PDF download functionality\n    console.log('Download PDF for:', creditNote);\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  refreshData() {\n    this.isLoading = true;\n    // Simulate data refresh\n    setTimeout(() => {\n      this.dataSource.data = [...this.creditNotes];\n      this.isLoading = false;\n    }, 1500);\n  }\n};\n__decorate([ViewChild(MatPaginator)], CreditnotePage.prototype, \"paginator\", void 0);\n__decorate([ViewChild(MatSort)], CreditnotePage.prototype, \"sort\", void 0);\nCreditnotePage = __decorate([Component({\n  selector: 'app-creditnote',\n  templateUrl: './creditnote.page.html',\n  styleUrls: ['./creditnote.page.scss']\n})], CreditnotePage);\nexport { CreditnotePage };", "map": {"version": 3, "names": ["Component", "ViewChild", "MatTableDataSource", "MatPaginator", "MatSort", "CreditnotePage", "constructor", "formBuilder", "router", "displayedColumns", "dataSource", "isLoading", "selectedTabIndex", "creditNotes", "id", "invoiceNumber", "amount", "currency", "status", "dateIssued", "Date", "dueDate", "reason", "customerName", "searchForm", "group", "searchTerm", "dateFrom", "dateTo", "ngOnInit", "data", "ngAfterViewInit", "paginator", "sort", "onSearch", "get", "value", "setTimeout", "filteredData", "filter", "item", "toLowerCase", "includes", "clearSearch", "reset", "getStatusColor", "getTotalAmount", "reduce", "total", "getApprovedAmount", "getPendingAmount", "viewDetails", "creditNote", "console", "log", "downloadPDF", "goBack", "navigate", "refreshData", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\creditnote\\creditnote.page.ts"], "sourcesContent": ["import { Component, OnInit, ViewChild } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatTableDataSource } from '@angular/material/table';\nimport { MatPaginator } from '@angular/material/paginator';\nimport { MatSort } from '@angular/material/sort';\n\nexport interface CreditNoteData {\n  id: string;\n  invoiceNumber: string;\n  amount: number;\n  currency: string;\n  status: string;\n  dateIssued: Date;\n  dueDate: Date;\n  reason: string;\n  customerName: string;\n}\n\n@Component({\n  selector: 'app-creditnote',\n  templateUrl: './creditnote.page.html',\n  styleUrls: ['./creditnote.page.scss'],\n})\nexport class CreditnotePage implements OnInit {\n\n  @ViewChild(MatPaginator) paginator!: MatPaginator;\n  @ViewChild(MatSort) sort!: MatSort;\n\n  searchForm: FormGroup;\n  displayedColumns: string[] = ['id', 'invoiceNumber', 'amount', 'status', 'dateIssued', 'dueDate', 'actions'];\n  dataSource = new MatTableDataSource<CreditNoteData>();\n  isLoading = false;\n  selectedTabIndex = 0;\n\n  // Sample data\n  creditNotes: CreditNoteData[] = [\n    {\n      id: 'CN-2024-001',\n      invoiceNumber: 'INV-2024-001',\n      amount: 15000.00,\n      currency: 'INR',\n      status: 'Approved',\n      dateIssued: new Date('2024-01-15'),\n      dueDate: new Date('2024-02-15'),\n      reason: 'Product quality issue - glass panels damaged',\n      customerName: 'ABC Construction Ltd.'\n    },\n    {\n      id: 'CN-2024-002',\n      invoiceNumber: 'INV-2024-002',\n      amount: 8500.00,\n      currency: 'INR',\n      status: 'Pending',\n      dateIssued: new Date('2024-01-18'),\n      dueDate: new Date('2024-02-18'),\n      reason: 'Delivery delay compensation',\n      customerName: 'XYZ Builders Pvt. Ltd.'\n    },\n    {\n      id: 'CN-2024-003',\n      invoiceNumber: 'INV-2024-003',\n      amount: 22000.00,\n      currency: 'INR',\n      status: 'Processing',\n      dateIssued: new Date('2024-01-20'),\n      dueDate: new Date('2024-02-20'),\n      reason: 'Incorrect specifications delivered',\n      customerName: 'Modern Glass Solutions'\n    },\n    {\n      id: 'CN-2024-004',\n      invoiceNumber: 'INV-2024-004',\n      amount: 5200.00,\n      currency: 'INR',\n      status: 'Rejected',\n      dateIssued: new Date('2024-01-12'),\n      dueDate: new Date('2024-02-12'),\n      reason: 'Installation service issues',\n      customerName: 'Premium Interiors'\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router\n  ) {\n    this.searchForm = this.formBuilder.group({\n      searchTerm: [''],\n      dateFrom: [''],\n      dateTo: [''],\n      status: ['']\n    });\n  }\n\n  ngOnInit() {\n    this.dataSource.data = this.creditNotes;\n  }\n\n  ngAfterViewInit() {\n    this.dataSource.paginator = this.paginator;\n    this.dataSource.sort = this.sort;\n  }\n\n  onSearch() {\n    const searchTerm = this.searchForm.get('searchTerm')?.value;\n    const status = this.searchForm.get('status')?.value;\n    \n    this.isLoading = true;\n    \n    // Simulate search delay\n    setTimeout(() => {\n      let filteredData = this.creditNotes;\n      \n      if (searchTerm) {\n        filteredData = filteredData.filter(item => \n          item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          item.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          item.customerName.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n      }\n      \n      if (status) {\n        filteredData = filteredData.filter(item => \n          item.status.toLowerCase() === status.toLowerCase()\n        );\n      }\n      \n      this.dataSource.data = filteredData;\n      this.isLoading = false;\n    }, 1000);\n  }\n\n  clearSearch() {\n    this.searchForm.reset();\n    this.dataSource.data = this.creditNotes;\n  }\n\n  getStatusColor(status: string): string {\n    switch (status.toLowerCase()) {\n      case 'approved':\n        return 'success';\n      case 'processing':\n        return 'primary';\n      case 'pending':\n        return 'warning';\n      case 'rejected':\n        return 'danger';\n      default:\n        return 'medium';\n    }\n  }\n\n  getTotalAmount(): number {\n    return this.dataSource.filteredData.reduce((total, item) => total + item.amount, 0);\n  }\n\n  getApprovedAmount(): number {\n    return this.dataSource.filteredData\n      .filter(item => item.status.toLowerCase() === 'approved')\n      .reduce((total, item) => total + item.amount, 0);\n  }\n\n  getPendingAmount(): number {\n    return this.dataSource.filteredData\n      .filter(item => item.status.toLowerCase() === 'pending')\n      .reduce((total, item) => total + item.amount, 0);\n  }\n\n  viewDetails(creditNote: CreditNoteData) {\n    // Navigate to credit note details or open modal\n    console.log('View details for:', creditNote);\n  }\n\n  downloadPDF(creditNote: CreditNoteData) {\n    // Implement PDF download functionality\n    console.log('Download PDF for:', creditNote);\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n\n  refreshData() {\n    this.isLoading = true;\n    \n    // Simulate data refresh\n    setTimeout(() => {\n      this.dataSource.data = [...this.creditNotes];\n      this.isLoading = false;\n    }, 1500);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,EAAUC,SAAS,QAAQ,eAAe;AAG5D,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAmBzC,IAAMC,cAAc,GAApB,MAAMA,cAAc;EA2DzBC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAvDhB,KAAAC,gBAAgB,GAAa,CAAC,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,CAAC;IAC5G,KAAAC,UAAU,GAAG,IAAIR,kBAAkB,EAAkB;IACrD,KAAAS,SAAS,GAAG,KAAK;IACjB,KAAAC,gBAAgB,GAAG,CAAC;IAEpB;IACA,KAAAC,WAAW,GAAqB,CAC9B;MACEC,EAAE,EAAE,aAAa;MACjBC,aAAa,EAAE,cAAc;MAC7BC,MAAM,EAAE,QAAQ;MAChBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MAClCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MAC/BE,MAAM,EAAE,8CAA8C;MACtDC,YAAY,EAAE;KACf,EACD;MACET,EAAE,EAAE,aAAa;MACjBC,aAAa,EAAE,cAAc;MAC7BC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MAClCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MAC/BE,MAAM,EAAE,6BAA6B;MACrCC,YAAY,EAAE;KACf,EACD;MACET,EAAE,EAAE,aAAa;MACjBC,aAAa,EAAE,cAAc;MAC7BC,MAAM,EAAE,QAAQ;MAChBC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,YAAY;MACpBC,UAAU,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MAClCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MAC/BE,MAAM,EAAE,oCAAoC;MAC5CC,YAAY,EAAE;KACf,EACD;MACET,EAAE,EAAE,aAAa;MACjBC,aAAa,EAAE,cAAc;MAC7BC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE,KAAK;MACfC,MAAM,EAAE,UAAU;MAClBC,UAAU,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MAClCC,OAAO,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MAC/BE,MAAM,EAAE,6BAA6B;MACrCC,YAAY,EAAE;KACf,CACF;IAMC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACjB,WAAW,CAACkB,KAAK,CAAC;MACvCC,UAAU,EAAE,CAAC,EAAE,CAAC;MAChBC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZV,MAAM,EAAE,CAAC,EAAE;KACZ,CAAC;EACJ;EAEAW,QAAQA,CAAA;IACN,IAAI,CAACnB,UAAU,CAACoB,IAAI,GAAG,IAAI,CAACjB,WAAW;EACzC;EAEAkB,eAAeA,CAAA;IACb,IAAI,CAACrB,UAAU,CAACsB,SAAS,GAAG,IAAI,CAACA,SAAS;IAC1C,IAAI,CAACtB,UAAU,CAACuB,IAAI,GAAG,IAAI,CAACA,IAAI;EAClC;EAEAC,QAAQA,CAAA;IACN,MAAMR,UAAU,GAAG,IAAI,CAACF,UAAU,CAACW,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAC3D,MAAMlB,MAAM,GAAG,IAAI,CAACM,UAAU,CAACW,GAAG,CAAC,QAAQ,CAAC,EAAEC,KAAK;IAEnD,IAAI,CAACzB,SAAS,GAAG,IAAI;IAErB;IACA0B,UAAU,CAAC,MAAK;MACd,IAAIC,YAAY,GAAG,IAAI,CAACzB,WAAW;MAEnC,IAAIa,UAAU,EAAE;QACdY,YAAY,GAAGA,YAAY,CAACC,MAAM,CAACC,IAAI,IACrCA,IAAI,CAAC1B,EAAE,CAAC2B,WAAW,EAAE,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,EAAE,CAAC,IACxDD,IAAI,CAACzB,aAAa,CAAC0B,WAAW,EAAE,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,EAAE,CAAC,IACnED,IAAI,CAACjB,YAAY,CAACkB,WAAW,EAAE,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,EAAE,CAAC,CACnE;;MAGH,IAAIvB,MAAM,EAAE;QACVoB,YAAY,GAAGA,YAAY,CAACC,MAAM,CAACC,IAAI,IACrCA,IAAI,CAACtB,MAAM,CAACuB,WAAW,EAAE,KAAKvB,MAAM,CAACuB,WAAW,EAAE,CACnD;;MAGH,IAAI,CAAC/B,UAAU,CAACoB,IAAI,GAAGQ,YAAY;MACnC,IAAI,CAAC3B,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAgC,WAAWA,CAAA;IACT,IAAI,CAACnB,UAAU,CAACoB,KAAK,EAAE;IACvB,IAAI,CAAClC,UAAU,CAACoB,IAAI,GAAG,IAAI,CAACjB,WAAW;EACzC;EAEAgC,cAAcA,CAAC3B,MAAc;IAC3B,QAAQA,MAAM,CAACuB,WAAW,EAAE;MAC1B,KAAK,UAAU;QACb,OAAO,SAAS;MAClB,KAAK,YAAY;QACf,OAAO,SAAS;MAClB,KAAK,SAAS;QACZ,OAAO,SAAS;MAClB,KAAK,UAAU;QACb,OAAO,QAAQ;MACjB;QACE,OAAO,QAAQ;;EAErB;EAEAK,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACpC,UAAU,CAAC4B,YAAY,CAACS,MAAM,CAAC,CAACC,KAAK,EAAER,IAAI,KAAKQ,KAAK,GAAGR,IAAI,CAACxB,MAAM,EAAE,CAAC,CAAC;EACrF;EAEAiC,iBAAiBA,CAAA;IACf,OAAO,IAAI,CAACvC,UAAU,CAAC4B,YAAY,CAChCC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACtB,MAAM,CAACuB,WAAW,EAAE,KAAK,UAAU,CAAC,CACxDM,MAAM,CAAC,CAACC,KAAK,EAAER,IAAI,KAAKQ,KAAK,GAAGR,IAAI,CAACxB,MAAM,EAAE,CAAC,CAAC;EACpD;EAEAkC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACxC,UAAU,CAAC4B,YAAY,CAChCC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACtB,MAAM,CAACuB,WAAW,EAAE,KAAK,SAAS,CAAC,CACvDM,MAAM,CAAC,CAACC,KAAK,EAAER,IAAI,KAAKQ,KAAK,GAAGR,IAAI,CAACxB,MAAM,EAAE,CAAC,CAAC;EACpD;EAEAmC,WAAWA,CAACC,UAA0B;IACpC;IACAC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,UAAU,CAAC;EAC9C;EAEAG,WAAWA,CAACH,UAA0B;IACpC;IACAC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,UAAU,CAAC;EAC9C;EAEAI,MAAMA,CAAA;IACJ,IAAI,CAAChD,MAAM,CAACiD,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC/C,SAAS,GAAG,IAAI;IAErB;IACA0B,UAAU,CAAC,MAAK;MACd,IAAI,CAAC3B,UAAU,CAACoB,IAAI,GAAG,CAAC,GAAG,IAAI,CAACjB,WAAW,CAAC;MAC5C,IAAI,CAACF,SAAS,GAAG,KAAK;IACxB,CAAC,EAAE,IAAI,CAAC;EACV;CACD;AAtK0BgD,UAAA,EAAxB1D,SAAS,CAACE,YAAY,CAAC,C,gDAA0B;AAC9BwD,UAAA,EAAnB1D,SAAS,CAACG,OAAO,CAAC,C,2CAAgB;AAHxBC,cAAc,GAAAsD,UAAA,EAL1B3D,SAAS,CAAC;EACT4D,QAAQ,EAAE,gBAAgB;EAC1BC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,wBAAwB;CACrC,CAAC,C,EACWzD,cAAc,CAwK1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}