{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-a1a47f01.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\nconst avatarIosCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}\";\nconst IonAvatarIosStyle0 = avatarIosCss;\nconst avatarMdCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}\";\nconst IonAvatarMdStyle0 = avatarMdCss;\nconst Avatar = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return h(Host, {\n      key: 'f6014b524497bb18ae919ba6f6928407310d6870',\n      class: getIonMode(this)\n    }, h(\"slot\", {\n      key: '192ff4a8e10c0b0a4a2ed795ff2675afa8b23449'\n    }));\n  }\n};\nAvatar.style = {\n  ios: IonAvatarIosStyle0,\n  md: IonAvatarMdStyle0\n};\nconst badgeIosCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}\";\nconst IonBadgeIosStyle0 = badgeIosCss;\nconst badgeMdCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}\";\nconst IonBadgeMdStyle0 = badgeMdCss;\nconst Badge = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.color = undefined;\n  }\n  render() {\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: '22d41ceefb76f40dfbf739fd71483f1272a45858',\n      class: createColorClasses(this.color, {\n        [mode]: true\n      })\n    }, h(\"slot\", {\n      key: 'e7e65463bac5903971a8f9f6be55515f42b81a83'\n    }));\n  }\n};\nBadge.style = {\n  ios: IonBadgeIosStyle0,\n  md: IonBadgeMdStyle0\n};\nconst thumbnailCss = \":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}\";\nconst IonThumbnailStyle0 = thumbnailCss;\nconst Thumbnail = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n  }\n  render() {\n    return h(Host, {\n      key: 'd2667635930e4c0896805f452357e7dc9086bc72',\n      class: getIonMode(this)\n    }, h(\"slot\", {\n      key: '66eb1487f3da4da2ef71b812a8d0f0fe884c7d81'\n    }));\n  }\n};\nThumbnail.style = IonThumbnailStyle0;\nexport { Avatar as ion_avatar, Badge as ion_badge, Thumbnail as ion_thumbnail };", "map": {"version": 3, "names": ["r", "registerInstance", "h", "H", "Host", "b", "getIonMode", "c", "createColorClasses", "avatarIosCss", "IonAvatarIosStyle0", "avatarMdCss", "IonAvatarMdStyle0", "Avatar", "constructor", "hostRef", "render", "key", "class", "style", "ios", "md", "badgeIosCss", "IonBadgeIosStyle0", "badgeMdCss", "IonBadgeMdStyle0", "Badge", "color", "undefined", "mode", "thumbnailCss", "IonThumbnailStyle0", "<PERSON><PERSON><PERSON><PERSON>", "ion_avatar", "ion_badge", "ion_thumbnail"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/ion-avatar_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, h, H as Host } from './index-a1a47f01.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createColorClasses } from './theme-01f3f29c.js';\n\nconst avatarIosCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:48px;height:48px}\";\nconst IonAvatarIosStyle0 = avatarIosCss;\n\nconst avatarMdCss = \":host{border-radius:var(--border-radius);display:block}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}:host{--border-radius:50%;width:64px;height:64px}\";\nconst IonAvatarMdStyle0 = avatarMdCss;\n\nconst Avatar = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: 'f6014b524497bb18ae919ba6f6928407310d6870', class: getIonMode(this) }, h(\"slot\", { key: '192ff4a8e10c0b0a4a2ed795ff2675afa8b23449' })));\n    }\n};\nAvatar.style = {\n    ios: IonAvatarIosStyle0,\n    md: IonAvatarMdStyle0\n};\n\nconst badgeIosCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{border-radius:10px;font-size:max(13px, 0.8125rem)}\";\nconst IonBadgeIosStyle0 = badgeIosCss;\n\nconst badgeMdCss = \":host{--background:var(--ion-color-primary, #3880ff);--color:var(--ion-color-primary-contrast, #fff);--padding-top:3px;--padding-end:8px;--padding-bottom:3px;--padding-start:8px;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:inline-block;min-width:10px;background:var(--background);color:var(--color);font-family:var(--ion-font-family, inherit);font-size:0.8125rem;font-weight:bold;line-height:1;text-align:center;white-space:nowrap;contain:content;vertical-align:baseline}:host(.ion-color){background:var(--ion-color-base);color:var(--ion-color-contrast)}:host(:empty){display:none}:host{--padding-top:3px;--padding-end:4px;--padding-bottom:4px;--padding-start:4px;border-radius:4px}\";\nconst IonBadgeMdStyle0 = badgeMdCss;\n\nconst Badge = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.color = undefined;\n    }\n    render() {\n        const mode = getIonMode(this);\n        return (h(Host, { key: '22d41ceefb76f40dfbf739fd71483f1272a45858', class: createColorClasses(this.color, {\n                [mode]: true,\n            }) }, h(\"slot\", { key: 'e7e65463bac5903971a8f9f6be55515f42b81a83' })));\n    }\n};\nBadge.style = {\n    ios: IonBadgeIosStyle0,\n    md: IonBadgeMdStyle0\n};\n\nconst thumbnailCss = \":host{--size:48px;--border-radius:0;border-radius:var(--border-radius);display:block;width:var(--size);height:var(--size)}::slotted(ion-img),::slotted(img){border-radius:var(--border-radius);width:100%;height:100%;-o-object-fit:cover;object-fit:cover;overflow:hidden}\";\nconst IonThumbnailStyle0 = thumbnailCss;\n\nconst Thumbnail = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n    }\n    render() {\n        return (h(Host, { key: 'd2667635930e4c0896805f452357e7dc9086bc72', class: getIonMode(this) }, h(\"slot\", { key: '66eb1487f3da4da2ef71b812a8d0f0fe884c7d81' })));\n    }\n};\nThumbnail.style = IonThumbnailStyle0;\n\nexport { Avatar as ion_avatar, Badge as ion_badge, Thumbnail as ion_thumbnail };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,QAAQ,qBAAqB;AACzE,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAC5D,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AAE7D,MAAMC,YAAY,GAAG,2PAA2P;AAChR,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,WAAW,GAAG,2PAA2P;AAC/Q,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,MAAM,GAAG,MAAM;EACjBC,WAAWA,CAACC,OAAO,EAAE;IACjBd,gBAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQd,CAAC,CAACE,IAAI,EAAE;MAAEa,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEZ,UAAU,CAAC,IAAI;IAAE,CAAC,EAAEJ,CAAC,CAAC,MAAM,EAAE;MAAEe,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACjK;AACJ,CAAC;AACDJ,MAAM,CAACM,KAAK,GAAG;EACXC,GAAG,EAAEV,kBAAkB;EACvBW,EAAE,EAAET;AACR,CAAC;AAED,MAAMU,WAAW,GAAG,q4BAAq4B;AACz5B,MAAMC,iBAAiB,GAAGD,WAAW;AAErC,MAAME,UAAU,GAAG,k7BAAk7B;AACr8B,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,KAAK,GAAG,MAAM;EAChBZ,WAAWA,CAACC,OAAO,EAAE;IACjBd,gBAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;IAC/B,IAAI,CAACY,KAAK,GAAGC,SAAS;EAC1B;EACAZ,MAAMA,CAAA,EAAG;IACL,MAAMa,IAAI,GAAGvB,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQJ,CAAC,CAACE,IAAI,EAAE;MAAEa,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEV,kBAAkB,CAAC,IAAI,CAACmB,KAAK,EAAE;QACjG,CAACE,IAAI,GAAG;MACZ,CAAC;IAAE,CAAC,EAAE3B,CAAC,CAAC,MAAM,EAAE;MAAEe,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC7E;AACJ,CAAC;AACDS,KAAK,CAACP,KAAK,GAAG;EACVC,GAAG,EAAEG,iBAAiB;EACtBF,EAAE,EAAEI;AACR,CAAC;AAED,MAAMK,YAAY,GAAG,6QAA6Q;AAClS,MAAMC,kBAAkB,GAAGD,YAAY;AAEvC,MAAME,SAAS,GAAG,MAAM;EACpBlB,WAAWA,CAACC,OAAO,EAAE;IACjBd,gBAAgB,CAAC,IAAI,EAAEc,OAAO,CAAC;EACnC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAQd,CAAC,CAACE,IAAI,EAAE;MAAEa,GAAG,EAAE,0CAA0C;MAAEC,KAAK,EAAEZ,UAAU,CAAC,IAAI;IAAE,CAAC,EAAEJ,CAAC,CAAC,MAAM,EAAE;MAAEe,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EACjK;AACJ,CAAC;AACDe,SAAS,CAACb,KAAK,GAAGY,kBAAkB;AAEpC,SAASlB,MAAM,IAAIoB,UAAU,EAAEP,KAAK,IAAIQ,SAAS,EAAEF,SAAS,IAAIG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}