{"ast": null, "code": "import { StatusBar } from '@capacitor/status-bar';\nimport { SplashScreen } from '@capacitor/splash-screen';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@angular/router\";\nexport class AppComponent {\n  constructor(platform, router) {\n    this.platform = platform;\n    this.router = router;\n  }\n  ngOnInit() {\n    this.initializeApp();\n  }\n  initializeApp() {\n    this.platform.ready().then(() => {\n      StatusBar.setStyle({\n        style: 'Default'\n      });\n      SplashScreen.hide();\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 2,\n      vars: 0,\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-app\");\n          i0.ɵɵelement(1, \"ion-router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.IonApp, i1.IonRouterOutlet],\n      styles: [\"\\n\\nion-app[_ngcontent-%COMP%] {\\n  font-family: \\\"Roboto\\\", sans-serif;\\n}\\n\\n\\n\\n.mat-toolbar[_ngcontent-%COMP%] {\\n  background: var(--ion-color-primary);\\n  color: white;\\n}\\n\\n.mat-card[_ngcontent-%COMP%] {\\n  margin: 16px;\\n  border-radius: 8px;\\n}\\n\\n.mat-raised-button[_ngcontent-%COMP%] {\\n  border-radius: 4px;\\n  text-transform: uppercase;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.app-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIiwid2VicGFjazovLy4vLi4vLi4vLi4vLi4vRCUyMGRyaXZlL0FJUy1TTUFSVCUyMENBUkUvQUlTU21hcnRDYXJlX05ldy9BSVNTbWFydENhcmVfTmV3L3NyYy9hcHAvYXBwLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHlCQUFBO0FBQ0E7RUFDRSxpQ0FBQTtBQ0NGOztBREVBLGtDQUFBO0FBQ0E7RUFDRSxvQ0FBQTtFQUNBLFlBQUE7QUNDRjs7QURFQTtFQUNFLFlBQUE7RUFDQSxrQkFBQTtBQ0NGOztBREVBO0VBQ0Usa0JBQUE7RUFDQSx5QkFBQTtFQUNBLGdCQUFBO0FDQ0Y7O0FERUEsc0JBQUE7QUFDQTtFQUNFLGFBQUE7RUFDQSxhQUFBO0VBQ0Esc0JBQUE7QUNDRjs7QURFQTtFQUNFLE9BQUE7RUFDQSxjQUFBO0FDQ0YiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBBcHAgQ29tcG9uZW50IFN0eWxlcyAqL1xuaW9uLWFwcCB7XG4gIGZvbnQtZmFtaWx5OiAnUm9ib3RvJywgc2Fucy1zZXJpZjtcbn1cblxuLyogR2xvYmFsIE1hdGVyaWFsIERlc2lnbiBTdHlsZXMgKi9cbi5tYXQtdG9vbGJhciB7XG4gIGJhY2tncm91bmQ6IHZhcigtLWlvbi1jb2xvci1wcmltYXJ5KTtcbiAgY29sb3I6IHdoaXRlO1xufVxuXG4ubWF0LWNhcmQge1xuICBtYXJnaW46IDE2cHg7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbn1cblxuLm1hdC1yYWlzZWQtYnV0dG9uIHtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4vKiBDdXN0b20gQXBwIFN0eWxlcyAqL1xuLmFwcC1jb250YWluZXIge1xuICBoZWlnaHQ6IDEwMHZoO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xufVxuXG4ubWFpbi1jb250ZW50IHtcbiAgZmxleDogMTtcbiAgb3ZlcmZsb3c6IGF1dG87XG59XG4iLCIvKiBBcHAgQ29tcG9uZW50IFN0eWxlcyAqL1xuaW9uLWFwcCB7XG4gIGZvbnQtZmFtaWx5OiBcIlJvYm90b1wiLCBzYW5zLXNlcmlmO1xufVxuXG4vKiBHbG9iYWwgTWF0ZXJpYWwgRGVzaWduIFN0eWxlcyAqL1xuLm1hdC10b29sYmFyIHtcbiAgYmFja2dyb3VuZDogdmFyKC0taW9uLWNvbG9yLXByaW1hcnkpO1xuICBjb2xvcjogd2hpdGU7XG59XG5cbi5tYXQtY2FyZCB7XG4gIG1hcmdpbjogMTZweDtcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xufVxuXG4ubWF0LXJhaXNlZC1idXR0b24ge1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbi8qIEN1c3RvbSBBcHAgU3R5bGVzICovXG4uYXBwLWNvbnRhaW5lciB7XG4gIGhlaWdodDogMTAwdmg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG59XG5cbi5tYWluLWNvbnRlbnQge1xuICBmbGV4OiAxO1xuICBvdmVyZmxvdzogYXV0bztcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["StatusBar", "SplashScreen", "AppComponent", "constructor", "platform", "router", "ngOnInit", "initializeApp", "ready", "then", "setStyle", "style", "hide", "i0", "ɵɵdirectiveInject", "i1", "Platform", "i2", "Router", "selectors", "decls", "vars", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\app.component.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { Platform } from '@ionic/angular';\r\nimport { StatusBar, Style } from '@capacitor/status-bar';\r\nimport { SplashScreen } from '@capacitor/splash-screen';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  templateUrl: 'app.component.html',\r\n  styleUrls: ['app.component.scss'],\r\n})\r\nexport class AppComponent implements OnInit {\r\n\r\n  constructor(\r\n    private platform: Platform,\r\n    private router: Router\r\n  ) {}\r\n\r\n  ngOnInit() {\r\n    this.initializeApp();\r\n  }\r\n\r\n  initializeApp() {\r\n    this.platform.ready().then(() => {\r\n      StatusBar.setStyle({ style: 'Default' });\r\n      SplashScreen.hide();\r\n    });\r\n  }\r\n\r\n}\r\n\r\n", "<ion-app>\n  <ion-router-outlet></ion-router-outlet>\n</ion-app>\n"], "mappings": "AAEA,SAASA,SAAS,QAAe,uBAAuB;AACxD,SAASC,YAAY,QAAQ,0BAA0B;;;;AAQvD,OAAM,MAAOC,YAAY;EAEvBC,YACUC,QAAkB,EAClBC,MAAc;IADd,KAAAD,QAAQ,GAARA,QAAQ;IACR,KAAAC,MAAM,GAANA,MAAM;EACb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,aAAa,EAAE;EACtB;EAEAA,aAAaA,CAAA;IACX,IAAI,CAACH,QAAQ,CAACI,KAAK,EAAE,CAACC,IAAI,CAAC,MAAK;MAC9BT,SAAS,CAACU,QAAQ,CAAC;QAAEC,KAAK,EAAE;MAAS,CAAE,CAAC;MACxCV,YAAY,CAACW,IAAI,EAAE;IACrB,CAAC,CAAC;EACJ;;;uBAhBWV,YAAY,EAAAW,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,QAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZhB,YAAY;MAAAiB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCXzBX,EAAA,CAAAa,cAAA,cAAS;UACPb,EAAA,CAAAc,SAAA,wBAAuC;UACzCd,EAAA,CAAAe,YAAA,EAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}