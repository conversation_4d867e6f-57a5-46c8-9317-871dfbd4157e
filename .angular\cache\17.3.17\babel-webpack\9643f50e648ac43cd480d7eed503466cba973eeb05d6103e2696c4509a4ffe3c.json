{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/core\";\nimport * as i12 from \"@angular/material/checkbox\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/toolbar\";\nimport * as i15 from \"@angular/material/radio\";\nfunction RegisterPage_mat_icon_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"2\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"4\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_44_mat_card_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 28);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_44_mat_card_12_Template_mat_card_click_0_listener() {\n      const type_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectComplaintType(type_r2));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-content\", 29)(2, \"div\", 30)(3, \"mat-icon\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 33);\n    i0.ɵɵelement(11, \"mat-radio-button\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const type_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r2.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_3_0.value) === type_r2.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ((tmp_4_0 = ctx_r2.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_4_0.value) === type_r2.value ? \"primary\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r2.icon, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(type_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(type_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", type_r2.value);\n  }\n}\nfunction RegisterPage_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-card\", 23)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 24);\n    i0.ɵɵtext(5, \"report_problem\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Select Complaint Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8, \"Choose the category that best describes your issue\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 25)(11, \"div\", 26);\n    i0.ɵɵtemplate(12, RegisterPage_div_44_mat_card_12_Template, 12, 7, \"mat-card\", 27);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintTypeForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.complaintTypes);\n  }\n}\nfunction RegisterPage_div_45_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 39)(1, \"mat-card-content\", 40)(2, \"div\", 41)(3, \"mat-icon\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-chip\", 44);\n    i0.ɵɵtext(11, \"Selected\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_3_0.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_4_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_4_0.description);\n  }\n}\nfunction RegisterPage_div_45_mat_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 45)(1, \"mat-card-content\", 46);\n    i0.ɵɵelement(2, \"mat-radio-button\", 47);\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const desc_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r2.complaintDescriptionForm.get(\"selectedDescription\")) == null ? null : tmp_3_0.value) === desc_r5.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", desc_r5.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(desc_r5.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(desc_r5.description);\n  }\n}\nfunction RegisterPage_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, RegisterPage_div_45_mat_card_2_Template, 12, 3, \"mat-card\", 36);\n    i0.ɵɵelementStart(3, \"mat-card\", 23)(4, \"mat-card-header\")(5, \"mat-card-title\")(6, \"mat-icon\", 24);\n    i0.ɵɵtext(7, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Select Specific Issue \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n    i0.ɵɵtext(10, \"Choose the description that best matches your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"form\", 25)(13, \"mat-radio-group\", 37);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_45_Template_mat_radio_group_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDescriptionChange($event));\n    });\n    i0.ɵɵtemplate(14, RegisterPage_div_45_mat_card_14_Template, 8, 5, \"mat-card\", 38);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getSelectedComplaintType());\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintDescriptionForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getComplaintDescriptions());\n  }\n}\nfunction RegisterPage_div_46_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.invoiceSearchForm, \"searchTerm\"), \" \");\n  }\n}\nfunction RegisterPage_div_46_div_27_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Search Results (\", ctx_r2.invoiceSearchResults.length, \" found) \");\n  }\n}\nfunction RegisterPage_div_46_div_27_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" All Available Invoices (\", ctx_r2.invoiceSearchResults.length, \" total) \");\n  }\n}\nfunction RegisterPage_div_46_div_27_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_46_div_27_div_5_Template_div_click_0_listener() {\n      const invoice_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectInvoice(invoice_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 66)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 67);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 68);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 69);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const invoice_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(invoice_r8.invoiceNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 5, invoice_r8.invoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(invoice_r8.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", invoice_r8.zone, \" - \", invoice_r8.operatingUnit, \"\");\n  }\n}\nfunction RegisterPage_div_46_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 62)(1, \"h4\");\n    i0.ɵɵtemplate(2, RegisterPage_div_46_div_27_span_2_Template, 2, 1, \"span\", 58)(3, RegisterPage_div_46_div_27_span_3_Template, 2, 1, \"span\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 63);\n    i0.ɵɵtemplate(5, RegisterPage_div_46_div_27_div_5_Template, 11, 8, \"div\", 64);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_2_0.value) && ((tmp_2_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_2_0.value.trim()) !== \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !((tmp_3_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_3_0.value) || ((tmp_3_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_3_0.value.trim()) === \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.invoiceSearchResults);\n  }\n}\nfunction RegisterPage_div_46_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 70)(1, \"p\");\n    i0.ɵɵtext(2, \"No invoices found matching your search criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 71);\n    i0.ɵɵtext(4, \"Try searching with different keywords or clear the search to see all invoices.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterPage_div_46_div_29_mat_card_63_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"mat-form-field\", 90)(2, \"mat-label\");\n    i0.ɵɵtext(3, \"Select Defected BarCode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-select\", 91);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function RegisterPage_div_46_div_29_mat_card_63_div_38_Template_mat_select_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const item_r11 = i0.ɵɵnextContext().$implicit;\n      i0.ɵɵtwoWayBindingSet(item_r11.defectedBarCode, $event) || (item_r11.defectedBarCode = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementStart(5, \"mat-option\", 92);\n    i0.ɵɵtext(6, \"No defect\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-option\", 93);\n    i0.ɵɵtext(8, \"Barcode 1\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-option\", 94);\n    i0.ɵɵtext(10, \"Barcode 2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"mat-option\", 95);\n    i0.ɵɵtext(12, \"Barcode 3\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtwoWayProperty(\"ngModel\", item_r11.defectedBarCode);\n  }\n}\nfunction RegisterPage_div_46_div_29_mat_card_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 82)(1, \"mat-card-header\")(2, \"mat-card-title\", 83);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"div\", 84);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 85)(8, \"div\", 86)(9, \"label\");\n    i0.ɵɵtext(10, \"Thickness(mm):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 87);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 86)(14, \"label\");\n    i0.ɵɵtext(15, \"Width(mm):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 87);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 86)(19, \"label\");\n    i0.ɵɵtext(20, \"Height(mm):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 87);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 86)(24, \"label\");\n    i0.ɵɵtext(25, \"Qty:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 87);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 86)(29, \"label\");\n    i0.ɵɵtext(30, \"CSQM:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 87);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 86)(34, \"label\");\n    i0.ɵɵtext(35, \"No. Of Received Boxes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 87);\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(38, RegisterPage_div_46_div_29_mat_card_63_div_38_Template, 13, 1, \"div\", 88);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r11 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r11.itemCode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", item_r11.description, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(item_r11.thickness);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r11.width);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r11.height);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r11.quantity);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r11.csqm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r11.receivedBoxes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentStep === 4);\n  }\n}\nfunction RegisterPage_div_46_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 72)(1, \"h4\");\n    i0.ɵɵtext(2, \"Selected Invoice Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-card\", 73)(4, \"mat-card-content\")(5, \"div\", 74)(6, \"h5\")(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Customer Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 75)(11, \"div\", 76)(12, \"label\");\n    i0.ɵɵtext(13, \"Invoice Number:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 76)(17, \"label\");\n    i0.ɵɵtext(18, \"Invoice Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 76)(23, \"label\");\n    i0.ɵɵtext(24, \"Customer Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 77)(28, \"label\");\n    i0.ɵɵtext(29, \"Customer Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 76)(33, \"label\");\n    i0.ɵɵtext(34, \"Zone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\");\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 76)(38, \"label\");\n    i0.ɵɵtext(39, \"Operating Unit:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\");\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 77)(43, \"label\");\n    i0.ɵɵtext(44, \"Organization:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 77)(48, \"label\");\n    i0.ɵɵtext(49, \"Bill To Location:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"span\");\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 77)(53, \"label\");\n    i0.ɵɵtext(54, \"Ship To Location:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"span\");\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(57, \"div\", 74)(58, \"h5\")(59, \"mat-icon\");\n    i0.ɵɵtext(60, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(61, \" Item Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(62, \"div\", 78);\n    i0.ɵɵtemplate(63, RegisterPage_div_46_div_29_mat_card_63_Template, 39, 9, \"mat-card\", 79);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"div\", 80)(65, \"button\", 81);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_46_div_29_Template_button_click_65_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearInvoiceSelection());\n    });\n    i0.ɵɵelementStart(66, \"mat-icon\");\n    i0.ɵɵtext(67, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(68, \" Clear Selection \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.invoiceNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(21, 10, ctx_r2.selectedInvoice.invoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.zone);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.operatingUnit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.organization);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.billToLocation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.shipToLocation);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedInvoice.items);\n  }\n}\nfunction RegisterPage_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"form\", 25)(2, \"div\", 50)(3, \"h3\");\n    i0.ɵɵtext(4, \"Search and Select Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 51);\n    i0.ɵɵtext(6, \"Search for your invoice by invoice number or customer name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 52)(8, \"h4\");\n    i0.ɵɵtext(9, \"Complaint Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"div\", 53)(12, \"strong\");\n    i0.ɵɵtext(13, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 53)(16, \"strong\");\n    i0.ɵɵtext(17, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 54)(20, \"mat-form-field\", 55)(21, \"mat-label\");\n    i0.ɵɵtext(22, \"Search Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 56);\n    i0.ɵɵlistener(\"input\", function RegisterPage_div_46_Template_input_input_23_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInvoiceSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-icon\", 57);\n    i0.ɵɵtext(25, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, RegisterPage_div_46_mat_error_26_Template, 2, 1, \"mat-error\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, RegisterPage_div_46_div_27_Template, 6, 3, \"div\", 59)(28, RegisterPage_div_46_div_28_Template, 5, 0, \"div\", 60);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, RegisterPage_div_46_div_29_Template, 69, 13, \"div\", 61);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.invoiceSearchForm);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.label, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r2.getSelectedComplaintDescription()) == null ? null : tmp_3_0.label, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n  }\n}\nfunction RegisterPage_div_47_div_25_div_64_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 130)(1, \"label\");\n    i0.ɵɵtext(2, \"Selected Defected BarCode:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 131);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r13 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r13.defectedBarCode);\n  }\n}\nfunction RegisterPage_div_47_div_25_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 124)(1, \"div\", 125)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 126);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 127)(7, \"div\", 128)(8, \"label\");\n    i0.ɵɵtext(9, \"Thickness(mm):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 128)(13, \"label\");\n    i0.ɵɵtext(14, \"Width(mm):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 128)(18, \"label\");\n    i0.ɵɵtext(19, \"Height(mm):\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 128)(23, \"label\");\n    i0.ɵɵtext(24, \"Qty:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 128)(28, \"label\");\n    i0.ɵɵtext(29, \"CSQM:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 128)(33, \"label\");\n    i0.ɵɵtext(34, \"Received Boxes:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\");\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(37, RegisterPage_div_47_div_25_div_64_div_37_Template, 5, 1, \"div\", 129);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r13 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r13.itemCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", item_r13.description, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(item_r13.thickness);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r13.width);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r13.height);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r13.quantity);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r13.csqm);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(item_r13.receivedBoxes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", item_r13.defectedBarCode);\n  }\n}\nfunction RegisterPage_div_47_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"h5\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"receipt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Complete Invoice Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 117)(6, \"div\", 118)(7, \"h6\")(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Customer Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 119)(12, \"div\", 120)(13, \"label\");\n    i0.ɵɵtext(14, \"Invoice Number:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 120)(18, \"label\");\n    i0.ɵɵtext(19, \"Invoice Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 120)(24, \"label\");\n    i0.ɵɵtext(25, \"Customer Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 121)(29, \"label\");\n    i0.ɵɵtext(30, \"Customer Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 120)(34, \"label\");\n    i0.ɵɵtext(35, \"Zone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\");\n    i0.ɵɵtext(37);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(38, \"div\", 120)(39, \"label\");\n    i0.ɵɵtext(40, \"Operating Unit:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"span\");\n    i0.ɵɵtext(42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 121)(44, \"label\");\n    i0.ɵɵtext(45, \"Organization:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"span\");\n    i0.ɵɵtext(47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 121)(49, \"label\");\n    i0.ɵɵtext(50, \"Bill To Location:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\");\n    i0.ɵɵtext(52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 121)(54, \"label\");\n    i0.ɵɵtext(55, \"Ship To Location:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"span\");\n    i0.ɵɵtext(57);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 118)(59, \"h6\")(60, \"mat-icon\");\n    i0.ɵɵtext(61, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(62, \" Item Information\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 122);\n    i0.ɵɵtemplate(64, RegisterPage_div_47_div_25_div_64_Template, 38, 9, \"div\", 123);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.invoiceNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(22, 10, ctx_r2.selectedInvoice.invoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.zone);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.operatingUnit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.organization);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.billToLocation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.shipToLocation);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedInvoice.items);\n  }\n}\nfunction RegisterPage_div_47_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactPersonName\"), \" \");\n  }\n}\nfunction RegisterPage_div_47_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactNumber\"), \" \");\n  }\n}\nfunction RegisterPage_div_47_div_62_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 141)(1, \"mat-icon\", 142);\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 143)(4, \"span\", 144);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 145);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 146);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_47_div_62_div_16_div_4_Template_button_click_8_listener() {\n      const i_r17 = i0.ɵɵrestoreView(_r16).index;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r17));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r18 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(file_r18.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", (file_r18.size / 1024 / 1024).toFixed(2), \" MB)\");\n  }\n}\nfunction RegisterPage_div_47_div_62_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 138)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 139);\n    i0.ɵɵtemplate(4, RegisterPage_div_47_div_62_div_16_div_4_Template, 11, 2, \"div\", 140);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Selected Files (\", ctx_r2.selectedFiles.length, \"):\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedFiles);\n  }\n}\nfunction RegisterPage_div_47_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 132)(1, \"h5\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Upload Supporting Documents \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 133)(6, \"input\", 134, 0);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_47_div_62_Template_input_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 135);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_47_div_62_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const fileInput_r15 = i0.ɵɵreference(7);\n      return i0.ɵɵresetView(fileInput_r15.click());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Choose Files \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 136)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, RegisterPage_div_47_div_62_div_16_Template, 5, 2, \"div\", 137);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFiles.length > 0);\n  }\n}\nfunction RegisterPage_div_47_mat_icon_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_47_mat_spinner_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 147);\n  }\n}\nfunction RegisterPage_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"div\", 50)(2, \"h3\");\n    i0.ɵɵtext(3, \"Final Complaint Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 51);\n    i0.ɵɵtext(5, \"Review all information and provide contact details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 96)(7, \"h4\")(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"assignment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Complete Complaint Summary \");\n    i0.ɵɵelementStart(11, \"span\", 97);\n    i0.ɵɵtext(12, \"FINAL REVIEW\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 98)(14, \"div\", 99)(15, \"h5\");\n    i0.ɵɵtext(16, \"Complaint Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 53)(18, \"strong\");\n    i0.ɵɵtext(19, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 53)(22, \"strong\");\n    i0.ɵɵtext(23, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, RegisterPage_div_47_div_25_Template, 65, 13, \"div\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"form\", 25)(27, \"div\", 101)(28, \"h4\")(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Contact Information (Required) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 102)(33, \"div\", 103)(34, \"mat-form-field\", 104)(35, \"mat-label\");\n    i0.ɵɵtext(36, \"Contact Person Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"input\", 105);\n    i0.ɵɵelementStart(38, \"mat-icon\", 57);\n    i0.ɵɵtext(39, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, RegisterPage_div_47_mat_error_40_Template, 2, 1, \"mat-error\", 58);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-form-field\", 104)(42, \"mat-label\");\n    i0.ɵɵtext(43, \"Contact Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"input\", 106);\n    i0.ɵɵelementStart(45, \"mat-icon\", 57);\n    i0.ɵɵtext(46, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, RegisterPage_div_47_mat_error_47_Template, 2, 1, \"mat-error\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"mat-form-field\", 107)(49, \"mat-label\");\n    i0.ɵɵtext(50, \"Additional Comments (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"textarea\", 108);\n    i0.ɵɵelementStart(52, \"mat-icon\", 57);\n    i0.ɵɵtext(53, \"comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"mat-hint\");\n    i0.ɵɵtext(55, \"Optional: Add any additional information that might be helpful\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 109)(57, \"mat-checkbox\", 110)(58, \"strong\");\n    i0.ɵɵtext(59, \"Do you have complaint letters to attach?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"p\", 111);\n    i0.ɵɵtext(61, \"Check this box if you have supporting documents, photos, or letters related to your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(62, RegisterPage_div_47_div_62_Template, 17, 1, \"div\", 112);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 113)(64, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_47_Template_button_click_64_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBackToStep3());\n    });\n    i0.ɵɵelementStart(65, \"mat-icon\");\n    i0.ɵɵtext(66, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \" Change Invoice \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_47_Template_button_click_68_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmitComplaint());\n    });\n    i0.ɵɵtemplate(69, RegisterPage_div_47_mat_icon_69_Template, 2, 0, \"mat-icon\", 58)(70, RegisterPage_div_47_mat_spinner_70_Template, 1, 0, \"mat-spinner\", 116);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_1_0.label, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r2.getSelectedComplaintDescription()) == null ? null : tmp_2_0.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintDetailsForm);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r2.complaintDetailsForm.get(\"hasComplaintLetters\")) == null ? null : tmp_7_0.value);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading || !ctx_r2.complaintDetailsForm.valid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isLoading ? \"Submitting Complaint...\" : \"Submit Complaint\", \" \");\n  }\n}\nexport class RegisterPage {\n  constructor(formBuilder, router, loadingController, toastController) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.isLoading = false;\n    this.selectedFiles = [];\n    this.selectedInvoice = null;\n    this.invoiceSearchResults = [];\n    this.showInvoiceResults = false;\n    this.currentStep = 1;\n    this.complaintTypes = [{\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    }, {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    }, {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    }, {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    }, {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    }, {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }];\n    this.complaintDescriptions = {\n      'glass_quality': [{\n        value: 'scratches',\n        label: 'Scratches on Glass Surface',\n        description: 'Visible scratches or marks on the glass surface'\n      }, {\n        value: 'cracks',\n        label: 'Cracks or Chips',\n        description: 'Cracks, chips, or fractures in the glass'\n      }, {\n        value: 'bubbles',\n        label: 'Air Bubbles',\n        description: 'Air bubbles or inclusions within the glass'\n      }, {\n        value: 'discoloration',\n        label: 'Discoloration',\n        description: 'Color variations or discoloration in the glass'\n      }, {\n        value: 'thickness',\n        label: 'Thickness Issues',\n        description: 'Incorrect thickness or uneven glass thickness'\n      }],\n      'installation': [{\n        value: 'alignment',\n        label: 'Alignment Problems',\n        description: 'Glass not properly aligned during installation'\n      }, {\n        value: 'sealing',\n        label: 'Sealing Issues',\n        description: 'Poor sealing or gaps around the glass'\n      }, {\n        value: 'hardware',\n        label: 'Hardware Problems',\n        description: 'Issues with hinges, handles, or other hardware'\n      }, {\n        value: 'fitting',\n        label: 'Poor Fitting',\n        description: 'Glass does not fit properly in the frame'\n      }, {\n        value: 'damage_during',\n        label: 'Damage During Installation',\n        description: 'Glass damaged during the installation process'\n      }],\n      'delivery_damage': [{\n        value: 'broken_transit',\n        label: 'Broken in Transit',\n        description: 'Glass broken during transportation'\n      }, {\n        value: 'packaging',\n        label: 'Poor Packaging',\n        description: 'Inadequate packaging causing damage'\n      }, {\n        value: 'handling',\n        label: 'Rough Handling',\n        description: 'Damage due to rough handling during delivery'\n      }, {\n        value: 'delayed',\n        label: 'Delayed Delivery',\n        description: 'Delivery was significantly delayed'\n      }, {\n        value: 'wrong_item',\n        label: 'Wrong Item Delivered',\n        description: 'Incorrect glass type or specifications delivered'\n      }],\n      'measurement': [{\n        value: 'wrong_size',\n        label: 'Wrong Size',\n        description: 'Glass delivered in incorrect dimensions'\n      }, {\n        value: 'measurement_error',\n        label: 'Measurement Error',\n        description: 'Error in initial measurements taken'\n      }, {\n        value: 'specification',\n        label: 'Specification Mismatch',\n        description: 'Glass does not match ordered specifications'\n      }, {\n        value: 'template',\n        label: 'Template Issues',\n        description: 'Problems with measurement template or pattern'\n      }],\n      'service': [{\n        value: 'communication',\n        label: 'Poor Communication',\n        description: 'Lack of proper communication from service team'\n      }, {\n        value: 'response_time',\n        label: 'Slow Response Time',\n        description: 'Delayed response to queries or complaints'\n      }, {\n        value: 'unprofessional',\n        label: 'Unprofessional Behavior',\n        description: 'Unprofessional conduct by service personnel'\n      }, {\n        value: 'incomplete_work',\n        label: 'Incomplete Work',\n        description: 'Service work left incomplete or unfinished'\n      }],\n      'billing': [{\n        value: 'wrong_amount',\n        label: 'Incorrect Amount',\n        description: 'Wrong amount charged in the invoice'\n      }, {\n        value: 'missing_details',\n        label: 'Missing Details',\n        description: 'Important details missing from invoice'\n      }, {\n        value: 'duplicate',\n        label: 'Duplicate Billing',\n        description: 'Charged multiple times for the same service'\n      }, {\n        value: 'tax_error',\n        label: 'Tax Calculation Error',\n        description: 'Incorrect tax calculation or application'\n      }]\n    };\n    // Sample invoice data for demonstration\n    this.sampleInvoices = [{\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP',\n      items: [{\n        itemCode: 'FGDGAGA100.36602440LN',\n        description: 'DARK GREY 100-3660x2440 DARK GREY 100',\n        thickness: 10,\n        width: 2440,\n        height: 3660,\n        quantity: 176,\n        csqm: 392.9376,\n        receivedBoxes: 4\n      }, {\n        itemCode: 'FGDGAGA120.36602440LN',\n        description: 'DARK GREY 120-3660x2440 DARK GREY 120',\n        thickness: 12,\n        width: 2440,\n        height: 3660,\n        quantity: 212,\n        csqm: 160.7472,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab',\n      items: [{\n        itemCode: 'FGCGAGA120.36602770LN',\n        description: 'CLEAR GREY 120-3660x2770 CLEAR GREY 120',\n        thickness: 12,\n        width: 2770,\n        height: 3660,\n        quantity: 150,\n        csqm: 278.5420,\n        receivedBoxes: 3\n      }, {\n        itemCode: 'FGTGAGA080.24401830LN',\n        description: 'TINTED GREY 080-2440x1830 TINTED GREY 080',\n        thickness: 8,\n        width: 2440,\n        height: 1830,\n        quantity: 95,\n        csqm: 124.3680,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore',\n      items: [{\n        itemCode: 'FGBGAGA060.18302440LN',\n        description: 'BLUE GREY 060-1830x2440 BLUE GREY 060',\n        thickness: 6,\n        width: 1830,\n        height: 2440,\n        quantity: 88,\n        csqm: 195.2640,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai',\n      items: [{\n        itemCode: 'FGGGAGA100.24403660LN',\n        description: 'GREEN GREY 100-2440x3660 GREEN GREY 100',\n        thickness: 10,\n        width: 2440,\n        height: 3660,\n        quantity: 120,\n        csqm: 267.8880,\n        receivedBoxes: 3\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata',\n      items: [{\n        itemCode: 'FGRGAGA080.18302440LN',\n        description: 'RED GREY 080-1830x2440 RED GREY 080',\n        thickness: 8,\n        width: 1830,\n        height: 2440,\n        quantity: 75,\n        csqm: 133.4400,\n        receivedBoxes: 2\n      }, {\n        itemCode: 'FGWGAGA120.36602440LN',\n        description: 'WHITE GREY 120-3660x2440 WHITE GREY 120',\n        thickness: 12,\n        width: 3660,\n        height: 2440,\n        quantity: 95,\n        csqm: 218.5680,\n        receivedBoxes: 1\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad',\n      items: [{\n        itemCode: 'FGYGAGA060.24401830LN',\n        description: 'YELLOW GREY 060-2440x1830 YELLOW GREY 060',\n        thickness: 6,\n        width: 2440,\n        height: 1830,\n        quantity: 65,\n        csqm: 145.2720,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat',\n      items: [{\n        itemCode: 'FGPGAGA100.36602770LN',\n        description: 'PURPLE GREY 100-3660x2770 PURPLE GREY 100',\n        thickness: 10,\n        width: 3660,\n        height: 2770,\n        quantity: 180,\n        csqm: 364.4520,\n        receivedBoxes: 4\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi',\n      items: [{\n        itemCode: 'FGOGAGA080.24402440LN',\n        description: 'ORANGE GREY 080-2440x2440 ORANGE GREY 080',\n        thickness: 8,\n        width: 2440,\n        height: 2440,\n        quantity: 110,\n        csqm: 195.3760,\n        receivedBoxes: 3\n      }, {\n        itemCode: 'FGSGAGA120.18303660LN',\n        description: 'SILVER GREY 120-1830x3660 SILVER GREY 120',\n        thickness: 12,\n        width: 1830,\n        height: 3660,\n        quantity: 85,\n        csqm: 142.8180,\n        receivedBoxes: 2\n      }]\n    }];\n    this.createForms();\n  }\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n  selectComplaintType(type) {\n    this.complaintTypeForm.patchValue({\n      selectedType: type.value\n    });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n  onDescriptionChange(event) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice => invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase()));\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n  selectInvoice(invoice) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: invoice.invoiceNumber\n    });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: ''\n    });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n  onSubmitComplaint() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.complaintTypeForm.valid && _this.complaintDescriptionForm.valid && _this.selectedInvoice && _this.complaintDetailsForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Registering complaint...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate registration process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          const toast = yield _this.toastController.create({\n            message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n            duration: 4000,\n            color: 'success',\n            position: 'top'\n          });\n          yield toast.present();\n          // Navigate to track page\n          _this.router.navigate(['/track']);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please complete all required steps and fill in all required fields.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({\n        attachedFile: files[0]\n      });\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({\n        attachedFile: null\n      });\n    }\n  }\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n  isStepCompleted(step) {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n  getErrorMessage(form, field) {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function RegisterPage_Factory(t) {\n      return new (t || RegisterPage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.LoadingController), i0.ɵɵdirectiveInject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterPage,\n      selectors: [[\"app-register\"]],\n      decls: 48,\n      vars: 34,\n      consts: [[\"fileInput\", \"\"], [\"color\", \"primary\", 1, \"modern-toolbar\"], [\"mat-icon-button\", \"\", 1, \"back-button\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [1, \"help-icon\"], [1, \"modern-content\"], [1, \"modern-header\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"modern-stepper\"], [1, \"stepper-container\"], [1, \"step-item\"], [1, \"step-circle\"], [\"class\", \"check-icon\", 4, \"ngIf\"], [\"class\", \"step-number\", 4, \"ngIf\"], [1, \"step-label\"], [1, \"step-line\"], [\"class\", \"modern-step-content\", 4, \"ngIf\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"check-icon\"], [1, \"step-number\"], [1, \"modern-step-content\"], [1, \"step-card\"], [1, \"step-icon\"], [3, \"formGroup\"], [1, \"modern-options-grid\"], [\"class\", \"option-card\", \"matRipple\", \"\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"matRipple\", \"\", 1, \"option-card\", 3, \"click\"], [1, \"option-content\"], [1, \"option-icon\"], [3, \"color\"], [1, \"option-text\"], [1, \"option-radio\"], [\"formControlName\", \"selectedType\", \"color\", \"primary\", 3, \"value\"], [1, \"step-layout\"], [\"class\", \"summary-card\", 4, \"ngIf\"], [\"formControlName\", \"selectedDescription\", 1, \"modern-radio-group\", 3, \"change\"], [\"class\", \"radio-option-card\", \"matRipple\", \"\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"summary-content\"], [1, \"summary-icon\"], [\"color\", \"primary\"], [1, \"summary-text\"], [\"color\", \"primary\", \"selected\", \"\"], [\"matRipple\", \"\", 1, \"radio-option-card\"], [1, \"radio-option-content\"], [\"color\", \"primary\", 1, \"radio-button\", 3, \"value\"], [1, \"radio-text\"], [1, \"step-content\"], [1, \"form-section\"], [1, \"section-description\"], [1, \"complaint-summary-display\"], [1, \"summary-item\"], [1, \"search-section\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"formControlName\", \"searchTerm\", \"placeholder\", \"Enter invoice number, customer name, or leave empty to see all\", 3, \"input\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"selected-invoice\", 4, \"ngIf\"], [1, \"search-results\"], [1, \"invoice-list\"], [\"class\", \"invoice-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"invoice-item\", 3, \"click\"], [1, \"invoice-header\"], [1, \"invoice-date\"], [1, \"invoice-customer\"], [1, \"invoice-zone\"], [1, \"no-results\"], [1, \"search-hint\"], [1, \"selected-invoice\"], [1, \"invoice-details-card\"], [1, \"section-header\"], [1, \"invoice-details-grid\"], [1, \"detail-item\"], [1, \"detail-item\", \"full-width\"], [1, \"items-container\"], [\"class\", \"item-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"invoice-actions\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"item-card\"], [1, \"item-code\"], [1, \"item-description\"], [1, \"item-specs-grid\"], [1, \"spec-item\"], [1, \"spec-value\"], [\"class\", \"defect-selection\", 4, \"ngIf\"], [1, \"defect-selection\"], [\"appearance\", \"outline\", 1, \"defect-field\"], [3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [\"value\", \"barcode1\"], [\"value\", \"barcode2\"], [\"value\", \"barcode3\"], [1, \"complete-summary-display\"], [1, \"final-review-badge\"], [1, \"summary-sections\"], [1, \"summary-section\"], [\"class\", \"summary-section\", 4, \"ngIf\"], [1, \"contact-form-section\"], [1, \"contact-form-card\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonName\", \"placeholder\", \"Enter contact person name\"], [\"matInput\", \"\", \"formControlName\", \"contactNumber\", \"placeholder\", \"Enter 10-digit contact number\", \"type\", \"tel\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"comments\", \"rows\", \"3\", \"placeholder\", \"Any additional comments or information\"], [1, \"complaint-letters-section\"], [\"formControlName\", \"hasComplaintLetters\", \"color\", \"primary\"], [1, \"checkbox-hint\"], [\"class\", \"file-upload-section\", 4, \"ngIf\"], [1, \"step-actions\"], [\"mat-button\", \"\", 1, \"back-button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"invoice-details-readonly\"], [1, \"section-header-readonly\"], [1, \"readonly-details-grid\"], [1, \"readonly-item\"], [1, \"readonly-item\", \"full-width\"], [1, \"readonly-items-container\"], [\"class\", \"readonly-item-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"readonly-item-card\"], [1, \"readonly-item-header\"], [1, \"readonly-item-description\"], [1, \"readonly-specs-grid\"], [1, \"readonly-spec\"], [\"class\", \"defect-selection-readonly\", 4, \"ngIf\"], [1, \"defect-selection-readonly\"], [1, \"defect-value\"], [1, \"file-upload-section\"], [1, \"upload-area\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \".pdf,.jpg,.jpeg,.png,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [1, \"upload-hint\"], [\"class\", \"selected-files\", 4, \"ngIf\"], [1, \"selected-files\"], [1, \"file-list\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-icon\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-size\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 1, \"remove-file\", 3, \"click\"], [\"diameter\", \"20\"]],\n      template: function RegisterPage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 1)(1, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_button_click_1_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5, \"Register Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"span\", 4);\n          i0.ɵɵelementStart(7, \"mat-icon\", 5);\n          i0.ɵɵtext(8, \"help_outline\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7)(11, \"h1\", 8);\n          i0.ɵɵtext(12, \"Register New Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\", 9);\n          i0.ɵɵtext(14, \"Complete your complaint in 4 simple steps\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13);\n          i0.ɵɵtemplate(19, RegisterPage_mat_icon_19_Template, 2, 0, \"mat-icon\", 14)(20, RegisterPage_span_20_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵtext(22, \"Type\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(23, \"div\", 17);\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"div\", 13);\n          i0.ɵɵtemplate(26, RegisterPage_mat_icon_26_Template, 2, 0, \"mat-icon\", 14)(27, RegisterPage_span_27_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 16);\n          i0.ɵɵtext(29, \"Description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(30, \"div\", 17);\n          i0.ɵɵelementStart(31, \"div\", 12)(32, \"div\", 13);\n          i0.ɵɵtemplate(33, RegisterPage_mat_icon_33_Template, 2, 0, \"mat-icon\", 14)(34, RegisterPage_span_34_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 16);\n          i0.ɵɵtext(36, \"Invoice\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(37, \"div\", 17);\n          i0.ɵɵelementStart(38, \"div\", 12)(39, \"div\", 13);\n          i0.ɵɵtemplate(40, RegisterPage_mat_icon_40_Template, 2, 0, \"mat-icon\", 14)(41, RegisterPage_span_41_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 16);\n          i0.ɵɵtext(43, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(44, RegisterPage_div_44_Template, 13, 2, \"div\", 18)(45, RegisterPage_div_45_Template, 15, 3, \"div\", 18)(46, RegisterPage_div_46_Template, 30, 7, \"div\", 19)(47, RegisterPage_div_47_Template, 72, 11, \"div\", 19);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(17);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(1))(\"active\", ctx.currentStep === 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(1));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(2))(\"active\", ctx.currentStep === 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(2));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(3))(\"active\", ctx.currentStep === 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(3));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(4));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(4))(\"active\", ctx.currentStep === 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(4));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(4));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(1) && !ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(2) && !ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(3) && !ctx.isStepCompleted(4));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.FormGroupDirective, i1.FormControlName, i5.MatFormField, i5.MatLabel, i5.MatHint, i5.MatError, i5.MatSuffix, i6.MatInput, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatSelect, i11.MatOption, i12.MatCheckbox, i13.MatProgressSpinner, i14.MatToolbar, i15.MatRadioGroup, i15.MatRadioButton, i4.DatePipe],\n      styles: [\".register-content[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n}\\n.register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  color: #999;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.active[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: #e0e0e0;\\n  margin: 0 16px;\\n  margin-bottom: 32px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 32px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0 0 32px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 12px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card.selected[_ngcontent-%COMP%] {\\n  border-color: #1976d2;\\n  background: #e3f2fd;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]   ion-radio[_ngcontent-%COMP%] {\\n  --color: #1976d2;\\n  --color-checked: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: white;\\n  transition: all 0.3s ease;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]:hover {\\n  border-color: #ff9800;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option.mat-radio-checked[_ngcontent-%COMP%] {\\n  border-color: #ff9800;\\n  background: #fff3e0;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%] {\\n  margin-left: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #f3e5f5;\\n  border: 2px solid #9c27b0;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 22px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   .final-review-badge[_ngcontent-%COMP%] {\\n  background: #1565c0;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-left: auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 2px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 20px;\\n  margin-top: 16px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%] {\\n  margin: 16px 0 12px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #c8e6c9;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2e7d32;\\n  font-weight: 600;\\n  font-size: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 11px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 4px;\\n  padding: 8px;\\n  color: #1b5e20;\\n  font-size: 13px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-item-header[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-weight: 600;\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  padding-bottom: 4px;\\n  border-bottom: 1px solid #e8f5e8;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-item-description[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-weight: 500;\\n  font-size: 13px;\\n  margin-bottom: 12px;\\n  background: #f1f8e9;\\n  padding: 6px;\\n  border-radius: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 3px;\\n  padding: 4px 6px;\\n  color: #1b5e20;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding-top: 12px;\\n  border-top: 1px solid #c8e6c9;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 11px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n  display: block;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%]   .defect-value[_ngcontent-%COMP%] {\\n  background: #ffebee;\\n  border: 1px solid #f8bbd9;\\n  border-radius: 4px;\\n  padding: 6px 8px;\\n  color: #c62828;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: inline-block;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"CONTACT DETAILS\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   .invoice-date[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-customer[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-zone[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p.search-hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  font-style: italic;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin: 20px 0 16px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #666;\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  padding: 12px 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1976d2;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-description[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 16px;\\n  padding: 8px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #666;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%]   .spec-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 0.9rem;\\n  padding: 6px 8px;\\n  background-color: #e3f2fd;\\n  border-radius: 4px;\\n  text-align: center;\\n  border: 1px solid #bbdefb;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .defect-selection[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .defect-selection[_ngcontent-%COMP%]   .defect-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e0e0e0;\\n  padding-top: 16px;\\n  text-align: right;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-size: 14px;\\n  font-weight: 500;\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"EDITABLE\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  padding: 16px;\\n  background: #f3e5f5;\\n  border: 1px solid #9c27b0;\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   .checkbox-hint[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 32px;\\n  color: #666;\\n  font-size: 13px;\\n  font-style: italic;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%] {\\n  margin: 24px 0 0 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border: 2px dashed #9c27b0;\\n  border-radius: 8px;\\n  background: #fce4ec;\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  border-color: #9c27b0;\\n  color: #7b1fa2;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(156, 39, 176, 0.04);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n  margin: 8px 0 0 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: white;\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  border: 1px solid #e1bee7;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  border-color: #9c27b0;\\n  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-size[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: space-between;\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  border-color: #ccc;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #999;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);\\n  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);\\n  transform: translateY(-1px);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  color: #999;\\n  box-shadow: none;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n    padding: 24px 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    margin: 0 8px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "RegisterPage_div_44_mat_card_12_Template_mat_card_click_0_listener", "type_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectComplaintType", "ɵɵelement", "ɵɵclassProp", "tmp_3_0", "complaintTypeForm", "get", "value", "ɵɵadvance", "ɵɵproperty", "tmp_4_0", "ɵɵtextInterpolate1", "icon", "ɵɵtextInterpolate", "label", "description", "ɵɵtemplate", "RegisterPage_div_44_mat_card_12_Template", "complaintTypes", "tmp_2_0", "getSelectedComplaintType", "complaintDescriptionForm", "desc_r5", "RegisterPage_div_45_mat_card_2_Template", "RegisterPage_div_45_Template_mat_radio_group_change_13_listener", "$event", "_r4", "onDescriptionChange", "RegisterPage_div_45_mat_card_14_Template", "getComplaintDescriptions", "getErrorMessage", "invoiceSearchForm", "invoiceSearchResults", "length", "RegisterPage_div_46_div_27_div_5_Template_div_click_0_listener", "invoice_r8", "_r7", "selectInvoice", "invoiceNumber", "ɵɵpipeBind2", "invoiceDate", "customerName", "ɵɵtextInterpolate2", "zone", "operatingUnit", "RegisterPage_div_46_div_27_span_2_Template", "RegisterPage_div_46_div_27_span_3_Template", "RegisterPage_div_46_div_27_div_5_Template", "trim", "ɵɵtwoWayListener", "RegisterPage_div_46_div_29_mat_card_63_div_38_Template_mat_select_ngModelChange_4_listener", "_r10", "item_r11", "ɵɵtwoWayBindingSet", "defectedBarCode", "ɵɵtwoWayProperty", "RegisterPage_div_46_div_29_mat_card_63_div_38_Template", "itemCode", "thickness", "width", "height", "quantity", "csqm", "receivedBoxes", "currentStep", "RegisterPage_div_46_div_29_mat_card_63_Template", "RegisterPage_div_46_div_29_Template_button_click_65_listener", "_r9", "clearInvoiceSelection", "selectedInvoice", "customerAddress", "organization", "billToLocation", "shipToLocation", "items", "RegisterPage_div_46_Template_input_input_23_listener", "_r6", "onInvoiceSearch", "RegisterPage_div_46_mat_error_26_Template", "RegisterPage_div_46_div_27_Template", "RegisterPage_div_46_div_28_Template", "RegisterPage_div_46_div_29_Template", "getSelectedComplaintDescription", "invalid", "touched", "showInvoiceResults", "item_r13", "RegisterPage_div_47_div_25_div_64_div_37_Template", "RegisterPage_div_47_div_25_div_64_Template", "complaintDetailsForm", "RegisterPage_div_47_div_62_div_16_div_4_Template_button_click_8_listener", "i_r17", "_r16", "index", "removeFile", "file_r18", "name", "size", "toFixed", "RegisterPage_div_47_div_62_div_16_div_4_Template", "selectedFiles", "RegisterPage_div_47_div_62_Template_input_change_6_listener", "_r14", "onFileSelected", "RegisterPage_div_47_div_62_Template_button_click_8_listener", "fileInput_r15", "ɵɵreference", "click", "RegisterPage_div_47_div_62_div_16_Template", "RegisterPage_div_47_div_25_Template", "RegisterPage_div_47_mat_error_40_Template", "RegisterPage_div_47_mat_error_47_Template", "RegisterPage_div_47_div_62_Template", "RegisterPage_div_47_Template_button_click_64_listener", "_r12", "goBackToStep3", "RegisterPage_div_47_Template_button_click_68_listener", "onSubmitComplaint", "RegisterPage_div_47_mat_icon_69_Template", "RegisterPage_div_47_mat_spinner_70_Template", "tmp_1_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "isLoading", "valid", "RegisterPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "complaintDescriptions", "sampleInvoices", "Date", "createForms", "ngOnInit", "showAllInvoices", "group", "selectedType", "required", "selectedDescription", "searchTerm", "contactPersonName", "<PERSON><PERSON><PERSON><PERSON>", "contactNumber", "pattern", "comments", "hasComplaintLetters", "attachedFile", "type", "patchValue", "setTimeout", "goToStep2", "selected<PERSON><PERSON><PERSON>", "descriptions", "find", "desc", "event", "goToStep3", "goToStep4", "goBackToStep1", "goBackToStep2", "filter", "invoice", "toLowerCase", "includes", "_this", "_asyncToGenerator", "loading", "create", "message", "duration", "present", "dismiss", "toast", "now", "color", "position", "navigate", "files", "target", "Array", "from", "splice", "isStepCompleted", "step", "form", "field", "control", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "goBack", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "LoadingController", "ToastController", "selectors", "decls", "vars", "consts", "template", "RegisterPage_Template", "rf", "ctx", "RegisterPage_Template_button_click_1_listener", "RegisterPage_mat_icon_19_Template", "RegisterPage_span_20_Template", "RegisterPage_mat_icon_26_Template", "RegisterPage_span_27_Template", "RegisterPage_mat_icon_33_Template", "RegisterPage_span_34_Template", "RegisterPage_mat_icon_40_Template", "RegisterPage_span_41_Template", "RegisterPage_div_44_Template", "RegisterPage_div_45_Template", "RegisterPage_div_46_Template", "RegisterPage_div_47_Template"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\n\nexport interface ItemInfo {\n  itemCode: string;\n  description: string;\n  thickness: number;\n  width: number;\n  height: number;\n  quantity: number;\n  csqm: number;\n  receivedBoxes: number;\n  defectedBarCode?: string;\n}\n\nexport interface InvoiceData {\n  invoiceNumber: string;\n  invoiceDate: Date;\n  customerName: string;\n  customerAddress: string;\n  zone: string;\n  operatingUnit: string;\n  organization: string;\n  billToLocation: string;\n  shipToLocation: string;\n  items: ItemInfo[];\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss'],\n})\nexport class RegisterPage implements OnInit {\n\n  complaintTypeForm!: FormGroup;\n  complaintDescriptionForm!: FormGroup;\n  invoiceSearchForm!: FormGroup;\n  complaintDetailsForm!: FormGroup;\n  isLoading = false;\n  selectedFiles: File[] = [];\n  selectedInvoice: InvoiceData | null = null;\n  invoiceSearchResults: InvoiceData[] = [];\n  showInvoiceResults = false;\n  currentStep = 1;\n\n  complaintTypes = [\n    {\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    },\n    {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    },\n    {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    },\n    {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    },\n    {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    },\n    {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }\n  ];\n\n  complaintDescriptions: { [key: string]: any[] } = {\n    'glass_quality': [\n      { value: 'scratches', label: 'Scratches on Glass Surface', description: 'Visible scratches or marks on the glass surface' },\n      { value: 'cracks', label: 'Cracks or Chips', description: 'Cracks, chips, or fractures in the glass' },\n      { value: 'bubbles', label: 'Air Bubbles', description: 'Air bubbles or inclusions within the glass' },\n      { value: 'discoloration', label: 'Discoloration', description: 'Color variations or discoloration in the glass' },\n      { value: 'thickness', label: 'Thickness Issues', description: 'Incorrect thickness or uneven glass thickness' }\n    ],\n    'installation': [\n      { value: 'alignment', label: 'Alignment Problems', description: 'Glass not properly aligned during installation' },\n      { value: 'sealing', label: 'Sealing Issues', description: 'Poor sealing or gaps around the glass' },\n      { value: 'hardware', label: 'Hardware Problems', description: 'Issues with hinges, handles, or other hardware' },\n      { value: 'fitting', label: 'Poor Fitting', description: 'Glass does not fit properly in the frame' },\n      { value: 'damage_during', label: 'Damage During Installation', description: 'Glass damaged during the installation process' }\n    ],\n    'delivery_damage': [\n      { value: 'broken_transit', label: 'Broken in Transit', description: 'Glass broken during transportation' },\n      { value: 'packaging', label: 'Poor Packaging', description: 'Inadequate packaging causing damage' },\n      { value: 'handling', label: 'Rough Handling', description: 'Damage due to rough handling during delivery' },\n      { value: 'delayed', label: 'Delayed Delivery', description: 'Delivery was significantly delayed' },\n      { value: 'wrong_item', label: 'Wrong Item Delivered', description: 'Incorrect glass type or specifications delivered' }\n    ],\n    'measurement': [\n      { value: 'wrong_size', label: 'Wrong Size', description: 'Glass delivered in incorrect dimensions' },\n      { value: 'measurement_error', label: 'Measurement Error', description: 'Error in initial measurements taken' },\n      { value: 'specification', label: 'Specification Mismatch', description: 'Glass does not match ordered specifications' },\n      { value: 'template', label: 'Template Issues', description: 'Problems with measurement template or pattern' }\n    ],\n    'service': [\n      { value: 'communication', label: 'Poor Communication', description: 'Lack of proper communication from service team' },\n      { value: 'response_time', label: 'Slow Response Time', description: 'Delayed response to queries or complaints' },\n      { value: 'unprofessional', label: 'Unprofessional Behavior', description: 'Unprofessional conduct by service personnel' },\n      { value: 'incomplete_work', label: 'Incomplete Work', description: 'Service work left incomplete or unfinished' }\n    ],\n    'billing': [\n      { value: 'wrong_amount', label: 'Incorrect Amount', description: 'Wrong amount charged in the invoice' },\n      { value: 'missing_details', label: 'Missing Details', description: 'Important details missing from invoice' },\n      { value: 'duplicate', label: 'Duplicate Billing', description: 'Charged multiple times for the same service' },\n      { value: 'tax_error', label: 'Tax Calculation Error', description: 'Incorrect tax calculation or application' }\n    ]\n  };\n\n  // Sample invoice data for demonstration\n  sampleInvoices: InvoiceData[] = [\n    {\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP',\n      items: [\n        {\n          itemCode: 'FGDGAGA100.36602440LN',\n          description: 'DARK GREY 100-3660x2440 DARK GREY 100',\n          thickness: 10,\n          width: 2440,\n          height: 3660,\n          quantity: 176,\n          csqm: 392.9376,\n          receivedBoxes: 4\n        },\n        {\n          itemCode: 'FGDGAGA120.36602440LN',\n          description: 'DARK GREY 120-3660x2440 DARK GREY 120',\n          thickness: 12,\n          width: 2440,\n          height: 3660,\n          quantity: 212,\n          csqm: 160.7472,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab',\n      items: [\n        {\n          itemCode: 'FGCGAGA120.36602770LN',\n          description: 'CLEAR GREY 120-3660x2770 CLEAR GREY 120',\n          thickness: 12,\n          width: 2770,\n          height: 3660,\n          quantity: 150,\n          csqm: 278.5420,\n          receivedBoxes: 3\n        },\n        {\n          itemCode: 'FGTGAGA080.24401830LN',\n          description: 'TINTED GREY 080-2440x1830 TINTED GREY 080',\n          thickness: 8,\n          width: 2440,\n          height: 1830,\n          quantity: 95,\n          csqm: 124.3680,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore',\n      items: [\n        {\n          itemCode: 'FGBGAGA060.18302440LN',\n          description: 'BLUE GREY 060-1830x2440 BLUE GREY 060',\n          thickness: 6,\n          width: 1830,\n          height: 2440,\n          quantity: 88,\n          csqm: 195.2640,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai',\n      items: [\n        {\n          itemCode: 'FGGGAGA100.24403660LN',\n          description: 'GREEN GREY 100-2440x3660 GREEN GREY 100',\n          thickness: 10,\n          width: 2440,\n          height: 3660,\n          quantity: 120,\n          csqm: 267.8880,\n          receivedBoxes: 3\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata',\n      items: [\n        {\n          itemCode: 'FGRGAGA080.18302440LN',\n          description: 'RED GREY 080-1830x2440 RED GREY 080',\n          thickness: 8,\n          width: 1830,\n          height: 2440,\n          quantity: 75,\n          csqm: 133.4400,\n          receivedBoxes: 2\n        },\n        {\n          itemCode: 'FGWGAGA120.36602440LN',\n          description: 'WHITE GREY 120-3660x2440 WHITE GREY 120',\n          thickness: 12,\n          width: 3660,\n          height: 2440,\n          quantity: 95,\n          csqm: 218.5680,\n          receivedBoxes: 1\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad',\n      items: [\n        {\n          itemCode: 'FGYGAGA060.24401830LN',\n          description: 'YELLOW GREY 060-2440x1830 YELLOW GREY 060',\n          thickness: 6,\n          width: 2440,\n          height: 1830,\n          quantity: 65,\n          csqm: 145.2720,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat',\n      items: [\n        {\n          itemCode: 'FGPGAGA100.36602770LN',\n          description: 'PURPLE GREY 100-3660x2770 PURPLE GREY 100',\n          thickness: 10,\n          width: 3660,\n          height: 2770,\n          quantity: 180,\n          csqm: 364.4520,\n          receivedBoxes: 4\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi',\n      items: [\n        {\n          itemCode: 'FGOGAGA080.24402440LN',\n          description: 'ORANGE GREY 080-2440x2440 ORANGE GREY 080',\n          thickness: 8,\n          width: 2440,\n          height: 2440,\n          quantity: 110,\n          csqm: 195.3760,\n          receivedBoxes: 3\n        },\n        {\n          itemCode: 'FGSGAGA120.18303660LN',\n          description: 'SILVER GREY 120-1830x3660 SILVER GREY 120',\n          thickness: 12,\n          width: 1830,\n          height: 3660,\n          quantity: 85,\n          csqm: 142.8180,\n          receivedBoxes: 2\n        }\n      ]\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController\n  ) {\n    this.createForms();\n  }\n\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n\n  selectComplaintType(type: any) {\n    this.complaintTypeForm.patchValue({ selectedType: type.value });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n\n  onDescriptionChange(event: any) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice =>\n          invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n\n  selectInvoice(invoice: InvoiceData) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({ searchTerm: invoice.invoiceNumber });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({ searchTerm: '' });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n\n  async onSubmitComplaint() {\n    if (this.complaintTypeForm.valid && this.complaintDescriptionForm.valid && this.selectedInvoice && this.complaintDetailsForm.valid) {\n      this.isLoading = true;\n\n      const loading = await this.loadingController.create({\n        message: 'Registering complaint...',\n        duration: 3000\n      });\n\n      await loading.present();\n\n      // Simulate registration process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n\n        const toast = await this.toastController.create({\n          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n          duration: 4000,\n          color: 'success',\n          position: 'top'\n        });\n        await toast.present();\n\n        // Navigate to track page\n        this.router.navigate(['/track']);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please complete all required steps and fill in all required fields.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({ attachedFile: files[0] });\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({ attachedFile: null });\n    }\n  }\n\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n\n  getErrorMessage(form: FormGroup, field: string): string {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n}\n", "<!-- Modern Material Design Header -->\n<mat-toolbar color=\"primary\" class=\"modern-toolbar\">\n  <button mat-icon-button (click)=\"goBack()\" class=\"back-button\">\n    <mat-icon>arrow_back</mat-icon>\n  </button>\n  <span class=\"toolbar-title\">Register Complaint</span>\n  <span class=\"spacer\"></span>\n  <mat-icon class=\"help-icon\">help_outline</mat-icon>\n</mat-toolbar>\n\n<!-- Modern Content Container -->\n<div class=\"modern-content\">\n  <!-- Compact Header -->\n  <div class=\"modern-header\">\n    <h1 class=\"page-title\">Register New Complaint</h1>\n    <p class=\"page-subtitle\">Complete your complaint in 4 simple steps</p>\n  </div>\n\n  <!-- Modern Horizontal Stepper -->\n  <div class=\"modern-stepper\">\n    <div class=\"stepper-container\">\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(1)\" [class.active]=\"currentStep === 1\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(1)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(1)\" class=\"step-number\">1</span>\n        </div>\n        <div class=\"step-label\">Type</div>\n      </div>\n\n      <div class=\"step-line\" [class.completed]=\"isStepCompleted(2)\"></div>\n\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(2)\" [class.active]=\"currentStep === 2\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(2)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(2)\" class=\"step-number\">2</span>\n        </div>\n        <div class=\"step-label\">Description</div>\n      </div>\n\n      <div class=\"step-line\" [class.completed]=\"isStepCompleted(3)\"></div>\n\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(3)\" [class.active]=\"currentStep === 3\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(3)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(3)\" class=\"step-number\">3</span>\n        </div>\n        <div class=\"step-label\">Invoice</div>\n      </div>\n\n      <div class=\"step-line\" [class.completed]=\"isStepCompleted(4)\"></div>\n\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(4)\" [class.active]=\"currentStep === 4\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(4)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(4)\" class=\"step-number\">4</span>\n        </div>\n        <div class=\"step-label\">Submit</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modern Step 1: Complaint Type Selection -->\n  <div class=\"modern-step-content\" *ngIf=\"!isStepCompleted(1)\">\n    <mat-card class=\"step-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon class=\"step-icon\">report_problem</mat-icon>\n          Select Complaint Type\n        </mat-card-title>\n        <mat-card-subtitle>Choose the category that best describes your issue</mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"complaintTypeForm\">\n          <div class=\"modern-options-grid\">\n            <mat-card\n              class=\"option-card\"\n              *ngFor=\"let type of complaintTypes\"\n              [class.selected]=\"complaintTypeForm.get('selectedType')?.value === type.value\"\n              (click)=\"selectComplaintType(type)\"\n              matRipple>\n              <mat-card-content class=\"option-content\">\n                <div class=\"option-icon\">\n                  <mat-icon [color]=\"complaintTypeForm.get('selectedType')?.value === type.value ? 'primary' : ''\">\n                    {{ type.icon }}\n                  </mat-icon>\n                </div>\n                <div class=\"option-text\">\n                  <h3>{{ type.label }}</h3>\n                  <p>{{ type.description }}</p>\n                </div>\n                <div class=\"option-radio\">\n                  <mat-radio-button\n                    [value]=\"type.value\"\n                    formControlName=\"selectedType\"\n                    color=\"primary\">\n                  </mat-radio-button>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Modern Step 2: Complaint Description Selection -->\n  <div class=\"modern-step-content\" *ngIf=\"isStepCompleted(1) && !isStepCompleted(2)\">\n    <div class=\"step-layout\">\n      <!-- Selected Type Summary -->\n      <mat-card class=\"summary-card\" *ngIf=\"getSelectedComplaintType()\">\n        <mat-card-content class=\"summary-content\">\n          <div class=\"summary-icon\">\n            <mat-icon color=\"primary\">{{ getSelectedComplaintType()?.icon }}</mat-icon>\n          </div>\n          <div class=\"summary-text\">\n            <h4>{{ getSelectedComplaintType()?.label }}</h4>\n            <p>{{ getSelectedComplaintType()?.description }}</p>\n          </div>\n          <mat-chip color=\"primary\" selected>Selected</mat-chip>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Description Selection -->\n      <mat-card class=\"step-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon class=\"step-icon\">description</mat-icon>\n            Select Specific Issue\n          </mat-card-title>\n          <mat-card-subtitle>Choose the description that best matches your complaint</mat-card-subtitle>\n        </mat-card-header>\n\n        <mat-card-content>\n          <form [formGroup]=\"complaintDescriptionForm\">\n            <mat-radio-group\n              formControlName=\"selectedDescription\"\n              class=\"modern-radio-group\"\n              (change)=\"onDescriptionChange($event)\">\n              <mat-card\n                class=\"radio-option-card\"\n                *ngFor=\"let desc of getComplaintDescriptions()\"\n                [class.selected]=\"complaintDescriptionForm.get('selectedDescription')?.value === desc.value\"\n                matRipple>\n                <mat-card-content class=\"radio-option-content\">\n                  <mat-radio-button\n                    [value]=\"desc.value\"\n                    color=\"primary\"\n                    class=\"radio-button\">\n                  </mat-radio-button>\n                  <div class=\"radio-text\">\n                    <h4>{{ desc.label }}</h4>\n                    <p>{{ desc.description }}</p>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-radio-group>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n    <!-- Step 3: Invoice Selection -->\n    <div class=\"step-content\" *ngIf=\"isStepCompleted(2) && !isStepCompleted(3)\">\n      <form [formGroup]=\"invoiceSearchForm\">\n        <div class=\"form-section\">\n          <h3>Search and Select Invoice</h3>\n          <p class=\"section-description\">Search for your invoice by invoice number or customer name</p>\n\n          <!-- Complaint Summary Display -->\n          <div class=\"complaint-summary-display\">\n            <h4>Complaint Summary</h4>\n            <div class=\"summary-card\">\n              <div class=\"summary-item\">\n                <strong>Type:</strong> {{ getSelectedComplaintType()?.label }}\n              </div>\n              <div class=\"summary-item\">\n                <strong>Description:</strong> {{ getSelectedComplaintDescription()?.label }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"search-section\">\n            <mat-form-field appearance=\"outline\" class=\"search-field\">\n              <mat-label>Search Invoice</mat-label>\n              <input matInput formControlName=\"searchTerm\" placeholder=\"Enter invoice number, customer name, or leave empty to see all\" (input)=\"onInvoiceSearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n              <mat-error *ngIf=\"invoiceSearchForm.get('searchTerm')?.invalid && invoiceSearchForm.get('searchTerm')?.touched\">\n                {{ getErrorMessage(invoiceSearchForm, 'searchTerm') }}\n              </mat-error>\n            </mat-form-field>\n\n            <div class=\"search-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length > 0\">\n              <h4>\n                <span *ngIf=\"invoiceSearchForm.get('searchTerm')?.value && invoiceSearchForm.get('searchTerm')?.value.trim() !== ''\">\n                  Search Results ({{ invoiceSearchResults.length }} found)\n                </span>\n                <span *ngIf=\"!invoiceSearchForm.get('searchTerm')?.value || invoiceSearchForm.get('searchTerm')?.value.trim() === ''\">\n                  All Available Invoices ({{ invoiceSearchResults.length }} total)\n                </span>\n              </h4>\n              <div class=\"invoice-list\">\n                <div class=\"invoice-item\" *ngFor=\"let invoice of invoiceSearchResults\" (click)=\"selectInvoice(invoice)\">\n                  <div class=\"invoice-header\">\n                    <strong>{{ invoice.invoiceNumber }}</strong>\n                    <span class=\"invoice-date\">{{ invoice.invoiceDate | date:'dd/MM/yyyy' }}</span>\n                  </div>\n                  <div class=\"invoice-customer\">{{ invoice.customerName }}</div>\n                  <div class=\"invoice-zone\">{{ invoice.zone }} - {{ invoice.operatingUnit }}</div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"no-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length === 0\">\n              <p>No invoices found matching your search criteria.</p>\n              <p class=\"search-hint\">Try searching with different keywords or clear the search to see all invoices.</p>\n            </div>\n          </div>\n\n          <!-- Selected Invoice Display -->\n          <div class=\"selected-invoice\" *ngIf=\"selectedInvoice\">\n            <h4>Selected Invoice Details</h4>\n            <mat-card class=\"invoice-details-card\">\n              <mat-card-content>\n                <!-- Customer Information Section -->\n                <div class=\"section-header\">\n                  <h5><mat-icon>business</mat-icon> Customer Information</h5>\n                </div>\n                <div class=\"invoice-details-grid\">\n                  <div class=\"detail-item\">\n                    <label>Invoice Number:</label>\n                    <span>{{ selectedInvoice.invoiceNumber }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Invoice Date:</label>\n                    <span>{{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Customer Name:</label>\n                    <span>{{ selectedInvoice.customerName }}</span>\n                  </div>\n                  <div class=\"detail-item full-width\">\n                    <label>Customer Address:</label>\n                    <span>{{ selectedInvoice.customerAddress }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Zone:</label>\n                    <span>{{ selectedInvoice.zone }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Operating Unit:</label>\n                    <span>{{ selectedInvoice.operatingUnit }}</span>\n                  </div>\n                  <div class=\"detail-item full-width\">\n                    <label>Organization:</label>\n                    <span>{{ selectedInvoice.organization }}</span>\n                  </div>\n                  <div class=\"detail-item full-width\">\n                    <label>Bill To Location:</label>\n                    <span>{{ selectedInvoice.billToLocation }}</span>\n                  </div>\n                  <div class=\"detail-item full-width\">\n                    <label>Ship To Location:</label>\n                    <span>{{ selectedInvoice.shipToLocation }}</span>\n                  </div>\n                </div>\n\n                <!-- Item Information Section -->\n                <div class=\"section-header\">\n                  <h5><mat-icon>inventory</mat-icon> Item Information</h5>\n                </div>\n                <div class=\"items-container\">\n                  <mat-card class=\"item-card\" *ngFor=\"let item of selectedInvoice.items; let i = index\">\n                    <mat-card-header>\n                      <mat-card-title class=\"item-code\">{{ item.itemCode }}</mat-card-title>\n                    </mat-card-header>\n                    <mat-card-content>\n                      <div class=\"item-description\">\n                        {{ item.description }}\n                      </div>\n                      <div class=\"item-specs-grid\">\n                        <div class=\"spec-item\">\n                          <label>Thickness(mm):</label>\n                          <span class=\"spec-value\">{{ item.thickness }}</span>\n                        </div>\n                        <div class=\"spec-item\">\n                          <label>Width(mm):</label>\n                          <span class=\"spec-value\">{{ item.width }}</span>\n                        </div>\n                        <div class=\"spec-item\">\n                          <label>Height(mm):</label>\n                          <span class=\"spec-value\">{{ item.height }}</span>\n                        </div>\n                        <div class=\"spec-item\">\n                          <label>Qty:</label>\n                          <span class=\"spec-value\">{{ item.quantity }}</span>\n                        </div>\n                        <div class=\"spec-item\">\n                          <label>CSQM:</label>\n                          <span class=\"spec-value\">{{ item.csqm }}</span>\n                        </div>\n                        <div class=\"spec-item\">\n                          <label>No. Of Received Boxes:</label>\n                          <span class=\"spec-value\">{{ item.receivedBoxes }}</span>\n                        </div>\n                      </div>\n                      <div class=\"defect-selection\" *ngIf=\"currentStep === 4\">\n                        <mat-form-field appearance=\"outline\" class=\"defect-field\">\n                          <mat-label>Select Defected BarCode</mat-label>\n                          <mat-select [(ngModel)]=\"item.defectedBarCode\">\n                            <mat-option value=\"\">No defect</mat-option>\n                            <mat-option value=\"barcode1\">Barcode 1</mat-option>\n                            <mat-option value=\"barcode2\">Barcode 2</mat-option>\n                            <mat-option value=\"barcode3\">Barcode 3</mat-option>\n                          </mat-select>\n                        </mat-form-field>\n                      </div>\n                    </mat-card-content>\n                  </mat-card>\n                </div>\n\n                <div class=\"invoice-actions\">\n                  <button mat-button color=\"warn\" (click)=\"clearInvoiceSelection()\">\n                    <mat-icon>clear</mat-icon>\n                    Clear Selection\n                  </button>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n\n\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 4: Complaint Details -->\n    <div class=\"step-content\" *ngIf=\"isStepCompleted(3) && !isStepCompleted(4)\">\n      <div class=\"form-section\">\n        <h3>Final Complaint Details</h3>\n        <p class=\"section-description\">Review all information and provide contact details</p>\n\n        <!-- Complete Summary Display -->\n        <div class=\"complete-summary-display\">\n          <h4>\n            <mat-icon>assignment</mat-icon>\n            Complete Complaint Summary\n            <span class=\"final-review-badge\">FINAL REVIEW</span>\n          </h4>\n          <div class=\"summary-sections\">\n            <div class=\"summary-section\">\n              <h5>Complaint Information</h5>\n              <div class=\"summary-item\">\n                <strong>Type:</strong> {{ getSelectedComplaintType()?.label }}\n              </div>\n              <div class=\"summary-item\">\n                <strong>Description:</strong> {{ getSelectedComplaintDescription()?.label }}\n              </div>\n            </div>\n\n            <!-- Complete Invoice Details Section -->\n            <div class=\"summary-section\" *ngIf=\"selectedInvoice\">\n              <h5>\n                <mat-icon>receipt</mat-icon>\n                Complete Invoice Details\n              </h5>\n\n              <!-- Customer Information -->\n              <div class=\"invoice-details-readonly\">\n                <div class=\"section-header-readonly\">\n                  <h6><mat-icon>business</mat-icon> Customer Information</h6>\n                </div>\n                <div class=\"readonly-details-grid\">\n                  <div class=\"readonly-item\">\n                    <label>Invoice Number:</label>\n                    <span>{{ selectedInvoice.invoiceNumber }}</span>\n                  </div>\n                  <div class=\"readonly-item\">\n                    <label>Invoice Date:</label>\n                    <span>{{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}</span>\n                  </div>\n                  <div class=\"readonly-item\">\n                    <label>Customer Name:</label>\n                    <span>{{ selectedInvoice.customerName }}</span>\n                  </div>\n                  <div class=\"readonly-item full-width\">\n                    <label>Customer Address:</label>\n                    <span>{{ selectedInvoice.customerAddress }}</span>\n                  </div>\n                  <div class=\"readonly-item\">\n                    <label>Zone:</label>\n                    <span>{{ selectedInvoice.zone }}</span>\n                  </div>\n                  <div class=\"readonly-item\">\n                    <label>Operating Unit:</label>\n                    <span>{{ selectedInvoice.operatingUnit }}</span>\n                  </div>\n                  <div class=\"readonly-item full-width\">\n                    <label>Organization:</label>\n                    <span>{{ selectedInvoice.organization }}</span>\n                  </div>\n                  <div class=\"readonly-item full-width\">\n                    <label>Bill To Location:</label>\n                    <span>{{ selectedInvoice.billToLocation }}</span>\n                  </div>\n                  <div class=\"readonly-item full-width\">\n                    <label>Ship To Location:</label>\n                    <span>{{ selectedInvoice.shipToLocation }}</span>\n                  </div>\n                </div>\n\n                <!-- Item Information -->\n                <div class=\"section-header-readonly\">\n                  <h6><mat-icon>inventory</mat-icon> Item Information</h6>\n                </div>\n                <div class=\"readonly-items-container\">\n                  <div class=\"readonly-item-card\" *ngFor=\"let item of selectedInvoice.items; let i = index\">\n                    <div class=\"readonly-item-header\">\n                      <strong>{{ item.itemCode }}</strong>\n                    </div>\n                    <div class=\"readonly-item-description\">\n                      {{ item.description }}\n                    </div>\n                    <div class=\"readonly-specs-grid\">\n                      <div class=\"readonly-spec\">\n                        <label>Thickness(mm):</label>\n                        <span>{{ item.thickness }}</span>\n                      </div>\n                      <div class=\"readonly-spec\">\n                        <label>Width(mm):</label>\n                        <span>{{ item.width }}</span>\n                      </div>\n                      <div class=\"readonly-spec\">\n                        <label>Height(mm):</label>\n                        <span>{{ item.height }}</span>\n                      </div>\n                      <div class=\"readonly-spec\">\n                        <label>Qty:</label>\n                        <span>{{ item.quantity }}</span>\n                      </div>\n                      <div class=\"readonly-spec\">\n                        <label>CSQM:</label>\n                        <span>{{ item.csqm }}</span>\n                      </div>\n                      <div class=\"readonly-spec\">\n                        <label>Received Boxes:</label>\n                        <span>{{ item.receivedBoxes }}</span>\n                      </div>\n                    </div>\n                    <div class=\"defect-selection-readonly\" *ngIf=\"item.defectedBarCode\">\n                      <label>Selected Defected BarCode:</label>\n                      <span class=\"defect-value\">{{ item.defectedBarCode }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Contact Details Form -->\n        <form [formGroup]=\"complaintDetailsForm\">\n          <div class=\"contact-form-section\">\n            <h4>\n              <mat-icon>contact_phone</mat-icon>\n              Contact Information (Required)\n            </h4>\n\n            <div class=\"contact-form-card\">\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Contact Person Name *</mat-label>\n                  <input matInput formControlName=\"contactPersonName\" placeholder=\"Enter contact person name\">\n                  <mat-icon matSuffix>person</mat-icon>\n                  <mat-error *ngIf=\"complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched\">\n                    {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Contact Number *</mat-label>\n                  <input matInput formControlName=\"contactNumber\" placeholder=\"Enter 10-digit contact number\" type=\"tel\">\n                  <mat-icon matSuffix>phone</mat-icon>\n                  <mat-error *ngIf=\"complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched\">\n                    {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Additional Comments (Optional)</mat-label>\n                <textarea matInput formControlName=\"comments\" rows=\"3\" placeholder=\"Any additional comments or information\"></textarea>\n                <mat-icon matSuffix>comment</mat-icon>\n                <mat-hint>Optional: Add any additional information that might be helpful</mat-hint>\n              </mat-form-field>\n\n              <!-- Complaint Letters Checkbox -->\n              <div class=\"complaint-letters-section\">\n                <mat-checkbox formControlName=\"hasComplaintLetters\" color=\"primary\">\n                  <strong>Do you have complaint letters to attach?</strong>\n                </mat-checkbox>\n                <p class=\"checkbox-hint\">Check this box if you have supporting documents, photos, or letters related to your complaint</p>\n              </div>\n\n              <!-- File Upload Section -->\n              <div class=\"file-upload-section\" *ngIf=\"complaintDetailsForm.get('hasComplaintLetters')?.value\">\n                <h5>\n                  <mat-icon>attach_file</mat-icon>\n                  Upload Supporting Documents\n                </h5>\n                <div class=\"upload-area\">\n                  <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" multiple accept=\".pdf,.jpg,.jpeg,.png,.doc,.docx\" style=\"display: none;\">\n\n                  <button mat-stroked-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                    <mat-icon>cloud_upload</mat-icon>\n                    Choose Files\n                  </button>\n                  <p class=\"upload-hint\">\n                    <mat-icon>info</mat-icon>\n                    Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each)\n                  </p>\n                </div>\n\n                <div class=\"selected-files\" *ngIf=\"selectedFiles.length > 0\">\n                  <h6>Selected Files ({{ selectedFiles.length }}):</h6>\n                  <div class=\"file-list\">\n                    <div class=\"file-item\" *ngFor=\"let file of selectedFiles; let i = index\">\n                      <mat-icon class=\"file-icon\">description</mat-icon>\n                      <div class=\"file-info\">\n                        <span class=\"file-name\">{{ file.name }}</span>\n                        <span class=\"file-size\">({{ (file.size / 1024 / 1024).toFixed(2) }} MB)</span>\n                      </div>\n                      <button mat-icon-button (click)=\"removeFile(i)\" class=\"remove-file\" color=\"warn\">\n                        <mat-icon>delete</mat-icon>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"step-actions\">\n            <button mat-button (click)=\"goBackToStep3()\" class=\"back-button\">\n              <mat-icon>arrow_back</mat-icon>\n              Change Invoice\n            </button>\n            <button mat-raised-button color=\"primary\" (click)=\"onSubmitComplaint()\" [disabled]=\"isLoading || !complaintDetailsForm.valid\" class=\"submit-button\">\n              <mat-icon *ngIf=\"!isLoading\">send</mat-icon>\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n              {{ isLoading ? 'Submitting Complaint...' : 'Submit Complaint' }}\n            </button>\n          </div>\n        </form>\n      </div>\n\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;ICsBzDC,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS9DH,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS9DH,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS9DH,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAqB5DH,EAAA,CAAAC,cAAA,mBAKY;IADVD,EAAA,CAAAI,UAAA,mBAAAC,mEAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,mBAAA,CAAAP,OAAA,CAAyB;IAAA,EAAC;IAI/BN,EAFJ,CAAAC,cAAA,2BAAyC,cACd,mBAC0E;IAC/FD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAAyB,SACnB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAc,SAAA,4BAImB;IAGzBd,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;;;;;;;IArBTH,EAAA,CAAAe,WAAA,eAAAC,OAAA,GAAAN,MAAA,CAAAO,iBAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,KAAA,MAAAb,OAAA,CAAAa,KAAA,CAA8E;IAKhEnB,EAAA,CAAAoB,SAAA,GAAsF;IAAtFpB,EAAA,CAAAqB,UAAA,YAAAC,OAAA,GAAAZ,MAAA,CAAAO,iBAAA,CAAAC,GAAA,mCAAAI,OAAA,CAAAH,KAAA,MAAAb,OAAA,CAAAa,KAAA,kBAAsF;IAC9FnB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAjB,OAAA,CAAAkB,IAAA,MACF;IAGIxB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAyB,iBAAA,CAAAnB,OAAA,CAAAoB,KAAA,CAAgB;IACjB1B,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAyB,iBAAA,CAAAnB,OAAA,CAAAqB,WAAA,CAAsB;IAIvB3B,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAqB,UAAA,UAAAf,OAAA,CAAAa,KAAA,CAAoB;;;;;IA3B9BnB,EAJR,CAAAC,cAAA,cAA6D,mBAC/B,sBACT,qBACC,mBACc;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,yDAAkD;IACvEF,EADuE,CAAAG,YAAA,EAAoB,EACzE;IAIdH,EAFJ,CAAAC,cAAA,uBAAkB,gBACsB,eACH;IAC/BD,EAAA,CAAA4B,UAAA,KAAAC,wCAAA,wBAKY;IAwBtB7B,EAJQ,CAAAG,YAAA,EAAM,EACD,EACU,EACV,EACP;;;;IA/BMH,EAAA,CAAAoB,SAAA,IAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAAO,iBAAA,CAA+B;IAIdjB,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAAoB,cAAA,CAAiB;;;;;IAoCpC9B,EAHN,CAAAC,cAAA,mBAAkE,2BACtB,cACd,mBACE;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAClEF,EADkE,CAAAG,YAAA,EAAW,EACvE;IAEJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAClDF,EADkD,CAAAG,YAAA,EAAI,EAChD;IACNH,EAAA,CAAAC,cAAA,oBAAmC;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAE/CF,EAF+C,CAAAG,YAAA,EAAW,EACrC,EACV;;;;;;;IARqBH,EAAA,CAAAoB,SAAA,GAAsC;IAAtCpB,EAAA,CAAAyB,iBAAA,EAAAM,OAAA,GAAArB,MAAA,CAAAsB,wBAAA,qBAAAD,OAAA,CAAAP,IAAA,CAAsC;IAG5DxB,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAyB,iBAAA,EAAAT,OAAA,GAAAN,MAAA,CAAAsB,wBAAA,qBAAAhB,OAAA,CAAAU,KAAA,CAAuC;IACxC1B,EAAA,CAAAoB,SAAA,GAA6C;IAA7CpB,EAAA,CAAAyB,iBAAA,EAAAH,OAAA,GAAAZ,MAAA,CAAAsB,wBAAA,qBAAAV,OAAA,CAAAK,WAAA,CAA6C;;;;;IA2B5C3B,EALF,CAAAC,cAAA,mBAIY,2BACqC;IAC7CD,EAAA,CAAAc,SAAA,2BAImB;IAEjBd,EADF,CAAAC,cAAA,cAAwB,SAClB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAG/BF,EAH+B,CAAAG,YAAA,EAAI,EACzB,EACW,EACV;;;;;;IAbTH,EAAA,CAAAe,WAAA,eAAAC,OAAA,GAAAN,MAAA,CAAAuB,wBAAA,CAAAf,GAAA,0CAAAF,OAAA,CAAAG,KAAA,MAAAe,OAAA,CAAAf,KAAA,CAA4F;IAIxFnB,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAqB,UAAA,UAAAa,OAAA,CAAAf,KAAA,CAAoB;IAKhBnB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAyB,iBAAA,CAAAS,OAAA,CAAAR,KAAA,CAAgB;IACjB1B,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAyB,iBAAA,CAAAS,OAAA,CAAAP,WAAA,CAAsB;;;;;;IA5CzC3B,EADF,CAAAC,cAAA,cAAmF,cACxD;IAEvBD,EAAA,CAAA4B,UAAA,IAAAO,uCAAA,wBAAkE;IAiB5DnC,EAHN,CAAAC,cAAA,mBAA4B,sBACT,qBACC,mBACc;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,+DAAuD;IAC5EF,EAD4E,CAAAG,YAAA,EAAoB,EAC9E;IAIdH,EAFJ,CAAAC,cAAA,wBAAkB,gBAC6B,2BAIF;IAAvCD,EAAA,CAAAI,UAAA,oBAAAgC,gEAAAC,MAAA;MAAArC,EAAA,CAAAO,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAA6B,mBAAA,CAAAF,MAAA,CAA2B;IAAA,EAAC;IACtCrC,EAAA,CAAA4B,UAAA,KAAAY,wCAAA,uBAIY;IAkBxBxC,EALU,CAAAG,YAAA,EAAkB,EACb,EACU,EACV,EACP,EACF;;;;IAnD8BH,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAsB,wBAAA,GAAgC;IAwBtDhC,EAAA,CAAAoB,SAAA,IAAsC;IAAtCpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAAuB,wBAAA,CAAsC;IAOrBjC,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAA+B,wBAAA,GAA6B;;;;;IA+ChDzC,EAAA,CAAAC,cAAA,gBAAgH;IAC9GD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAAgC,eAAA,CAAAhC,MAAA,CAAAiC,iBAAA,qBACF;;;;;IAKE3C,EAAA,CAAAC,cAAA,WAAqH;IACnHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,sBAAAb,MAAA,CAAAkC,oBAAA,CAAAC,MAAA,aACF;;;;;IACA7C,EAAA,CAAAC,cAAA,WAAsH;IACpHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,8BAAAb,MAAA,CAAAkC,oBAAA,CAAAC,MAAA,aACF;;;;;;IAGA7C,EAAA,CAAAC,cAAA,cAAwG;IAAjCD,EAAA,CAAAI,UAAA,mBAAA0C,+DAAA;MAAA,MAAAC,UAAA,GAAA/C,EAAA,CAAAO,aAAA,CAAAyC,GAAA,EAAAvC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuC,aAAA,CAAAF,UAAA,CAAsB;IAAA,EAAC;IAEnG/C,EADF,CAAAC,cAAA,cAA4B,aAClB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6C;;IAC1EF,EAD0E,CAAAG,YAAA,EAAO,EAC3E;IACNH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9DH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAC5EF,EAD4E,CAAAG,YAAA,EAAM,EAC5E;;;;IALMH,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAAyB,iBAAA,CAAAsB,UAAA,CAAAG,aAAA,CAA2B;IACRlD,EAAA,CAAAoB,SAAA,GAA6C;IAA7CpB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAmD,WAAA,OAAAJ,UAAA,CAAAK,WAAA,gBAA6C;IAE5CpD,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAyB,iBAAA,CAAAsB,UAAA,CAAAM,YAAA,CAA0B;IAC9BrD,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAsD,kBAAA,KAAAP,UAAA,CAAAQ,IAAA,SAAAR,UAAA,CAAAS,aAAA,KAAgD;;;;;IAf9ExD,EADF,CAAAC,cAAA,cAA0F,SACpF;IAIFD,EAHA,CAAA4B,UAAA,IAAA6B,0CAAA,mBAAqH,IAAAC,0CAAA,mBAGC;IAGxH1D,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA4B,UAAA,IAAA+B,yCAAA,mBAAwG;IAS5G3D,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;IAjBKH,EAAA,CAAAoB,SAAA,GAA4G;IAA5GpB,EAAA,CAAAqB,UAAA,WAAAU,OAAA,GAAArB,MAAA,CAAAiC,iBAAA,CAAAzB,GAAA,iCAAAa,OAAA,CAAAZ,KAAA,OAAAY,OAAA,GAAArB,MAAA,CAAAiC,iBAAA,CAAAzB,GAAA,iCAAAa,OAAA,CAAAZ,KAAA,CAAAyC,IAAA,WAA4G;IAG5G5D,EAAA,CAAAoB,SAAA,EAA6G;IAA7GpB,EAAA,CAAAqB,UAAA,YAAAL,OAAA,GAAAN,MAAA,CAAAiC,iBAAA,CAAAzB,GAAA,iCAAAF,OAAA,CAAAG,KAAA,OAAAH,OAAA,GAAAN,MAAA,CAAAiC,iBAAA,CAAAzB,GAAA,iCAAAF,OAAA,CAAAG,KAAA,CAAAyC,IAAA,WAA6G;IAKtE5D,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAAkC,oBAAA,CAAuB;;;;;IAYvE5C,EADF,CAAAC,cAAA,cAAwF,QACnF;IAAAD,EAAA,CAAAE,MAAA,uDAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvDH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,qFAA8E;IACvGF,EADuG,CAAAG,YAAA,EAAI,EACrG;;;;;;IA4FQH,EAFJ,CAAAC,cAAA,cAAwD,yBACI,gBAC7C;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9CH,EAAA,CAAAC,cAAA,qBAA+C;IAAnCD,EAAA,CAAA6D,gBAAA,2BAAAC,2FAAAzB,MAAA;MAAArC,EAAA,CAAAO,aAAA,CAAAwD,IAAA;MAAA,MAAAC,QAAA,GAAAhE,EAAA,CAAAW,aAAA,GAAAF,SAAA;MAAAT,EAAA,CAAAiE,kBAAA,CAAAD,QAAA,CAAAE,eAAA,EAAA7B,MAAA,MAAA2B,QAAA,CAAAE,eAAA,GAAA7B,MAAA;MAAA,OAAArC,EAAA,CAAAY,WAAA,CAAAyB,MAAA;IAAA,EAAkC;IAC5CrC,EAAA,CAAAC,cAAA,qBAAqB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC3CH,EAAA,CAAAC,cAAA,qBAA6B;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACnDH,EAAA,CAAAC,cAAA,qBAA6B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACnDH,EAAA,CAAAC,cAAA,sBAA6B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAG5CF,EAH4C,CAAAG,YAAA,EAAa,EACxC,EACE,EACb;;;;IAPUH,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAmE,gBAAA,YAAAH,QAAA,CAAAE,eAAA,CAAkC;;;;;IAnClDlE,EAFJ,CAAAC,cAAA,mBAAsF,sBACnE,yBACmB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACvDF,EADuD,CAAAG,YAAA,EAAiB,EACtD;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,cACc;IAC5BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,cAA6B,cACJ,YACd;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7BH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EAChD;IAEJH,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;IAEJH,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpBH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IAEJH,EADF,CAAAC,cAAA,eAAuB,aACd;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrCH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAErDF,EAFqD,CAAAG,YAAA,EAAO,EACpD,EACF;IACNH,EAAA,CAAA4B,UAAA,KAAAwC,sDAAA,mBAAwD;IAY5DpE,EADE,CAAAG,YAAA,EAAmB,EACV;;;;;IA5C2BH,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAAyB,iBAAA,CAAAuC,QAAA,CAAAK,QAAA,CAAmB;IAInDrE,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAyC,QAAA,CAAArC,WAAA,MACF;IAI6B3B,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAyB,iBAAA,CAAAuC,QAAA,CAAAM,SAAA,CAAoB;IAIpBtE,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAyB,iBAAA,CAAAuC,QAAA,CAAAO,KAAA,CAAgB;IAIhBvE,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAyB,iBAAA,CAAAuC,QAAA,CAAAQ,MAAA,CAAiB;IAIjBxE,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAAyB,iBAAA,CAAAuC,QAAA,CAAAS,QAAA,CAAmB;IAInBzE,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAAyB,iBAAA,CAAAuC,QAAA,CAAAU,IAAA,CAAe;IAIf1E,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAyB,iBAAA,CAAAuC,QAAA,CAAAW,aAAA,CAAwB;IAGtB3E,EAAA,CAAAoB,SAAA,EAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAkE,WAAA,OAAuB;;;;;;IArFhE5E,EADF,CAAAC,cAAA,cAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAKvBH,EAJV,CAAAC,cAAA,mBAAuC,uBACnB,cAEY,SACtB,eAAU;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,4BAAoB;IACxDF,EADwD,CAAAG,YAAA,EAAK,EACvD;IAGFH,EAFJ,CAAAC,cAAA,eAAkC,eACP,aAChB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqD;;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IAEJH,EADF,CAAAC,cAAA,eAAoC,aAC3B;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IAEJH,EADF,CAAAC,cAAA,eAAoC,aAC3B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IAEJH,EADF,CAAAC,cAAA,eAAoC,aAC3B;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;IAEJH,EADF,CAAAC,cAAA,eAAoC,aAC3B;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAE9CF,EAF8C,CAAAG,YAAA,EAAO,EAC7C,EACF;IAIAH,EADN,CAAAC,cAAA,eAA4B,UACtB,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,yBAAgB;IACrDF,EADqD,CAAAG,YAAA,EAAK,EACpD;IACNH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA4B,UAAA,KAAAiD,+CAAA,wBAAsF;IA+CxF7E,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAA6B,kBACuC;IAAlCD,EAAA,CAAAI,UAAA,mBAAA0E,6DAAA;MAAA9E,EAAA,CAAAO,aAAA,CAAAwE,GAAA;MAAA,MAAArE,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAsE,qBAAA,EAAuB;IAAA,EAAC;IAC/DhF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,yBACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;IAlGUH,EAAA,CAAAoB,SAAA,IAAmC;IAAnCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAA/B,aAAA,CAAmC;IAInClD,EAAA,CAAAoB,SAAA,GAAqD;IAArDpB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAmD,WAAA,SAAAzC,MAAA,CAAAuE,eAAA,CAAA7B,WAAA,gBAAqD;IAIrDpD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAA5B,YAAA,CAAkC;IAIlCrD,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAC,eAAA,CAAqC;IAIrClF,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAA1B,IAAA,CAA0B;IAI1BvD,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAzB,aAAA,CAAmC;IAInCxD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAE,YAAA,CAAkC;IAIlCnF,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAG,cAAA,CAAoC;IAIpCpF,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAI,cAAA,CAAoC;IASCrF,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAAuE,eAAA,CAAAK,KAAA,CAA0B;;;;;;IA1G/EtF,EAHN,CAAAC,cAAA,cAA4E,eACpC,cACV,SACpB;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,iEAA0D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI3FH,EADF,CAAAC,cAAA,cAAuC,SACjC;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGtBH,EAFJ,CAAAC,cAAA,eAA0B,eACE,cAChB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACzB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,cAChB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAChC;IAEJF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA4B,0BACgC,iBAC7C;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAC,cAAA,iBAAsJ;IAA5BD,EAAA,CAAAI,UAAA,mBAAAmF,qDAAA;MAAAvF,EAAA,CAAAO,aAAA,CAAAiF,GAAA;MAAA,MAAA9E,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAA+E,eAAA,EAAiB;IAAA,EAAC;IAArJzF,EAAA,CAAAG,YAAA,EAAsJ;IACtJH,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrCH,EAAA,CAAA4B,UAAA,KAAA8D,yCAAA,wBAAgH;IAGlH1F,EAAA,CAAAG,YAAA,EAAiB;IAuBjBH,EArBA,CAAA4B,UAAA,KAAA+D,mCAAA,kBAA0F,KAAAC,mCAAA,kBAqBF;IAI1F5F,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA4B,UAAA,KAAAiE,mCAAA,oBAAsD;IAkH5D7F,EAFI,CAAAG,YAAA,EAAM,EACD,EACH;;;;;;;IA1KEH,EAAA,CAAAoB,SAAA,EAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAAiC,iBAAA,CAA+B;IAUJ3C,EAAA,CAAAoB,SAAA,IACzB;IADyBpB,EAAA,CAAAuB,kBAAA,OAAAQ,OAAA,GAAArB,MAAA,CAAAsB,wBAAA,qBAAAD,OAAA,CAAAL,KAAA,MACzB;IAEgC1B,EAAA,CAAAoB,SAAA,GAChC;IADgCpB,EAAA,CAAAuB,kBAAA,OAAAP,OAAA,GAAAN,MAAA,CAAAoF,+BAAA,qBAAA9E,OAAA,CAAAU,KAAA,MAChC;IASY1B,EAAA,CAAAoB,SAAA,GAAkG;IAAlGpB,EAAA,CAAAqB,UAAA,WAAAC,OAAA,GAAAZ,MAAA,CAAAiC,iBAAA,CAAAzB,GAAA,iCAAAI,OAAA,CAAAyE,OAAA,OAAAzE,OAAA,GAAAZ,MAAA,CAAAiC,iBAAA,CAAAzB,GAAA,iCAAAI,OAAA,CAAA0E,OAAA,EAAkG;IAKnFhG,EAAA,CAAAoB,SAAA,EAA2D;IAA3DpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAuF,kBAAA,IAAAvF,MAAA,CAAAkC,oBAAA,CAAAC,MAAA,KAA2D;IAqB/D7C,EAAA,CAAAoB,SAAA,EAA6D;IAA7DpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAuF,kBAAA,IAAAvF,MAAA,CAAAkC,oBAAA,CAAAC,MAAA,OAA6D;IAOzD7C,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAuE,eAAA,CAAqB;;;;;IAsOxCjF,EADF,CAAAC,cAAA,eAAoE,YAC3D;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACvDF,EADuD,CAAAG,YAAA,EAAO,EACxD;;;;IADuBH,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAAhC,eAAA,CAA0B;;;;;IAjCrDlE,EAFJ,CAAAC,cAAA,eAA0F,eACtD,aACxB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC7BF,EAD6B,CAAAG,YAAA,EAAS,EAChC;IACNH,EAAA,CAAAC,cAAA,eAAuC;IACrCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGFH,EAFJ,CAAAC,cAAA,eAAiC,eACJ,YAClB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EACzB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAC3BF,EAD2B,CAAAG,YAAA,EAAO,EAC5B;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAe;IACvBF,EADuB,CAAAG,YAAA,EAAO,EACxB;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAElCF,EAFkC,CAAAG,YAAA,EAAO,EACjC,EACF;IACNH,EAAA,CAAA4B,UAAA,KAAAuE,iDAAA,mBAAoE;IAItEnG,EAAA,CAAAG,YAAA,EAAM;;;;IAnCMH,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAA7B,QAAA,CAAmB;IAG3BrE,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAA2E,QAAA,CAAAvE,WAAA,MACF;IAIU3B,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAA5B,SAAA,CAAoB;IAIpBtE,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAA3B,KAAA,CAAgB;IAIhBvE,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAA1B,MAAA,CAAiB;IAIjBxE,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAAzB,QAAA,CAAmB;IAInBzE,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAAxB,IAAA,CAAe;IAIf1E,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAyB,iBAAA,CAAAyE,QAAA,CAAAvB,aAAA,CAAwB;IAGM3E,EAAA,CAAAoB,SAAA,EAA0B;IAA1BpB,EAAA,CAAAqB,UAAA,SAAA6E,QAAA,CAAAhC,eAAA,CAA0B;;;;;IAtFtElE,EAFJ,CAAAC,cAAA,cAAqD,SAC/C,eACQ;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,iCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAKGH,EAFR,CAAAC,cAAA,eAAsC,eACC,SAC/B,eAAU;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,6BAAoB;IACxDF,EADwD,CAAAG,YAAA,EAAK,EACvD;IAGFH,EAFJ,CAAAC,cAAA,gBAAmC,gBACN,aAClB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqD;;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IAEJH,EADF,CAAAC,cAAA,gBAAsC,aAC7B;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACpBH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAClCF,EADkC,CAAAG,YAAA,EAAO,EACnC;IAEJH,EADF,CAAAC,cAAA,gBAA2B,aAClB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IAC3CF,EAD2C,CAAAG,YAAA,EAAO,EAC5C;IAEJH,EADF,CAAAC,cAAA,gBAAsC,aAC7B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IAEJH,EADF,CAAAC,cAAA,gBAAsC,aAC7B;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;IAEJH,EADF,CAAAC,cAAA,gBAAsC,aAC7B;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAE9CF,EAF8C,CAAAG,YAAA,EAAO,EAC7C,EACF;IAIAH,EADN,CAAAC,cAAA,gBAAqC,UAC/B,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,yBAAgB;IACrDF,EADqD,CAAAG,YAAA,EAAK,EACpD;IACNH,EAAA,CAAAC,cAAA,gBAAsC;IACpCD,EAAA,CAAA4B,UAAA,KAAAwE,0CAAA,oBAA0F;IAwChGpG,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IAjFQH,EAAA,CAAAoB,SAAA,IAAmC;IAAnCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAA/B,aAAA,CAAmC;IAInClD,EAAA,CAAAoB,SAAA,GAAqD;IAArDpB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAAmD,WAAA,SAAAzC,MAAA,CAAAuE,eAAA,CAAA7B,WAAA,gBAAqD;IAIrDpD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAA5B,YAAA,CAAkC;IAIlCrD,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAC,eAAA,CAAqC;IAIrClF,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAA1B,IAAA,CAA0B;IAI1BvD,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAzB,aAAA,CAAmC;IAInCxD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAE,YAAA,CAAkC;IAIlCnF,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAG,cAAA,CAAoC;IAIpCpF,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAAuE,eAAA,CAAAI,cAAA,CAAoC;IASKrF,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAAuE,eAAA,CAAAK,KAAA,CAA0B;;;;;IA0D3EtF,EAAA,CAAAC,cAAA,gBAAoI;IAClID,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAAgC,eAAA,CAAAhC,MAAA,CAAA2F,oBAAA,4BACF;;;;;IAOArG,EAAA,CAAAC,cAAA,gBAA4H;IAC1HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAAgC,eAAA,CAAAhC,MAAA,CAAA2F,oBAAA,wBACF;;;;;;IA0CIrG,EADF,CAAAC,cAAA,eAAyE,oBAC3C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhDH,EADF,CAAAC,cAAA,eAAuB,gBACG;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,GAA+C;IACzEF,EADyE,CAAAG,YAAA,EAAO,EAC1E;IACNH,EAAA,CAAAC,cAAA,kBAAiF;IAAzDD,EAAA,CAAAI,UAAA,mBAAAkG,yEAAA;MAAA,MAAAC,KAAA,GAAAvG,EAAA,CAAAO,aAAA,CAAAiG,IAAA,EAAAC,KAAA;MAAA,MAAA/F,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAgG,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAC7CvG,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAEpBF,EAFoB,CAAAG,YAAA,EAAW,EACpB,EACL;;;;IANsBH,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAAyB,iBAAA,CAAAkF,QAAA,CAAAC,IAAA,CAAe;IACf5G,EAAA,CAAAoB,SAAA,GAA+C;IAA/CpB,EAAA,CAAAuB,kBAAA,OAAAoF,QAAA,CAAAE,IAAA,gBAAAC,OAAA,YAA+C;;;;;IAN7E9G,EADF,CAAAC,cAAA,eAA6D,SACvD;IAAAD,EAAA,CAAAE,MAAA,GAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrDH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAA4B,UAAA,IAAAmF,gDAAA,oBAAyE;IAW7E/G,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAbAH,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAAuB,kBAAA,qBAAAb,MAAA,CAAAsG,aAAA,CAAAnE,MAAA,OAA4C;IAEN7C,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAAsG,aAAA,CAAkB;;;;;;IAnB5DhH,EAFJ,CAAAC,cAAA,eAAgG,SAC1F,eACQ;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAyB,oBACkH;IAA3GD,EAAA,CAAAI,UAAA,oBAAA6G,4DAAA5E,MAAA;MAAArC,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAxG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAyG,cAAA,CAAA9E,MAAA,CAAsB;IAAA,EAAC;IAA/DrC,EAAA,CAAAG,YAAA,EAAyI;IAEzIH,EAAA,CAAAC,cAAA,kBAA6F;IAAlDD,EAAA,CAAAI,UAAA,mBAAAgH,4DAAA;MAAApH,EAAA,CAAAO,aAAA,CAAA2G,IAAA;MAAA,MAAAG,aAAA,GAAArH,EAAA,CAAAsH,WAAA;MAAA,OAAAtH,EAAA,CAAAY,WAAA,CAASyG,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IACpEvH,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,cAAuB,gBACX;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,oEACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAENH,EAAA,CAAA4B,UAAA,KAAA4F,0CAAA,mBAA6D;IAe/DxH,EAAA,CAAAG,YAAA,EAAM;;;;IAfyBH,EAAA,CAAAoB,SAAA,IAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAsG,aAAA,CAAAnE,MAAA,KAA8B;;;;;IAyB7D7C,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC5CH,EAAA,CAAAc,SAAA,uBAA2D;;;;;;IAlNjEd,EAFJ,CAAAC,cAAA,cAA4E,cAChD,SACpB;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,yDAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAKjFH,EAFJ,CAAAC,cAAA,cAAsC,SAChC,eACQ;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,oCACA;IAAAF,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAC/CF,EAD+C,CAAAG,YAAA,EAAO,EACjD;IAGDH,EAFJ,CAAAC,cAAA,eAA8B,eACC,UACvB;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE5BH,EADF,CAAAC,cAAA,eAA0B,cAChB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACzB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA0B,cAChB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAChC;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAA4B,UAAA,KAAA6F,mCAAA,qBAAqD;IAiGzDzH,EADE,CAAAG,YAAA,EAAM,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAyC,gBACL,UAC5B,gBACQ;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAE,MAAA,wCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAKCH,EAHN,CAAAC,cAAA,gBAA+B,gBACP,2BACoC,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5CH,EAAA,CAAAc,SAAA,kBAA4F;IAC5Fd,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrCH,EAAA,CAAA4B,UAAA,KAAA8F,yCAAA,wBAAoI;IAGtI1H,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,2BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAc,SAAA,kBAAuG;IACvGd,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAA4B,UAAA,KAAA+F,yCAAA,wBAA4H;IAIhI3H,EADE,CAAAG,YAAA,EAAiB,EACb;IAGJH,EADF,CAAAC,cAAA,2BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,sCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrDH,EAAA,CAAAc,SAAA,qBAAuH;IACvHd,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,sEAA8D;IAC1EF,EAD0E,CAAAG,YAAA,EAAW,EACpE;IAKbH,EAFJ,CAAAC,cAAA,gBAAuC,yBAC+B,cAC1D;IAAAD,EAAA,CAAAE,MAAA,gDAAwC;IAClDF,EADkD,CAAAG,YAAA,EAAS,EAC5C;IACfH,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,qGAA6F;IACxHF,EADwH,CAAAG,YAAA,EAAI,EACtH;IAGNH,EAAA,CAAA4B,UAAA,KAAAgG,mCAAA,oBAAgG;IAmCpG5H,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,gBAA0B,mBACyC;IAA9CD,EAAA,CAAAI,UAAA,mBAAAyH,sDAAA;MAAA7H,EAAA,CAAAO,aAAA,CAAAuH,IAAA;MAAA,MAAApH,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAqH,aAAA,EAAe;IAAA,EAAC;IAC1C/H,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,mBAAoJ;IAA1GD,EAAA,CAAAI,UAAA,mBAAA4H,sDAAA;MAAAhI,EAAA,CAAAO,aAAA,CAAAuH,IAAA;MAAA,MAAApH,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAuH,iBAAA,EAAmB;IAAA,EAAC;IAErEjI,EADA,CAAA4B,UAAA,KAAAsG,wCAAA,uBAA6B,KAAAC,2CAAA,2BACgB;IAC7CnI,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACD,EACH,EAzNoE;;;;;;;;;IAgBzCH,EAAA,CAAAoB,SAAA,IACzB;IADyBpB,EAAA,CAAAuB,kBAAA,OAAA6G,OAAA,GAAA1H,MAAA,CAAAsB,wBAAA,qBAAAoG,OAAA,CAAA1G,KAAA,MACzB;IAEgC1B,EAAA,CAAAoB,SAAA,GAChC;IADgCpB,EAAA,CAAAuB,kBAAA,OAAAQ,OAAA,GAAArB,MAAA,CAAAoF,+BAAA,qBAAA/D,OAAA,CAAAL,KAAA,MAChC;IAI4B1B,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAuE,eAAA,CAAqB;IAoGjDjF,EAAA,CAAAoB,SAAA,EAAkC;IAAlCpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAA2F,oBAAA,CAAkC;IAalBrG,EAAA,CAAAoB,SAAA,IAAsH;IAAtHpB,EAAA,CAAAqB,UAAA,WAAAgH,OAAA,GAAA3H,MAAA,CAAA2F,oBAAA,CAAAnF,GAAA,wCAAAmH,OAAA,CAAAtC,OAAA,OAAAsC,OAAA,GAAA3H,MAAA,CAAA2F,oBAAA,CAAAnF,GAAA,wCAAAmH,OAAA,CAAArC,OAAA,EAAsH;IAStHhG,EAAA,CAAAoB,SAAA,GAA8G;IAA9GpB,EAAA,CAAAqB,UAAA,WAAAiH,OAAA,GAAA5H,MAAA,CAAA2F,oBAAA,CAAAnF,GAAA,oCAAAoH,OAAA,CAAAvC,OAAA,OAAAuC,OAAA,GAAA5H,MAAA,CAAA2F,oBAAA,CAAAnF,GAAA,oCAAAoH,OAAA,CAAAtC,OAAA,EAA8G;IAsB5FhG,EAAA,CAAAoB,SAAA,IAA4D;IAA5DpB,EAAA,CAAAqB,UAAA,UAAAkH,OAAA,GAAA7H,MAAA,CAAA2F,oBAAA,CAAAnF,GAAA,0CAAAqH,OAAA,CAAApH,KAAA,CAA4D;IA0CxBnB,EAAA,CAAAoB,SAAA,GAAqD;IAArDpB,EAAA,CAAAqB,UAAA,aAAAX,MAAA,CAAA8H,SAAA,KAAA9H,MAAA,CAAA2F,oBAAA,CAAAoC,KAAA,CAAqD;IAChHzI,EAAA,CAAAoB,SAAA,EAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,UAAAX,MAAA,CAAA8H,SAAA,CAAgB;IACbxI,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAA8H,SAAA,CAAe;IAC7BxI,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAA8H,SAAA,uDACF;;;ADrgBZ,OAAM,MAAOE,YAAY;EAkUvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IAhUzB,KAAAP,SAAS,GAAG,KAAK;IACjB,KAAAxB,aAAa,GAAW,EAAE;IAC1B,KAAA/B,eAAe,GAAuB,IAAI;IAC1C,KAAArC,oBAAoB,GAAkB,EAAE;IACxC,KAAAqD,kBAAkB,GAAG,KAAK;IAC1B,KAAArB,WAAW,GAAG,CAAC;IAEf,KAAA9C,cAAc,GAAG,CACf;MACEX,KAAK,EAAE,eAAe;MACtBO,KAAK,EAAE,sBAAsB;MAC7BF,IAAI,EAAE,iBAAiB;MACvBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,cAAc;MACrBO,KAAK,EAAE,uBAAuB;MAC9BF,IAAI,EAAE,mBAAmB;MACzBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,iBAAiB;MACxBO,KAAK,EAAE,2BAA2B;MAClCF,IAAI,EAAE,aAAa;MACnBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,aAAa;MACpBO,KAAK,EAAE,oBAAoB;MAC3BF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,SAAS;MAChBO,KAAK,EAAE,iBAAiB;MACxBF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,SAAS;MAChBO,KAAK,EAAE,yBAAyB;MAChCF,IAAI,EAAE,uBAAuB;MAC7BG,WAAW,EAAE;KACd,CACF;IAED,KAAAqH,qBAAqB,GAA6B;MAChD,eAAe,EAAE,CACf;QAAE7H,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,4BAA4B;QAAEC,WAAW,EAAE;MAAiD,CAAE,EAC3H;QAAER,KAAK,EAAE,QAAQ;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA0C,CAAE,EACtG;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,aAAa;QAAEC,WAAW,EAAE;MAA4C,CAAE,EACrG;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,eAAe;QAAEC,WAAW,EAAE;MAAgD,CAAE,EACjH;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAChH;MACD,cAAc,EAAE,CACd;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EAClH;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAAuC,CAAE,EACnG;QAAER,KAAK,EAAE,UAAU;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EAChH;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,cAAc;QAAEC,WAAW,EAAE;MAA0C,CAAE,EACpG;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,4BAA4B;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAC9H;MACD,iBAAiB,EAAE,CACjB;QAAER,KAAK,EAAE,gBAAgB;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAoC,CAAE,EAC1G;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EACnG;QAAER,KAAK,EAAE,UAAU;QAAEO,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAA8C,CAAE,EAC3G;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAAoC,CAAE,EAClG;QAAER,KAAK,EAAE,YAAY;QAAEO,KAAK,EAAE,sBAAsB;QAAEC,WAAW,EAAE;MAAkD,CAAE,CACxH;MACD,aAAa,EAAE,CACb;QAAER,KAAK,EAAE,YAAY;QAAEO,KAAK,EAAE,YAAY;QAAEC,WAAW,EAAE;MAAyC,CAAE,EACpG;QAAER,KAAK,EAAE,mBAAmB;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EAC9G;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,wBAAwB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EACvH;QAAER,KAAK,EAAE,UAAU;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAC9G;MACD,SAAS,EAAE,CACT;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EACtH;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAA2C,CAAE,EACjH;QAAER,KAAK,EAAE,gBAAgB;QAAEO,KAAK,EAAE,yBAAyB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EACzH;QAAER,KAAK,EAAE,iBAAiB;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA4C,CAAE,CAClH;MACD,SAAS,EAAE,CACT;QAAER,KAAK,EAAE,cAAc;QAAEO,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EACxG;QAAER,KAAK,EAAE,iBAAiB;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAAwC,CAAE,EAC7G;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EAC9G;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,uBAAuB;QAAEC,WAAW,EAAE;MAA0C,CAAE;KAElH;IAED;IACA,KAAAsH,cAAc,GAAkB,CAC9B;MACE/F,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,uBAAuB;MACrC6B,eAAe,EAAE,yDAAyD;MAC1E3B,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,uBAAuB;MACvCC,cAAc,EAAE,yBAAyB;MACzCC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,uCAAuC;QACpD2C,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,uCAAuC;QACpD2C,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEzB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,wBAAwB;MACtC6B,eAAe,EAAE,mDAAmD;MACpE3B,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,uBAAuB;MACtC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,8BAA8B;MAC9CC,cAAc,EAAE,+BAA+B;MAC/CC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,yCAAyC;QACtD2C,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,2CAA2C;QACxD2C,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEzB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,wBAAwB;MACtC6B,eAAe,EAAE,oDAAoD;MACrE3B,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE,yCAAyC;MACzDC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,uCAAuC;QACpD2C,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEzB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,6BAA6B;MAC3C6B,eAAe,EAAE,0DAA0D;MAC3E3B,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,mBAAmB;MAClC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,+BAA+B;MAC/CC,cAAc,EAAE,gCAAgC;MAChDC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,yCAAyC;QACtD2C,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEzB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,kBAAkB;MAChC6B,eAAe,EAAE,0CAA0C;MAC3D3B,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,oBAAoB;MACnC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,gCAAgC;MAChDC,cAAc,EAAE,uCAAuC;MACvDC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,qCAAqC;QAClD2C,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,yCAAyC;QACtD2C,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEzB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,oBAAoB;MAClC6B,eAAe,EAAE,wDAAwD;MACzE3B,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE,0CAA0C;MAC1DC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,2CAA2C;QACxD2C,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEzB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,mBAAmB;MACjC6B,eAAe,EAAE,2CAA2C;MAC5D3B,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,sBAAsB;MACrC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,kCAAkC;MAClDC,cAAc,EAAE,yCAAyC;MACzDC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,2CAA2C;QACxD2C,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEzB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI8F,IAAI,CAAC,YAAY,CAAC;MACnC7F,YAAY,EAAE,wBAAwB;MACtC6B,eAAe,EAAE,0DAA0D;MAC3E3B,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjC2B,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,yBAAyB;MACzCC,cAAc,EAAE,oCAAoC;MACpDC,KAAK,EAAE,CACL;QACEjB,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,2CAA2C;QACxD2C,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC1C,WAAW,EAAE,2CAA2C;QACxD2C,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,CACF;IAQC,IAAI,CAACwE,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAF,WAAWA,CAAA;IACT,IAAI,CAAClI,iBAAiB,GAAG,IAAI,CAAC2H,WAAW,CAACU,KAAK,CAAC;MAC9CC,YAAY,EAAE,CAAC,EAAE,EAAExJ,UAAU,CAACyJ,QAAQ;KACvC,CAAC;IAEF,IAAI,CAACvH,wBAAwB,GAAG,IAAI,CAAC2G,WAAW,CAACU,KAAK,CAAC;MACrDG,mBAAmB,EAAE,CAAC,EAAE,EAAE1J,UAAU,CAACyJ,QAAQ;KAC9C,CAAC;IAEF,IAAI,CAAC7G,iBAAiB,GAAG,IAAI,CAACiG,WAAW,CAACU,KAAK,CAAC;MAC9CI,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;KAClB,CAAC;IAEF,IAAI,CAACrD,oBAAoB,GAAG,IAAI,CAACuC,WAAW,CAACU,KAAK,CAAC;MACjDK,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC5J,UAAU,CAACyJ,QAAQ,EAAEzJ,UAAU,CAAC6J,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC9J,UAAU,CAACyJ,QAAQ,EAAEzJ,UAAU,CAAC+J,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAC7EC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,YAAY,EAAE,CAAC,IAAI;KACpB,CAAC;EACJ;EAEApJ,mBAAmBA,CAACqJ,IAAS;IAC3B,IAAI,CAACjJ,iBAAiB,CAACkJ,UAAU,CAAC;MAAEZ,YAAY,EAAEW,IAAI,CAAC/I;IAAK,CAAE,CAAC;IAC/D;IACAiJ,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA5H,wBAAwBA,CAAA;IACtB,MAAM8G,YAAY,GAAG,IAAI,CAACtI,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACtE,OAAO,IAAI,CAAC6H,qBAAqB,CAACO,YAAY,CAAC,IAAI,EAAE;EACvD;EAEAzD,+BAA+BA,CAAA;IAC7B,MAAMwE,aAAa,GAAG,IAAI,CAACrI,wBAAwB,CAACf,GAAG,CAAC,qBAAqB,CAAC,EAAEC,KAAK;IACrF,MAAMoJ,YAAY,GAAG,IAAI,CAAC9H,wBAAwB,EAAE;IACpD,OAAO8H,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACtJ,KAAK,KAAKmJ,aAAa,CAAC;EAChE;EAEA/H,mBAAmBA,CAACmI,KAAU;IAC5B;IACAN,UAAU,CAAC,MAAK;MACd,IAAI,CAACO,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAN,SAASA,CAAA;IACP,IAAI,IAAI,CAACpJ,iBAAiB,CAACwH,KAAK,EAAE;MAChC,IAAI,CAAC7D,WAAW,GAAG,CAAC;;EAExB;EAEA+F,SAASA,CAAA;IACP,IAAI,IAAI,CAAC1I,wBAAwB,CAACwG,KAAK,EAAE;MACvC,IAAI,CAAC7D,WAAW,GAAG,CAAC;MACpB,IAAI,CAACyE,eAAe,EAAE;;EAE1B;EAEAuB,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3F,eAAe,EAAE;MACxB,IAAI,CAACL,WAAW,GAAG,CAAC;;EAExB;EAEAiG,aAAaA,CAAA;IACX,IAAI,CAACjG,WAAW,GAAG,CAAC;EACtB;EAEAkG,aAAaA,CAAA;IACX,IAAI,CAAClG,WAAW,GAAG,CAAC;EACtB;EAEAmD,aAAaA,CAAA;IACX,IAAI,CAACnD,WAAW,GAAG,CAAC;IACpB,IAAI,CAACyE,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACzG,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAACqG,cAAc,CAAC;IACpD,IAAI,CAAChD,kBAAkB,GAAG,IAAI;EAChC;EAEAR,eAAeA,CAAA;IACb,MAAMiE,UAAU,GAAG,IAAI,CAAC/G,iBAAiB,CAACzB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAElE,IAAI,CAACuI,UAAU,IAAIA,UAAU,CAAC9F,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3C;MACA,IAAI,CAACyF,eAAe,EAAE;MACtB;;IAGF,IAAIK,UAAU,CAAC7G,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAAC2F,SAAS,GAAG,IAAI;MAErB;MACA4B,UAAU,CAAC,MAAK;QACd,IAAI,CAACxH,oBAAoB,GAAG,IAAI,CAACqG,cAAc,CAAC8B,MAAM,CAACC,OAAO,IAC5DA,OAAO,CAAC9H,aAAa,CAAC+H,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,IACtED,OAAO,CAAC3H,YAAY,CAAC4H,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,IACrED,OAAO,CAACzH,IAAI,CAAC0H,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,IAC7DD,OAAO,CAACxH,aAAa,CAACyH,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,CACvE;QACD,IAAI,CAAChF,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACuC,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEAvF,aAAaA,CAAC+H,OAAoB;IAChC,IAAI,CAAC/F,eAAe,GAAG+F,OAAO;IAC9B,IAAI,CAAC/E,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACtD,iBAAiB,CAACwH,UAAU,CAAC;MAAET,UAAU,EAAEsB,OAAO,CAAC9H;IAAa,CAAE,CAAC;IACxE;IACAkH,UAAU,CAAC,MAAK;MACd,IAAI,CAACQ,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA5F,qBAAqBA,CAAA;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACtC,iBAAiB,CAACwH,UAAU,CAAC;MAAET,UAAU,EAAE;IAAE,CAAE,CAAC;IACrD;IACA,IAAI,CAACL,eAAe,EAAE;EACxB;EAEMpB,iBAAiBA,CAAA;IAAA,IAAAkD,KAAA;IAAA,OAAAC,iBAAA;MACrB,IAAID,KAAI,CAAClK,iBAAiB,CAACwH,KAAK,IAAI0C,KAAI,CAAClJ,wBAAwB,CAACwG,KAAK,IAAI0C,KAAI,CAAClG,eAAe,IAAIkG,KAAI,CAAC9E,oBAAoB,CAACoC,KAAK,EAAE;QAClI0C,KAAI,CAAC3C,SAAS,GAAG,IAAI;QAErB,MAAM6C,OAAO,SAASF,KAAI,CAACrC,iBAAiB,CAACwC,MAAM,CAAC;UAClDC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB;QACArB,UAAU,cAAAgB,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAAC3C,SAAS,GAAG,KAAK;UACtB,MAAM6C,OAAO,CAACK,OAAO,EAAE;UAEvB,MAAMC,KAAK,SAASR,KAAI,CAACpC,eAAe,CAACuC,MAAM,CAAC;YAC9CC,OAAO,EAAE,uDAAuD,GAAGrC,IAAI,CAAC0C,GAAG,EAAE;YAC7EJ,QAAQ,EAAE,IAAI;YACdK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,MAAMH,KAAK,CAACF,OAAO,EAAE;UAErB;UACAN,KAAI,CAACtC,MAAM,CAACkD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMJ,KAAK,SAASR,KAAI,CAACpC,eAAe,CAACuC,MAAM,CAAC;UAC9CC,OAAO,EAAE,qEAAqE;UAC9EC,QAAQ,EAAE,IAAI;UACdK,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;SACX,CAAC;QACF,MAAMH,KAAK,CAACF,OAAO,EAAE;;IACtB;EACH;EAEAtE,cAAcA,CAACuD,KAAU;IACvB,MAAMsB,KAAK,GAAGtB,KAAK,CAACuB,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAACnJ,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACmE,aAAa,GAAGkF,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACtC,IAAI,CAAC3F,oBAAoB,CAAC8D,UAAU,CAAC;QAAEF,YAAY,EAAE+B,KAAK,CAAC,CAAC;MAAC,CAAE,CAAC;;EAEpE;EAEAtF,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACO,aAAa,CAACoF,MAAM,CAAC3F,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,IAAI,CAACO,aAAa,CAACnE,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACwD,oBAAoB,CAAC8D,UAAU,CAAC;QAAEF,YAAY,EAAE;MAAI,CAAE,CAAC;;EAEhE;EAEAjI,wBAAwBA,CAAA;IACtB,MAAMsI,aAAa,GAAG,IAAI,CAACrJ,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACvE,OAAO,IAAI,CAACW,cAAc,CAAC0I,IAAI,CAACN,IAAI,IAAIA,IAAI,CAAC/I,KAAK,KAAKmJ,aAAa,CAAC;EACvE;EAEA+B,eAAeA,CAACC,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO,IAAI,CAACrL,iBAAiB,CAACwH,KAAK,IAAI,IAAI,CAAC7D,WAAW,GAAG,CAAC;MAC7D,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC3C,wBAAwB,CAACwG,KAAK,IAAI,IAAI,CAAC7D,WAAW,GAAG,CAAC;MACpE,KAAK,CAAC;QACJ,OAAO,IAAI,CAACK,eAAe,KAAK,IAAI,IAAI,IAAI,CAACL,WAAW,GAAG,CAAC;MAC9D,KAAK,CAAC;QACJ,OAAO,IAAI,CAACyB,oBAAoB,CAACoC,KAAK,IAAI,IAAI,CAAC7D,WAAW,GAAG,CAAC;MAChE;QACE,OAAO,KAAK;;EAElB;EAEAlC,eAAeA,CAAC6J,IAAe,EAAEC,KAAa;IAC5C,MAAMC,OAAO,GAAGF,IAAI,CAACrL,GAAG,CAACsL,KAAK,CAAC;IAC/B,IAAIC,OAAO,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAM9C,SAAS,GAAG6C,OAAO,CAACG,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACH,KAAK,CAAC,qBAAqB5C,SAAS,aAAa;;IAEhF,IAAI6C,OAAO,EAAEC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACH,KAAa;IACjC,MAAMM,MAAM,GAA8B;MACxCvD,YAAY,EAAE,gBAAgB;MAC9BE,mBAAmB,EAAE,uBAAuB;MAC5CC,UAAU,EAAE,aAAa;MACzBC,iBAAiB,EAAE,qBAAqB;MACxCE,aAAa,EAAE,gBAAgB;MAC/BE,QAAQ,EAAE;KACX;IACD,OAAO+C,MAAM,CAACN,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAO,MAAMA,CAAA;IACJ,IAAI,CAAClE,MAAM,CAACkD,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBAvjBWrD,YAAY,EAAA1I,EAAA,CAAAgN,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlN,EAAA,CAAAgN,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAApN,EAAA,CAAAgN,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAtN,EAAA,CAAAgN,iBAAA,CAAAK,EAAA,CAAAE,eAAA;IAAA;EAAA;;;YAAZ7E,YAAY;MAAA8E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCvB9N,EADF,CAAAC,cAAA,qBAAoD,gBACa;UAAvCD,EAAA,CAAAI,UAAA,mBAAA4N,8CAAA;YAAA,OAASD,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UACxC/M,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UACTH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrDH,EAAA,CAAAc,SAAA,cAA4B;UAC5Bd,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAC1CF,EAD0C,CAAAG,YAAA,EAAW,EACvC;UAMVH,EAHJ,CAAAC,cAAA,aAA4B,cAEC,aACF;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,iDAAyC;UACpEF,EADoE,CAAAG,YAAA,EAAI,EAClE;UAMAH,EAHN,CAAAC,cAAA,eAA4B,eACK,eACoE,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAAqM,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxDlO,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAC9BF,EAD8B,CAAAG,YAAA,EAAM,EAC9B;UAENH,EAAA,CAAAc,SAAA,eAAoE;UAGlEd,EADF,CAAAC,cAAA,eAAiG,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAAuM,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxDpO,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACrCF,EADqC,CAAAG,YAAA,EAAM,EACrC;UAENH,EAAA,CAAAc,SAAA,eAAoE;UAGlEd,EADF,CAAAC,cAAA,eAAiG,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAAyM,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxDtO,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACjCF,EADiC,CAAAG,YAAA,EAAM,EACjC;UAENH,EAAA,CAAAc,SAAA,eAAoE;UAGlEd,EADF,CAAAC,cAAA,eAAiG,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAA2M,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxDxO,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAGpCF,EAHoC,CAAAG,YAAA,EAAM,EAChC,EACF,EACF;UAuRJH,EApRF,CAAA4B,UAAA,KAAA6M,4BAAA,mBAA6D,KAAAC,4BAAA,mBA6CsB,KAAAC,4BAAA,mBAyDL,KAAAC,4BAAA,oBA8KA;UAvUhF5O,EAAA,CAAAG,YAAA,EAA4B;;;UAUCH,EAAA,CAAAoB,SAAA,IAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAgN,GAAA,CAAA1B,eAAA,IAAsC,WAAA0B,GAAA,CAAAnJ,WAAA,OAAmC;UAEjF5E,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAA0M,GAAA,CAAA1B,eAAA,IAAwB;UAC5BrM,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAA0M,GAAA,CAAA1B,eAAA,IAAyB;UAKbrM,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAgN,GAAA,CAAA1B,eAAA,IAAsC;UAEtCrM,EAAA,CAAAoB,SAAA,EAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAgN,GAAA,CAAA1B,eAAA,IAAsC,WAAA0B,GAAA,CAAAnJ,WAAA,OAAmC;UAEjF5E,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAA0M,GAAA,CAAA1B,eAAA,IAAwB;UAC5BrM,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAA0M,GAAA,CAAA1B,eAAA,IAAyB;UAKbrM,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAgN,GAAA,CAAA1B,eAAA,IAAsC;UAEtCrM,EAAA,CAAAoB,SAAA,EAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAgN,GAAA,CAAA1B,eAAA,IAAsC,WAAA0B,GAAA,CAAAnJ,WAAA,OAAmC;UAEjF5E,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAA0M,GAAA,CAAA1B,eAAA,IAAwB;UAC5BrM,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAA0M,GAAA,CAAA1B,eAAA,IAAyB;UAKbrM,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAgN,GAAA,CAAA1B,eAAA,IAAsC;UAEtCrM,EAAA,CAAAoB,SAAA,EAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAgN,GAAA,CAAA1B,eAAA,IAAsC,WAAA0B,GAAA,CAAAnJ,WAAA,OAAmC;UAEjF5E,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAA0M,GAAA,CAAA1B,eAAA,IAAwB;UAC5BrM,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAA0M,GAAA,CAAA1B,eAAA,IAAyB;UAQNrM,EAAA,CAAAoB,SAAA,GAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAA0M,GAAA,CAAA1B,eAAA,IAAyB;UA6CzBrM,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAA0M,GAAA,CAAA1B,eAAA,QAAA0B,GAAA,CAAA1B,eAAA,IAA+C;UAyDpDrM,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAA0M,GAAA,CAAA1B,eAAA,QAAA0B,GAAA,CAAA1B,eAAA,IAA+C;UA8K/CrM,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAA0M,GAAA,CAAA1B,eAAA,QAAA0B,GAAA,CAAA1B,eAAA,IAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}