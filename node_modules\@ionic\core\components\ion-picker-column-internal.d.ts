import type { Components, JSX } from "../dist/types/components";

interface IonPickerColumnInternal extends Components.IonPickerColumnInternal, HTMLElement {}
export const IonPickerColumnInternal: {
    prototype: IonPickerColumnInternal;
    new (): IonPickerColumnInternal;
};
/**
 * Used to define this component and all nested components recursively.
 */
export const defineCustomElement: () => void;
