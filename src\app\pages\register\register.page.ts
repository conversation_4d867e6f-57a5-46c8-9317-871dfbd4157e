import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LoadingController, ToastController } from '@ionic/angular';

export interface InvoiceData {
  invoiceNumber: string;
  invoiceDate: Date;
  customerName: string;
  customerAddress: string;
  zone: string;
  operatingUnit: string;
  organization: string;
  billToLocation: string;
  shipToLocation: string;
}

@Component({
  selector: 'app-register',
  templateUrl: './register.page.html',
  styleUrls: ['./register.page.scss'],
})
export class RegisterPage implements OnInit {

  complaintTypeForm!: FormGroup;
  complaintDescriptionForm!: FormGroup;
  invoiceSearchForm!: FormGroup;
  complaintDetailsForm!: FormGroup;
  isLoading = false;
  selectedFiles: File[] = [];
  selectedInvoice: InvoiceData | null = null;
  invoiceSearchResults: InvoiceData[] = [];
  showInvoiceResults = false;
  currentStep = 1;

  complaintTypes = [
    {
      value: 'glass_quality',
      label: 'Glass Quality Issues',
      icon: 'diamond-outline',
      description: 'Scratches, cracks, or defects in glass products'
    },
    {
      value: 'installation',
      label: 'Installation Problems',
      icon: 'construct-outline',
      description: 'Issues during glass installation process'
    },
    {
      value: 'delivery_damage',
      label: 'Delivery & Transportation',
      icon: 'car-outline',
      description: 'Damage during delivery or transportation'
    },
    {
      value: 'measurement',
      label: 'Measurement Issues',
      icon: 'resize-outline',
      description: 'Incorrect measurements or sizing problems'
    },
    {
      value: 'service',
      label: 'Service Related',
      icon: 'people-outline',
      description: 'Customer service or support issues'
    },
    {
      value: 'billing',
      label: 'Billing & Documentation',
      icon: 'document-text-outline',
      description: 'Invoice, billing, or documentation issues'
    }
  ];

  complaintDescriptions: { [key: string]: any[] } = {
    'glass_quality': [
      { value: 'scratches', label: 'Scratches on Glass Surface', description: 'Visible scratches or marks on the glass surface' },
      { value: 'cracks', label: 'Cracks or Chips', description: 'Cracks, chips, or fractures in the glass' },
      { value: 'bubbles', label: 'Air Bubbles', description: 'Air bubbles or inclusions within the glass' },
      { value: 'discoloration', label: 'Discoloration', description: 'Color variations or discoloration in the glass' },
      { value: 'thickness', label: 'Thickness Issues', description: 'Incorrect thickness or uneven glass thickness' }
    ],
    'installation': [
      { value: 'alignment', label: 'Alignment Problems', description: 'Glass not properly aligned during installation' },
      { value: 'sealing', label: 'Sealing Issues', description: 'Poor sealing or gaps around the glass' },
      { value: 'hardware', label: 'Hardware Problems', description: 'Issues with hinges, handles, or other hardware' },
      { value: 'fitting', label: 'Poor Fitting', description: 'Glass does not fit properly in the frame' },
      { value: 'damage_during', label: 'Damage During Installation', description: 'Glass damaged during the installation process' }
    ],
    'delivery_damage': [
      { value: 'broken_transit', label: 'Broken in Transit', description: 'Glass broken during transportation' },
      { value: 'packaging', label: 'Poor Packaging', description: 'Inadequate packaging causing damage' },
      { value: 'handling', label: 'Rough Handling', description: 'Damage due to rough handling during delivery' },
      { value: 'delayed', label: 'Delayed Delivery', description: 'Delivery was significantly delayed' },
      { value: 'wrong_item', label: 'Wrong Item Delivered', description: 'Incorrect glass type or specifications delivered' }
    ],
    'measurement': [
      { value: 'wrong_size', label: 'Wrong Size', description: 'Glass delivered in incorrect dimensions' },
      { value: 'measurement_error', label: 'Measurement Error', description: 'Error in initial measurements taken' },
      { value: 'specification', label: 'Specification Mismatch', description: 'Glass does not match ordered specifications' },
      { value: 'template', label: 'Template Issues', description: 'Problems with measurement template or pattern' }
    ],
    'service': [
      { value: 'communication', label: 'Poor Communication', description: 'Lack of proper communication from service team' },
      { value: 'response_time', label: 'Slow Response Time', description: 'Delayed response to queries or complaints' },
      { value: 'unprofessional', label: 'Unprofessional Behavior', description: 'Unprofessional conduct by service personnel' },
      { value: 'incomplete_work', label: 'Incomplete Work', description: 'Service work left incomplete or unfinished' }
    ],
    'billing': [
      { value: 'wrong_amount', label: 'Incorrect Amount', description: 'Wrong amount charged in the invoice' },
      { value: 'missing_details', label: 'Missing Details', description: 'Important details missing from invoice' },
      { value: 'duplicate', label: 'Duplicate Billing', description: 'Charged multiple times for the same service' },
      { value: 'tax_error', label: 'Tax Calculation Error', description: 'Incorrect tax calculation or application' }
    ]
  };

  // Sample invoice data for demonstration
  sampleInvoices: InvoiceData[] = [
    {
      invoiceNumber: 'INV-2024-001',
      invoiceDate: new Date('2024-01-15'),
      customerName: 'ABC Construction Ltd.',
      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',
      zone: 'North Zone',
      operatingUnit: 'Delhi Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'Head Office - Gurgaon',
      shipToLocation: 'Site Office - Noida, UP'
    },
    {
      invoiceNumber: 'INV-2024-002',
      invoiceDate: new Date('2024-01-18'),
      customerName: 'XYZ Builders Pvt. Ltd.',
      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',
      zone: 'North Zone',
      operatingUnit: 'Chandigarh Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'Regional Office - Chandigarh',
      shipToLocation: 'Project Site - Mohali, Punjab'
    },
    {
      invoiceNumber: 'INV-2024-003',
      invoiceDate: new Date('2024-01-20'),
      customerName: 'Modern Glass Solutions',
      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',
      zone: 'South Zone',
      operatingUnit: 'Bangalore Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'South Regional Office - Bangalore',
      shipToLocation: 'Client Location - Whitefield, Bangalore'
    },
    {
      invoiceNumber: 'INV-2024-004',
      invoiceDate: new Date('2024-01-22'),
      customerName: 'Premium Interiors Pvt. Ltd.',
      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',
      zone: 'West Zone',
      operatingUnit: 'Mumbai Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'West Regional Office - Mumbai',
      shipToLocation: 'Project Site - Andheri, Mumbai'
    },
    {
      invoiceNumber: 'INV-2024-005',
      invoiceDate: new Date('2024-01-25'),
      customerName: 'Elite Developers',
      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',
      zone: 'East Zone',
      operatingUnit: 'Kolkata Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'East Regional Office - Kolkata',
      shipToLocation: 'Construction Site - New Town, Kolkata'
    },
    {
      invoiceNumber: 'INV-2024-006',
      invoiceDate: new Date('2024-01-28'),
      customerName: 'Skyline Architects',
      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',
      zone: 'South Zone',
      operatingUnit: 'Hyderabad Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'South Regional Office - Hyderabad',
      shipToLocation: 'Project Location - Gachibowli, Hyderabad'
    },
    {
      invoiceNumber: 'INV-2024-007',
      invoiceDate: new Date('2024-02-01'),
      customerName: 'Royal Glass Works',
      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',
      zone: 'West Zone',
      operatingUnit: 'Ahmedabad Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'West Regional Office - Ahmedabad',
      shipToLocation: 'Factory Location - Gandhinagar, Gujarat'
    },
    {
      invoiceNumber: 'INV-2024-008',
      invoiceDate: new Date('2024-02-05'),
      customerName: 'Metro Construction Co.',
      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',
      zone: 'North Zone',
      operatingUnit: 'Delhi Operations',
      organization: 'AIS Glass Solutions Pvt. Ltd.',
      billToLocation: 'Head Office - New Delhi',
      shipToLocation: 'Metro Station Site - Dwarka, Delhi'
    }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private loadingController: LoadingController,
    private toastController: ToastController
  ) {
    this.createForms();
  }

  ngOnInit() {
    // Show all invoices initially
    this.showAllInvoices();
  }

  createForms() {
    this.complaintTypeForm = this.formBuilder.group({
      selectedType: ['', Validators.required]
    });

    this.complaintDescriptionForm = this.formBuilder.group({
      selectedDescription: ['', Validators.required]
    });

    this.invoiceSearchForm = this.formBuilder.group({
      searchTerm: [''] // No validation required since empty search shows all invoices
    });

    this.complaintDetailsForm = this.formBuilder.group({
      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],
      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      comments: [''],
      hasComplaintLetters: [false],
      attachedFile: [null]
    });
  }

  selectComplaintType(type: any) {
    this.complaintTypeForm.patchValue({ selectedType: type.value });
    // Automatically go to step 2 when type is selected
    setTimeout(() => {
      this.goToStep2();
    }, 300);
  }

  getComplaintDescriptions() {
    const selectedType = this.complaintTypeForm.get('selectedType')?.value;
    return this.complaintDescriptions[selectedType] || [];
  }

  getSelectedComplaintDescription() {
    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;
    const descriptions = this.getComplaintDescriptions();
    return descriptions.find(desc => desc.value === selectedValue);
  }

  onDescriptionChange(event: any) {
    // Automatically go to step 3 when description is selected
    setTimeout(() => {
      this.goToStep3();
    }, 300);
  }

  // Navigation methods
  goToStep2() {
    if (this.complaintTypeForm.valid) {
      this.currentStep = 2;
    }
  }

  goToStep3() {
    if (this.complaintDescriptionForm.valid) {
      this.currentStep = 3;
      this.showAllInvoices();
    }
  }

  goToStep4() {
    if (this.selectedInvoice) {
      this.currentStep = 4;
    }
  }

  goBackToStep1() {
    this.currentStep = 1;
  }

  goBackToStep2() {
    this.currentStep = 2;
  }

  goBackToStep3() {
    this.currentStep = 3;
    this.showAllInvoices();
  }

  showAllInvoices() {
    this.invoiceSearchResults = [...this.sampleInvoices];
    this.showInvoiceResults = true;
  }

  onInvoiceSearch() {
    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;

    if (!searchTerm || searchTerm.trim() === '') {
      // Show all invoices when search is empty
      this.showAllInvoices();
      return;
    }

    if (searchTerm.length >= 1) {
      this.isLoading = true;

      // Simulate search delay
      setTimeout(() => {
        this.invoiceSearchResults = this.sampleInvoices.filter(invoice =>
          invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
          invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||
          invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase())
        );
        this.showInvoiceResults = true;
        this.isLoading = false;
      }, 500);
    }
  }

  selectInvoice(invoice: InvoiceData) {
    this.selectedInvoice = invoice;
    this.showInvoiceResults = false;
    this.invoiceSearchForm.patchValue({ searchTerm: invoice.invoiceNumber });
    // Automatically go to step 4 when invoice is selected
    setTimeout(() => {
      this.goToStep4();
    }, 300);
  }

  clearInvoiceSelection() {
    this.selectedInvoice = null;
    this.invoiceSearchForm.patchValue({ searchTerm: '' });
    // Show all invoices again when clearing selection
    this.showAllInvoices();
  }

  async onSubmitComplaint() {
    if (this.complaintTypeForm.valid && this.complaintDescriptionForm.valid && this.selectedInvoice && this.complaintDetailsForm.valid) {
      this.isLoading = true;

      const loading = await this.loadingController.create({
        message: 'Registering complaint...',
        duration: 3000
      });

      await loading.present();

      // Simulate registration process
      setTimeout(async () => {
        this.isLoading = false;
        await loading.dismiss();

        const toast = await this.toastController.create({
          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),
          duration: 4000,
          color: 'success',
          position: 'top'
        });
        await toast.present();

        // Navigate to track page
        this.router.navigate(['/track']);
      }, 3000);
    } else {
      const toast = await this.toastController.create({
        message: 'Please complete all required steps and fill in all required fields.',
        duration: 3000,
        color: 'danger',
        position: 'top'
      });
      await toast.present();
    }
  }

  onFileSelected(event: any) {
    const files = event.target.files;
    if (files && files.length > 0) {
      this.selectedFiles = Array.from(files);
      this.complaintDetailsForm.patchValue({ attachedFile: files[0] });
    }
  }

  removeFile(index: number) {
    this.selectedFiles.splice(index, 1);
    if (this.selectedFiles.length === 0) {
      this.complaintDetailsForm.patchValue({ attachedFile: null });
    }
  }

  getSelectedComplaintType() {
    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;
    return this.complaintTypes.find(type => type.value === selectedValue);
  }

  isStepCompleted(step: number): boolean {
    switch (step) {
      case 1:
        return this.complaintTypeForm.valid && this.currentStep > 1;
      case 2:
        return this.complaintDescriptionForm.valid && this.currentStep > 2;
      case 3:
        return this.selectedInvoice !== null && this.currentStep > 3;
      case 4:
        return this.complaintDetailsForm.valid && this.currentStep > 4;
      default:
        return false;
    }
  }

  getErrorMessage(form: FormGroup, field: string): string {
    const control = form.get(field);
    if (control?.hasError('required')) {
      return `${this.getFieldLabel(field)} is required`;
    }
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength'].requiredLength;
      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;
    }
    if (control?.hasError('pattern')) {
      return 'Please enter a valid phone number (10 digits)';
    }
    return '';
  }

  private getFieldLabel(field: string): string {
    const labels: { [key: string]: string } = {
      selectedType: 'Complaint Type',
      selectedDescription: 'Complaint Description',
      searchTerm: 'Search Term',
      contactPersonName: 'Contact Person Name',
      contactNumber: 'Contact Number',
      comments: 'Comments'
    };
    return labels[field] || field;
  }

  goBack() {
    this.router.navigate(['/home']);
  }
}
