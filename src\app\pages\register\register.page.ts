import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { LoadingController, ToastController } from '@ionic/angular';

@Component({
  selector: 'app-register',
  templateUrl: './register.page.html',
  styleUrls: ['./register.page.scss'],
})
export class RegisterPage implements OnInit {

  personalInfoForm!: FormGroup;
  complaintForm!: FormGroup;
  isLoading = false;
  selectedFiles: File[] = [];

  complaintTypes = [
    { value: 'quality', label: 'Quality Issue' },
    { value: 'delivery', label: 'Delivery Issue' },
    { value: 'damage', label: 'Damage' },
    { value: 'missing', label: 'Missing Items' },
    { value: 'other', label: 'Other' }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private loadingController: LoadingController,
    private toastController: ToastController
  ) {
    this.createForms();
  }

  ngOnInit() {}

  createForms() {
    this.personalInfoForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      company: ['', Validators.required],
      address: ['', Validators.required]
    });

    this.complaintForm = this.formBuilder.group({
      invoiceNumber: ['', Validators.required],
      complaintType: ['', Validators.required],
      description: ['', [Validators.required, Validators.minLength(10)]],
      urgency: ['medium', Validators.required],
      agreedToTerms: [false, Validators.requiredTrue]
    });
  }

  async onSubmit() {
    if (this.personalInfoForm.valid && this.complaintForm.valid) {
      this.isLoading = true;
      
      const loading = await this.loadingController.create({
        message: 'Registering complaint...',
        duration: 3000
      });
      
      await loading.present();
      
      // Simulate registration process
      setTimeout(async () => {
        this.isLoading = false;
        await loading.dismiss();
        
        const toast = await this.toastController.create({
          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),
          duration: 4000,
          color: 'success',
          position: 'top'
        });
        await toast.present();
        
        // Navigate to track page
        this.router.navigate(['/track']);
      }, 3000);
    } else {
      const toast = await this.toastController.create({
        message: 'Please fill in all required fields correctly.',
        duration: 3000,
        color: 'danger',
        position: 'top'
      });
      await toast.present();
    }
  }

  onFileSelected(event: any) {
    const files = event.target.files;
    if (files) {
      this.selectedFiles = Array.from(files);
    }
  }

  removeFile(index: number) {
    this.selectedFiles.splice(index, 1);
  }

  getErrorMessage(form: FormGroup, field: string): string {
    const control = form.get(field);
    if (control?.hasError('required')) {
      return `${this.getFieldLabel(field)} is required`;
    }
    if (control?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    if (control?.hasError('minlength')) {
      const minLength = control.errors?.['minlength'].requiredLength;
      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;
    }
    if (control?.hasError('pattern')) {
      return 'Please enter a valid phone number (10 digits)';
    }
    return '';
  }

  private getFieldLabel(field: string): string {
    const labels: { [key: string]: string } = {
      firstName: 'First Name',
      lastName: 'Last Name',
      email: 'Email',
      phone: 'Phone',
      company: 'Company',
      address: 'Address',
      invoiceNumber: 'Invoice Number',
      complaintType: 'Complaint Type',
      description: 'Description',
      urgency: 'Urgency'
    };
    return labels[field] || field;
  }

  goBack() {
    this.router.navigate(['/home']);
  }
}
