/* Material Design Home Page Styles */

.material-header {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
              0px 4px 5px 0px rgba(0, 0, 0, 0.14),
              0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.material-toolbar {
  height: 64px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  
  .toolbar-title {
    font-size: 20px;
    font-weight: 500;
    margin-left: 16px;
  }
  
  .spacer {
    flex: 1 1 auto;
  }
}

.material-content {
  --background: #fafafa;
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  padding: 40px 20px;
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }
  
  .hero-content {
    position: relative;
    z-index: 1;
  }
  
  .hero-title {
    font-size: 28px;
    font-weight: 300;
    margin: 0 0 8px 0;
    letter-spacing: 0.5px;
  }
  
  .hero-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
  }
}

/* Tab Navigation */
.tab-navigation {
  background: white;
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2);
  
  .custom-tabs {
    .mat-tab-label {
      min-width: 0;
      padding: 12px 16px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      
      .tab-icon {
        margin-right: 8px;
        font-size: 18px;
      }
    }
    
    .mat-ink-bar {
      height: 3px;
      background: var(--ion-color-primary);
    }
  }
}

/* Content Section */
.content-section {
  padding: 24px 0;
  min-height: calc(100vh - 300px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.section-title {
  font-size: 24px;
  font-weight: 400;
  color: #333;
  margin: 0 0 8px 0;
  text-align: center;
}

.section-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0 0 32px 0;
  text-align: center;
}

/* Cards Grid */
.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  padding: 0 8px;
}

.info-card {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-4px);
    box-shadow: 0px 8px 25px rgba(0, 0, 0, 0.15);
  }
  
  .mat-card-header {
    padding: 20px 20px 16px 20px;
    
    .card-avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      .card-icon {
        font-size: 24px;
        color: white;
      }
    }
    
    .card-title {
      font-size: 18px;
      font-weight: 500;
      line-height: 1.3;
      margin: 0;
    }
  }
  
  .mat-card-content {
    padding: 0 20px 16px 20px;
    
    .card-description {
      color: #666;
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
    }
  }
  
  .mat-card-actions {
    padding: 8px 20px 20px 20px;
    
    .mat-button {
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
  }
}

/* Card Color Variants */
.card-primary .card-avatar {
  background: linear-gradient(135deg, #1976d2, #1565c0);
}

.card-secondary .card-avatar {
  background: linear-gradient(135deg, #3dc2ff, #36abe0);
}

.card-tertiary .card-avatar {
  background: linear-gradient(135deg, #5260ff, #4854e0);
}

.card-success .card-avatar {
  background: linear-gradient(135deg, #2dd36f, #28ba62);
}

.card-warning .card-avatar {
  background: linear-gradient(135deg, #ffc409, #e0ac08);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    padding: 32px 16px;
    
    .hero-title {
      font-size: 24px;
    }
    
    .hero-subtitle {
      font-size: 14px;
    }
  }
  
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 0 4px;
  }
  
  .container {
    padding: 0 12px;
  }
  
  .section-title {
    font-size: 20px;
  }
  
  .section-subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .material-toolbar .toolbar-title {
    font-size: 18px;
  }
  
  .hero-section {
    padding: 24px 12px;
    
    .hero-title {
      font-size: 20px;
    }
  }
  
  .tab-navigation .custom-tabs .mat-tab-label {
    padding: 12px 8px;
    font-size: 12px;
    
    .tab-icon {
      margin-right: 4px;
      font-size: 16px;
    }
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Floating Action Button */
ion-fab-button {
  --background: var(--ion-color-primary);
  --color: white;
  --box-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),
                0px 6px 10px 0px rgba(0, 0, 0, 0.14),
                0px 1px 18px 0px rgba(0, 0, 0, 0.12);
  
  &:hover {
    --box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
                  0px 8px 10px 1px rgba(0, 0, 0, 0.14),
                  0px 3px 14px 2px rgba(0, 0, 0, 0.12);
  }
}
