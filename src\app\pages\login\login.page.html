<ion-content class="login-content">
  <div class="login-container">
    <!-- Logo Section -->
    <div class="logo-section">
      <div class="logo-circle">
        <ion-icon name="business" class="logo-icon"></ion-icon>
      </div>
      <h1 class="app-title">AIS Smart Care</h1>
      <p class="app-subtitle">Your Glass Care Solution</p>
    </div>

    <!-- Login Form -->
    <mat-card class="login-card">
      <mat-card-header>
        <mat-card-title class="login-title">Welcome Back</mat-card-title>
        <mat-card-subtitle>Sign in to your account</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="login-form">
          <!-- Username Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Username</mat-label>
            <input 
              matInput 
              formControlName="username" 
              placeholder="Enter your username"
              autocomplete="username">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="loginForm.get('username')?.invalid && loginForm.get('username')?.touched">
              {{ getErrorMessage('username') }}
            </mat-error>
          </mat-form-field>

          <!-- Password Field -->
          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Password</mat-label>
            <input 
              matInput 
              [type]="hidePassword ? 'password' : 'text'"
              formControlName="password" 
              placeholder="Enter your password"
              autocomplete="current-password">
            <button 
              mat-icon-button 
              matSuffix 
              type="button"
              (click)="togglePasswordVisibility()"
              [attr.aria-label]="'Hide password'"
              [attr.aria-pressed]="hidePassword">
              <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
            </button>
            <mat-error *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
              {{ getErrorMessage('password') }}
            </mat-error>
          </mat-form-field>

          <!-- Login Button -->
          <button 
            mat-raised-button 
            color="primary" 
            type="submit"
            class="login-button full-width"
            [disabled]="isLoading || loginForm.invalid">
            <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
            <span *ngIf="!isLoading">Sign In</span>
            <span *ngIf="isLoading">Signing In...</span>
          </button>
        </form>
      </mat-card-content>

      <mat-card-actions class="login-actions">
        <button mat-button color="primary" class="forgot-password">
          Forgot Password?
        </button>
      </mat-card-actions>
    </mat-card>

    <!-- Footer -->
    <div class="login-footer">
      <p class="footer-text">
        Don't have an account? 
        <button mat-button color="primary" routerLink="/register" class="register-link">
          Register here
        </button>
      </p>
      <p class="version-text">Version 1.0.0</p>
    </div>
  </div>
</ion-content>
