<ion-header [translucent]="true" class="material-header">
  <mat-toolbar class="material-toolbar">
    <button mat-icon-button (click)="openMenu()" aria-label="Menu">
      <mat-icon>menu</mat-icon>
    </button>
    <span class="toolbar-title">AIS Smart Care</span>
    <span class="spacer"></span>
  </mat-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="material-content">
  <!-- Hero Section -->
  <div class="hero-section">
    <div class="hero-content">
      <h1 class="hero-title">Welcome to AIS Smart Care</h1>
      <p class="hero-subtitle">Your comprehensive glass care solution</p>
    </div>
  </div>

  <!-- Navigation Tabs -->
  <div class="tab-navigation">
    <mat-tab-group 
      [selectedIndex]="activeTab === 'home' ? 0 : activeTab === 'register' ? 1 : activeTab === 'track' ? 2 : 3"
      (selectedTabChange)="onTabChange($event.index === 0 ? 'home' : $event.index === 1 ? 'register' : $event.index === 2 ? 'track' : 'feedback')"
      mat-stretch-tabs
      class="custom-tabs">
      <mat-tab label="Home">
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">home</mat-icon>
          Home
        </ng-template>
      </mat-tab>
      <mat-tab label="Register">
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">add_circle</mat-icon>
          Register
        </ng-template>
      </mat-tab>
      <mat-tab label="Track">
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">track_changes</mat-icon>
          Track
        </ng-template>
      </mat-tab>
      <mat-tab label="Feedback">
        <ng-template mat-tab-label>
          <mat-icon class="tab-icon">feedback</mat-icon>
          Feedback
        </ng-template>
      </mat-tab>
    </mat-tab-group>
  </div>

  <!-- Content Section -->
  <div class="content-section" *ngIf="activeTab === 'home'">
    <div class="container">
      <h2 class="section-title">Quick Access</h2>
      <p class="section-subtitle">Access important documents and guidelines</p>
      
      <div class="cards-grid">
        <mat-card 
          *ngFor="let item of menuItems; let i = index" 
          class="info-card"
          matRipple
          (click)="onCardClick(item)"
          [class]="'card-' + item.color">
          
          <mat-card-header>
            <div mat-card-avatar class="card-avatar">
              <ion-icon [name]="item.icon" class="card-icon"></ion-icon>
            </div>
            <mat-card-title class="card-title">{{ item.title }}</mat-card-title>
          </mat-card-header>
          
          <mat-card-content>
            <p class="card-description">{{ item.description }}</p>
          </mat-card-content>
          
          <mat-card-actions align="end">
            <button mat-button [color]="item.color">
              <mat-icon>arrow_forward</mat-icon>
              View
            </button>
          </mat-card-actions>
        </mat-card>
      </div>
    </div>
  </div>

  <!-- Floating Action Button -->
  <ion-fab vertical="bottom" horizontal="end" slot="fixed" *ngIf="activeTab === 'home'">
    <ion-fab-button color="primary" (click)="onTabChange('register')">
      <ion-icon name="add"></ion-icon>
    </ion-fab-button>
  </ion-fab>
</ion-content>

<!-- Side Menu -->
<ion-menu side="start" menuId="main-menu" contentId="main-content">
  <ion-header>
    <ion-toolbar color="primary">
      <ion-title>Menu</ion-title>
    </ion-toolbar>
  </ion-header>
  
  <ion-content>
    <ion-list>
      <ion-item button (click)="onTabChange('home')">
        <ion-icon name="home" slot="start"></ion-icon>
        <ion-label>Home</ion-label>
      </ion-item>
      
      <ion-item button routerLink="/creditnote">
        <ion-icon name="document-text" slot="start"></ion-icon>
        <ion-label>Credit Note</ion-label>
      </ion-item>
      
      <ion-item button>
        <ion-icon name="information-circle" slot="start"></ion-icon>
        <ion-label>About</ion-label>
      </ion-item>
      
      <ion-item button routerLink="/login">
        <ion-icon name="log-out" slot="start"></ion-icon>
        <ion-label>Log Out</ion-label>
      </ion-item>
    </ion-list>
  </ion-content>
</ion-menu>
