/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import "~@ionic/angular/css/core.css";

/* Basic CSS for apps built with Ionic */
@import "~@ionic/angular/css/normalize.css";
@import "~@ionic/angular/css/structure.css";
@import "~@ionic/angular/css/typography.css";
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "~@ionic/angular/css/padding.css";
@import "~@ionic/angular/css/float-elements.css";
@import "~@ionic/angular/css/text-alignment.css";
@import "~@ionic/angular/css/text-transformation.css";
@import "~@ionic/angular/css/flex-utils.css";

/* Material Design Styles */
html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }

/* Custom Material Design Components */
.mat-elevation-z1 {
  box-shadow: 0px 2px 1px -1px rgba(0, 0, 0, 0.2),
              0px 1px 1px 0px rgba(0, 0, 0, 0.14),
              0px 1px 3px 0px rgba(0, 0, 0, 0.12);
}

.mat-elevation-z2 {
  box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.2),
              0px 2px 2px 0px rgba(0, 0, 0, 0.14),
              0px 1px 5px 0px rgba(0, 0, 0, 0.12);
}

.mat-elevation-z4 {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
              0px 4px 5px 0px rgba(0, 0, 0, 0.14),
              0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.mat-elevation-z8 {
  box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),
              0px 8px 10px 1px rgba(0, 0, 0, 0.14),
              0px 3px 14px 2px rgba(0, 0, 0, 0.12);
}

/* Custom Material Card Styles */
.material-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin: 8px;
  @extend .mat-elevation-z2;
  
  &:hover {
    @extend .mat-elevation-z4;
    transition: box-shadow 0.3s ease;
  }
}

/* Material Design Button Styles */
.mat-raised-button {
  border-radius: 4px;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.0892857143em;
}

.mat-fab {
  border-radius: 50%;
  width: 56px;
  height: 56px;
}

/* Custom Layout Classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 8px;
  }
  
  .material-card {
    margin: 4px;
    padding: 12px;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { 
    transform: translateY(20px);
    opacity: 0;
  }
  to { 
    transform: translateY(0);
    opacity: 1;
  }
}

/* Custom Color Classes */
.text-primary {
  color: var(--ion-color-primary);
}

.text-secondary {
  color: var(--ion-color-secondary);
}

.text-success {
  color: var(--ion-color-success);
}

.text-warning {
  color: var(--ion-color-warning);
}

.text-danger {
  color: var(--ion-color-danger);
}

.bg-primary {
  background-color: var(--ion-color-primary);
  color: var(--ion-color-primary-contrast);
}

.bg-secondary {
  background-color: var(--ion-color-secondary);
  color: var(--ion-color-secondary-contrast);
}

/* Material Design Form Styles */
.mat-form-field {
  width: 100%;
  margin-bottom: 16px;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  border-radius: 8px;
}

/* Custom Ionic + Material Design Integration */
ion-content.material-content {
  --background: #fafafa;
}

ion-header.material-header {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
              0px 4px 5px 0px rgba(0, 0, 0, 0.14),
              0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

ion-toolbar.material-toolbar {
  --background: var(--mat-primary);
  --color: white;
  height: 64px;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

/* Error States */
.error-message {
  color: var(--ion-color-danger);
  font-size: 14px;
  margin-top: 4px;
}

.success-message {
  color: var(--ion-color-success);
  font-size: 14px;
  margin-top: 4px;
}
