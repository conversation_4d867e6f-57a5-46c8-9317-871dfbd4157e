{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.0625rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.625rem, 65.988px)}:host(.checkbox-disabled){opacity:0.3}:host(.in-item.legacy-checkbox){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:9px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:8px;margin-bottom:8px}\";\nconst IonCheckboxIosStyle0 = checkboxIosCss;\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.legacy-checkbox.checkbox-disabled),:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}:host(.in-item.legacy-checkbox){margin-left:0;margin-right:0;margin-top:18px;margin-bottom:18px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:36px;margin-inline-end:36px;margin-top:18px;margin-bottom:18px}\";\nconst IonCheckboxMdStyle0 = checkboxMdCss;\nconst Checkbox = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionChange = createEvent(this, \"ionChange\", 7);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.ionStyle = createEvent(this, \"ionStyle\", 7);\n    this.inputId = `ion-cb-${checkboxIds++}`;\n    this.inheritedAttributes = {};\n    // TODO(FW-3100): remove this\n    // This flag ensures we log the deprecation warning at most once.\n    this.hasLoggedDeprecationWarning = false;\n    /**\n     * Sets the checked property and emits\n     * the ionChange event. Use this to update the\n     * checked state in response to user-generated\n     * actions such as a click.\n     */\n    this.setChecked = state => {\n      const isChecked = this.checked = state;\n      this.ionChange.emit({\n        checked: isChecked,\n        value: this.value\n      });\n    };\n    this.toggleChecked = ev => {\n      ev.preventDefault();\n      this.setFocus();\n      this.setChecked(!this.checked);\n      this.indeterminate = false;\n    };\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.onClick = ev => {\n      if (this.disabled) {\n        return;\n      }\n      this.toggleChecked(ev);\n    };\n    this.color = undefined;\n    this.name = this.inputId;\n    this.checked = false;\n    this.indeterminate = false;\n    this.disabled = false;\n    this.value = 'on';\n    this.labelPlacement = 'start';\n    this.justify = 'space-between';\n    this.alignment = 'center';\n    this.legacy = undefined;\n  }\n  connectedCallback() {\n    this.legacyFormController = createLegacyFormController(this.el); // TODO(FW-3100): remove this\n  }\n  componentWillLoad() {\n    this.emitStyle();\n    // TODO(FW-3100): remove check\n    if (!this.legacyFormController.hasLegacyControl()) {\n      this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n    }\n  }\n  styleChanged() {\n    this.emitStyle();\n  }\n  emitStyle() {\n    const style = {\n      'interactive-disabled': this.disabled,\n      // TODO(FW-3100): remove this\n      legacy: !!this.legacy\n    };\n    // TODO(FW-3100): remove this\n    if (this.legacyFormController.hasLegacyControl()) {\n      style['checkbox-checked'] = this.checked;\n    }\n    this.ionStyle.emit(style);\n  }\n  setFocus() {\n    if (this.focusEl) {\n      this.focusEl.focus();\n    }\n  }\n  // TODO(FW-3100): run contents of renderCheckbox directly instead\n  render() {\n    const {\n      legacyFormController\n    } = this;\n    return legacyFormController.hasLegacyControl() ? this.renderLegacyCheckbox() : this.renderCheckbox();\n  }\n  renderCheckbox() {\n    const {\n      color,\n      checked,\n      disabled,\n      el,\n      getSVGPath,\n      indeterminate,\n      inheritedAttributes,\n      inputId,\n      justify,\n      labelPlacement,\n      name,\n      value,\n      alignment\n    } = this;\n    const mode = getIonMode(this);\n    const path = getSVGPath(mode, indeterminate);\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    return h(Host, {\n      \"aria-checked\": indeterminate ? 'mixed' : `${checked}`,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'checkbox-checked': checked,\n        'checkbox-disabled': disabled,\n        'checkbox-indeterminate': indeterminate,\n        interactive: true,\n        [`checkbox-justify-${justify}`]: true,\n        [`checkbox-alignment-${alignment}`]: true,\n        [`checkbox-label-placement-${labelPlacement}`]: true\n      }),\n      onClick: this.onClick\n    }, h(\"label\", {\n      class: \"checkbox-wrapper\"\n    }, h(\"input\", Object.assign({\n      type: \"checkbox\",\n      checked: checked ? true : undefined,\n      disabled: disabled,\n      id: inputId,\n      onChange: this.toggleChecked,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl\n    }, inheritedAttributes)), h(\"div\", {\n      class: {\n        'label-text-wrapper': true,\n        'label-text-wrapper-hidden': el.textContent === ''\n      },\n      part: \"label\"\n    }, h(\"slot\", null)), h(\"div\", {\n      class: \"native-wrapper\"\n    }, h(\"svg\", {\n      class: \"checkbox-icon\",\n      viewBox: \"0 0 24 24\",\n      part: \"container\"\n    }, path))));\n  }\n  // TODO(FW-3100): remove this\n  renderLegacyCheckbox() {\n    if (!this.hasLoggedDeprecationWarning) {\n      printIonWarning(`ion-checkbox now requires providing a label with either the default slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-checkbox>Label</ion-checkbox>\nExample with aria-label: <ion-checkbox aria-label=\"Label\"></ion-checkbox>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n      if (this.legacy) {\n        printIonWarning(`ion-checkbox is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new checkbox syntax.`, this.el);\n      }\n      this.hasLoggedDeprecationWarning = true;\n    }\n    const {\n      color,\n      checked,\n      disabled,\n      el,\n      getSVGPath,\n      indeterminate,\n      inputId,\n      name,\n      value\n    } = this;\n    const mode = getIonMode(this);\n    const {\n      label,\n      labelId,\n      labelText\n    } = getAriaLabel(el, inputId);\n    const path = getSVGPath(mode, indeterminate);\n    renderHiddenInput(true, el, name, checked ? value : '', disabled);\n    return h(Host, {\n      \"aria-labelledby\": label ? labelId : null,\n      \"aria-checked\": `${checked}`,\n      \"aria-hidden\": disabled ? 'true' : null,\n      role: \"checkbox\",\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-item': hostContext('ion-item', el),\n        'checkbox-checked': checked,\n        'checkbox-disabled': disabled,\n        'checkbox-indeterminate': indeterminate,\n        'legacy-checkbox': true,\n        interactive: true\n      }),\n      onClick: this.onClick\n    }, h(\"svg\", {\n      class: \"checkbox-icon\",\n      viewBox: \"0 0 24 24\",\n      part: \"container\"\n    }, path), h(\"label\", {\n      htmlFor: inputId\n    }, labelText), h(\"input\", {\n      type: \"checkbox\",\n      \"aria-checked\": `${checked}`,\n      disabled: disabled,\n      id: inputId,\n      onChange: this.toggleChecked,\n      onFocus: () => this.onFocus(),\n      onBlur: () => this.onBlur(),\n      ref: focusEl => this.focusEl = focusEl\n    }));\n  }\n  getSVGPath(mode, indeterminate) {\n    let path = indeterminate ? h(\"path\", {\n      d: \"M6 12L18 12\",\n      part: \"mark\"\n    }) : h(\"path\", {\n      d: \"M5.9,12.5l3.8,3.8l8.8-8.8\",\n      part: \"mark\"\n    });\n    if (mode === 'md') {\n      path = indeterminate ? h(\"path\", {\n        d: \"M2 12H22\",\n        part: \"mark\"\n      }) : h(\"path\", {\n        d: \"M1.73,12.91 8.1,19.28 22.79,4.59\",\n        part: \"mark\"\n      });\n    }\n    return path;\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"checked\": [\"styleChanged\"],\n      \"disabled\": [\"styleChanged\"]\n    };\n  }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n  ios: IonCheckboxIosStyle0,\n  md: IonCheckboxMdStyle0\n};\nexport { Checkbox as ion_checkbox };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "c", "createLegacyFormController", "i", "inheritAriaAttributes", "renderHiddenInput", "e", "getAriaLabel", "p", "printIonWarning", "createColorClasses", "hostContext", "b", "getIonMode", "checkboxIosCss", "IonCheckboxIosStyle0", "checkboxMdCss", "IonCheckboxMdStyle0", "Checkbox", "constructor", "hostRef", "ionChange", "ionFocus", "ionBlur", "ionStyle", "inputId", "checkboxIds", "inheritedAttributes", "hasLoggedDeprecationWarning", "setChecked", "state", "isChecked", "checked", "emit", "value", "toggleChecked", "ev", "preventDefault", "setFocus", "indeterminate", "onFocus", "onBlur", "onClick", "disabled", "color", "undefined", "name", "labelPlacement", "justify", "alignment", "legacy", "connectedCallback", "legacyFormController", "el", "componentWillLoad", "emitStyle", "hasLegacyControl", "Object", "assign", "styleChanged", "style", "focusEl", "focus", "render", "renderLegacyCheckbox", "renderCheckbox", "getSV<PERSON>ath", "mode", "path", "class", "interactive", "type", "id", "onChange", "ref", "textContent", "part", "viewBox", "label", "labelId", "labelText", "role", "htmlFor", "watchers", "ios", "md", "ion_checkbox"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/ion-checkbox.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { c as createLegacyFormController } from './form-controller-21dd62b1.js';\nimport { i as inheritAriaAttributes, d as renderHiddenInput, e as getAriaLabel } from './helpers-be245865.js';\nimport { p as printIonWarning } from './index-9b0d46f4.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst checkboxIosCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:50%;--border-width:0.0625rem;--border-style:solid;--border-color:rgba(var(--ion-text-color-rgb, 0, 0, 0), 0.23);--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--size:min(1.625rem, 65.988px)}:host(.checkbox-disabled){opacity:0.3}:host(.in-item.legacy-checkbox){-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:8px;margin-inline-end:8px;margin-top:10px;margin-bottom:9px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:2px;margin-inline-start:2px;-webkit-margin-end:16px;margin-inline-end:16px;margin-top:8px;margin-bottom:8px}\";\nconst IonCheckboxIosStyle0 = checkboxIosCss;\n\nconst checkboxMdCss = \":host{--checkbox-background-checked:var(--ion-color-primary, #3880ff);--border-color-checked:var(--ion-color-primary, #3880ff);--checkmark-color:var(--ion-color-primary-contrast, #fff);--checkmark-width:1;--transition:none;display:inline-block;position:relative;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:2}:host(.in-item){width:100%;height:100%}:host([slot=start]:not(.legacy-checkbox)),:host([slot=end]:not(.legacy-checkbox)){width:auto}:host(.legacy-checkbox){width:var(--size);height:var(--size)}:host(.ion-color){--checkbox-background-checked:var(--ion-color-base);--border-color-checked:var(--ion-color-base);--checkmark-color:var(--ion-color-contrast)}:host(.legacy-checkbox) label{top:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;position:absolute;width:100%;height:100%;border:0;background:transparent;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none;outline:none;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;opacity:0}@supports (inset-inline-start: 0){:host(.legacy-checkbox) label{inset-inline-start:0}}@supports not (inset-inline-start: 0){:host(.legacy-checkbox) label{left:0}:host-context([dir=rtl]):host(.legacy-checkbox) label,:host-context([dir=rtl]).legacy-checkbox label{left:unset;right:unset;right:0}@supports selector(:dir(rtl)){:host(.legacy-checkbox:dir(rtl)) label{left:unset;right:unset;right:0}}}:host(.legacy-checkbox) label::-moz-focus-inner{border:0}.checkbox-wrapper{display:-ms-flexbox;display:flex;-ms-flex-positive:1;flex-grow:1;-ms-flex-align:center;align-items:center;height:inherit;cursor:inherit}.label-text-wrapper{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}:host(.in-item:not(.legacy-checkbox)) .label-text-wrapper{margin-top:10px;margin-bottom:10px}:host(.in-item.checkbox-label-placement-stacked) .label-text-wrapper{margin-top:10px;margin-bottom:16px}:host(.in-item.checkbox-label-placement-stacked) .native-wrapper{margin-bottom:10px}.label-text-wrapper-hidden{display:none}input{position:absolute;top:0;left:0;right:0;bottom:0;width:100%;height:100%;margin:0;padding:0;border:0;outline:0;clip:rect(0 0 0 0);opacity:0;overflow:hidden;-webkit-appearance:none;-moz-appearance:none}.native-wrapper{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center}.checkbox-icon{border-radius:var(--border-radius);position:relative;-webkit-transition:var(--transition);transition:var(--transition);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--checkbox-background);-webkit-box-sizing:border-box;box-sizing:border-box}:host(.legacy-checkbox) .checkbox-icon{display:block;width:100%;height:100%}:host(:not(.legacy-checkbox)) .checkbox-icon{width:var(--size);height:var(--size)}.checkbox-icon path{fill:none;stroke:var(--checkmark-color);stroke-width:var(--checkmark-width);opacity:0}:host(.checkbox-justify-space-between) .checkbox-wrapper{-ms-flex-pack:justify;justify-content:space-between}:host(.checkbox-justify-start) .checkbox-wrapper{-ms-flex-pack:start;justify-content:start}:host(.checkbox-justify-end) .checkbox-wrapper{-ms-flex-pack:end;justify-content:end}:host(.checkbox-alignment-start) .checkbox-wrapper{-ms-flex-align:start;align-items:start}:host(.checkbox-alignment-center) .checkbox-wrapper{-ms-flex-align:center;align-items:center}:host(.checkbox-label-placement-start) .checkbox-wrapper{-ms-flex-direction:row;flex-direction:row}:host(.checkbox-label-placement-start) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-end) .checkbox-wrapper{-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.checkbox-label-placement-end) .label-text-wrapper{-webkit-margin-start:16px;margin-inline-start:16px;-webkit-margin-end:0;margin-inline-end:0}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-webkit-margin-start:0;margin-inline-start:0;-webkit-margin-end:16px;margin-inline-end:16px}:host(.checkbox-label-placement-fixed) .label-text-wrapper{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}:host(.checkbox-label-placement-stacked) .checkbox-wrapper{-ms-flex-direction:column;flex-direction:column}:host(.checkbox-label-placement-stacked) .label-text-wrapper{-webkit-transform:scale(0.75);transform:scale(0.75);margin-left:0;margin-right:0;margin-bottom:16px;max-width:calc(100% / 0.75)}:host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper{-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-start) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-start .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-start:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:right top;transform-origin:right top}}:host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper{-webkit-transform-origin:center top;transform-origin:center top}:host-context([dir=rtl]):host(.checkbox-label-placement-stacked.checkbox-alignment-center) .label-text-wrapper,:host-context([dir=rtl]).checkbox-label-placement-stacked.checkbox-alignment-center .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}@supports selector(:dir(rtl)){:host(.checkbox-label-placement-stacked.checkbox-alignment-center:dir(rtl)) .label-text-wrapper{-webkit-transform-origin:calc(100% - center) top;transform-origin:calc(100% - center) top}}:host(.checkbox-checked) .checkbox-icon,:host(.checkbox-indeterminate) .checkbox-icon{border-color:var(--border-color-checked);background:var(--checkbox-background-checked)}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{opacity:1}:host(.checkbox-disabled){pointer-events:none}:host{--border-radius:calc(var(--size) * .125);--border-width:2px;--border-style:solid;--border-color:rgb(var(--ion-text-color-rgb, 0, 0, 0), 0.6);--checkmark-width:3;--checkbox-background:var(--ion-item-background, var(--ion-background-color, #fff));--transition:background 180ms cubic-bezier(0.4, 0, 0.2, 1);--size:18px}.checkbox-icon path{stroke-dasharray:30;stroke-dashoffset:30}:host(.checkbox-checked) .checkbox-icon path,:host(.checkbox-indeterminate) .checkbox-icon path{stroke-dashoffset:0;-webkit-transition:stroke-dashoffset 90ms linear 90ms;transition:stroke-dashoffset 90ms linear 90ms}:host(.legacy-checkbox.checkbox-disabled),:host(.checkbox-disabled) .label-text-wrapper{opacity:0.38}:host(.checkbox-disabled) .native-wrapper{opacity:0.63}:host(.in-item.legacy-checkbox){margin-left:0;margin-right:0;margin-top:18px;margin-bottom:18px;display:block;position:static}:host(.in-item.legacy-checkbox[slot=start]){-webkit-margin-start:4px;margin-inline-start:4px;-webkit-margin-end:36px;margin-inline-end:36px;margin-top:18px;margin-bottom:18px}\";\nconst IonCheckboxMdStyle0 = checkboxMdCss;\n\nconst Checkbox = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionChange = createEvent(this, \"ionChange\", 7);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.ionStyle = createEvent(this, \"ionStyle\", 7);\n        this.inputId = `ion-cb-${checkboxIds++}`;\n        this.inheritedAttributes = {};\n        // TODO(FW-3100): remove this\n        // This flag ensures we log the deprecation warning at most once.\n        this.hasLoggedDeprecationWarning = false;\n        /**\n         * Sets the checked property and emits\n         * the ionChange event. Use this to update the\n         * checked state in response to user-generated\n         * actions such as a click.\n         */\n        this.setChecked = (state) => {\n            const isChecked = (this.checked = state);\n            this.ionChange.emit({\n                checked: isChecked,\n                value: this.value,\n            });\n        };\n        this.toggleChecked = (ev) => {\n            ev.preventDefault();\n            this.setFocus();\n            this.setChecked(!this.checked);\n            this.indeterminate = false;\n        };\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.onClick = (ev) => {\n            if (this.disabled) {\n                return;\n            }\n            this.toggleChecked(ev);\n        };\n        this.color = undefined;\n        this.name = this.inputId;\n        this.checked = false;\n        this.indeterminate = false;\n        this.disabled = false;\n        this.value = 'on';\n        this.labelPlacement = 'start';\n        this.justify = 'space-between';\n        this.alignment = 'center';\n        this.legacy = undefined;\n    }\n    connectedCallback() {\n        this.legacyFormController = createLegacyFormController(this.el); // TODO(FW-3100): remove this\n    }\n    componentWillLoad() {\n        this.emitStyle();\n        // TODO(FW-3100): remove check\n        if (!this.legacyFormController.hasLegacyControl()) {\n            this.inheritedAttributes = Object.assign({}, inheritAriaAttributes(this.el));\n        }\n    }\n    styleChanged() {\n        this.emitStyle();\n    }\n    emitStyle() {\n        const style = {\n            'interactive-disabled': this.disabled,\n            // TODO(FW-3100): remove this\n            legacy: !!this.legacy,\n        };\n        // TODO(FW-3100): remove this\n        if (this.legacyFormController.hasLegacyControl()) {\n            style['checkbox-checked'] = this.checked;\n        }\n        this.ionStyle.emit(style);\n    }\n    setFocus() {\n        if (this.focusEl) {\n            this.focusEl.focus();\n        }\n    }\n    // TODO(FW-3100): run contents of renderCheckbox directly instead\n    render() {\n        const { legacyFormController } = this;\n        return legacyFormController.hasLegacyControl() ? this.renderLegacyCheckbox() : this.renderCheckbox();\n    }\n    renderCheckbox() {\n        const { color, checked, disabled, el, getSVGPath, indeterminate, inheritedAttributes, inputId, justify, labelPlacement, name, value, alignment, } = this;\n        const mode = getIonMode(this);\n        const path = getSVGPath(mode, indeterminate);\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { \"aria-checked\": indeterminate ? 'mixed' : `${checked}`, class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'checkbox-checked': checked,\n                'checkbox-disabled': disabled,\n                'checkbox-indeterminate': indeterminate,\n                interactive: true,\n                [`checkbox-justify-${justify}`]: true,\n                [`checkbox-alignment-${alignment}`]: true,\n                [`checkbox-label-placement-${labelPlacement}`]: true,\n            }), onClick: this.onClick }, h(\"label\", { class: \"checkbox-wrapper\" }, h(\"input\", Object.assign({ type: \"checkbox\", checked: checked ? true : undefined, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) }, inheritedAttributes)), h(\"div\", { class: {\n                'label-text-wrapper': true,\n                'label-text-wrapper-hidden': el.textContent === '',\n            }, part: \"label\" }, h(\"slot\", null)), h(\"div\", { class: \"native-wrapper\" }, h(\"svg\", { class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path)))));\n    }\n    // TODO(FW-3100): remove this\n    renderLegacyCheckbox() {\n        if (!this.hasLoggedDeprecationWarning) {\n            printIonWarning(`ion-checkbox now requires providing a label with either the default slot or the \"aria-label\" attribute. To migrate, remove any usage of \"ion-label\" and pass the label text to either the component or the \"aria-label\" attribute.\n\nExample: <ion-checkbox>Label</ion-checkbox>\nExample with aria-label: <ion-checkbox aria-label=\"Label\"></ion-checkbox>\n\nDevelopers can use the \"legacy\" property to continue using the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.`, this.el);\n            if (this.legacy) {\n                printIonWarning(`ion-checkbox is being used with the \"legacy\" property enabled which will forcibly enable the legacy form markup. This property will be removed in an upcoming major release of Ionic where this form control will use the modern form markup.\nDevelopers can dismiss this warning by removing their usage of the \"legacy\" property and using the new checkbox syntax.`, this.el);\n            }\n            this.hasLoggedDeprecationWarning = true;\n        }\n        const { color, checked, disabled, el, getSVGPath, indeterminate, inputId, name, value } = this;\n        const mode = getIonMode(this);\n        const { label, labelId, labelText } = getAriaLabel(el, inputId);\n        const path = getSVGPath(mode, indeterminate);\n        renderHiddenInput(true, el, name, checked ? value : '', disabled);\n        return (h(Host, { \"aria-labelledby\": label ? labelId : null, \"aria-checked\": `${checked}`, \"aria-hidden\": disabled ? 'true' : null, role: \"checkbox\", class: createColorClasses(color, {\n                [mode]: true,\n                'in-item': hostContext('ion-item', el),\n                'checkbox-checked': checked,\n                'checkbox-disabled': disabled,\n                'checkbox-indeterminate': indeterminate,\n                'legacy-checkbox': true,\n                interactive: true,\n            }), onClick: this.onClick }, h(\"svg\", { class: \"checkbox-icon\", viewBox: \"0 0 24 24\", part: \"container\" }, path), h(\"label\", { htmlFor: inputId }, labelText), h(\"input\", { type: \"checkbox\", \"aria-checked\": `${checked}`, disabled: disabled, id: inputId, onChange: this.toggleChecked, onFocus: () => this.onFocus(), onBlur: () => this.onBlur(), ref: (focusEl) => (this.focusEl = focusEl) })));\n    }\n    getSVGPath(mode, indeterminate) {\n        let path = indeterminate ? (h(\"path\", { d: \"M6 12L18 12\", part: \"mark\" })) : (h(\"path\", { d: \"M5.9,12.5l3.8,3.8l8.8-8.8\", part: \"mark\" }));\n        if (mode === 'md') {\n            path = indeterminate ? (h(\"path\", { d: \"M2 12H22\", part: \"mark\" })) : (h(\"path\", { d: \"M1.73,12.91 8.1,19.28 22.79,4.59\", part: \"mark\" }));\n        }\n        return path;\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"checked\": [\"styleChanged\"],\n        \"disabled\": [\"styleChanged\"]\n    }; }\n};\nlet checkboxIds = 0;\nCheckbox.style = {\n    ios: IonCheckboxIosStyle0,\n    md: IonCheckboxMdStyle0\n};\n\nexport { Checkbox as ion_checkbox };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASC,CAAC,IAAIC,0BAA0B,QAAQ,+BAA+B;AAC/E,SAASC,CAAC,IAAIC,qBAAqB,EAAEV,CAAC,IAAIW,iBAAiB,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAC7G,SAASC,CAAC,IAAIC,eAAe,QAAQ,qBAAqB;AAC1D,SAASR,CAAC,IAAIS,kBAAkB,EAAEd,CAAC,IAAIe,WAAW,QAAQ,qBAAqB;AAC/E,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,MAAMC,cAAc,GAAG,knNAAknN;AACzoN,MAAMC,oBAAoB,GAAGD,cAAc;AAE3C,MAAME,aAAa,GAAG,4gOAA4gO;AACliO,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,QAAQ,GAAG,MAAM;EACnBC,WAAWA,CAACC,OAAO,EAAE;IACjB3B,gBAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACC,SAAS,GAAG1B,WAAW,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;IAClD,IAAI,CAAC2B,QAAQ,GAAG3B,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC4B,OAAO,GAAG5B,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC6B,QAAQ,GAAG7B,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC8B,OAAO,GAAG,UAAUC,WAAW,EAAE,EAAE;IACxC,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,2BAA2B,GAAG,KAAK;IACxC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAIC,KAAK,IAAK;MACzB,MAAMC,SAAS,GAAI,IAAI,CAACC,OAAO,GAAGF,KAAM;MACxC,IAAI,CAACT,SAAS,CAACY,IAAI,CAAC;QAChBD,OAAO,EAAED,SAAS;QAClBG,KAAK,EAAE,IAAI,CAACA;MAChB,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACC,aAAa,GAAIC,EAAE,IAAK;MACzBA,EAAE,CAACC,cAAc,CAAC,CAAC;MACnB,IAAI,CAACC,QAAQ,CAAC,CAAC;MACf,IAAI,CAACT,UAAU,CAAC,CAAC,IAAI,CAACG,OAAO,CAAC;MAC9B,IAAI,CAACO,aAAa,GAAG,KAAK;IAC9B,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAAClB,QAAQ,CAACW,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACQ,MAAM,GAAG,MAAM;MAChB,IAAI,CAAClB,OAAO,CAACU,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACS,OAAO,GAAIN,EAAE,IAAK;MACnB,IAAI,IAAI,CAACO,QAAQ,EAAE;QACf;MACJ;MACA,IAAI,CAACR,aAAa,CAACC,EAAE,CAAC;IAC1B,CAAC;IACD,IAAI,CAACQ,KAAK,GAAGC,SAAS;IACtB,IAAI,CAACC,IAAI,GAAG,IAAI,CAACrB,OAAO;IACxB,IAAI,CAACO,OAAO,GAAG,KAAK;IACpB,IAAI,CAACO,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACT,KAAK,GAAG,IAAI;IACjB,IAAI,CAACa,cAAc,GAAG,OAAO;IAC7B,IAAI,CAACC,OAAO,GAAG,eAAe;IAC9B,IAAI,CAACC,SAAS,GAAG,QAAQ;IACzB,IAAI,CAACC,MAAM,GAAGL,SAAS;EAC3B;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,oBAAoB,GAAGlD,0BAA0B,CAAC,IAAI,CAACmD,EAAE,CAAC,CAAC,CAAC;EACrE;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB;IACA,IAAI,CAAC,IAAI,CAACH,oBAAoB,CAACI,gBAAgB,CAAC,CAAC,EAAE;MAC/C,IAAI,CAAC7B,mBAAmB,GAAG8B,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtD,qBAAqB,CAAC,IAAI,CAACiD,EAAE,CAAC,CAAC;IAChF;EACJ;EACAM,YAAYA,CAAA,EAAG;IACX,IAAI,CAACJ,SAAS,CAAC,CAAC;EACpB;EACAA,SAASA,CAAA,EAAG;IACR,MAAMK,KAAK,GAAG;MACV,sBAAsB,EAAE,IAAI,CAACjB,QAAQ;MACrC;MACAO,MAAM,EAAE,CAAC,CAAC,IAAI,CAACA;IACnB,CAAC;IACD;IACA,IAAI,IAAI,CAACE,oBAAoB,CAACI,gBAAgB,CAAC,CAAC,EAAE;MAC9CI,KAAK,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC5B,OAAO;IAC5C;IACA,IAAI,CAACR,QAAQ,CAACS,IAAI,CAAC2B,KAAK,CAAC;EAC7B;EACAtB,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAACuB,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACC,KAAK,CAAC,CAAC;IACxB;EACJ;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEX;IAAqB,CAAC,GAAG,IAAI;IACrC,OAAOA,oBAAoB,CAACI,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACQ,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EACxG;EACAA,cAAcA,CAAA,EAAG;IACb,MAAM;MAAErB,KAAK;MAAEZ,OAAO;MAAEW,QAAQ;MAAEU,EAAE;MAAEa,UAAU;MAAE3B,aAAa;MAAEZ,mBAAmB;MAAEF,OAAO;MAAEuB,OAAO;MAAED,cAAc;MAAED,IAAI;MAAEZ,KAAK;MAAEe;IAAW,CAAC,GAAG,IAAI;IACxJ,MAAMkB,IAAI,GAAGtD,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuD,IAAI,GAAGF,UAAU,CAACC,IAAI,EAAE5B,aAAa,CAAC;IAC5ClC,iBAAiB,CAAC,IAAI,EAAEgD,EAAE,EAAEP,IAAI,EAAEd,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAES,QAAQ,CAAC;IACjE,OAAQ/C,CAAC,CAACE,IAAI,EAAE;MAAE,cAAc,EAAEyC,aAAa,GAAG,OAAO,GAAG,GAAGP,OAAO,EAAE;MAAEqC,KAAK,EAAE3D,kBAAkB,CAACkC,KAAK,EAAE;QACnG,CAACuB,IAAI,GAAG,IAAI;QACZ,SAAS,EAAExD,WAAW,CAAC,UAAU,EAAE0C,EAAE,CAAC;QACtC,kBAAkB,EAAErB,OAAO;QAC3B,mBAAmB,EAAEW,QAAQ;QAC7B,wBAAwB,EAAEJ,aAAa;QACvC+B,WAAW,EAAE,IAAI;QACjB,CAAC,oBAAoBtB,OAAO,EAAE,GAAG,IAAI;QACrC,CAAC,sBAAsBC,SAAS,EAAE,GAAG,IAAI;QACzC,CAAC,4BAA4BF,cAAc,EAAE,GAAG;MACpD,CAAC,CAAC;MAAEL,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAE9C,CAAC,CAAC,OAAO,EAAE;MAAEyE,KAAK,EAAE;IAAmB,CAAC,EAAEzE,CAAC,CAAC,OAAO,EAAE6D,MAAM,CAACC,MAAM,CAAC;MAAEa,IAAI,EAAE,UAAU;MAAEvC,OAAO,EAAEA,OAAO,GAAG,IAAI,GAAGa,SAAS;MAAEF,QAAQ,EAAEA,QAAQ;MAAE6B,EAAE,EAAE/C,OAAO;MAAEgD,QAAQ,EAAE,IAAI,CAACtC,aAAa;MAAEK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAEiC,GAAG,EAAGb,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,EAAElC,mBAAmB,CAAC,CAAC,EAAE/B,CAAC,CAAC,KAAK,EAAE;MAAEyE,KAAK,EAAE;QACvW,oBAAoB,EAAE,IAAI;QAC1B,2BAA2B,EAAEhB,EAAE,CAACsB,WAAW,KAAK;MACpD,CAAC;MAAEC,IAAI,EAAE;IAAQ,CAAC,EAAEhF,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC,EAAEA,CAAC,CAAC,KAAK,EAAE;MAAEyE,KAAK,EAAE;IAAiB,CAAC,EAAEzE,CAAC,CAAC,KAAK,EAAE;MAAEyE,KAAK,EAAE,eAAe;MAAEQ,OAAO,EAAE,WAAW;MAAED,IAAI,EAAE;IAAY,CAAC,EAAER,IAAI,CAAC,CAAC,CAAC,CAAC;EAC1K;EACA;EACAJ,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAACpC,2BAA2B,EAAE;MACnCnB,eAAe,CAAC;AAC5B;AACA;AACA;AACA;AACA,gNAAgN,EAAE,IAAI,CAAC4C,EAAE,CAAC;MAC9M,IAAI,IAAI,CAACH,MAAM,EAAE;QACbzC,eAAe,CAAC;AAChC,wHAAwH,EAAE,IAAI,CAAC4C,EAAE,CAAC;MACtH;MACA,IAAI,CAACzB,2BAA2B,GAAG,IAAI;IAC3C;IACA,MAAM;MAAEgB,KAAK;MAAEZ,OAAO;MAAEW,QAAQ;MAAEU,EAAE;MAAEa,UAAU;MAAE3B,aAAa;MAAEd,OAAO;MAAEqB,IAAI;MAAEZ;IAAM,CAAC,GAAG,IAAI;IAC9F,MAAMiC,IAAI,GAAGtD,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAM;MAAEiE,KAAK;MAAEC,OAAO;MAAEC;IAAU,CAAC,GAAGzE,YAAY,CAAC8C,EAAE,EAAE5B,OAAO,CAAC;IAC/D,MAAM2C,IAAI,GAAGF,UAAU,CAACC,IAAI,EAAE5B,aAAa,CAAC;IAC5ClC,iBAAiB,CAAC,IAAI,EAAEgD,EAAE,EAAEP,IAAI,EAAEd,OAAO,GAAGE,KAAK,GAAG,EAAE,EAAES,QAAQ,CAAC;IACjE,OAAQ/C,CAAC,CAACE,IAAI,EAAE;MAAE,iBAAiB,EAAEgF,KAAK,GAAGC,OAAO,GAAG,IAAI;MAAE,cAAc,EAAE,GAAG/C,OAAO,EAAE;MAAE,aAAa,EAAEW,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEsC,IAAI,EAAE,UAAU;MAAEZ,KAAK,EAAE3D,kBAAkB,CAACkC,KAAK,EAAE;QAC/K,CAACuB,IAAI,GAAG,IAAI;QACZ,SAAS,EAAExD,WAAW,CAAC,UAAU,EAAE0C,EAAE,CAAC;QACtC,kBAAkB,EAAErB,OAAO;QAC3B,mBAAmB,EAAEW,QAAQ;QAC7B,wBAAwB,EAAEJ,aAAa;QACvC,iBAAiB,EAAE,IAAI;QACvB+B,WAAW,EAAE;MACjB,CAAC,CAAC;MAAE5B,OAAO,EAAE,IAAI,CAACA;IAAQ,CAAC,EAAE9C,CAAC,CAAC,KAAK,EAAE;MAAEyE,KAAK,EAAE,eAAe;MAAEQ,OAAO,EAAE,WAAW;MAAED,IAAI,EAAE;IAAY,CAAC,EAAER,IAAI,CAAC,EAAExE,CAAC,CAAC,OAAO,EAAE;MAAEsF,OAAO,EAAEzD;IAAQ,CAAC,EAAEuD,SAAS,CAAC,EAAEpF,CAAC,CAAC,OAAO,EAAE;MAAE2E,IAAI,EAAE,UAAU;MAAE,cAAc,EAAE,GAAGvC,OAAO,EAAE;MAAEW,QAAQ,EAAEA,QAAQ;MAAE6B,EAAE,EAAE/C,OAAO;MAAEgD,QAAQ,EAAE,IAAI,CAACtC,aAAa;MAAEK,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACA,OAAO,CAAC,CAAC;MAAEC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACA,MAAM,CAAC,CAAC;MAAEiC,GAAG,EAAGb,OAAO,IAAM,IAAI,CAACA,OAAO,GAAGA;IAAS,CAAC,CAAC,CAAC;EAC7Y;EACAK,UAAUA,CAACC,IAAI,EAAE5B,aAAa,EAAE;IAC5B,IAAI6B,IAAI,GAAG7B,aAAa,GAAI3C,CAAC,CAAC,MAAM,EAAE;MAAEF,CAAC,EAAE,aAAa;MAAEkF,IAAI,EAAE;IAAO,CAAC,CAAC,GAAKhF,CAAC,CAAC,MAAM,EAAE;MAAEF,CAAC,EAAE,2BAA2B;MAAEkF,IAAI,EAAE;IAAO,CAAC,CAAE;IAC1I,IAAIT,IAAI,KAAK,IAAI,EAAE;MACfC,IAAI,GAAG7B,aAAa,GAAI3C,CAAC,CAAC,MAAM,EAAE;QAAEF,CAAC,EAAE,UAAU;QAAEkF,IAAI,EAAE;MAAO,CAAC,CAAC,GAAKhF,CAAC,CAAC,MAAM,EAAE;QAAEF,CAAC,EAAE,kCAAkC;QAAEkF,IAAI,EAAE;MAAO,CAAC,CAAE;IAC9I;IACA,OAAOR,IAAI;EACf;EACA,IAAIf,EAAEA,CAAA,EAAG;IAAE,OAAOrD,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWmF,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,SAAS,EAAE,CAAC,cAAc,CAAC;MAC3B,UAAU,EAAE,CAAC,cAAc;IAC/B,CAAC;EAAE;AACP,CAAC;AACD,IAAIzD,WAAW,GAAG,CAAC;AACnBR,QAAQ,CAAC0C,KAAK,GAAG;EACbwB,GAAG,EAAErE,oBAAoB;EACzBsE,EAAE,EAAEpE;AACR,CAAC;AAED,SAASC,QAAQ,IAAIoE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}