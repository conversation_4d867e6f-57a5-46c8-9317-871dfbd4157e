{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/toolbar\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/form-field\";\nimport * as i11 from \"@angular/material/input\";\nimport * as i12 from \"@angular/material/select\";\nimport * as i13 from \"@angular/material/core\";\nimport * as i14 from \"@angular/material/checkbox\";\nimport * as i15 from \"@angular/material/divider\";\nfunction FeedbackPage_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"name\"), \" \");\n  }\n}\nfunction FeedbackPage_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"email\"), \" \");\n  }\n}\nfunction FeedbackPage_mat_error_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"phone\"), \" \");\n  }\n}\nfunction FeedbackPage_mat_option_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 48)(1, \"div\", 49)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const category_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", category_r2.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(category_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r2.label);\n  }\n}\nfunction FeedbackPage_mat_error_68_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"category\"), \" \");\n  }\n}\nfunction FeedbackPage_button_79_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 50);\n    i0.ɵɵlistener(\"click\", function FeedbackPage_button_79_Template_button_click_0_listener() {\n      const rating_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onRatingChange(rating_r4.value));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"star\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const rating_r4 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"color\", ctx_r0.selectedRating >= rating_r4.value ? rating_r4.color : \"#ddd\");\n    i0.ɵɵclassProp(\"active\", ctx_r0.selectedRating >= rating_r4.value);\n  }\n}\nfunction FeedbackPage_mat_error_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"subject\"), \" \");\n  }\n}\nfunction FeedbackPage_mat_error_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(\"message\"), \" \");\n  }\n}\nfunction FeedbackPage_mat_icon_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 51);\n    i0.ɵɵtext(1, \"refresh\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeedbackPage_mat_icon_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeedbackPage_span_129_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Submit Feedback\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FeedbackPage_span_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Submitting...\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class FeedbackPage {\n  constructor(formBuilder, router, loadingController, toastController, snackBar) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.selectedRating = 5;\n    this.feedbackCategories = [{\n      value: 'service',\n      label: 'Customer Service',\n      icon: 'people'\n    }, {\n      value: 'product',\n      label: 'Product Quality',\n      icon: 'star'\n    }, {\n      value: 'delivery',\n      label: 'Delivery Experience',\n      icon: 'car'\n    }, {\n      value: 'website',\n      label: 'Website/App',\n      icon: 'phone-portrait'\n    }, {\n      value: 'support',\n      label: 'Technical Support',\n      icon: 'help-circle'\n    }, {\n      value: 'other',\n      label: 'Other',\n      icon: 'ellipsis-horizontal'\n    }];\n    this.ratingLabels = [{\n      value: 1,\n      label: 'Very Poor',\n      color: '#f44336',\n      icon: 'sad'\n    }, {\n      value: 2,\n      label: 'Poor',\n      color: '#ff9800',\n      icon: 'sad'\n    }, {\n      value: 3,\n      label: 'Average',\n      color: '#ffc107',\n      icon: 'remove-circle'\n    }, {\n      value: 4,\n      label: 'Good',\n      color: '#4caf50',\n      icon: 'happy'\n    }, {\n      value: 5,\n      label: 'Excellent',\n      color: '#2196f3',\n      icon: 'happy'\n    }];\n    this.createForm();\n  }\n  ngOnInit() {}\n  createForm() {\n    this.feedbackForm = this.formBuilder.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^\\d{10}$/)]],\n      category: ['', Validators.required],\n      rating: [5, [Validators.required, Validators.min(1), Validators.max(5)]],\n      subject: ['', [Validators.required, Validators.minLength(5)]],\n      message: ['', [Validators.required, Validators.minLength(10)]],\n      wouldRecommend: [true],\n      allowContact: [false]\n    });\n  }\n  onRatingChange(rating) {\n    this.selectedRating = rating;\n    this.feedbackForm.patchValue({\n      rating\n    });\n  }\n  getRatingInfo() {\n    return this.ratingLabels.find(r => r.value === this.selectedRating) || this.ratingLabels[4];\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.feedbackForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Submitting feedback...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate submission process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          // Show success message\n          _this.snackBar.open('Thank you for your feedback! We appreciate your input.', 'Close', {\n            duration: 5000,\n            panelClass: ['success-snackbar'],\n            horizontalPosition: 'center',\n            verticalPosition: 'top'\n          });\n          // Reset form\n          _this.feedbackForm.reset();\n          _this.selectedRating = 5;\n          _this.feedbackForm.patchValue({\n            rating: 5,\n            wouldRecommend: true,\n            allowContact: false\n          });\n          // Navigate back to home\n          setTimeout(() => {\n            _this.router.navigate(['/home']);\n          }, 2000);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please fill in all required fields correctly.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  getErrorMessage(field) {\n    const control = this.feedbackForm.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      name: 'Name',\n      email: 'Email',\n      phone: 'Phone',\n      category: 'Category',\n      subject: 'Subject',\n      message: 'Message'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function FeedbackPage_Factory(t) {\n      return new (t || FeedbackPage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.LoadingController), i0.ɵɵdirectiveInject(i3.ToastController), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FeedbackPage,\n      selectors: [[\"app-feedback\"]],\n      decls: 140,\n      vars: 20,\n      consts: [[1, \"material-header\", 3, \"translucent\"], [1, \"material-toolbar\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Back\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [1, \"material-content\", 3, \"fullscreen\"], [1, \"feedback-container\"], [1, \"header-section\"], [1, \"header-content\"], [1, \"header-icon\"], [1, \"header-title\"], [1, \"header-subtitle\"], [1, \"feedback-card\"], [1, \"form-title\"], [1, \"title-icon\"], [1, \"feedback-form\", 3, \"ngSubmit\", \"formGroup\"], [1, \"form-section\"], [1, \"section-title\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"name\", \"placeholder\", \"Enter your full name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"email\", \"placeholder\", \"Enter your email\", \"type\", \"email\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"phone\", \"placeholder\", \"Enter 10-digit phone number\", \"type\", \"tel\"], [1, \"section-divider\"], [\"formControlName\", \"category\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-section\"], [1, \"rating-label\"], [1, \"rating-stars\"], [\"type\", \"button\", \"mat-icon-button\", \"\", \"class\", \"rating-star\", 3, \"active\", \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"rating-info\"], [\"matInput\", \"\", \"formControlName\", \"subject\", \"placeholder\", \"Brief summary of your feedback\"], [\"matInput\", \"\", \"formControlName\", \"message\", \"placeholder\", \"Please provide detailed feedback about your experience\", \"rows\", \"5\"], [1, \"checkbox-group\"], [\"formControlName\", \"wouldRecommend\", \"color\", \"primary\", 1, \"checkbox-item\"], [1, \"checkbox-content\"], [1, \"checkbox-label\"], [1, \"checkbox-description\"], [\"formControlName\", \"allowContact\", \"color\", \"primary\", 1, \"checkbox-item\"], [1, \"submit-section\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"submit-button\", 3, \"disabled\"], [\"class\", \"spinning\", 4, \"ngIf\"], [1, \"thank-you-card\"], [1, \"thank-you-content\"], [1, \"thank-you-icon\"], [3, \"value\"], [1, \"category-option\"], [\"type\", \"button\", \"mat-icon-button\", \"\", 1, \"rating-star\", 3, \"click\"], [1, \"spinning\"]],\n      template: function FeedbackPage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"mat-toolbar\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function FeedbackPage_Template_button_click_2_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"span\", 3);\n          i0.ɵɵtext(6, \"Feedback\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"ion-content\", 5)(9, \"div\", 6)(10, \"div\", 7)(11, \"div\", 8)(12, \"mat-icon\", 9);\n          i0.ɵɵtext(13, \"feedback\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"h1\", 10);\n          i0.ɵɵtext(15, \"We Value Your Feedback\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 11);\n          i0.ɵɵtext(17, \"Help us improve our services by sharing your experience\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"mat-card\", 12)(19, \"mat-card-header\")(20, \"mat-card-title\", 13)(21, \"mat-icon\", 14);\n          i0.ɵɵtext(22, \"rate_review\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(23, \" Share Your Experience \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"mat-card-subtitle\");\n          i0.ɵɵtext(25, \"All fields marked with * are required\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"mat-card-content\")(27, \"form\", 15);\n          i0.ɵɵlistener(\"ngSubmit\", function FeedbackPage_Template_form_ngSubmit_27_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(28, \"div\", 16)(29, \"h3\", 17)(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(32, \" Personal Information \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"div\", 18)(34, \"mat-form-field\", 19)(35, \"mat-label\");\n          i0.ɵɵtext(36, \"Full Name *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(37, \"input\", 20);\n          i0.ɵɵelementStart(38, \"mat-icon\", 21);\n          i0.ɵɵtext(39, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, FeedbackPage_mat_error_40_Template, 2, 1, \"mat-error\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"mat-form-field\", 19)(42, \"mat-label\");\n          i0.ɵɵtext(43, \"Email Address *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(44, \"input\", 23);\n          i0.ɵɵelementStart(45, \"mat-icon\", 21);\n          i0.ɵɵtext(46, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(47, FeedbackPage_mat_error_47_Template, 2, 1, \"mat-error\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"mat-form-field\", 24)(49, \"mat-label\");\n          i0.ɵɵtext(50, \"Phone Number (Optional)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(51, \"input\", 25);\n          i0.ɵɵelementStart(52, \"mat-icon\", 21);\n          i0.ɵɵtext(53, \"phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(54, FeedbackPage_mat_error_54_Template, 2, 1, \"mat-error\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(55, \"mat-divider\", 26);\n          i0.ɵɵelementStart(56, \"div\", 16)(57, \"h3\", 17)(58, \"mat-icon\");\n          i0.ɵɵtext(59, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(60, \" Feedback Category \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"mat-form-field\", 24)(62, \"mat-label\");\n          i0.ɵɵtext(63, \"What is your feedback about? *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"mat-select\", 27);\n          i0.ɵɵtemplate(65, FeedbackPage_mat_option_65_Template, 6, 3, \"mat-option\", 28);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"mat-icon\", 21);\n          i0.ɵɵtext(67, \"category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(68, FeedbackPage_mat_error_68_Template, 2, 1, \"mat-error\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(69, \"mat-divider\", 26);\n          i0.ɵɵelementStart(70, \"div\", 16)(71, \"h3\", 17)(72, \"mat-icon\");\n          i0.ɵɵtext(73, \"star_rate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(74, \" Overall Rating \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"div\", 29)(76, \"p\", 30);\n          i0.ɵɵtext(77, \"How would you rate your overall experience? *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"div\", 31);\n          i0.ɵɵtemplate(79, FeedbackPage_button_79_Template, 3, 4, \"button\", 32);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"div\", 33)(81, \"mat-icon\");\n          i0.ɵɵtext(82);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"span\");\n          i0.ɵɵtext(84);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(85, \"mat-divider\", 26);\n          i0.ɵɵelementStart(86, \"div\", 16)(87, \"h3\", 17)(88, \"mat-icon\");\n          i0.ɵɵtext(89, \"message\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(90, \" Feedback Details \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"mat-form-field\", 24)(92, \"mat-label\");\n          i0.ɵɵtext(93, \"Subject *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(94, \"input\", 34);\n          i0.ɵɵelementStart(95, \"mat-icon\", 21);\n          i0.ɵɵtext(96, \"subject\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(97, FeedbackPage_mat_error_97_Template, 2, 1, \"mat-error\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"mat-form-field\", 24)(99, \"mat-label\");\n          i0.ɵɵtext(100, \"Detailed Message *\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"textarea\", 35);\n          i0.ɵɵtext(102, \"              \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(103, \"mat-icon\", 21);\n          i0.ɵɵtext(104, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(105, FeedbackPage_mat_error_105_Template, 2, 1, \"mat-error\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(106, \"mat-divider\", 26);\n          i0.ɵɵelementStart(107, \"div\", 16)(108, \"h3\", 17)(109, \"mat-icon\");\n          i0.ɵɵtext(110, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(111, \" Additional Options \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(112, \"div\", 36)(113, \"mat-checkbox\", 37)(114, \"div\", 38)(115, \"span\", 39);\n          i0.ɵɵtext(116, \"Would you recommend AIS Smart Care to others?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"span\", 40);\n          i0.ɵɵtext(118, \"Help us understand if you'd refer our services\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(119, \"mat-checkbox\", 41)(120, \"div\", 38)(121, \"span\", 39);\n          i0.ɵɵtext(122, \"Allow us to contact you for follow-up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(123, \"span\", 40);\n          i0.ɵɵtext(124, \"We may reach out to discuss your feedback further\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(125, \"div\", 42)(126, \"button\", 43);\n          i0.ɵɵtemplate(127, FeedbackPage_mat_icon_127_Template, 2, 0, \"mat-icon\", 44)(128, FeedbackPage_mat_icon_128_Template, 2, 0, \"mat-icon\", 22)(129, FeedbackPage_span_129_Template, 2, 0, \"span\", 22)(130, FeedbackPage_span_130_Template, 2, 0, \"span\", 22);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(131, \"mat-card\", 45)(132, \"mat-card-content\")(133, \"div\", 46)(134, \"mat-icon\", 47);\n          i0.ɵɵtext(135, \"favorite\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(136, \"h3\");\n          i0.ɵɵtext(137, \"Thank You!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(138, \"p\");\n          i0.ɵɵtext(139, \"Your feedback helps us improve our services and provide better experiences for all our customers.\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          let tmp_3_0;\n          let tmp_4_0;\n          let tmp_5_0;\n          let tmp_7_0;\n          let tmp_12_0;\n          let tmp_13_0;\n          i0.ɵɵproperty(\"translucent\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"fullscreen\", true);\n          i0.ɵɵadvance(19);\n          i0.ɵɵproperty(\"formGroup\", ctx.feedbackForm);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx.feedbackForm.get(\"name\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.feedbackForm.get(\"name\")) == null ? null : tmp_3_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx.feedbackForm.get(\"email\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx.feedbackForm.get(\"email\")) == null ? null : tmp_4_0.touched));\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx.feedbackForm.get(\"phone\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx.feedbackForm.get(\"phone\")) == null ? null : tmp_5_0.touched));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.feedbackCategories);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx.feedbackForm.get(\"category\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx.feedbackForm.get(\"category\")) == null ? null : tmp_7_0.touched));\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"ngForOf\", ctx.ratingLabels);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"color\", ctx.getRatingInfo().color);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getRatingInfo().icon);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getRatingInfo().label);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = ctx.feedbackForm.get(\"subject\")) == null ? null : tmp_12_0.invalid) && ((tmp_12_0 = ctx.feedbackForm.get(\"subject\")) == null ? null : tmp_12_0.touched));\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", ((tmp_13_0 = ctx.feedbackForm.get(\"message\")) == null ? null : tmp_13_0.invalid) && ((tmp_13_0 = ctx.feedbackForm.get(\"message\")) == null ? null : tmp_13_0.touched));\n          i0.ɵɵadvance(21);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.feedbackForm.invalid);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.IonContent, i3.IonHeader, i6.MatToolbar, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatFormField, i10.MatLabel, i10.MatError, i10.MatSuffix, i11.MatInput, i12.MatSelect, i13.MatOption, i14.MatCheckbox, i15.MatDivider],\n      styles: [\"\\n\\n.material-header[_ngcontent-%COMP%] {\\n  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);\\n}\\n\\n.material-toolbar[_ngcontent-%COMP%] {\\n  height: 64px;\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\\n  color: white;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin-left: 16px;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.material-content[_ngcontent-%COMP%] {\\n  --background: #f5f5f5;\\n  padding: 20px;\\n}\\n\\n.feedback-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n}\\n\\n\\n\\n.header-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\\n  color: white;\\n  padding: 40px 20px;\\n  text-align: center;\\n  border-radius: 12px;\\n  margin-bottom: 24px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.header-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n.header-section[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  margin-bottom: 16px;\\n  opacity: 0.9;\\n}\\n.header-section[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 300;\\n  margin: 0 0 8px 0;\\n  letter-spacing: 0.5px;\\n}\\n.header-section[_ngcontent-%COMP%]   .header-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n\\n\\n\\n.feedback-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  margin-bottom: 24px;\\n  overflow: hidden;\\n}\\n.feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\\n  padding: 24px;\\n}\\n.feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 24px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0;\\n}\\n.feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 28px;\\n  color: #1976d2;\\n}\\n.feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .mat-card-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin-top: 8px;\\n}\\n.feedback-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n\\n\\n\\n.feedback-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.feedback-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #333;\\n  margin: 0 0 20px 0;\\n}\\n.feedback-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  color: #1976d2;\\n  font-size: 20px;\\n}\\n.feedback-form[_ngcontent-%COMP%]   .section-divider[_ngcontent-%COMP%] {\\n  margin: 32px 0;\\n  border-color: #e0e0e0;\\n}\\n.feedback-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .feedback-form[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n.feedback-form[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.feedback-form[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n@media (max-width: 768px) {\\n  .feedback-form[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.feedback-form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.feedback-form[_ngcontent-%COMP%]   .mat-form-field[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n.feedback-form[_ngcontent-%COMP%]   .mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  border-color: #1976d2;\\n}\\n\\n\\n\\n.category-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n.category-option[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n}\\n\\n\\n\\n.rating-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n  margin-bottom: 20px;\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   .rating-star[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  transition: all 0.3s ease;\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   .rating-star[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   .rating-star.active[_ngcontent-%COMP%] {\\n  transform: scale(1.1);\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   .rating-star[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 16px;\\n  font-weight: 500;\\n}\\n.rating-section[_ngcontent-%COMP%]   .rating-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n\\n\\n\\n.checkbox-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n.checkbox-group[_ngcontent-%COMP%]   .checkbox-item[_ngcontent-%COMP%]   .checkbox-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n.checkbox-group[_ngcontent-%COMP%]   .checkbox-item[_ngcontent-%COMP%]   .checkbox-content[_ngcontent-%COMP%]   .checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.checkbox-group[_ngcontent-%COMP%]   .checkbox-item[_ngcontent-%COMP%]   .checkbox-content[_ngcontent-%COMP%]   .checkbox-description[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #666;\\n  margin-top: 4px;\\n}\\n\\n\\n\\n.submit-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.submit-section[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\\n  padding: 12px 32px;\\n  border-radius: 8px;\\n  font-size: 16px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n}\\n.submit-section[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   .spinning[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n.submit-section[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n.thank-you-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);\\n}\\n.thank-you-card[_ngcontent-%COMP%]   .thank-you-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px;\\n}\\n.thank-you-card[_ngcontent-%COMP%]   .thank-you-content[_ngcontent-%COMP%]   .thank-you-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  color: #4caf50;\\n  margin-bottom: 16px;\\n}\\n.thank-you-card[_ngcontent-%COMP%]   .thank-you-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  color: #2e7d32;\\n  margin: 0 0 12px 0;\\n}\\n.thank-you-card[_ngcontent-%COMP%]   .thank-you-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #388e3c;\\n  font-size: 14px;\\n  line-height: 1.5;\\n  margin: 0;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .material-content[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .header-section[_ngcontent-%COMP%] {\\n    padding: 32px 16px;\\n    margin-bottom: 16px;\\n    border-radius: 8px;\\n  }\\n  .header-section[_ngcontent-%COMP%]   .header-icon[_ngcontent-%COMP%] {\\n    font-size: 40px;\\n  }\\n  .header-section[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .header-section[_ngcontent-%COMP%]   .header-subtitle[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .feedback-card[_ngcontent-%COMP%] {\\n    border-radius: 8px;\\n  }\\n  .feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .feedback-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .feedback-form[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .rating-section[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   .rating-star[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n  }\\n  .rating-section[_ngcontent-%COMP%]   .rating-stars[_ngcontent-%COMP%]   .rating-star[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 28px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .header-section[_ngcontent-%COMP%] {\\n    padding: 24px 12px;\\n  }\\n  .header-section[_ngcontent-%COMP%]   .header-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n  .feedback-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .form-title[_ngcontent-%COMP%]   .title-icon[_ngcontent-%COMP%] {\\n    margin-right: 0;\\n    margin-bottom: 8px;\\n  }\\n  .submit-section[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    padding: 16px;\\n  }\\n}\\n\\n\\n  .success-snackbar {\\n  background-color: #4caf50 !important;\\n  color: white !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "getErrorMessage", "ɵɵproperty", "category_r2", "value", "ɵɵtextInterpolate", "icon", "label", "ɵɵlistener", "FeedbackPage_button_79_Template_button_click_0_listener", "rating_r4", "ɵɵrestoreView", "_r3", "$implicit", "ɵɵnextContext", "ɵɵresetView", "onRatingChange", "ɵɵstyleProp", "selectedRating", "color", "ɵɵclassProp", "FeedbackPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "snackBar", "isLoading", "feedbackCategories", "ratingLabels", "createForm", "ngOnInit", "feedbackForm", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "phone", "pattern", "category", "rating", "min", "max", "subject", "message", "wouldRecommend", "allowContact", "patchValue", "getRatingInfo", "find", "r", "onSubmit", "_this", "_asyncToGenerator", "valid", "loading", "create", "duration", "present", "setTimeout", "dismiss", "open", "panelClass", "horizontalPosition", "verticalPosition", "reset", "navigate", "toast", "position", "field", "control", "get", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "goBack", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "LoadingController", "ToastController", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "FeedbackPage_Template", "rf", "ctx", "FeedbackPage_Template_button_click_2_listener", "ɵɵelement", "FeedbackPage_Template_form_ngSubmit_27_listener", "ɵɵtemplate", "FeedbackPage_mat_error_40_Template", "FeedbackPage_mat_error_47_Template", "FeedbackPage_mat_error_54_Template", "FeedbackPage_mat_option_65_Template", "FeedbackPage_mat_error_68_Template", "FeedbackPage_button_79_Template", "FeedbackPage_mat_error_97_Template", "FeedbackPage_mat_error_105_Template", "FeedbackPage_mat_icon_127_Template", "FeedbackPage_mat_icon_128_Template", "FeedbackPage_span_129_Template", "FeedbackPage_span_130_Template", "tmp_3_0", "invalid", "touched", "tmp_4_0", "tmp_5_0", "tmp_7_0", "tmp_12_0", "tmp_13_0"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\feedback\\feedback.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\feedback\\feedback.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\nimport { MatSnackBar } from '@angular/material/snack-bar';\n\n@Component({\n  selector: 'app-feedback',\n  templateUrl: './feedback.page.html',\n  styleUrls: ['./feedback.page.scss'],\n})\nexport class FeedbackPage implements OnInit {\n\n  feedbackForm!: FormGroup;\n  isLoading = false;\n  selectedRating = 5;\n\n  feedbackCategories = [\n    { value: 'service', label: 'Customer Service', icon: 'people' },\n    { value: 'product', label: 'Product Quality', icon: 'star' },\n    { value: 'delivery', label: 'Delivery Experience', icon: 'car' },\n    { value: 'website', label: 'Website/App', icon: 'phone-portrait' },\n    { value: 'support', label: 'Technical Support', icon: 'help-circle' },\n    { value: 'other', label: 'Other', icon: 'ellipsis-horizontal' }\n  ];\n\n  ratingLabels = [\n    { value: 1, label: 'Very Poor', color: '#f44336', icon: 'sad' },\n    { value: 2, label: 'Poor', color: '#ff9800', icon: 'sad' },\n    { value: 3, label: 'Average', color: '#ffc107', icon: 'remove-circle' },\n    { value: 4, label: 'Good', color: '#4caf50', icon: 'happy' },\n    { value: 5, label: 'Excellent', color: '#2196f3', icon: 'happy' }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController,\n    private snackBar: MatSnackBar\n  ) {\n    this.createForm();\n  }\n\n  ngOnInit() {}\n\n  createForm() {\n    this.feedbackForm = this.formBuilder.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^\\d{10}$/)]],\n      category: ['', Validators.required],\n      rating: [5, [Validators.required, Validators.min(1), Validators.max(5)]],\n      subject: ['', [Validators.required, Validators.minLength(5)]],\n      message: ['', [Validators.required, Validators.minLength(10)]],\n      wouldRecommend: [true],\n      allowContact: [false]\n    });\n  }\n\n  onRatingChange(rating: number) {\n    this.selectedRating = rating;\n    this.feedbackForm.patchValue({ rating });\n  }\n\n  getRatingInfo() {\n    return this.ratingLabels.find(r => r.value === this.selectedRating) || this.ratingLabels[4];\n  }\n\n  async onSubmit() {\n    if (this.feedbackForm.valid) {\n      this.isLoading = true;\n      \n      const loading = await this.loadingController.create({\n        message: 'Submitting feedback...',\n        duration: 3000\n      });\n      \n      await loading.present();\n      \n      // Simulate submission process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n        \n        // Show success message\n        this.snackBar.open(\n          'Thank you for your feedback! We appreciate your input.',\n          'Close',\n          {\n            duration: 5000,\n            panelClass: ['success-snackbar'],\n            horizontalPosition: 'center',\n            verticalPosition: 'top'\n          }\n        );\n        \n        // Reset form\n        this.feedbackForm.reset();\n        this.selectedRating = 5;\n        this.feedbackForm.patchValue({ \n          rating: 5, \n          wouldRecommend: true, \n          allowContact: false \n        });\n        \n        // Navigate back to home\n        setTimeout(() => {\n          this.router.navigate(['/home']);\n        }, 2000);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please fill in all required fields correctly.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  getErrorMessage(field: string): string {\n    const control = this.feedbackForm.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('email')) {\n      return 'Please enter a valid email address';\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      name: 'Name',\n      email: 'Email',\n      phone: 'Phone',\n      category: 'Category',\n      subject: 'Subject',\n      message: 'Message'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n}\n", "<ion-header [translucent]=\"true\" class=\"material-header\">\n  <mat-toolbar class=\"material-toolbar\">\n    <button mat-icon-button (click)=\"goBack()\" aria-label=\"Back\">\n      <mat-icon>arrow_back</mat-icon>\n    </button>\n    <span class=\"toolbar-title\">Feedback</span>\n    <span class=\"spacer\"></span>\n  </mat-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\" class=\"material-content\">\n  <div class=\"feedback-container\">\n    \n    <!-- Header Section -->\n    <div class=\"header-section\">\n      <div class=\"header-content\">\n        <mat-icon class=\"header-icon\">feedback</mat-icon>\n        <h1 class=\"header-title\">We Value Your Feedback</h1>\n        <p class=\"header-subtitle\">Help us improve our services by sharing your experience</p>\n      </div>\n    </div>\n\n    <!-- Feedback Form -->\n    <mat-card class=\"feedback-card\">\n      <mat-card-header>\n        <mat-card-title class=\"form-title\">\n          <mat-icon class=\"title-icon\">rate_review</mat-icon>\n          Share Your Experience\n        </mat-card-title>\n        <mat-card-subtitle>All fields marked with * are required</mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"feedbackForm\" (ngSubmit)=\"onSubmit()\" class=\"feedback-form\">\n          \n          <!-- Personal Information Section -->\n          <div class=\"form-section\">\n            <h3 class=\"section-title\">\n              <mat-icon>person</mat-icon>\n              Personal Information\n            </h3>\n            \n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Full Name *</mat-label>\n                <input matInput formControlName=\"name\" placeholder=\"Enter your full name\">\n                <mat-icon matSuffix>person</mat-icon>\n                <mat-error *ngIf=\"feedbackForm.get('name')?.invalid && feedbackForm.get('name')?.touched\">\n                  {{ getErrorMessage('name') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"half-width\">\n                <mat-label>Email Address *</mat-label>\n                <input matInput formControlName=\"email\" placeholder=\"Enter your email\" type=\"email\">\n                <mat-icon matSuffix>email</mat-icon>\n                <mat-error *ngIf=\"feedbackForm.get('email')?.invalid && feedbackForm.get('email')?.touched\">\n                  {{ getErrorMessage('email') }}\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Phone Number (Optional)</mat-label>\n              <input matInput formControlName=\"phone\" placeholder=\"Enter 10-digit phone number\" type=\"tel\">\n              <mat-icon matSuffix>phone</mat-icon>\n              <mat-error *ngIf=\"feedbackForm.get('phone')?.invalid && feedbackForm.get('phone')?.touched\">\n                {{ getErrorMessage('phone') }}\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <mat-divider class=\"section-divider\"></mat-divider>\n\n          <!-- Feedback Category Section -->\n          <div class=\"form-section\">\n            <h3 class=\"section-title\">\n              <mat-icon>category</mat-icon>\n              Feedback Category\n            </h3>\n            \n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>What is your feedback about? *</mat-label>\n              <mat-select formControlName=\"category\">\n                <mat-option *ngFor=\"let category of feedbackCategories\" [value]=\"category.value\">\n                  <div class=\"category-option\">\n                    <mat-icon>{{ category.icon }}</mat-icon>\n                    <span>{{ category.label }}</span>\n                  </div>\n                </mat-option>\n              </mat-select>\n              <mat-icon matSuffix>category</mat-icon>\n              <mat-error *ngIf=\"feedbackForm.get('category')?.invalid && feedbackForm.get('category')?.touched\">\n                {{ getErrorMessage('category') }}\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <mat-divider class=\"section-divider\"></mat-divider>\n\n          <!-- Rating Section -->\n          <div class=\"form-section\">\n            <h3 class=\"section-title\">\n              <mat-icon>star_rate</mat-icon>\n              Overall Rating\n            </h3>\n            \n            <div class=\"rating-section\">\n              <p class=\"rating-label\">How would you rate your overall experience? *</p>\n              \n              <div class=\"rating-stars\">\n                <button \n                  *ngFor=\"let rating of ratingLabels\" \n                  type=\"button\"\n                  mat-icon-button \n                  class=\"rating-star\"\n                  [class.active]=\"selectedRating >= rating.value\"\n                  [style.color]=\"selectedRating >= rating.value ? rating.color : '#ddd'\"\n                  (click)=\"onRatingChange(rating.value)\">\n                  <mat-icon>star</mat-icon>\n                </button>\n              </div>\n              \n              <div class=\"rating-info\" [style.color]=\"getRatingInfo().color\">\n                <mat-icon>{{ getRatingInfo().icon }}</mat-icon>\n                <span>{{ getRatingInfo().label }}</span>\n              </div>\n            </div>\n          </div>\n\n          <mat-divider class=\"section-divider\"></mat-divider>\n\n          <!-- Feedback Details Section -->\n          <div class=\"form-section\">\n            <h3 class=\"section-title\">\n              <mat-icon>message</mat-icon>\n              Feedback Details\n            </h3>\n            \n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Subject *</mat-label>\n              <input matInput formControlName=\"subject\" placeholder=\"Brief summary of your feedback\">\n              <mat-icon matSuffix>subject</mat-icon>\n              <mat-error *ngIf=\"feedbackForm.get('subject')?.invalid && feedbackForm.get('subject')?.touched\">\n                {{ getErrorMessage('subject') }}\n              </mat-error>\n            </mat-form-field>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Detailed Message *</mat-label>\n              <textarea \n                matInput \n                formControlName=\"message\" \n                placeholder=\"Please provide detailed feedback about your experience\"\n                rows=\"5\">\n              </textarea>\n              <mat-icon matSuffix>description</mat-icon>\n              <mat-error *ngIf=\"feedbackForm.get('message')?.invalid && feedbackForm.get('message')?.touched\">\n                {{ getErrorMessage('message') }}\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <mat-divider class=\"section-divider\"></mat-divider>\n\n          <!-- Additional Options Section -->\n          <div class=\"form-section\">\n            <h3 class=\"section-title\">\n              <mat-icon>settings</mat-icon>\n              Additional Options\n            </h3>\n            \n            <div class=\"checkbox-group\">\n              <mat-checkbox formControlName=\"wouldRecommend\" color=\"primary\" class=\"checkbox-item\">\n                <div class=\"checkbox-content\">\n                  <span class=\"checkbox-label\">Would you recommend AIS Smart Care to others?</span>\n                  <span class=\"checkbox-description\">Help us understand if you'd refer our services</span>\n                </div>\n              </mat-checkbox>\n\n              <mat-checkbox formControlName=\"allowContact\" color=\"primary\" class=\"checkbox-item\">\n                <div class=\"checkbox-content\">\n                  <span class=\"checkbox-label\">Allow us to contact you for follow-up</span>\n                  <span class=\"checkbox-description\">We may reach out to discuss your feedback further</span>\n                </div>\n              </mat-checkbox>\n            </div>\n          </div>\n\n          <!-- Submit Button -->\n          <div class=\"submit-section\">\n            <button \n              mat-raised-button \n              color=\"primary\" \n              type=\"submit\"\n              class=\"submit-button\"\n              [disabled]=\"isLoading || feedbackForm.invalid\">\n              <mat-icon *ngIf=\"isLoading\" class=\"spinning\">refresh</mat-icon>\n              <mat-icon *ngIf=\"!isLoading\">send</mat-icon>\n              <span *ngIf=\"!isLoading\">Submit Feedback</span>\n              <span *ngIf=\"isLoading\">Submitting...</span>\n            </button>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Thank You Message -->\n    <mat-card class=\"thank-you-card\">\n      <mat-card-content>\n        <div class=\"thank-you-content\">\n          <mat-icon class=\"thank-you-icon\">favorite</mat-icon>\n          <h3>Thank You!</h3>\n          <p>Your feedback helps us improve our services and provide better experiences for all our customers.</p>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</ion-content>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;IC8CnDC,EAAA,CAAAC,cAAA,gBAA0F;IACxFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,cACF;;;;;IAOAP,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,eACF;;;;;IAQFP,EAAA,CAAAC,cAAA,gBAA4F;IAC1FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,eACF;;;;;IAkBMP,EAFJ,CAAAC,cAAA,qBAAiF,cAClD,eACjB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAE9BF,EAF8B,CAAAG,YAAA,EAAO,EAC7B,EACK;;;;IAL2CH,EAAA,CAAAQ,UAAA,UAAAC,WAAA,CAAAC,KAAA,CAAwB;IAElEV,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAW,iBAAA,CAAAF,WAAA,CAAAG,IAAA,CAAmB;IACvBZ,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAW,iBAAA,CAAAF,WAAA,CAAAI,KAAA,CAAoB;;;;;IAKhCb,EAAA,CAAAC,cAAA,gBAAkG;IAChGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,kBACF;;;;;;IAiBEP,EAAA,CAAAC,cAAA,iBAOyC;IAAvCD,EAAA,CAAAc,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,SAAA,GAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAb,MAAA,GAAAN,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAASf,MAAA,CAAAgB,cAAA,CAAAN,SAAA,CAAAN,KAAA,CAA4B;IAAA,EAAC;IACtCV,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;;;;;IAHPH,EAAA,CAAAuB,WAAA,UAAAjB,MAAA,CAAAkB,cAAA,IAAAR,SAAA,CAAAN,KAAA,GAAAM,SAAA,CAAAS,KAAA,UAAsE;IADtEzB,EAAA,CAAA0B,WAAA,WAAApB,MAAA,CAAAkB,cAAA,IAAAR,SAAA,CAAAN,KAAA,CAA+C;;;;;IA2BnDV,EAAA,CAAAC,cAAA,gBAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,iBACF;;;;;IAYAP,EAAA,CAAAC,cAAA,gBAAgG;IAC9FD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,eAAA,iBACF;;;;;IAsCAP,EAAA,CAAAC,cAAA,mBAA6C;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/DH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC5CH,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAC/CH,EAAA,CAAAC,cAAA,WAAwB;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD7L1D,OAAM,MAAOwB,YAAY;EAuBvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC,EAChCC,QAAqB;IAJrB,KAAAJ,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,QAAQ,GAARA,QAAQ;IAzBlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAV,cAAc,GAAG,CAAC;IAElB,KAAAW,kBAAkB,GAAG,CACnB;MAAEzB,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE,kBAAkB;MAAED,IAAI,EAAE;IAAQ,CAAE,EAC/D;MAAEF,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE,iBAAiB;MAAED,IAAI,EAAE;IAAM,CAAE,EAC5D;MAAEF,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE,qBAAqB;MAAED,IAAI,EAAE;IAAK,CAAE,EAChE;MAAEF,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE,aAAa;MAAED,IAAI,EAAE;IAAgB,CAAE,EAClE;MAAEF,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE,mBAAmB;MAAED,IAAI,EAAE;IAAa,CAAE,EACrE;MAAEF,KAAK,EAAE,OAAO;MAAEG,KAAK,EAAE,OAAO;MAAED,IAAI,EAAE;IAAqB,CAAE,CAChE;IAED,KAAAwB,YAAY,GAAG,CACb;MAAE1B,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,WAAW;MAAEY,KAAK,EAAE,SAAS;MAAEb,IAAI,EAAE;IAAK,CAAE,EAC/D;MAAEF,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,MAAM;MAAEY,KAAK,EAAE,SAAS;MAAEb,IAAI,EAAE;IAAK,CAAE,EAC1D;MAAEF,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,SAAS;MAAEY,KAAK,EAAE,SAAS;MAAEb,IAAI,EAAE;IAAe,CAAE,EACvE;MAAEF,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,MAAM;MAAEY,KAAK,EAAE,SAAS;MAAEb,IAAI,EAAE;IAAO,CAAE,EAC5D;MAAEF,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,WAAW;MAAEY,KAAK,EAAE,SAAS;MAAEb,IAAI,EAAE;IAAO,CAAE,CAClE;IASC,IAAI,CAACyB,UAAU,EAAE;EACnB;EAEAC,QAAQA,CAAA,GAAI;EAEZD,UAAUA,CAAA;IACR,IAAI,CAACE,YAAY,GAAG,IAAI,CAACV,WAAW,CAACW,KAAK,CAAC;MACzCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC7C,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC6C,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC9C,UAAU,CAAC+C,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;MAC7CC,QAAQ,EAAE,CAAC,EAAE,EAAEhD,UAAU,CAAC2C,QAAQ,CAAC;MACnCM,MAAM,EAAE,CAAC,CAAC,EAAE,CAACjD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAACkD,GAAG,CAAC,CAAC,CAAC,EAAElD,UAAU,CAACmD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACxEC,OAAO,EAAE,CAAC,EAAE,EAAE,CAACpD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7DS,OAAO,EAAE,CAAC,EAAE,EAAE,CAACrD,UAAU,CAAC2C,QAAQ,EAAE3C,UAAU,CAAC4C,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC9DU,cAAc,EAAE,CAAC,IAAI,CAAC;MACtBC,YAAY,EAAE,CAAC,KAAK;KACrB,CAAC;EACJ;EAEAhC,cAAcA,CAAC0B,MAAc;IAC3B,IAAI,CAACxB,cAAc,GAAGwB,MAAM;IAC5B,IAAI,CAACT,YAAY,CAACgB,UAAU,CAAC;MAAEP;IAAM,CAAE,CAAC;EAC1C;EAEAQ,aAAaA,CAAA;IACX,OAAO,IAAI,CAACpB,YAAY,CAACqB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChD,KAAK,KAAK,IAAI,CAACc,cAAc,CAAC,IAAI,IAAI,CAACY,YAAY,CAAC,CAAC,CAAC;EAC7F;EAEMuB,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAID,KAAI,CAACrB,YAAY,CAACuB,KAAK,EAAE;QAC3BF,KAAI,CAAC1B,SAAS,GAAG,IAAI;QAErB,MAAM6B,OAAO,SAASH,KAAI,CAAC7B,iBAAiB,CAACiC,MAAM,CAAC;UAClDZ,OAAO,EAAE,wBAAwB;UACjCa,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMF,OAAO,CAACG,OAAO,EAAE;QAEvB;QACAC,UAAU,cAAAN,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAAC1B,SAAS,GAAG,KAAK;UACtB,MAAM6B,OAAO,CAACK,OAAO,EAAE;UAEvB;UACAR,KAAI,CAAC3B,QAAQ,CAACoC,IAAI,CAChB,wDAAwD,EACxD,OAAO,EACP;YACEJ,QAAQ,EAAE,IAAI;YACdK,UAAU,EAAE,CAAC,kBAAkB,CAAC;YAChCC,kBAAkB,EAAE,QAAQ;YAC5BC,gBAAgB,EAAE;WACnB,CACF;UAED;UACAZ,KAAI,CAACrB,YAAY,CAACkC,KAAK,EAAE;UACzBb,KAAI,CAACpC,cAAc,GAAG,CAAC;UACvBoC,KAAI,CAACrB,YAAY,CAACgB,UAAU,CAAC;YAC3BP,MAAM,EAAE,CAAC;YACTK,cAAc,EAAE,IAAI;YACpBC,YAAY,EAAE;WACf,CAAC;UAEF;UACAa,UAAU,CAAC,MAAK;YACdP,KAAI,CAAC9B,MAAM,CAAC4C,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;UACjC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMC,KAAK,SAASf,KAAI,CAAC5B,eAAe,CAACgC,MAAM,CAAC;UAC9CZ,OAAO,EAAE,+CAA+C;UACxDa,QAAQ,EAAE,IAAI;UACdxC,KAAK,EAAE,QAAQ;UACfmD,QAAQ,EAAE;SACX,CAAC;QACF,MAAMD,KAAK,CAACT,OAAO,EAAE;;IACtB;EACH;EAEA3D,eAAeA,CAACsE,KAAa;IAC3B,MAAMC,OAAO,GAAG,IAAI,CAACvC,YAAY,CAACwC,GAAG,CAACF,KAAK,CAAC;IAC5C,IAAIC,OAAO,EAAEE,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACJ,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC9B,OAAO,oCAAoC;;IAE7C,IAAIF,OAAO,EAAEE,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMrC,SAAS,GAAGmC,OAAO,CAACI,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACJ,KAAK,CAAC,qBAAqBlC,SAAS,aAAa;;IAEhF,IAAImC,OAAO,EAAEE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACJ,KAAa;IACjC,MAAMO,MAAM,GAA8B;MACxC3C,IAAI,EAAE,MAAM;MACZG,KAAK,EAAE,OAAO;MACdC,KAAK,EAAE,OAAO;MACdE,QAAQ,EAAE,UAAU;MACpBI,OAAO,EAAE,SAAS;MAClBC,OAAO,EAAE;KACV;IACD,OAAOgC,MAAM,CAACP,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAQ,MAAMA,CAAA;IACJ,IAAI,CAACvD,MAAM,CAAC4C,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBA/IW/C,YAAY,EAAA3B,EAAA,CAAAsF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxF,EAAA,CAAAsF,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA1F,EAAA,CAAAsF,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAA5F,EAAA,CAAAsF,iBAAA,CAAAK,EAAA,CAAAE,eAAA,GAAA7F,EAAA,CAAAsF,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAZpE,YAAY;MAAAqE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTrBtG,EAFJ,CAAAC,cAAA,oBAAyD,qBACjB,gBACyB;UAArCD,EAAA,CAAAc,UAAA,mBAAA0F,8CAAA;YAAA,OAASD,GAAA,CAAAlB,MAAA,EAAQ;UAAA,EAAC;UACxCrF,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UACTH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3CH,EAAA,CAAAyG,SAAA,cAA4B;UAEhCzG,EADE,CAAAG,YAAA,EAAc,EACH;UAQLH,EANR,CAAAC,cAAA,qBAA0D,aACxB,cAGF,cACE,mBACI;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACjDH,EAAA,CAAAC,cAAA,cAAyB;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACpDH,EAAA,CAAAC,cAAA,aAA2B;UAAAD,EAAA,CAAAE,MAAA,+DAAuD;UAEtFF,EAFsF,CAAAG,YAAA,EAAI,EAClF,EACF;UAMAH,EAHN,CAAAC,cAAA,oBAAgC,uBACb,0BACoB,oBACJ;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACnDH,EAAA,CAAAE,MAAA,+BACF;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACjBH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,6CAAqC;UAC1DF,EAD0D,CAAAG,YAAA,EAAoB,EAC5D;UAGhBH,EADF,CAAAC,cAAA,wBAAkB,gBAC+D;UAA9CD,EAAA,CAAAc,UAAA,sBAAA4F,gDAAA;YAAA,OAAYH,GAAA,CAAA5C,QAAA,EAAU;UAAA,EAAC;UAKlD3D,EAFJ,CAAAC,cAAA,eAA0B,cACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC3BH,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAIDH,EAFJ,CAAAC,cAAA,eAAsB,0BACoC,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAyG,SAAA,iBAA0E;UAC1EzG,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAA2G,UAAA,KAAAC,kCAAA,wBAA0F;UAG5F5G,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACtCH,EAAA,CAAAyG,SAAA,iBAAoF;UACpFzG,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA2G,UAAA,KAAAE,kCAAA,wBAA4F;UAIhG7G,EADE,CAAAG,YAAA,EAAiB,EACb;UAGJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC9CH,EAAA,CAAAyG,SAAA,iBAA6F;UAC7FzG,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAA2G,UAAA,KAAAG,kCAAA,wBAA4F;UAIhG9G,EADE,CAAAG,YAAA,EAAiB,EACb;UAENH,EAAA,CAAAyG,SAAA,uBAAmD;UAK/CzG,EAFJ,CAAAC,cAAA,eAA0B,cACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGHH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,sCAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrDH,EAAA,CAAAC,cAAA,sBAAuC;UACrCD,EAAA,CAAA2G,UAAA,KAAAI,mCAAA,yBAAiF;UAMnF/G,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACvCH,EAAA,CAAA2G,UAAA,KAAAK,kCAAA,wBAAkG;UAItGhH,EADE,CAAAG,YAAA,EAAiB,EACb;UAENH,EAAA,CAAAyG,SAAA,uBAAmD;UAK/CzG,EAFJ,CAAAC,cAAA,eAA0B,cACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGHH,EADF,CAAAC,cAAA,eAA4B,aACF;UAAAD,EAAA,CAAAE,MAAA,qDAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEzEH,EAAA,CAAAC,cAAA,eAA0B;UACxBD,EAAA,CAAA2G,UAAA,KAAAM,+BAAA,qBAOyC;UAG3CjH,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,eAA+D,gBACnD;UAAAD,EAAA,CAAAE,MAAA,IAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/CH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UAGvCF,EAHuC,CAAAG,YAAA,EAAO,EACpC,EACF,EACF;UAENH,EAAA,CAAAyG,SAAA,uBAAmD;UAK/CzG,EAFJ,CAAAC,cAAA,eAA0B,cACE,gBACd;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAGHH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChCH,EAAA,CAAAyG,SAAA,iBAAuF;UACvFzG,EAAA,CAAAC,cAAA,oBAAoB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACtCH,EAAA,CAAA2G,UAAA,KAAAO,kCAAA,wBAAgG;UAGlGlH,EAAA,CAAAG,YAAA,EAAiB;UAGfH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACzCH,EAAA,CAAAC,cAAA,qBAIW;UACXD,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAC,cAAA,qBAAoB;UAAAD,EAAA,CAAAE,MAAA,oBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC1CH,EAAA,CAAA2G,UAAA,MAAAQ,mCAAA,wBAAgG;UAIpGnH,EADE,CAAAG,YAAA,EAAiB,EACb;UAENH,EAAA,CAAAyG,SAAA,wBAAmD;UAK/CzG,EAFJ,CAAAC,cAAA,gBAA0B,eACE,iBACd;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAKCH,EAHN,CAAAC,cAAA,gBAA4B,yBAC2D,gBACrD,iBACC;UAAAD,EAAA,CAAAE,MAAA,sDAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjFH,EAAA,CAAAC,cAAA,iBAAmC;UAAAD,EAAA,CAAAE,MAAA,uDAA8C;UAErFF,EAFqF,CAAAG,YAAA,EAAO,EACpF,EACO;UAIXH,EAFJ,CAAAC,cAAA,yBAAmF,gBACnD,iBACC;UAAAD,EAAA,CAAAE,MAAA,8CAAqC;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzEH,EAAA,CAAAC,cAAA,iBAAmC;UAAAD,EAAA,CAAAE,MAAA,0DAAiD;UAI5FF,EAJ4F,CAAAG,YAAA,EAAO,EACvF,EACO,EACX,EACF;UAIJH,EADF,CAAAC,cAAA,gBAA4B,mBAMuB;UAI/CD,EAHA,CAAA2G,UAAA,MAAAS,kCAAA,uBAA6C,MAAAC,kCAAA,uBAChB,MAAAC,8BAAA,mBACJ,MAAAC,8BAAA,mBACD;UAKlCvH,EAJQ,CAAAG,YAAA,EAAS,EACL,EACD,EACU,EACV;UAMLH,EAHN,CAAAC,cAAA,qBAAiC,yBACb,gBACe,qBACI;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpDH,EAAA,CAAAC,cAAA,WAAI;UAAAD,EAAA,CAAAE,MAAA,mBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,0GAAiG;UAK9GF,EAL8G,CAAAG,YAAA,EAAI,EACpG,EACW,EACV,EACP,EACM;;;;;;;;;UA1NFH,EAAA,CAAAQ,UAAA,qBAAoB;UAUnBR,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAQ,UAAA,oBAAmB;UAuBlBR,EAAA,CAAAI,SAAA,IAA0B;UAA1BJ,EAAA,CAAAQ,UAAA,cAAA+F,GAAA,CAAAhE,YAAA,CAA0B;UAcZvC,EAAA,CAAAI,SAAA,IAA4E;UAA5EJ,EAAA,CAAAQ,UAAA,WAAAgH,OAAA,GAAAjB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,2BAAAyC,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAjB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,2BAAAyC,OAAA,CAAAE,OAAA,EAA4E;UAS5E1H,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAQ,UAAA,WAAAmH,OAAA,GAAApB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,4BAAA4C,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAApB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,4BAAA4C,OAAA,CAAAD,OAAA,EAA8E;UAUhF1H,EAAA,CAAAI,SAAA,GAA8E;UAA9EJ,EAAA,CAAAQ,UAAA,WAAAoH,OAAA,GAAArB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,4BAAA6C,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAArB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,4BAAA6C,OAAA,CAAAF,OAAA,EAA8E;UAkBvD1H,EAAA,CAAAI,SAAA,IAAqB;UAArBJ,EAAA,CAAAQ,UAAA,YAAA+F,GAAA,CAAApE,kBAAA,CAAqB;UAQ5CnC,EAAA,CAAAI,SAAA,GAAoF;UAApFJ,EAAA,CAAAQ,UAAA,WAAAqH,OAAA,GAAAtB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,+BAAA8C,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAAtB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,+BAAA8C,OAAA,CAAAH,OAAA,EAAoF;UAoBzE1H,EAAA,CAAAI,SAAA,IAAe;UAAfJ,EAAA,CAAAQ,UAAA,YAAA+F,GAAA,CAAAnE,YAAA,CAAe;UAWbpC,EAAA,CAAAI,SAAA,EAAqC;UAArCJ,EAAA,CAAAuB,WAAA,UAAAgF,GAAA,CAAA/C,aAAA,GAAA/B,KAAA,CAAqC;UAClDzB,EAAA,CAAAI,SAAA,GAA0B;UAA1BJ,EAAA,CAAAW,iBAAA,CAAA4F,GAAA,CAAA/C,aAAA,GAAA5C,IAAA,CAA0B;UAC9BZ,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAW,iBAAA,CAAA4F,GAAA,CAAA/C,aAAA,GAAA3C,KAAA,CAA2B;UAkBvBb,EAAA,CAAAI,SAAA,IAAkF;UAAlFJ,EAAA,CAAAQ,UAAA,WAAAsH,QAAA,GAAAvB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,8BAAA+C,QAAA,CAAAL,OAAA,OAAAK,QAAA,GAAAvB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,8BAAA+C,QAAA,CAAAJ,OAAA,EAAkF;UAclF1H,EAAA,CAAAI,SAAA,GAAkF;UAAlFJ,EAAA,CAAAQ,UAAA,WAAAuH,QAAA,GAAAxB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,8BAAAgD,QAAA,CAAAN,OAAA,OAAAM,QAAA,GAAAxB,GAAA,CAAAhE,YAAA,CAAAwC,GAAA,8BAAAgD,QAAA,CAAAL,OAAA,EAAkF;UAuC9F1H,EAAA,CAAAI,SAAA,IAA8C;UAA9CJ,EAAA,CAAAQ,UAAA,aAAA+F,GAAA,CAAArE,SAAA,IAAAqE,GAAA,CAAAhE,YAAA,CAAAkF,OAAA,CAA8C;UACnCzH,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAQ,UAAA,SAAA+F,GAAA,CAAArE,SAAA,CAAe;UACflC,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAQ,UAAA,UAAA+F,GAAA,CAAArE,SAAA,CAAgB;UACpBlC,EAAA,CAAAI,SAAA,EAAgB;UAAhBJ,EAAA,CAAAQ,UAAA,UAAA+F,GAAA,CAAArE,SAAA,CAAgB;UAChBlC,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAQ,UAAA,SAAA+F,GAAA,CAAArE,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}