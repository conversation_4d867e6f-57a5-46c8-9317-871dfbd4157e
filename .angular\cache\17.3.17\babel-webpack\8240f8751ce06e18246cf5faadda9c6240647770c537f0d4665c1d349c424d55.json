{"ast": null, "code": "/*!\n    localForage -- Offline Storage, Improved\n    Version 1.10.0\n    https://localforage.github.io/localForage\n    (c) 2013-2017 Mozilla, Apache License 2.0\n*/\n(function (f) {\n  if (typeof exports === \"object\" && typeof module !== \"undefined\") {\n    module.exports = f();\n  } else if (typeof define === \"function\" && define.amd) {\n    define([], f);\n  } else {\n    var g;\n    if (typeof window !== \"undefined\") {\n      g = window;\n    } else if (typeof global !== \"undefined\") {\n      g = global;\n    } else if (typeof self !== \"undefined\") {\n      g = self;\n    } else {\n      g = this;\n    }\n    g.localforage = f();\n  }\n})(function () {\n  var define, module, exports;\n  return function e(t, n, r) {\n    function s(o, u) {\n      if (!n[o]) {\n        if (!t[o]) {\n          var a = typeof require == \"function\" && require;\n          if (!u && a) return a(o, !0);\n          if (i) return i(o, !0);\n          var f = new Error(\"Cannot find module '\" + o + \"'\");\n          throw f.code = \"MODULE_NOT_FOUND\", f;\n        }\n        var l = n[o] = {\n          exports: {}\n        };\n        t[o][0].call(l.exports, function (e) {\n          var n = t[o][1][e];\n          return s(n ? n : e);\n        }, l, l.exports, e, t, n, r);\n      }\n      return n[o].exports;\n    }\n    var i = typeof require == \"function\" && require;\n    for (var o = 0; o < r.length; o++) s(r[o]);\n    return s;\n  }({\n    1: [function (_dereq_, module, exports) {\n      (function (global) {\n        'use strict';\n\n        var Mutation = global.MutationObserver || global.WebKitMutationObserver;\n        var scheduleDrain;\n        {\n          if (Mutation) {\n            var called = 0;\n            var observer = new Mutation(nextTick);\n            var element = global.document.createTextNode('');\n            observer.observe(element, {\n              characterData: true\n            });\n            scheduleDrain = function () {\n              element.data = called = ++called % 2;\n            };\n          } else if (!global.setImmediate && typeof global.MessageChannel !== 'undefined') {\n            var channel = new global.MessageChannel();\n            channel.port1.onmessage = nextTick;\n            scheduleDrain = function () {\n              channel.port2.postMessage(0);\n            };\n          } else if ('document' in global && 'onreadystatechange' in global.document.createElement('script')) {\n            scheduleDrain = function () {\n              // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n              // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n              var scriptEl = global.document.createElement('script');\n              scriptEl.onreadystatechange = function () {\n                nextTick();\n                scriptEl.onreadystatechange = null;\n                scriptEl.parentNode.removeChild(scriptEl);\n                scriptEl = null;\n              };\n              global.document.documentElement.appendChild(scriptEl);\n            };\n          } else {\n            scheduleDrain = function () {\n              setTimeout(nextTick, 0);\n            };\n          }\n        }\n        var draining;\n        var queue = [];\n        //named nextTick for less confusing stack traces\n        function nextTick() {\n          draining = true;\n          var i, oldQueue;\n          var len = queue.length;\n          while (len) {\n            oldQueue = queue;\n            queue = [];\n            i = -1;\n            while (++i < len) {\n              oldQueue[i]();\n            }\n            len = queue.length;\n          }\n          draining = false;\n        }\n        module.exports = immediate;\n        function immediate(task) {\n          if (queue.push(task) === 1 && !draining) {\n            scheduleDrain();\n          }\n        }\n      }).call(this, typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {});\n    }, {}],\n    2: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var immediate = _dereq_(1);\n\n      /* istanbul ignore next */\n      function INTERNAL() {}\n      var handlers = {};\n      var REJECTED = ['REJECTED'];\n      var FULFILLED = ['FULFILLED'];\n      var PENDING = ['PENDING'];\n      module.exports = Promise;\n      function Promise(resolver) {\n        if (typeof resolver !== 'function') {\n          throw new TypeError('resolver must be a function');\n        }\n        this.state = PENDING;\n        this.queue = [];\n        this.outcome = void 0;\n        if (resolver !== INTERNAL) {\n          safelyResolveThenable(this, resolver);\n        }\n      }\n      Promise.prototype[\"catch\"] = function (onRejected) {\n        return this.then(null, onRejected);\n      };\n      Promise.prototype.then = function (onFulfilled, onRejected) {\n        if (typeof onFulfilled !== 'function' && this.state === FULFILLED || typeof onRejected !== 'function' && this.state === REJECTED) {\n          return this;\n        }\n        var promise = new this.constructor(INTERNAL);\n        if (this.state !== PENDING) {\n          var resolver = this.state === FULFILLED ? onFulfilled : onRejected;\n          unwrap(promise, resolver, this.outcome);\n        } else {\n          this.queue.push(new QueueItem(promise, onFulfilled, onRejected));\n        }\n        return promise;\n      };\n      function QueueItem(promise, onFulfilled, onRejected) {\n        this.promise = promise;\n        if (typeof onFulfilled === 'function') {\n          this.onFulfilled = onFulfilled;\n          this.callFulfilled = this.otherCallFulfilled;\n        }\n        if (typeof onRejected === 'function') {\n          this.onRejected = onRejected;\n          this.callRejected = this.otherCallRejected;\n        }\n      }\n      QueueItem.prototype.callFulfilled = function (value) {\n        handlers.resolve(this.promise, value);\n      };\n      QueueItem.prototype.otherCallFulfilled = function (value) {\n        unwrap(this.promise, this.onFulfilled, value);\n      };\n      QueueItem.prototype.callRejected = function (value) {\n        handlers.reject(this.promise, value);\n      };\n      QueueItem.prototype.otherCallRejected = function (value) {\n        unwrap(this.promise, this.onRejected, value);\n      };\n      function unwrap(promise, func, value) {\n        immediate(function () {\n          var returnValue;\n          try {\n            returnValue = func(value);\n          } catch (e) {\n            return handlers.reject(promise, e);\n          }\n          if (returnValue === promise) {\n            handlers.reject(promise, new TypeError('Cannot resolve promise with itself'));\n          } else {\n            handlers.resolve(promise, returnValue);\n          }\n        });\n      }\n      handlers.resolve = function (self, value) {\n        var result = tryCatch(getThen, value);\n        if (result.status === 'error') {\n          return handlers.reject(self, result.value);\n        }\n        var thenable = result.value;\n        if (thenable) {\n          safelyResolveThenable(self, thenable);\n        } else {\n          self.state = FULFILLED;\n          self.outcome = value;\n          var i = -1;\n          var len = self.queue.length;\n          while (++i < len) {\n            self.queue[i].callFulfilled(value);\n          }\n        }\n        return self;\n      };\n      handlers.reject = function (self, error) {\n        self.state = REJECTED;\n        self.outcome = error;\n        var i = -1;\n        var len = self.queue.length;\n        while (++i < len) {\n          self.queue[i].callRejected(error);\n        }\n        return self;\n      };\n      function getThen(obj) {\n        // Make sure we only access the accessor once as required by the spec\n        var then = obj && obj.then;\n        if (obj && (typeof obj === 'object' || typeof obj === 'function') && typeof then === 'function') {\n          return function appyThen() {\n            then.apply(obj, arguments);\n          };\n        }\n      }\n      function safelyResolveThenable(self, thenable) {\n        // Either fulfill, reject or reject with error\n        var called = false;\n        function onError(value) {\n          if (called) {\n            return;\n          }\n          called = true;\n          handlers.reject(self, value);\n        }\n        function onSuccess(value) {\n          if (called) {\n            return;\n          }\n          called = true;\n          handlers.resolve(self, value);\n        }\n        function tryToUnwrap() {\n          thenable(onSuccess, onError);\n        }\n        var result = tryCatch(tryToUnwrap);\n        if (result.status === 'error') {\n          onError(result.value);\n        }\n      }\n      function tryCatch(func, value) {\n        var out = {};\n        try {\n          out.value = func(value);\n          out.status = 'success';\n        } catch (e) {\n          out.status = 'error';\n          out.value = e;\n        }\n        return out;\n      }\n      Promise.resolve = resolve;\n      function resolve(value) {\n        if (value instanceof this) {\n          return value;\n        }\n        return handlers.resolve(new this(INTERNAL), value);\n      }\n      Promise.reject = reject;\n      function reject(reason) {\n        var promise = new this(INTERNAL);\n        return handlers.reject(promise, reason);\n      }\n      Promise.all = all;\n      function all(iterable) {\n        var self = this;\n        if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n          return this.reject(new TypeError('must be an array'));\n        }\n        var len = iterable.length;\n        var called = false;\n        if (!len) {\n          return this.resolve([]);\n        }\n        var values = new Array(len);\n        var resolved = 0;\n        var i = -1;\n        var promise = new this(INTERNAL);\n        while (++i < len) {\n          allResolver(iterable[i], i);\n        }\n        return promise;\n        function allResolver(value, i) {\n          self.resolve(value).then(resolveFromAll, function (error) {\n            if (!called) {\n              called = true;\n              handlers.reject(promise, error);\n            }\n          });\n          function resolveFromAll(outValue) {\n            values[i] = outValue;\n            if (++resolved === len && !called) {\n              called = true;\n              handlers.resolve(promise, values);\n            }\n          }\n        }\n      }\n      Promise.race = race;\n      function race(iterable) {\n        var self = this;\n        if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n          return this.reject(new TypeError('must be an array'));\n        }\n        var len = iterable.length;\n        var called = false;\n        if (!len) {\n          return this.resolve([]);\n        }\n        var i = -1;\n        var promise = new this(INTERNAL);\n        while (++i < len) {\n          resolver(iterable[i]);\n        }\n        return promise;\n        function resolver(value) {\n          self.resolve(value).then(function (response) {\n            if (!called) {\n              called = true;\n              handlers.resolve(promise, response);\n            }\n          }, function (error) {\n            if (!called) {\n              called = true;\n              handlers.reject(promise, error);\n            }\n          });\n        }\n      }\n    }, {\n      \"1\": 1\n    }],\n    3: [function (_dereq_, module, exports) {\n      (function (global) {\n        'use strict';\n\n        if (typeof global.Promise !== 'function') {\n          global.Promise = _dereq_(2);\n        }\n      }).call(this, typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {});\n    }, {\n      \"2\": 2\n    }],\n    4: [function (_dereq_, module, exports) {\n      'use strict';\n\n      var _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) {\n        return typeof obj;\n      } : function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n      function _classCallCheck(instance, Constructor) {\n        if (!(instance instanceof Constructor)) {\n          throw new TypeError(\"Cannot call a class as a function\");\n        }\n      }\n      function getIDB() {\n        /* global indexedDB,webkitIndexedDB,mozIndexedDB,OIndexedDB,msIndexedDB */\n        try {\n          if (typeof indexedDB !== 'undefined') {\n            return indexedDB;\n          }\n          if (typeof webkitIndexedDB !== 'undefined') {\n            return webkitIndexedDB;\n          }\n          if (typeof mozIndexedDB !== 'undefined') {\n            return mozIndexedDB;\n          }\n          if (typeof OIndexedDB !== 'undefined') {\n            return OIndexedDB;\n          }\n          if (typeof msIndexedDB !== 'undefined') {\n            return msIndexedDB;\n          }\n        } catch (e) {\n          return;\n        }\n      }\n      var idb = getIDB();\n      function isIndexedDBValid() {\n        try {\n          // Initialize IndexedDB; fall back to vendor-prefixed versions\n          // if needed.\n          if (!idb || !idb.open) {\n            return false;\n          }\n          // We mimic PouchDB here;\n          //\n          // We test for openDatabase because IE Mobile identifies itself\n          // as Safari. Oh the lulz...\n          var isSafari = typeof openDatabase !== 'undefined' && /(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent) && !/BlackBerry/.test(navigator.platform);\n          var hasFetch = typeof fetch === 'function' && fetch.toString().indexOf('[native code') !== -1;\n\n          // Safari <10.1 does not meet our requirements for IDB support\n          // (see: https://github.com/pouchdb/pouchdb/issues/5572).\n          // Safari 10.1 shipped with fetch, we can use that to detect it.\n          // Note: this creates issues with `window.fetch` polyfills and\n          // overrides; see:\n          // https://github.com/localForage/localForage/issues/856\n          return (!isSafari || hasFetch) && typeof indexedDB !== 'undefined' &&\n          // some outdated implementations of IDB that appear on Samsung\n          // and HTC Android devices <4.4 are missing IDBKeyRange\n          // See: https://github.com/mozilla/localForage/issues/128\n          // See: https://github.com/mozilla/localForage/issues/272\n          typeof IDBKeyRange !== 'undefined';\n        } catch (e) {\n          return false;\n        }\n      }\n\n      // Abstracts constructing a Blob object, so it also works in older\n      // browsers that don't support the native Blob constructor. (i.e.\n      // old QtWebKit versions, at least).\n      // Abstracts constructing a Blob object, so it also works in older\n      // browsers that don't support the native Blob constructor. (i.e.\n      // old QtWebKit versions, at least).\n      function createBlob(parts, properties) {\n        /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n        parts = parts || [];\n        properties = properties || {};\n        try {\n          return new Blob(parts, properties);\n        } catch (e) {\n          if (e.name !== 'TypeError') {\n            throw e;\n          }\n          var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder : typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder : typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : WebKitBlobBuilder;\n          var builder = new Builder();\n          for (var i = 0; i < parts.length; i += 1) {\n            builder.append(parts[i]);\n          }\n          return builder.getBlob(properties.type);\n        }\n      }\n\n      // This is CommonJS because lie is an external dependency, so Rollup\n      // can just ignore it.\n      if (typeof Promise === 'undefined') {\n        // In the \"nopromises\" build this will just throw if you don't have\n        // a global promise object, but it would throw anyway later.\n        _dereq_(3);\n      }\n      var Promise$1 = Promise;\n      function executeCallback(promise, callback) {\n        if (callback) {\n          promise.then(function (result) {\n            callback(null, result);\n          }, function (error) {\n            callback(error);\n          });\n        }\n      }\n      function executeTwoCallbacks(promise, callback, errorCallback) {\n        if (typeof callback === 'function') {\n          promise.then(callback);\n        }\n        if (typeof errorCallback === 'function') {\n          promise[\"catch\"](errorCallback);\n        }\n      }\n      function normalizeKey(key) {\n        // Cast the key to a string, as that's all we can set as a key.\n        if (typeof key !== 'string') {\n          console.warn(key + ' used as a key, but it is not a string.');\n          key = String(key);\n        }\n        return key;\n      }\n      function getCallback() {\n        if (arguments.length && typeof arguments[arguments.length - 1] === 'function') {\n          return arguments[arguments.length - 1];\n        }\n      }\n\n      // Some code originally from async_storage.js in\n      // [Gaia](https://github.com/mozilla-b2g/gaia).\n\n      var DETECT_BLOB_SUPPORT_STORE = 'local-forage-detect-blob-support';\n      var supportsBlobs = void 0;\n      var dbContexts = {};\n      var toString = Object.prototype.toString;\n\n      // Transaction Modes\n      var READ_ONLY = 'readonly';\n      var READ_WRITE = 'readwrite';\n\n      // Transform a binary string to an array buffer, because otherwise\n      // weird stuff happens when you try to work with the binary string directly.\n      // It is known.\n      // From http://stackoverflow.com/questions/14967647/ (continues on next line)\n      // encode-decode-image-with-base64-breaks-image (2013-04-21)\n      function _binStringToArrayBuffer(bin) {\n        var length = bin.length;\n        var buf = new ArrayBuffer(length);\n        var arr = new Uint8Array(buf);\n        for (var i = 0; i < length; i++) {\n          arr[i] = bin.charCodeAt(i);\n        }\n        return buf;\n      }\n\n      //\n      // Blobs are not supported in all versions of IndexedDB, notably\n      // Chrome <37 and Android <5. In those versions, storing a blob will throw.\n      //\n      // Various other blob bugs exist in Chrome v37-42 (inclusive).\n      // Detecting them is expensive and confusing to users, and Chrome 37-42\n      // is at very low usage worldwide, so we do a hacky userAgent check instead.\n      //\n      // content-type bug: https://code.google.com/p/chromium/issues/detail?id=408120\n      // 404 bug: https://code.google.com/p/chromium/issues/detail?id=447916\n      // FileReader bug: https://code.google.com/p/chromium/issues/detail?id=447836\n      //\n      // Code borrowed from PouchDB. See:\n      // https://github.com/pouchdb/pouchdb/blob/master/packages/node_modules/pouchdb-adapter-idb/src/blobSupport.js\n      //\n      function _checkBlobSupportWithoutCaching(idb) {\n        return new Promise$1(function (resolve) {\n          var txn = idb.transaction(DETECT_BLOB_SUPPORT_STORE, READ_WRITE);\n          var blob = createBlob(['']);\n          txn.objectStore(DETECT_BLOB_SUPPORT_STORE).put(blob, 'key');\n          txn.onabort = function (e) {\n            // If the transaction aborts now its due to not being able to\n            // write to the database, likely due to the disk being full\n            e.preventDefault();\n            e.stopPropagation();\n            resolve(false);\n          };\n          txn.oncomplete = function () {\n            var matchedChrome = navigator.userAgent.match(/Chrome\\/(\\d+)/);\n            var matchedEdge = navigator.userAgent.match(/Edge\\//);\n            // MS Edge pretends to be Chrome 42:\n            // https://msdn.microsoft.com/en-us/library/hh869301%28v=vs.85%29.aspx\n            resolve(matchedEdge || !matchedChrome || parseInt(matchedChrome[1], 10) >= 43);\n          };\n        })[\"catch\"](function () {\n          return false; // error, so assume unsupported\n        });\n      }\n      function _checkBlobSupport(idb) {\n        if (typeof supportsBlobs === 'boolean') {\n          return Promise$1.resolve(supportsBlobs);\n        }\n        return _checkBlobSupportWithoutCaching(idb).then(function (value) {\n          supportsBlobs = value;\n          return supportsBlobs;\n        });\n      }\n      function _deferReadiness(dbInfo) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Create a deferred object representing the current database operation.\n        var deferredOperation = {};\n        deferredOperation.promise = new Promise$1(function (resolve, reject) {\n          deferredOperation.resolve = resolve;\n          deferredOperation.reject = reject;\n        });\n\n        // Enqueue the deferred operation.\n        dbContext.deferredOperations.push(deferredOperation);\n\n        // Chain its promise to the database readiness.\n        if (!dbContext.dbReady) {\n          dbContext.dbReady = deferredOperation.promise;\n        } else {\n          dbContext.dbReady = dbContext.dbReady.then(function () {\n            return deferredOperation.promise;\n          });\n        }\n      }\n      function _advanceReadiness(dbInfo) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Dequeue a deferred operation.\n        var deferredOperation = dbContext.deferredOperations.pop();\n\n        // Resolve its promise (which is part of the database readiness\n        // chain of promises).\n        if (deferredOperation) {\n          deferredOperation.resolve();\n          return deferredOperation.promise;\n        }\n      }\n      function _rejectReadiness(dbInfo, err) {\n        var dbContext = dbContexts[dbInfo.name];\n\n        // Dequeue a deferred operation.\n        var deferredOperation = dbContext.deferredOperations.pop();\n\n        // Reject its promise (which is part of the database readiness\n        // chain of promises).\n        if (deferredOperation) {\n          deferredOperation.reject(err);\n          return deferredOperation.promise;\n        }\n      }\n      function _getConnection(dbInfo, upgradeNeeded) {\n        return new Promise$1(function (resolve, reject) {\n          dbContexts[dbInfo.name] = dbContexts[dbInfo.name] || createDbContext();\n          if (dbInfo.db) {\n            if (upgradeNeeded) {\n              _deferReadiness(dbInfo);\n              dbInfo.db.close();\n            } else {\n              return resolve(dbInfo.db);\n            }\n          }\n          var dbArgs = [dbInfo.name];\n          if (upgradeNeeded) {\n            dbArgs.push(dbInfo.version);\n          }\n          var openreq = idb.open.apply(idb, dbArgs);\n          if (upgradeNeeded) {\n            openreq.onupgradeneeded = function (e) {\n              var db = openreq.result;\n              try {\n                db.createObjectStore(dbInfo.storeName);\n                if (e.oldVersion <= 1) {\n                  // Added when support for blob shims was added\n                  db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);\n                }\n              } catch (ex) {\n                if (ex.name === 'ConstraintError') {\n                  console.warn('The database \"' + dbInfo.name + '\"' + ' has been upgraded from version ' + e.oldVersion + ' to version ' + e.newVersion + ', but the storage \"' + dbInfo.storeName + '\" already exists.');\n                } else {\n                  throw ex;\n                }\n              }\n            };\n          }\n          openreq.onerror = function (e) {\n            e.preventDefault();\n            reject(openreq.error);\n          };\n          openreq.onsuccess = function () {\n            var db = openreq.result;\n            db.onversionchange = function (e) {\n              // Triggered when the database is modified (e.g. adding an objectStore) or\n              // deleted (even when initiated by other sessions in different tabs).\n              // Closing the connection here prevents those operations from being blocked.\n              // If the database is accessed again later by this instance, the connection\n              // will be reopened or the database recreated as needed.\n              e.target.close();\n            };\n            resolve(db);\n            _advanceReadiness(dbInfo);\n          };\n        });\n      }\n      function _getOriginalConnection(dbInfo) {\n        return _getConnection(dbInfo, false);\n      }\n      function _getUpgradedConnection(dbInfo) {\n        return _getConnection(dbInfo, true);\n      }\n      function _isUpgradeNeeded(dbInfo, defaultVersion) {\n        if (!dbInfo.db) {\n          return true;\n        }\n        var isNewStore = !dbInfo.db.objectStoreNames.contains(dbInfo.storeName);\n        var isDowngrade = dbInfo.version < dbInfo.db.version;\n        var isUpgrade = dbInfo.version > dbInfo.db.version;\n        if (isDowngrade) {\n          // If the version is not the default one\n          // then warn for impossible downgrade.\n          if (dbInfo.version !== defaultVersion) {\n            console.warn('The database \"' + dbInfo.name + '\"' + \" can't be downgraded from version \" + dbInfo.db.version + ' to version ' + dbInfo.version + '.');\n          }\n          // Align the versions to prevent errors.\n          dbInfo.version = dbInfo.db.version;\n        }\n        if (isUpgrade || isNewStore) {\n          // If the store is new then increment the version (if needed).\n          // This will trigger an \"upgradeneeded\" event which is required\n          // for creating a store.\n          if (isNewStore) {\n            var incVersion = dbInfo.db.version + 1;\n            if (incVersion > dbInfo.version) {\n              dbInfo.version = incVersion;\n            }\n          }\n          return true;\n        }\n        return false;\n      }\n\n      // encode a blob for indexeddb engines that don't support blobs\n      function _encodeBlob(blob) {\n        return new Promise$1(function (resolve, reject) {\n          var reader = new FileReader();\n          reader.onerror = reject;\n          reader.onloadend = function (e) {\n            var base64 = btoa(e.target.result || '');\n            resolve({\n              __local_forage_encoded_blob: true,\n              data: base64,\n              type: blob.type\n            });\n          };\n          reader.readAsBinaryString(blob);\n        });\n      }\n\n      // decode an encoded blob\n      function _decodeBlob(encodedBlob) {\n        var arrayBuff = _binStringToArrayBuffer(atob(encodedBlob.data));\n        return createBlob([arrayBuff], {\n          type: encodedBlob.type\n        });\n      }\n\n      // is this one of our fancy encoded blobs?\n      function _isEncodedBlob(value) {\n        return value && value.__local_forage_encoded_blob;\n      }\n\n      // Specialize the default `ready()` function by making it dependent\n      // on the current database operations. Thus, the driver will be actually\n      // ready when it's been initialized (default) *and* there are no pending\n      // operations on the database (initiated by some other instances).\n      function _fullyReady(callback) {\n        var self = this;\n        var promise = self._initReady().then(function () {\n          var dbContext = dbContexts[self._dbInfo.name];\n          if (dbContext && dbContext.dbReady) {\n            return dbContext.dbReady;\n          }\n        });\n        executeTwoCallbacks(promise, callback, callback);\n        return promise;\n      }\n\n      // Try to establish a new db connection to replace the\n      // current one which is broken (i.e. experiencing\n      // InvalidStateError while creating a transaction).\n      function _tryReconnect(dbInfo) {\n        _deferReadiness(dbInfo);\n        var dbContext = dbContexts[dbInfo.name];\n        var forages = dbContext.forages;\n        for (var i = 0; i < forages.length; i++) {\n          var forage = forages[i];\n          if (forage._dbInfo.db) {\n            forage._dbInfo.db.close();\n            forage._dbInfo.db = null;\n          }\n        }\n        dbInfo.db = null;\n        return _getOriginalConnection(dbInfo).then(function (db) {\n          dbInfo.db = db;\n          if (_isUpgradeNeeded(dbInfo)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n          }\n          return db;\n        }).then(function (db) {\n          // store the latest db reference\n          // in case the db was upgraded\n          dbInfo.db = dbContext.db = db;\n          for (var i = 0; i < forages.length; i++) {\n            forages[i]._dbInfo.db = db;\n          }\n        })[\"catch\"](function (err) {\n          _rejectReadiness(dbInfo, err);\n          throw err;\n        });\n      }\n\n      // FF doesn't like Promises (micro-tasks) and IDDB store operations,\n      // so we have to do it with callbacks\n      function createTransaction(dbInfo, mode, callback, retries) {\n        if (retries === undefined) {\n          retries = 1;\n        }\n        try {\n          var tx = dbInfo.db.transaction(dbInfo.storeName, mode);\n          callback(null, tx);\n        } catch (err) {\n          if (retries > 0 && (!dbInfo.db || err.name === 'InvalidStateError' || err.name === 'NotFoundError')) {\n            return Promise$1.resolve().then(function () {\n              if (!dbInfo.db || err.name === 'NotFoundError' && !dbInfo.db.objectStoreNames.contains(dbInfo.storeName) && dbInfo.version <= dbInfo.db.version) {\n                // increase the db version, to create the new ObjectStore\n                if (dbInfo.db) {\n                  dbInfo.version = dbInfo.db.version + 1;\n                }\n                // Reopen the database for upgrading.\n                return _getUpgradedConnection(dbInfo);\n              }\n            }).then(function () {\n              return _tryReconnect(dbInfo).then(function () {\n                createTransaction(dbInfo, mode, callback, retries - 1);\n              });\n            })[\"catch\"](callback);\n          }\n          callback(err);\n        }\n      }\n      function createDbContext() {\n        return {\n          // Running localForages sharing a database.\n          forages: [],\n          // Shared database.\n          db: null,\n          // Database readiness (promise).\n          dbReady: null,\n          // Deferred operations on the database.\n          deferredOperations: []\n        };\n      }\n\n      // Open the IndexedDB database (automatically creates one if one didn't\n      // previously exist), using any options set in the config.\n      function _initStorage(options) {\n        var self = this;\n        var dbInfo = {\n          db: null\n        };\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = options[i];\n          }\n        }\n\n        // Get the current context of the database;\n        var dbContext = dbContexts[dbInfo.name];\n\n        // ...or create a new context.\n        if (!dbContext) {\n          dbContext = createDbContext();\n          // Register the new context in the global container.\n          dbContexts[dbInfo.name] = dbContext;\n        }\n\n        // Register itself as a running localForage in the current context.\n        dbContext.forages.push(self);\n\n        // Replace the default `ready()` function with the specialized one.\n        if (!self._initReady) {\n          self._initReady = self.ready;\n          self.ready = _fullyReady;\n        }\n\n        // Create an array of initialization states of the related localForages.\n        var initPromises = [];\n        function ignoreErrors() {\n          // Don't handle errors here,\n          // just makes sure related localForages aren't pending.\n          return Promise$1.resolve();\n        }\n        for (var j = 0; j < dbContext.forages.length; j++) {\n          var forage = dbContext.forages[j];\n          if (forage !== self) {\n            // Don't wait for itself...\n            initPromises.push(forage._initReady()[\"catch\"](ignoreErrors));\n          }\n        }\n\n        // Take a snapshot of the related localForages.\n        var forages = dbContext.forages.slice(0);\n\n        // Initialize the connection process only when\n        // all the related localForages aren't pending.\n        return Promise$1.all(initPromises).then(function () {\n          dbInfo.db = dbContext.db;\n          // Get the connection or open a new one without upgrade.\n          return _getOriginalConnection(dbInfo);\n        }).then(function (db) {\n          dbInfo.db = db;\n          if (_isUpgradeNeeded(dbInfo, self._defaultConfig.version)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n          }\n          return db;\n        }).then(function (db) {\n          dbInfo.db = dbContext.db = db;\n          self._dbInfo = dbInfo;\n          // Share the final connection amongst related localForages.\n          for (var k = 0; k < forages.length; k++) {\n            var forage = forages[k];\n            if (forage !== self) {\n              // Self is already up-to-date.\n              forage._dbInfo.db = dbInfo.db;\n              forage._dbInfo.version = dbInfo.version;\n            }\n          }\n        });\n      }\n      function getItem(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.get(key);\n                req.onsuccess = function () {\n                  var value = req.result;\n                  if (value === undefined) {\n                    value = null;\n                  }\n                  if (_isEncodedBlob(value)) {\n                    value = _decodeBlob(value);\n                  }\n                  resolve(value);\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Iterate over all items stored in database.\n      function iterate(iterator, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.openCursor();\n                var iterationNumber = 1;\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (cursor) {\n                    var value = cursor.value;\n                    if (_isEncodedBlob(value)) {\n                      value = _decodeBlob(value);\n                    }\n                    var result = iterator(value, cursor.key, iterationNumber++);\n\n                    // when the iterator callback returns any\n                    // (non-`undefined`) value, then we stop\n                    // the iteration immediately\n                    if (result !== void 0) {\n                      resolve(result);\n                    } else {\n                      cursor[\"continue\"]();\n                    }\n                  } else {\n                    resolve();\n                  }\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function setItem(key, value, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          var dbInfo;\n          self.ready().then(function () {\n            dbInfo = self._dbInfo;\n            if (toString.call(value) === '[object Blob]') {\n              return _checkBlobSupport(dbInfo.db).then(function (blobSupport) {\n                if (blobSupport) {\n                  return value;\n                }\n                return _encodeBlob(value);\n              });\n            }\n            return value;\n          }).then(function (value) {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n\n                // The reason we don't _save_ null is because IE 10 does\n                // not support saving the `null` type in IndexedDB. How\n                // ironic, given the bug below!\n                // See: https://github.com/mozilla/localForage/issues/161\n                if (value === null) {\n                  value = undefined;\n                }\n                var req = store.put(value, key);\n                transaction.oncomplete = function () {\n                  // Cast to undefined so the value passed to\n                  // callback/promise is the same as what one would get out\n                  // of `getItem()` later. This leads to some weirdness\n                  // (setItem('foo', undefined) will return `null`), but\n                  // it's not my fault localStorage is our baseline and that\n                  // it's weird.\n                  if (value === undefined) {\n                    value = null;\n                  }\n                  resolve(value);\n                };\n                transaction.onabort = transaction.onerror = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function removeItem(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                // We use a Grunt task to make this safe for IE and some\n                // versions of Android (including those used by Cordova).\n                // Normally IE won't like `.delete()` and will insist on\n                // using `['delete']()`, but we have a build step that\n                // fixes this for us now.\n                var req = store[\"delete\"](key);\n                transaction.oncomplete = function () {\n                  resolve();\n                };\n                transaction.onerror = function () {\n                  reject(req.error);\n                };\n\n                // The request will be also be aborted if we've exceeded our storage\n                // space.\n                transaction.onabort = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function clear(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.clear();\n                transaction.oncomplete = function () {\n                  resolve();\n                };\n                transaction.onabort = transaction.onerror = function () {\n                  var err = req.error ? req.error : req.transaction.error;\n                  reject(err);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function length(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.count();\n                req.onsuccess = function () {\n                  resolve(req.result);\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function key(n, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          if (n < 0) {\n            resolve(null);\n            return;\n          }\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var advanced = false;\n                var req = store.openKeyCursor();\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (!cursor) {\n                    // this means there weren't enough keys\n                    resolve(null);\n                    return;\n                  }\n                  if (n === 0) {\n                    // We have the first key, return it if that's what they\n                    // wanted.\n                    resolve(cursor.key);\n                  } else {\n                    if (!advanced) {\n                      // Otherwise, ask the cursor to skip ahead n\n                      // records.\n                      advanced = true;\n                      cursor.advance(n);\n                    } else {\n                      // When we get here, we've got the nth key.\n                      resolve(cursor.key);\n                    }\n                  }\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n              if (err) {\n                return reject(err);\n              }\n              try {\n                var store = transaction.objectStore(self._dbInfo.storeName);\n                var req = store.openKeyCursor();\n                var keys = [];\n                req.onsuccess = function () {\n                  var cursor = req.result;\n                  if (!cursor) {\n                    resolve(keys);\n                    return;\n                  }\n                  keys.push(cursor.key);\n                  cursor[\"continue\"]();\n                };\n                req.onerror = function () {\n                  reject(req.error);\n                };\n              } catch (e) {\n                reject(e);\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function dropInstance(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        var currentConfig = this.config();\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          var isCurrentDb = options.name === currentConfig.name && self._dbInfo.db;\n          var dbPromise = isCurrentDb ? Promise$1.resolve(self._dbInfo.db) : _getOriginalConnection(options).then(function (db) {\n            var dbContext = dbContexts[options.name];\n            var forages = dbContext.forages;\n            dbContext.db = db;\n            for (var i = 0; i < forages.length; i++) {\n              forages[i]._dbInfo.db = db;\n            }\n            return db;\n          });\n          if (!options.storeName) {\n            promise = dbPromise.then(function (db) {\n              _deferReadiness(options);\n              var dbContext = dbContexts[options.name];\n              var forages = dbContext.forages;\n              db.close();\n              for (var i = 0; i < forages.length; i++) {\n                var forage = forages[i];\n                forage._dbInfo.db = null;\n              }\n              var dropDBPromise = new Promise$1(function (resolve, reject) {\n                var req = idb.deleteDatabase(options.name);\n                req.onerror = function () {\n                  var db = req.result;\n                  if (db) {\n                    db.close();\n                  }\n                  reject(req.error);\n                };\n                req.onblocked = function () {\n                  // Closing all open connections in onversionchange handler should prevent this situation, but if\n                  // we do get here, it just means the request remains pending - eventually it will succeed or error\n                  console.warn('dropInstance blocked for database \"' + options.name + '\" until all open connections are closed');\n                };\n                req.onsuccess = function () {\n                  var db = req.result;\n                  if (db) {\n                    db.close();\n                  }\n                  resolve(db);\n                };\n              });\n              return dropDBPromise.then(function (db) {\n                dbContext.db = db;\n                for (var i = 0; i < forages.length; i++) {\n                  var _forage = forages[i];\n                  _advanceReadiness(_forage._dbInfo);\n                }\n              })[\"catch\"](function (err) {\n                (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                throw err;\n              });\n            });\n          } else {\n            promise = dbPromise.then(function (db) {\n              if (!db.objectStoreNames.contains(options.storeName)) {\n                return;\n              }\n              var newVersion = db.version + 1;\n              _deferReadiness(options);\n              var dbContext = dbContexts[options.name];\n              var forages = dbContext.forages;\n              db.close();\n              for (var i = 0; i < forages.length; i++) {\n                var forage = forages[i];\n                forage._dbInfo.db = null;\n                forage._dbInfo.version = newVersion;\n              }\n              var dropObjectPromise = new Promise$1(function (resolve, reject) {\n                var req = idb.open(options.name, newVersion);\n                req.onerror = function (err) {\n                  var db = req.result;\n                  db.close();\n                  reject(err);\n                };\n                req.onupgradeneeded = function () {\n                  var db = req.result;\n                  db.deleteObjectStore(options.storeName);\n                };\n                req.onsuccess = function () {\n                  var db = req.result;\n                  db.close();\n                  resolve(db);\n                };\n              });\n              return dropObjectPromise.then(function (db) {\n                dbContext.db = db;\n                for (var j = 0; j < forages.length; j++) {\n                  var _forage2 = forages[j];\n                  _forage2._dbInfo.db = db;\n                  _advanceReadiness(_forage2._dbInfo);\n                }\n              })[\"catch\"](function (err) {\n                (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                throw err;\n              });\n            });\n          }\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var asyncStorage = {\n        _driver: 'asyncStorage',\n        _initStorage: _initStorage,\n        _support: isIndexedDBValid(),\n        iterate: iterate,\n        getItem: getItem,\n        setItem: setItem,\n        removeItem: removeItem,\n        clear: clear,\n        length: length,\n        key: key,\n        keys: keys,\n        dropInstance: dropInstance\n      };\n      function isWebSQLValid() {\n        return typeof openDatabase === 'function';\n      }\n\n      // Sadly, the best way to save binary data in WebSQL/localStorage is serializing\n      // it to Base64, so this is how we store it to prevent very strange errors with less\n      // verbose ways of binary <-> string data storage.\n      var BASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n      var BLOB_TYPE_PREFIX = '~~local_forage_type~';\n      var BLOB_TYPE_PREFIX_REGEX = /^~~local_forage_type~([^~]+)~/;\n      var SERIALIZED_MARKER = '__lfsc__:';\n      var SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER.length;\n\n      // OMG the serializations!\n      var TYPE_ARRAYBUFFER = 'arbf';\n      var TYPE_BLOB = 'blob';\n      var TYPE_INT8ARRAY = 'si08';\n      var TYPE_UINT8ARRAY = 'ui08';\n      var TYPE_UINT8CLAMPEDARRAY = 'uic8';\n      var TYPE_INT16ARRAY = 'si16';\n      var TYPE_INT32ARRAY = 'si32';\n      var TYPE_UINT16ARRAY = 'ur16';\n      var TYPE_UINT32ARRAY = 'ui32';\n      var TYPE_FLOAT32ARRAY = 'fl32';\n      var TYPE_FLOAT64ARRAY = 'fl64';\n      var TYPE_SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER_LENGTH + TYPE_ARRAYBUFFER.length;\n      var toString$1 = Object.prototype.toString;\n      function stringToBuffer(serializedString) {\n        // Fill the string into a ArrayBuffer.\n        var bufferLength = serializedString.length * 0.75;\n        var len = serializedString.length;\n        var i;\n        var p = 0;\n        var encoded1, encoded2, encoded3, encoded4;\n        if (serializedString[serializedString.length - 1] === '=') {\n          bufferLength--;\n          if (serializedString[serializedString.length - 2] === '=') {\n            bufferLength--;\n          }\n        }\n        var buffer = new ArrayBuffer(bufferLength);\n        var bytes = new Uint8Array(buffer);\n        for (i = 0; i < len; i += 4) {\n          encoded1 = BASE_CHARS.indexOf(serializedString[i]);\n          encoded2 = BASE_CHARS.indexOf(serializedString[i + 1]);\n          encoded3 = BASE_CHARS.indexOf(serializedString[i + 2]);\n          encoded4 = BASE_CHARS.indexOf(serializedString[i + 3]);\n\n          /*jslint bitwise: true */\n          bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n          bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n          bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n        }\n        return buffer;\n      }\n\n      // Converts a buffer to a string to store, serialized, in the backend\n      // storage library.\n      function bufferToString(buffer) {\n        // base64-arraybuffer\n        var bytes = new Uint8Array(buffer);\n        var base64String = '';\n        var i;\n        for (i = 0; i < bytes.length; i += 3) {\n          /*jslint bitwise: true */\n          base64String += BASE_CHARS[bytes[i] >> 2];\n          base64String += BASE_CHARS[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n          base64String += BASE_CHARS[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n          base64String += BASE_CHARS[bytes[i + 2] & 63];\n        }\n        if (bytes.length % 3 === 2) {\n          base64String = base64String.substring(0, base64String.length - 1) + '=';\n        } else if (bytes.length % 3 === 1) {\n          base64String = base64String.substring(0, base64String.length - 2) + '==';\n        }\n        return base64String;\n      }\n\n      // Serialize a value, afterwards executing a callback (which usually\n      // instructs the `setItem()` callback/promise to be executed). This is how\n      // we store binary data with localStorage.\n      function serialize(value, callback) {\n        var valueType = '';\n        if (value) {\n          valueType = toString$1.call(value);\n        }\n\n        // Cannot use `value instanceof ArrayBuffer` or such here, as these\n        // checks fail when running the tests using casper.js...\n        //\n        // TODO: See why those tests fail and use a better solution.\n        if (value && (valueType === '[object ArrayBuffer]' || value.buffer && toString$1.call(value.buffer) === '[object ArrayBuffer]')) {\n          // Convert binary arrays to a string and prefix the string with\n          // a special marker.\n          var buffer;\n          var marker = SERIALIZED_MARKER;\n          if (value instanceof ArrayBuffer) {\n            buffer = value;\n            marker += TYPE_ARRAYBUFFER;\n          } else {\n            buffer = value.buffer;\n            if (valueType === '[object Int8Array]') {\n              marker += TYPE_INT8ARRAY;\n            } else if (valueType === '[object Uint8Array]') {\n              marker += TYPE_UINT8ARRAY;\n            } else if (valueType === '[object Uint8ClampedArray]') {\n              marker += TYPE_UINT8CLAMPEDARRAY;\n            } else if (valueType === '[object Int16Array]') {\n              marker += TYPE_INT16ARRAY;\n            } else if (valueType === '[object Uint16Array]') {\n              marker += TYPE_UINT16ARRAY;\n            } else if (valueType === '[object Int32Array]') {\n              marker += TYPE_INT32ARRAY;\n            } else if (valueType === '[object Uint32Array]') {\n              marker += TYPE_UINT32ARRAY;\n            } else if (valueType === '[object Float32Array]') {\n              marker += TYPE_FLOAT32ARRAY;\n            } else if (valueType === '[object Float64Array]') {\n              marker += TYPE_FLOAT64ARRAY;\n            } else {\n              callback(new Error('Failed to get type for BinaryArray'));\n            }\n          }\n          callback(marker + bufferToString(buffer));\n        } else if (valueType === '[object Blob]') {\n          // Conver the blob to a binaryArray and then to a string.\n          var fileReader = new FileReader();\n          fileReader.onload = function () {\n            // Backwards-compatible prefix for the blob type.\n            var str = BLOB_TYPE_PREFIX + value.type + '~' + bufferToString(this.result);\n            callback(SERIALIZED_MARKER + TYPE_BLOB + str);\n          };\n          fileReader.readAsArrayBuffer(value);\n        } else {\n          try {\n            callback(JSON.stringify(value));\n          } catch (e) {\n            console.error(\"Couldn't convert value into a JSON string: \", value);\n            callback(null, e);\n          }\n        }\n      }\n\n      // Deserialize data we've inserted into a value column/field. We place\n      // special markers into our strings to mark them as encoded; this isn't\n      // as nice as a meta field, but it's the only sane thing we can do whilst\n      // keeping localStorage support intact.\n      //\n      // Oftentimes this will just deserialize JSON content, but if we have a\n      // special marker (SERIALIZED_MARKER, defined above), we will extract\n      // some kind of arraybuffer/binary data/typed array out of the string.\n      function deserialize(value) {\n        // If we haven't marked this string as being specially serialized (i.e.\n        // something other than serialized JSON), we can just return it and be\n        // done with it.\n        if (value.substring(0, SERIALIZED_MARKER_LENGTH) !== SERIALIZED_MARKER) {\n          return JSON.parse(value);\n        }\n\n        // The following code deals with deserializing some kind of Blob or\n        // TypedArray. First we separate out the type of data we're dealing\n        // with from the data itself.\n        var serializedString = value.substring(TYPE_SERIALIZED_MARKER_LENGTH);\n        var type = value.substring(SERIALIZED_MARKER_LENGTH, TYPE_SERIALIZED_MARKER_LENGTH);\n        var blobType;\n        // Backwards-compatible blob type serialization strategy.\n        // DBs created with older versions of localForage will simply not have the blob type.\n        if (type === TYPE_BLOB && BLOB_TYPE_PREFIX_REGEX.test(serializedString)) {\n          var matcher = serializedString.match(BLOB_TYPE_PREFIX_REGEX);\n          blobType = matcher[1];\n          serializedString = serializedString.substring(matcher[0].length);\n        }\n        var buffer = stringToBuffer(serializedString);\n\n        // Return the right type based on the code/type set during\n        // serialization.\n        switch (type) {\n          case TYPE_ARRAYBUFFER:\n            return buffer;\n          case TYPE_BLOB:\n            return createBlob([buffer], {\n              type: blobType\n            });\n          case TYPE_INT8ARRAY:\n            return new Int8Array(buffer);\n          case TYPE_UINT8ARRAY:\n            return new Uint8Array(buffer);\n          case TYPE_UINT8CLAMPEDARRAY:\n            return new Uint8ClampedArray(buffer);\n          case TYPE_INT16ARRAY:\n            return new Int16Array(buffer);\n          case TYPE_UINT16ARRAY:\n            return new Uint16Array(buffer);\n          case TYPE_INT32ARRAY:\n            return new Int32Array(buffer);\n          case TYPE_UINT32ARRAY:\n            return new Uint32Array(buffer);\n          case TYPE_FLOAT32ARRAY:\n            return new Float32Array(buffer);\n          case TYPE_FLOAT64ARRAY:\n            return new Float64Array(buffer);\n          default:\n            throw new Error('Unkown type: ' + type);\n        }\n      }\n      var localforageSerializer = {\n        serialize: serialize,\n        deserialize: deserialize,\n        stringToBuffer: stringToBuffer,\n        bufferToString: bufferToString\n      };\n\n      /*\n       * Includes code from:\n       *\n       * base64-arraybuffer\n       * https://github.com/niklasvh/base64-arraybuffer\n       *\n       * Copyright (c) 2012 Niklas von Hertzen\n       * Licensed under the MIT license.\n       */\n\n      function createDbTable(t, dbInfo, callback, errorCallback) {\n        t.executeSql('CREATE TABLE IF NOT EXISTS ' + dbInfo.storeName + ' ' + '(id INTEGER PRIMARY KEY, key unique, value)', [], callback, errorCallback);\n      }\n\n      // Open the WebSQL database (automatically creates one if one didn't\n      // previously exist), using any options set in the config.\n      function _initStorage$1(options) {\n        var self = this;\n        var dbInfo = {\n          db: null\n        };\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = typeof options[i] !== 'string' ? options[i].toString() : options[i];\n          }\n        }\n        var dbInfoPromise = new Promise$1(function (resolve, reject) {\n          // Open the database; the openDatabase API will automatically\n          // create it for us if it doesn't exist.\n          try {\n            dbInfo.db = openDatabase(dbInfo.name, String(dbInfo.version), dbInfo.description, dbInfo.size);\n          } catch (e) {\n            return reject(e);\n          }\n\n          // Create our key/value table if it doesn't exist.\n          dbInfo.db.transaction(function (t) {\n            createDbTable(t, dbInfo, function () {\n              self._dbInfo = dbInfo;\n              resolve();\n            }, function (t, error) {\n              reject(error);\n            });\n          }, reject);\n        });\n        dbInfo.serializer = localforageSerializer;\n        return dbInfoPromise;\n      }\n      function tryExecuteSql(t, dbInfo, sqlStatement, args, callback, errorCallback) {\n        t.executeSql(sqlStatement, args, callback, function (t, error) {\n          if (error.code === error.SYNTAX_ERR) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name = ?\", [dbInfo.storeName], function (t, results) {\n              if (!results.rows.length) {\n                // if the table is missing (was deleted)\n                // re-create it table and retry\n                createDbTable(t, dbInfo, function () {\n                  t.executeSql(sqlStatement, args, callback, errorCallback);\n                }, errorCallback);\n              } else {\n                errorCallback(t, error);\n              }\n            }, errorCallback);\n          } else {\n            errorCallback(t, error);\n          }\n        }, errorCallback);\n      }\n      function getItem$1(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName + ' WHERE key = ? LIMIT 1', [key], function (t, results) {\n                var result = results.rows.length ? results.rows.item(0).value : null;\n\n                // Check to see if this is serialized content we need to\n                // unpack.\n                if (result) {\n                  result = dbInfo.serializer.deserialize(result);\n                }\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function iterate$1(iterator, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName, [], function (t, results) {\n                var rows = results.rows;\n                var length = rows.length;\n                for (var i = 0; i < length; i++) {\n                  var item = rows.item(i);\n                  var result = item.value;\n\n                  // Check to see if this is serialized content\n                  // we need to unpack.\n                  if (result) {\n                    result = dbInfo.serializer.deserialize(result);\n                  }\n                  result = iterator(result, item.key, i + 1);\n\n                  // void(0) prevents problems with redefinition\n                  // of `undefined`.\n                  if (result !== void 0) {\n                    resolve(result);\n                    return;\n                  }\n                }\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function _setItem(key, value, callback, retriesLeft) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            // The localStorage API doesn't return undefined values in an\n            // \"expected\" way, so undefined is always cast to null in all\n            // drivers. See: https://github.com/mozilla/localForage/pull/42\n            if (value === undefined) {\n              value = null;\n            }\n\n            // Save the original value to pass to the callback.\n            var originalValue = value;\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n              if (error) {\n                reject(error);\n              } else {\n                dbInfo.db.transaction(function (t) {\n                  tryExecuteSql(t, dbInfo, 'INSERT OR REPLACE INTO ' + dbInfo.storeName + ' ' + '(key, value) VALUES (?, ?)', [key, value], function () {\n                    resolve(originalValue);\n                  }, function (t, error) {\n                    reject(error);\n                  });\n                }, function (sqlError) {\n                  // The transaction failed; check\n                  // to see if it's a quota error.\n                  if (sqlError.code === sqlError.QUOTA_ERR) {\n                    // We reject the callback outright for now, but\n                    // it's worth trying to re-run the transaction.\n                    // Even if the user accepts the prompt to use\n                    // more storage on Safari, this error will\n                    // be called.\n                    //\n                    // Try to re-run the transaction.\n                    if (retriesLeft > 0) {\n                      resolve(_setItem.apply(self, [key, originalValue, callback, retriesLeft - 1]));\n                      return;\n                    }\n                    reject(sqlError);\n                  }\n                });\n              }\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function setItem$1(key, value, callback) {\n        return _setItem.apply(this, [key, value, callback, 1]);\n      }\n      function removeItem$1(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName + ' WHERE key = ?', [key], function () {\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Deletes every item in the table.\n      // TODO: Find out if this resets the AUTO_INCREMENT number.\n      function clear$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName, [], function () {\n                resolve();\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Does a simple `COUNT(key)` to get the number of items stored in\n      // localForage.\n      function length$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              // Ahhh, SQL makes this one soooooo easy.\n              tryExecuteSql(t, dbInfo, 'SELECT COUNT(key) as c FROM ' + dbInfo.storeName, [], function (t, results) {\n                var result = results.rows.item(0).c;\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Return the key located at key index X; essentially gets the key from a\n      // `WHERE id = ?`. This is the most efficient way I can think to implement\n      // this rarely-used (in my experience) part of the API, but it can seem\n      // inconsistent, because we do `INSERT OR REPLACE INTO` on `setItem()`, so\n      // the ID of each key will change every time it's updated. Perhaps a stored\n      // procedure for the `setItem()` SQL would solve this problem?\n      // TODO: Don't change ID on `setItem()`.\n      function key$1(n, callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName + ' WHERE id = ? LIMIT 1', [n + 1], function (t, results) {\n                var result = results.rows.length ? results.rows.item(0).key : null;\n                resolve(result);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys$1(callback) {\n        var self = this;\n        var promise = new Promise$1(function (resolve, reject) {\n          self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n              tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName, [], function (t, results) {\n                var keys = [];\n                for (var i = 0; i < results.rows.length; i++) {\n                  keys.push(results.rows.item(i).key);\n                }\n                resolve(keys);\n              }, function (t, error) {\n                reject(error);\n              });\n            });\n          })[\"catch\"](reject);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // https://www.w3.org/TR/webdatabase/#databases\n      // > There is no way to enumerate or delete the databases available for an origin from this API.\n      function getAllStoreNames(db) {\n        return new Promise$1(function (resolve, reject) {\n          db.transaction(function (t) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'\", [], function (t, results) {\n              var storeNames = [];\n              for (var i = 0; i < results.rows.length; i++) {\n                storeNames.push(results.rows.item(i).name);\n              }\n              resolve({\n                db: db,\n                storeNames: storeNames\n              });\n            }, function (t, error) {\n              reject(error);\n            });\n          }, function (sqlError) {\n            reject(sqlError);\n          });\n        });\n      }\n      function dropInstance$1(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        var currentConfig = this.config();\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          promise = new Promise$1(function (resolve) {\n            var db;\n            if (options.name === currentConfig.name) {\n              // use the db reference of the current instance\n              db = self._dbInfo.db;\n            } else {\n              db = openDatabase(options.name, '', '', 0);\n            }\n            if (!options.storeName) {\n              // drop all database tables\n              resolve(getAllStoreNames(db));\n            } else {\n              resolve({\n                db: db,\n                storeNames: [options.storeName]\n              });\n            }\n          }).then(function (operationInfo) {\n            return new Promise$1(function (resolve, reject) {\n              operationInfo.db.transaction(function (t) {\n                function dropTable(storeName) {\n                  return new Promise$1(function (resolve, reject) {\n                    t.executeSql('DROP TABLE IF EXISTS ' + storeName, [], function () {\n                      resolve();\n                    }, function (t, error) {\n                      reject(error);\n                    });\n                  });\n                }\n                var operations = [];\n                for (var i = 0, len = operationInfo.storeNames.length; i < len; i++) {\n                  operations.push(dropTable(operationInfo.storeNames[i]));\n                }\n                Promise$1.all(operations).then(function () {\n                  resolve();\n                })[\"catch\"](function (e) {\n                  reject(e);\n                });\n              }, function (sqlError) {\n                reject(sqlError);\n              });\n            });\n          });\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var webSQLStorage = {\n        _driver: 'webSQLStorage',\n        _initStorage: _initStorage$1,\n        _support: isWebSQLValid(),\n        iterate: iterate$1,\n        getItem: getItem$1,\n        setItem: setItem$1,\n        removeItem: removeItem$1,\n        clear: clear$1,\n        length: length$1,\n        key: key$1,\n        keys: keys$1,\n        dropInstance: dropInstance$1\n      };\n      function isLocalStorageValid() {\n        try {\n          return typeof localStorage !== 'undefined' && 'setItem' in localStorage &&\n          // in IE8 typeof localStorage.setItem === 'object'\n          !!localStorage.setItem;\n        } catch (e) {\n          return false;\n        }\n      }\n      function _getKeyPrefix(options, defaultConfig) {\n        var keyPrefix = options.name + '/';\n        if (options.storeName !== defaultConfig.storeName) {\n          keyPrefix += options.storeName + '/';\n        }\n        return keyPrefix;\n      }\n\n      // Check if localStorage throws when saving an item\n      function checkIfLocalStorageThrows() {\n        var localStorageTestKey = '_localforage_support_test';\n        try {\n          localStorage.setItem(localStorageTestKey, true);\n          localStorage.removeItem(localStorageTestKey);\n          return false;\n        } catch (e) {\n          return true;\n        }\n      }\n\n      // Check if localStorage is usable and allows to save an item\n      // This method checks if localStorage is usable in Safari Private Browsing\n      // mode, or in any other case where the available quota for localStorage\n      // is 0 and there wasn't any saved items yet.\n      function _isLocalStorageUsable() {\n        return !checkIfLocalStorageThrows() || localStorage.length > 0;\n      }\n\n      // Config the localStorage backend, using options set in the config.\n      function _initStorage$2(options) {\n        var self = this;\n        var dbInfo = {};\n        if (options) {\n          for (var i in options) {\n            dbInfo[i] = options[i];\n          }\n        }\n        dbInfo.keyPrefix = _getKeyPrefix(options, self._defaultConfig);\n        if (!_isLocalStorageUsable()) {\n          return Promise$1.reject();\n        }\n        self._dbInfo = dbInfo;\n        dbInfo.serializer = localforageSerializer;\n        return Promise$1.resolve();\n      }\n\n      // Remove all keys from the datastore, effectively destroying all data in\n      // the app's key/value store!\n      function clear$2(callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var keyPrefix = self._dbInfo.keyPrefix;\n          for (var i = localStorage.length - 1; i >= 0; i--) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) === 0) {\n              localStorage.removeItem(key);\n            }\n          }\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Retrieve an item from the store. Unlike the original async_storage\n      // library in Gaia, we don't modify return values at all. If a key's value\n      // is `undefined`, we pass that value to the callback function.\n      function getItem$2(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var result = localStorage.getItem(dbInfo.keyPrefix + key);\n\n          // If a result was found, parse it from the serialized\n          // string into a JS object. If result isn't truthy, the key\n          // is likely undefined and we'll pass it straight to the\n          // callback.\n          if (result) {\n            result = dbInfo.serializer.deserialize(result);\n          }\n          return result;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Iterate over all items in the store.\n      function iterate$2(iterator, callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var keyPrefix = dbInfo.keyPrefix;\n          var keyPrefixLength = keyPrefix.length;\n          var length = localStorage.length;\n\n          // We use a dedicated iterator instead of the `i` variable below\n          // so other keys we fetch in localStorage aren't counted in\n          // the `iterationNumber` argument passed to the `iterate()`\n          // callback.\n          //\n          // See: github.com/mozilla/localForage/pull/435#discussion_r38061530\n          var iterationNumber = 1;\n          for (var i = 0; i < length; i++) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) !== 0) {\n              continue;\n            }\n            var value = localStorage.getItem(key);\n\n            // If a result was found, parse it from the serialized\n            // string into a JS object. If result isn't truthy, the\n            // key is likely undefined and we'll pass it straight\n            // to the iterator.\n            if (value) {\n              value = dbInfo.serializer.deserialize(value);\n            }\n            value = iterator(value, key.substring(keyPrefixLength), iterationNumber++);\n            if (value !== void 0) {\n              return value;\n            }\n          }\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Same as localStorage's key() method, except takes a callback.\n      function key$2(n, callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var result;\n          try {\n            result = localStorage.key(n);\n          } catch (error) {\n            result = null;\n          }\n\n          // Remove the prefix from the key, if a key is found.\n          if (result) {\n            result = result.substring(dbInfo.keyPrefix.length);\n          }\n          return result;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function keys$2(callback) {\n        var self = this;\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          var length = localStorage.length;\n          var keys = [];\n          for (var i = 0; i < length; i++) {\n            var itemKey = localStorage.key(i);\n            if (itemKey.indexOf(dbInfo.keyPrefix) === 0) {\n              keys.push(itemKey.substring(dbInfo.keyPrefix.length));\n            }\n          }\n          return keys;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Supply the number of keys in the datastore to the callback function.\n      function length$2(callback) {\n        var self = this;\n        var promise = self.keys().then(function (keys) {\n          return keys.length;\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Remove an item from the store, nice and simple.\n      function removeItem$2(key, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          var dbInfo = self._dbInfo;\n          localStorage.removeItem(dbInfo.keyPrefix + key);\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n\n      // Set a key's value and run an optional callback once the value is set.\n      // Unlike Gaia's implementation, the callback function is passed the value,\n      // in case you want to operate on that value only after you're sure it\n      // saved, or something like that.\n      function setItem$2(key, value, callback) {\n        var self = this;\n        key = normalizeKey(key);\n        var promise = self.ready().then(function () {\n          // Convert undefined values to null.\n          // https://github.com/mozilla/localForage/pull/42\n          if (value === undefined) {\n            value = null;\n          }\n\n          // Save the original value to pass to the callback.\n          var originalValue = value;\n          return new Promise$1(function (resolve, reject) {\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n              if (error) {\n                reject(error);\n              } else {\n                try {\n                  localStorage.setItem(dbInfo.keyPrefix + key, value);\n                  resolve(originalValue);\n                } catch (e) {\n                  // localStorage capacity exceeded.\n                  // TODO: Make this a specific error/event.\n                  if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {\n                    reject(e);\n                  }\n                  reject(e);\n                }\n              }\n            });\n          });\n        });\n        executeCallback(promise, callback);\n        return promise;\n      }\n      function dropInstance$2(options, callback) {\n        callback = getCallback.apply(this, arguments);\n        options = typeof options !== 'function' && options || {};\n        if (!options.name) {\n          var currentConfig = this.config();\n          options.name = options.name || currentConfig.name;\n          options.storeName = options.storeName || currentConfig.storeName;\n        }\n        var self = this;\n        var promise;\n        if (!options.name) {\n          promise = Promise$1.reject('Invalid arguments');\n        } else {\n          promise = new Promise$1(function (resolve) {\n            if (!options.storeName) {\n              resolve(options.name + '/');\n            } else {\n              resolve(_getKeyPrefix(options, self._defaultConfig));\n            }\n          }).then(function (keyPrefix) {\n            for (var i = localStorage.length - 1; i >= 0; i--) {\n              var key = localStorage.key(i);\n              if (key.indexOf(keyPrefix) === 0) {\n                localStorage.removeItem(key);\n              }\n            }\n          });\n        }\n        executeCallback(promise, callback);\n        return promise;\n      }\n      var localStorageWrapper = {\n        _driver: 'localStorageWrapper',\n        _initStorage: _initStorage$2,\n        _support: isLocalStorageValid(),\n        iterate: iterate$2,\n        getItem: getItem$2,\n        setItem: setItem$2,\n        removeItem: removeItem$2,\n        clear: clear$2,\n        length: length$2,\n        key: key$2,\n        keys: keys$2,\n        dropInstance: dropInstance$2\n      };\n      var sameValue = function sameValue(x, y) {\n        return x === y || typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y);\n      };\n      var includes = function includes(array, searchElement) {\n        var len = array.length;\n        var i = 0;\n        while (i < len) {\n          if (sameValue(array[i], searchElement)) {\n            return true;\n          }\n          i++;\n        }\n        return false;\n      };\n      var isArray = Array.isArray || function (arg) {\n        return Object.prototype.toString.call(arg) === '[object Array]';\n      };\n\n      // Drivers are stored here when `defineDriver()` is called.\n      // They are shared across all instances of localForage.\n      var DefinedDrivers = {};\n      var DriverSupport = {};\n      var DefaultDrivers = {\n        INDEXEDDB: asyncStorage,\n        WEBSQL: webSQLStorage,\n        LOCALSTORAGE: localStorageWrapper\n      };\n      var DefaultDriverOrder = [DefaultDrivers.INDEXEDDB._driver, DefaultDrivers.WEBSQL._driver, DefaultDrivers.LOCALSTORAGE._driver];\n      var OptionalDriverMethods = ['dropInstance'];\n      var LibraryMethods = ['clear', 'getItem', 'iterate', 'key', 'keys', 'length', 'removeItem', 'setItem'].concat(OptionalDriverMethods);\n      var DefaultConfig = {\n        description: '',\n        driver: DefaultDriverOrder.slice(),\n        name: 'localforage',\n        // Default DB size is _JUST UNDER_ 5MB, as it's the highest size\n        // we can use without a prompt.\n        size: 4980736,\n        storeName: 'keyvaluepairs',\n        version: 1.0\n      };\n      function callWhenReady(localForageInstance, libraryMethod) {\n        localForageInstance[libraryMethod] = function () {\n          var _args = arguments;\n          return localForageInstance.ready().then(function () {\n            return localForageInstance[libraryMethod].apply(localForageInstance, _args);\n          });\n        };\n      }\n      function extend() {\n        for (var i = 1; i < arguments.length; i++) {\n          var arg = arguments[i];\n          if (arg) {\n            for (var _key in arg) {\n              if (arg.hasOwnProperty(_key)) {\n                if (isArray(arg[_key])) {\n                  arguments[0][_key] = arg[_key].slice();\n                } else {\n                  arguments[0][_key] = arg[_key];\n                }\n              }\n            }\n          }\n        }\n        return arguments[0];\n      }\n      var LocalForage = function () {\n        function LocalForage(options) {\n          _classCallCheck(this, LocalForage);\n          for (var driverTypeKey in DefaultDrivers) {\n            if (DefaultDrivers.hasOwnProperty(driverTypeKey)) {\n              var driver = DefaultDrivers[driverTypeKey];\n              var driverName = driver._driver;\n              this[driverTypeKey] = driverName;\n              if (!DefinedDrivers[driverName]) {\n                // we don't need to wait for the promise,\n                // since the default drivers can be defined\n                // in a blocking manner\n                this.defineDriver(driver);\n              }\n            }\n          }\n          this._defaultConfig = extend({}, DefaultConfig);\n          this._config = extend({}, this._defaultConfig, options);\n          this._driverSet = null;\n          this._initDriver = null;\n          this._ready = false;\n          this._dbInfo = null;\n          this._wrapLibraryMethodsWithReady();\n          this.setDriver(this._config.driver)[\"catch\"](function () {});\n        }\n\n        // Set any config values for localForage; can be called anytime before\n        // the first API call (e.g. `getItem`, `setItem`).\n        // We loop through options so we don't overwrite existing config\n        // values.\n\n        LocalForage.prototype.config = function config(options) {\n          // If the options argument is an object, we use it to set values.\n          // Otherwise, we return either a specified config value or all\n          // config values.\n          if ((typeof options === 'undefined' ? 'undefined' : _typeof(options)) === 'object') {\n            // If localforage is ready and fully initialized, we can't set\n            // any new configuration values. Instead, we return an error.\n            if (this._ready) {\n              return new Error(\"Can't call config() after localforage \" + 'has been used.');\n            }\n            for (var i in options) {\n              if (i === 'storeName') {\n                options[i] = options[i].replace(/\\W/g, '_');\n              }\n              if (i === 'version' && typeof options[i] !== 'number') {\n                return new Error('Database version must be a number.');\n              }\n              this._config[i] = options[i];\n            }\n\n            // after all config options are set and\n            // the driver option is used, try setting it\n            if ('driver' in options && options.driver) {\n              return this.setDriver(this._config.driver);\n            }\n            return true;\n          } else if (typeof options === 'string') {\n            return this._config[options];\n          } else {\n            return this._config;\n          }\n        };\n\n        // Used to define a custom driver, shared across all instances of\n        // localForage.\n\n        LocalForage.prototype.defineDriver = function defineDriver(driverObject, callback, errorCallback) {\n          var promise = new Promise$1(function (resolve, reject) {\n            try {\n              var driverName = driverObject._driver;\n              var complianceError = new Error('Custom driver not compliant; see ' + 'https://mozilla.github.io/localForage/#definedriver');\n\n              // A driver name should be defined and not overlap with the\n              // library-defined, default drivers.\n              if (!driverObject._driver) {\n                reject(complianceError);\n                return;\n              }\n              var driverMethods = LibraryMethods.concat('_initStorage');\n              for (var i = 0, len = driverMethods.length; i < len; i++) {\n                var driverMethodName = driverMethods[i];\n\n                // when the property is there,\n                // it should be a method even when optional\n                var isRequired = !includes(OptionalDriverMethods, driverMethodName);\n                if ((isRequired || driverObject[driverMethodName]) && typeof driverObject[driverMethodName] !== 'function') {\n                  reject(complianceError);\n                  return;\n                }\n              }\n              var configureMissingMethods = function configureMissingMethods() {\n                var methodNotImplementedFactory = function methodNotImplementedFactory(methodName) {\n                  return function () {\n                    var error = new Error('Method ' + methodName + ' is not implemented by the current driver');\n                    var promise = Promise$1.reject(error);\n                    executeCallback(promise, arguments[arguments.length - 1]);\n                    return promise;\n                  };\n                };\n                for (var _i = 0, _len = OptionalDriverMethods.length; _i < _len; _i++) {\n                  var optionalDriverMethod = OptionalDriverMethods[_i];\n                  if (!driverObject[optionalDriverMethod]) {\n                    driverObject[optionalDriverMethod] = methodNotImplementedFactory(optionalDriverMethod);\n                  }\n                }\n              };\n              configureMissingMethods();\n              var setDriverSupport = function setDriverSupport(support) {\n                if (DefinedDrivers[driverName]) {\n                  console.info('Redefining LocalForage driver: ' + driverName);\n                }\n                DefinedDrivers[driverName] = driverObject;\n                DriverSupport[driverName] = support;\n                // don't use a then, so that we can define\n                // drivers that have simple _support methods\n                // in a blocking manner\n                resolve();\n              };\n              if ('_support' in driverObject) {\n                if (driverObject._support && typeof driverObject._support === 'function') {\n                  driverObject._support().then(setDriverSupport, reject);\n                } else {\n                  setDriverSupport(!!driverObject._support);\n                }\n              } else {\n                setDriverSupport(true);\n              }\n            } catch (e) {\n              reject(e);\n            }\n          });\n          executeTwoCallbacks(promise, callback, errorCallback);\n          return promise;\n        };\n        LocalForage.prototype.driver = function driver() {\n          return this._driver || null;\n        };\n        LocalForage.prototype.getDriver = function getDriver(driverName, callback, errorCallback) {\n          var getDriverPromise = DefinedDrivers[driverName] ? Promise$1.resolve(DefinedDrivers[driverName]) : Promise$1.reject(new Error('Driver not found.'));\n          executeTwoCallbacks(getDriverPromise, callback, errorCallback);\n          return getDriverPromise;\n        };\n        LocalForage.prototype.getSerializer = function getSerializer(callback) {\n          var serializerPromise = Promise$1.resolve(localforageSerializer);\n          executeTwoCallbacks(serializerPromise, callback);\n          return serializerPromise;\n        };\n        LocalForage.prototype.ready = function ready(callback) {\n          var self = this;\n          var promise = self._driverSet.then(function () {\n            if (self._ready === null) {\n              self._ready = self._initDriver();\n            }\n            return self._ready;\n          });\n          executeTwoCallbacks(promise, callback, callback);\n          return promise;\n        };\n        LocalForage.prototype.setDriver = function setDriver(drivers, callback, errorCallback) {\n          var self = this;\n          if (!isArray(drivers)) {\n            drivers = [drivers];\n          }\n          var supportedDrivers = this._getSupportedDrivers(drivers);\n          function setDriverToConfig() {\n            self._config.driver = self.driver();\n          }\n          function extendSelfWithDriver(driver) {\n            self._extend(driver);\n            setDriverToConfig();\n            self._ready = self._initStorage(self._config);\n            return self._ready;\n          }\n          function initDriver(supportedDrivers) {\n            return function () {\n              var currentDriverIndex = 0;\n              function driverPromiseLoop() {\n                while (currentDriverIndex < supportedDrivers.length) {\n                  var driverName = supportedDrivers[currentDriverIndex];\n                  currentDriverIndex++;\n                  self._dbInfo = null;\n                  self._ready = null;\n                  return self.getDriver(driverName).then(extendSelfWithDriver)[\"catch\"](driverPromiseLoop);\n                }\n                setDriverToConfig();\n                var error = new Error('No available storage method found.');\n                self._driverSet = Promise$1.reject(error);\n                return self._driverSet;\n              }\n              return driverPromiseLoop();\n            };\n          }\n\n          // There might be a driver initialization in progress\n          // so wait for it to finish in order to avoid a possible\n          // race condition to set _dbInfo\n          var oldDriverSetDone = this._driverSet !== null ? this._driverSet[\"catch\"](function () {\n            return Promise$1.resolve();\n          }) : Promise$1.resolve();\n          this._driverSet = oldDriverSetDone.then(function () {\n            var driverName = supportedDrivers[0];\n            self._dbInfo = null;\n            self._ready = null;\n            return self.getDriver(driverName).then(function (driver) {\n              self._driver = driver._driver;\n              setDriverToConfig();\n              self._wrapLibraryMethodsWithReady();\n              self._initDriver = initDriver(supportedDrivers);\n            });\n          })[\"catch\"](function () {\n            setDriverToConfig();\n            var error = new Error('No available storage method found.');\n            self._driverSet = Promise$1.reject(error);\n            return self._driverSet;\n          });\n          executeTwoCallbacks(this._driverSet, callback, errorCallback);\n          return this._driverSet;\n        };\n        LocalForage.prototype.supports = function supports(driverName) {\n          return !!DriverSupport[driverName];\n        };\n        LocalForage.prototype._extend = function _extend(libraryMethodsAndProperties) {\n          extend(this, libraryMethodsAndProperties);\n        };\n        LocalForage.prototype._getSupportedDrivers = function _getSupportedDrivers(drivers) {\n          var supportedDrivers = [];\n          for (var i = 0, len = drivers.length; i < len; i++) {\n            var driverName = drivers[i];\n            if (this.supports(driverName)) {\n              supportedDrivers.push(driverName);\n            }\n          }\n          return supportedDrivers;\n        };\n        LocalForage.prototype._wrapLibraryMethodsWithReady = function _wrapLibraryMethodsWithReady() {\n          // Add a stub for each driver API method that delays the call to the\n          // corresponding driver method until localForage is ready. These stubs\n          // will be replaced by the driver methods as soon as the driver is\n          // loaded, so there is no performance impact.\n          for (var i = 0, len = LibraryMethods.length; i < len; i++) {\n            callWhenReady(this, LibraryMethods[i]);\n          }\n        };\n        LocalForage.prototype.createInstance = function createInstance(options) {\n          return new LocalForage(options);\n        };\n        return LocalForage;\n      }();\n\n      // The actual localForage object that we expose as a module or via a\n      // global. It's extended by pulling in one of our other libraries.\n\n      var localforage_js = new LocalForage();\n      module.exports = localforage_js;\n    }, {\n      \"3\": 3\n    }]\n  }, {}, [4])(4);\n});", "map": {"version": 3, "names": ["f", "exports", "module", "define", "amd", "g", "window", "global", "self", "localforage", "e", "t", "n", "r", "s", "o", "u", "a", "require", "i", "Error", "code", "l", "call", "length", "_dereq_", "Mutation", "MutationObserver", "WebKitMutationObserver", "scheduleDrain", "called", "observer", "nextTick", "element", "document", "createTextNode", "observe", "characterData", "data", "setImmediate", "MessageChannel", "channel", "port1", "onmessage", "port2", "postMessage", "createElement", "scriptEl", "onreadystatechange", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "documentElement", "append<PERSON><PERSON><PERSON>", "setTimeout", "draining", "queue", "oldQueue", "len", "immediate", "task", "push", "INTERNAL", "handlers", "REJECTED", "FULFILLED", "PENDING", "Promise", "resolver", "TypeError", "state", "outcome", "safelyResolveThenable", "prototype", "onRejected", "then", "onFulfilled", "promise", "constructor", "unwrap", "QueueItem", "callFulfilled", "otherCallFulfilled", "callRejected", "otherCallRejected", "value", "resolve", "reject", "func", "returnValue", "result", "tryCatch", "getThen", "status", "thenable", "error", "obj", "appy<PERSON><PERSON>", "apply", "arguments", "onError", "onSuccess", "tryToUnwrap", "out", "reason", "all", "iterable", "Object", "toString", "values", "Array", "resolved", "allResolver", "resolveFromAll", "outValue", "race", "response", "_typeof", "Symbol", "iterator", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "getIDB", "indexedDB", "webkitIndexedDB", "mozIndexedDB", "OIndexedDB", "msIndexedDB", "idb", "isIndexedDBValid", "open", "<PERSON><PERSON><PERSON><PERSON>", "openDatabase", "test", "navigator", "userAgent", "platform", "hasFetch", "fetch", "indexOf", "IDBKeyRange", "createBlob", "parts", "properties", "Blob", "name", "Builder", "BlobBuilder", "MSBlobBuilder", "MozBlobBuilder", "WebKitBlobBuilder", "builder", "append", "getBlob", "type", "Promise$1", "executeCallback", "callback", "executeTwoCallbacks", "<PERSON><PERSON><PERSON><PERSON>", "normalizeKey", "key", "console", "warn", "String", "get<PERSON>allback", "DETECT_BLOB_SUPPORT_STORE", "supportsBlobs", "db<PERSON><PERSON><PERSON><PERSON>", "READ_ONLY", "READ_WRITE", "_binStringToArrayBuffer", "bin", "buf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "arr", "Uint8Array", "charCodeAt", "_checkBlobSupportWithoutCaching", "txn", "transaction", "blob", "objectStore", "put", "<PERSON>ab<PERSON>", "preventDefault", "stopPropagation", "oncomplete", "matchedChrome", "match", "matchedEdge", "parseInt", "_checkBlobSupport", "_deferReadiness", "dbInfo", "dbContext", "deferredOperation", "deferredOperations", "db<PERSON><PERSON><PERSON>", "_advanceReadiness", "pop", "_rejectReadiness", "err", "_getConnection", "upgradeNeeded", "createDbContext", "db", "close", "db<PERSON><PERSON>s", "version", "openreq", "onupgradeneeded", "createObjectStore", "storeName", "oldVersion", "ex", "newVersion", "onerror", "onsuccess", "onversionchange", "target", "_getOriginalConnection", "_getUpgradedConnection", "_isUpgradeNeeded", "defaultVersion", "isNewStore", "objectStoreNames", "contains", "isDowngrade", "isUpgrade", "incVersion", "_encodeBlob", "reader", "FileReader", "onloadend", "base64", "btoa", "__local_forage_encoded_blob", "readAsBinaryString", "_decodeBlob", "encodedBlob", "arrayBuff", "atob", "_isEncodedBlob", "_fullyReady", "_initReady", "_dbInfo", "_tryReconnect", "forages", "forage", "createTransaction", "mode", "retries", "undefined", "tx", "_initStorage", "options", "ready", "initPromises", "ignoreErrors", "j", "slice", "_defaultConfig", "k", "getItem", "store", "req", "get", "iterate", "openCursor", "iterationNumber", "cursor", "setItem", "blobSupport", "removeItem", "clear", "count", "advanced", "openKeyCursor", "advance", "keys", "dropInstance", "currentConfig", "config", "isCurrentDb", "db<PERSON><PERSON><PERSON>", "dropDBPromise", "deleteDatabase", "onblocked", "_forage", "dropObjectPromise", "deleteObjectStore", "_forage2", "asyncStorage", "_driver", "_support", "isWebSQLValid", "BASE_CHARS", "BLOB_TYPE_PREFIX", "BLOB_TYPE_PREFIX_REGEX", "SERIALIZED_MARKER", "SERIALIZED_MARKER_LENGTH", "TYPE_ARRAYBUFFER", "TYPE_BLOB", "TYPE_INT8ARRAY", "TYPE_UINT8ARRAY", "TYPE_UINT8CLAMPEDARRAY", "TYPE_INT16ARRAY", "TYPE_INT32ARRAY", "TYPE_UINT16ARRAY", "TYPE_UINT32ARRAY", "TYPE_FLOAT32ARRAY", "TYPE_FLOAT64ARRAY", "TYPE_SERIALIZED_MARKER_LENGTH", "toString$1", "string<PERSON>o<PERSON>uffer", "serializedString", "bufferLength", "p", "encoded1", "encoded2", "encoded3", "encoded4", "buffer", "bytes", "bufferToString", "base64String", "substring", "serialize", "valueType", "marker", "fileReader", "onload", "str", "readAsA<PERSON>y<PERSON><PERSON>er", "JSON", "stringify", "deserialize", "parse", "blobType", "matcher", "Int8Array", "Uint8ClampedArray", "Int16Array", "Uint16Array", "Int32Array", "Uint32Array", "Float32Array", "Float64Array", "localforageSerializer", "createDbTable", "executeSql", "_initStorage$1", "dbInfoPromise", "description", "size", "serializer", "tryExecuteSql", "sqlStatement", "args", "SYNTAX_ERR", "results", "rows", "getItem$1", "item", "iterate$1", "_setItem", "retriesLeft", "originalValue", "sqlError", "QUOTA_ERR", "setItem$1", "removeItem$1", "clear$1", "length$1", "c", "key$1", "keys$1", "getAllStoreNames", "storeNames", "dropInstance$1", "operationInfo", "dropTable", "operations", "webSQLStorage", "isLocalStorageValid", "localStorage", "_getKeyPrefix", "defaultConfig", "keyPrefix", "checkIfLocalStorageThrows", "localStorageTestKey", "_isLocalStorageUsable", "_initStorage$2", "clear$2", "getItem$2", "iterate$2", "keyPrefix<PERSON><PERSON><PERSON>", "key$2", "keys$2", "itemKey", "length$2", "removeItem$2", "setItem$2", "dropInstance$2", "localStorageWrapper", "sameValue", "x", "y", "isNaN", "includes", "array", "searchElement", "isArray", "arg", "DefinedDrivers", "DriverSupport", "DefaultDrivers", "INDEXEDDB", "WEBSQL", "LOCALSTORAGE", "DefaultDriverOrder", "OptionalDriverMethods", "LibraryMethods", "concat", "DefaultConfig", "driver", "callWhenReady", "localForageInstance", "libraryMethod", "_args", "extend", "_key", "hasOwnProperty", "LocalForage", "driverT<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "defineDriver", "_config", "_driverSet", "_initDriver", "_ready", "_wrapLibraryMethodsWithReady", "setDriver", "replace", "driverObject", "complianceError", "driverMethods", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isRequired", "configureMissingMethods", "methodNotImplementedFactory", "methodName", "_i", "_len", "optionalDriverMethod", "setDriverSupport", "support", "info", "getDriver", "getDriverPromise", "getSerializer", "serializerPromise", "drivers", "supportedDrivers", "_getSupportedDrivers", "setDriverToConfig", "extendSelfWithDriver", "_extend", "initDriver", "currentDriverIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "oldDriverSetDone", "supports", "libraryMethodsAndProperties", "createInstance", "localforage_js"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/localforage/dist/localforage.js"], "sourcesContent": ["/*!\n    localForage -- Offline Storage, Improved\n    Version 1.10.0\n    https://localforage.github.io/localForage\n    (c) 2013-2017 Mozilla, Apache License 2.0\n*/\n(function(f){if(typeof exports===\"object\"&&typeof module!==\"undefined\"){module.exports=f()}else if(typeof define===\"function\"&&define.amd){define([],f)}else{var g;if(typeof window!==\"undefined\"){g=window}else if(typeof global!==\"undefined\"){g=global}else if(typeof self!==\"undefined\"){g=self}else{g=this}g.localforage = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require==\"function\"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error(\"Cannot find module '\"+o+\"'\");throw (f.code=\"MODULE_NOT_FOUND\", f)}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require==\"function\"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(_dereq_,module,exports){\n(function (global){\n'use strict';\nvar Mutation = global.MutationObserver || global.WebKitMutationObserver;\n\nvar scheduleDrain;\n\n{\n  if (Mutation) {\n    var called = 0;\n    var observer = new Mutation(nextTick);\n    var element = global.document.createTextNode('');\n    observer.observe(element, {\n      characterData: true\n    });\n    scheduleDrain = function () {\n      element.data = (called = ++called % 2);\n    };\n  } else if (!global.setImmediate && typeof global.MessageChannel !== 'undefined') {\n    var channel = new global.MessageChannel();\n    channel.port1.onmessage = nextTick;\n    scheduleDrain = function () {\n      channel.port2.postMessage(0);\n    };\n  } else if ('document' in global && 'onreadystatechange' in global.document.createElement('script')) {\n    scheduleDrain = function () {\n\n      // Create a <script> element; its readystatechange event will be fired asynchronously once it is inserted\n      // into the document. Do so, thus queuing up the task. Remember to clean up once it's been called.\n      var scriptEl = global.document.createElement('script');\n      scriptEl.onreadystatechange = function () {\n        nextTick();\n\n        scriptEl.onreadystatechange = null;\n        scriptEl.parentNode.removeChild(scriptEl);\n        scriptEl = null;\n      };\n      global.document.documentElement.appendChild(scriptEl);\n    };\n  } else {\n    scheduleDrain = function () {\n      setTimeout(nextTick, 0);\n    };\n  }\n}\n\nvar draining;\nvar queue = [];\n//named nextTick for less confusing stack traces\nfunction nextTick() {\n  draining = true;\n  var i, oldQueue;\n  var len = queue.length;\n  while (len) {\n    oldQueue = queue;\n    queue = [];\n    i = -1;\n    while (++i < len) {\n      oldQueue[i]();\n    }\n    len = queue.length;\n  }\n  draining = false;\n}\n\nmodule.exports = immediate;\nfunction immediate(task) {\n  if (queue.push(task) === 1 && !draining) {\n    scheduleDrain();\n  }\n}\n\n}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{}],2:[function(_dereq_,module,exports){\n'use strict';\nvar immediate = _dereq_(1);\n\n/* istanbul ignore next */\nfunction INTERNAL() {}\n\nvar handlers = {};\n\nvar REJECTED = ['REJECTED'];\nvar FULFILLED = ['FULFILLED'];\nvar PENDING = ['PENDING'];\n\nmodule.exports = Promise;\n\nfunction Promise(resolver) {\n  if (typeof resolver !== 'function') {\n    throw new TypeError('resolver must be a function');\n  }\n  this.state = PENDING;\n  this.queue = [];\n  this.outcome = void 0;\n  if (resolver !== INTERNAL) {\n    safelyResolveThenable(this, resolver);\n  }\n}\n\nPromise.prototype[\"catch\"] = function (onRejected) {\n  return this.then(null, onRejected);\n};\nPromise.prototype.then = function (onFulfilled, onRejected) {\n  if (typeof onFulfilled !== 'function' && this.state === FULFILLED ||\n    typeof onRejected !== 'function' && this.state === REJECTED) {\n    return this;\n  }\n  var promise = new this.constructor(INTERNAL);\n  if (this.state !== PENDING) {\n    var resolver = this.state === FULFILLED ? onFulfilled : onRejected;\n    unwrap(promise, resolver, this.outcome);\n  } else {\n    this.queue.push(new QueueItem(promise, onFulfilled, onRejected));\n  }\n\n  return promise;\n};\nfunction QueueItem(promise, onFulfilled, onRejected) {\n  this.promise = promise;\n  if (typeof onFulfilled === 'function') {\n    this.onFulfilled = onFulfilled;\n    this.callFulfilled = this.otherCallFulfilled;\n  }\n  if (typeof onRejected === 'function') {\n    this.onRejected = onRejected;\n    this.callRejected = this.otherCallRejected;\n  }\n}\nQueueItem.prototype.callFulfilled = function (value) {\n  handlers.resolve(this.promise, value);\n};\nQueueItem.prototype.otherCallFulfilled = function (value) {\n  unwrap(this.promise, this.onFulfilled, value);\n};\nQueueItem.prototype.callRejected = function (value) {\n  handlers.reject(this.promise, value);\n};\nQueueItem.prototype.otherCallRejected = function (value) {\n  unwrap(this.promise, this.onRejected, value);\n};\n\nfunction unwrap(promise, func, value) {\n  immediate(function () {\n    var returnValue;\n    try {\n      returnValue = func(value);\n    } catch (e) {\n      return handlers.reject(promise, e);\n    }\n    if (returnValue === promise) {\n      handlers.reject(promise, new TypeError('Cannot resolve promise with itself'));\n    } else {\n      handlers.resolve(promise, returnValue);\n    }\n  });\n}\n\nhandlers.resolve = function (self, value) {\n  var result = tryCatch(getThen, value);\n  if (result.status === 'error') {\n    return handlers.reject(self, result.value);\n  }\n  var thenable = result.value;\n\n  if (thenable) {\n    safelyResolveThenable(self, thenable);\n  } else {\n    self.state = FULFILLED;\n    self.outcome = value;\n    var i = -1;\n    var len = self.queue.length;\n    while (++i < len) {\n      self.queue[i].callFulfilled(value);\n    }\n  }\n  return self;\n};\nhandlers.reject = function (self, error) {\n  self.state = REJECTED;\n  self.outcome = error;\n  var i = -1;\n  var len = self.queue.length;\n  while (++i < len) {\n    self.queue[i].callRejected(error);\n  }\n  return self;\n};\n\nfunction getThen(obj) {\n  // Make sure we only access the accessor once as required by the spec\n  var then = obj && obj.then;\n  if (obj && (typeof obj === 'object' || typeof obj === 'function') && typeof then === 'function') {\n    return function appyThen() {\n      then.apply(obj, arguments);\n    };\n  }\n}\n\nfunction safelyResolveThenable(self, thenable) {\n  // Either fulfill, reject or reject with error\n  var called = false;\n  function onError(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.reject(self, value);\n  }\n\n  function onSuccess(value) {\n    if (called) {\n      return;\n    }\n    called = true;\n    handlers.resolve(self, value);\n  }\n\n  function tryToUnwrap() {\n    thenable(onSuccess, onError);\n  }\n\n  var result = tryCatch(tryToUnwrap);\n  if (result.status === 'error') {\n    onError(result.value);\n  }\n}\n\nfunction tryCatch(func, value) {\n  var out = {};\n  try {\n    out.value = func(value);\n    out.status = 'success';\n  } catch (e) {\n    out.status = 'error';\n    out.value = e;\n  }\n  return out;\n}\n\nPromise.resolve = resolve;\nfunction resolve(value) {\n  if (value instanceof this) {\n    return value;\n  }\n  return handlers.resolve(new this(INTERNAL), value);\n}\n\nPromise.reject = reject;\nfunction reject(reason) {\n  var promise = new this(INTERNAL);\n  return handlers.reject(promise, reason);\n}\n\nPromise.all = all;\nfunction all(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var values = new Array(len);\n  var resolved = 0;\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    allResolver(iterable[i], i);\n  }\n  return promise;\n  function allResolver(value, i) {\n    self.resolve(value).then(resolveFromAll, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n    function resolveFromAll(outValue) {\n      values[i] = outValue;\n      if (++resolved === len && !called) {\n        called = true;\n        handlers.resolve(promise, values);\n      }\n    }\n  }\n}\n\nPromise.race = race;\nfunction race(iterable) {\n  var self = this;\n  if (Object.prototype.toString.call(iterable) !== '[object Array]') {\n    return this.reject(new TypeError('must be an array'));\n  }\n\n  var len = iterable.length;\n  var called = false;\n  if (!len) {\n    return this.resolve([]);\n  }\n\n  var i = -1;\n  var promise = new this(INTERNAL);\n\n  while (++i < len) {\n    resolver(iterable[i]);\n  }\n  return promise;\n  function resolver(value) {\n    self.resolve(value).then(function (response) {\n      if (!called) {\n        called = true;\n        handlers.resolve(promise, response);\n      }\n    }, function (error) {\n      if (!called) {\n        called = true;\n        handlers.reject(promise, error);\n      }\n    });\n  }\n}\n\n},{\"1\":1}],3:[function(_dereq_,module,exports){\n(function (global){\n'use strict';\nif (typeof global.Promise !== 'function') {\n  global.Promise = _dereq_(2);\n}\n\n}).call(this,typeof global !== \"undefined\" ? global : typeof self !== \"undefined\" ? self : typeof window !== \"undefined\" ? window : {})\n},{\"2\":2}],4:[function(_dereq_,module,exports){\n'use strict';\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction getIDB() {\n    /* global indexedDB,webkitIndexedDB,mozIndexedDB,OIndexedDB,msIndexedDB */\n    try {\n        if (typeof indexedDB !== 'undefined') {\n            return indexedDB;\n        }\n        if (typeof webkitIndexedDB !== 'undefined') {\n            return webkitIndexedDB;\n        }\n        if (typeof mozIndexedDB !== 'undefined') {\n            return mozIndexedDB;\n        }\n        if (typeof OIndexedDB !== 'undefined') {\n            return OIndexedDB;\n        }\n        if (typeof msIndexedDB !== 'undefined') {\n            return msIndexedDB;\n        }\n    } catch (e) {\n        return;\n    }\n}\n\nvar idb = getIDB();\n\nfunction isIndexedDBValid() {\n    try {\n        // Initialize IndexedDB; fall back to vendor-prefixed versions\n        // if needed.\n        if (!idb || !idb.open) {\n            return false;\n        }\n        // We mimic PouchDB here;\n        //\n        // We test for openDatabase because IE Mobile identifies itself\n        // as Safari. Oh the lulz...\n        var isSafari = typeof openDatabase !== 'undefined' && /(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent) && !/BlackBerry/.test(navigator.platform);\n\n        var hasFetch = typeof fetch === 'function' && fetch.toString().indexOf('[native code') !== -1;\n\n        // Safari <10.1 does not meet our requirements for IDB support\n        // (see: https://github.com/pouchdb/pouchdb/issues/5572).\n        // Safari 10.1 shipped with fetch, we can use that to detect it.\n        // Note: this creates issues with `window.fetch` polyfills and\n        // overrides; see:\n        // https://github.com/localForage/localForage/issues/856\n        return (!isSafari || hasFetch) && typeof indexedDB !== 'undefined' &&\n        // some outdated implementations of IDB that appear on Samsung\n        // and HTC Android devices <4.4 are missing IDBKeyRange\n        // See: https://github.com/mozilla/localForage/issues/128\n        // See: https://github.com/mozilla/localForage/issues/272\n        typeof IDBKeyRange !== 'undefined';\n    } catch (e) {\n        return false;\n    }\n}\n\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor. (i.e.\n// old QtWebKit versions, at least).\n// Abstracts constructing a Blob object, so it also works in older\n// browsers that don't support the native Blob constructor. (i.e.\n// old QtWebKit versions, at least).\nfunction createBlob(parts, properties) {\n    /* global BlobBuilder,MSBlobBuilder,MozBlobBuilder,WebKitBlobBuilder */\n    parts = parts || [];\n    properties = properties || {};\n    try {\n        return new Blob(parts, properties);\n    } catch (e) {\n        if (e.name !== 'TypeError') {\n            throw e;\n        }\n        var Builder = typeof BlobBuilder !== 'undefined' ? BlobBuilder : typeof MSBlobBuilder !== 'undefined' ? MSBlobBuilder : typeof MozBlobBuilder !== 'undefined' ? MozBlobBuilder : WebKitBlobBuilder;\n        var builder = new Builder();\n        for (var i = 0; i < parts.length; i += 1) {\n            builder.append(parts[i]);\n        }\n        return builder.getBlob(properties.type);\n    }\n}\n\n// This is CommonJS because lie is an external dependency, so Rollup\n// can just ignore it.\nif (typeof Promise === 'undefined') {\n    // In the \"nopromises\" build this will just throw if you don't have\n    // a global promise object, but it would throw anyway later.\n    _dereq_(3);\n}\nvar Promise$1 = Promise;\n\nfunction executeCallback(promise, callback) {\n    if (callback) {\n        promise.then(function (result) {\n            callback(null, result);\n        }, function (error) {\n            callback(error);\n        });\n    }\n}\n\nfunction executeTwoCallbacks(promise, callback, errorCallback) {\n    if (typeof callback === 'function') {\n        promise.then(callback);\n    }\n\n    if (typeof errorCallback === 'function') {\n        promise[\"catch\"](errorCallback);\n    }\n}\n\nfunction normalizeKey(key) {\n    // Cast the key to a string, as that's all we can set as a key.\n    if (typeof key !== 'string') {\n        console.warn(key + ' used as a key, but it is not a string.');\n        key = String(key);\n    }\n\n    return key;\n}\n\nfunction getCallback() {\n    if (arguments.length && typeof arguments[arguments.length - 1] === 'function') {\n        return arguments[arguments.length - 1];\n    }\n}\n\n// Some code originally from async_storage.js in\n// [Gaia](https://github.com/mozilla-b2g/gaia).\n\nvar DETECT_BLOB_SUPPORT_STORE = 'local-forage-detect-blob-support';\nvar supportsBlobs = void 0;\nvar dbContexts = {};\nvar toString = Object.prototype.toString;\n\n// Transaction Modes\nvar READ_ONLY = 'readonly';\nvar READ_WRITE = 'readwrite';\n\n// Transform a binary string to an array buffer, because otherwise\n// weird stuff happens when you try to work with the binary string directly.\n// It is known.\n// From http://stackoverflow.com/questions/14967647/ (continues on next line)\n// encode-decode-image-with-base64-breaks-image (2013-04-21)\nfunction _binStringToArrayBuffer(bin) {\n    var length = bin.length;\n    var buf = new ArrayBuffer(length);\n    var arr = new Uint8Array(buf);\n    for (var i = 0; i < length; i++) {\n        arr[i] = bin.charCodeAt(i);\n    }\n    return buf;\n}\n\n//\n// Blobs are not supported in all versions of IndexedDB, notably\n// Chrome <37 and Android <5. In those versions, storing a blob will throw.\n//\n// Various other blob bugs exist in Chrome v37-42 (inclusive).\n// Detecting them is expensive and confusing to users, and Chrome 37-42\n// is at very low usage worldwide, so we do a hacky userAgent check instead.\n//\n// content-type bug: https://code.google.com/p/chromium/issues/detail?id=408120\n// 404 bug: https://code.google.com/p/chromium/issues/detail?id=447916\n// FileReader bug: https://code.google.com/p/chromium/issues/detail?id=447836\n//\n// Code borrowed from PouchDB. See:\n// https://github.com/pouchdb/pouchdb/blob/master/packages/node_modules/pouchdb-adapter-idb/src/blobSupport.js\n//\nfunction _checkBlobSupportWithoutCaching(idb) {\n    return new Promise$1(function (resolve) {\n        var txn = idb.transaction(DETECT_BLOB_SUPPORT_STORE, READ_WRITE);\n        var blob = createBlob(['']);\n        txn.objectStore(DETECT_BLOB_SUPPORT_STORE).put(blob, 'key');\n\n        txn.onabort = function (e) {\n            // If the transaction aborts now its due to not being able to\n            // write to the database, likely due to the disk being full\n            e.preventDefault();\n            e.stopPropagation();\n            resolve(false);\n        };\n\n        txn.oncomplete = function () {\n            var matchedChrome = navigator.userAgent.match(/Chrome\\/(\\d+)/);\n            var matchedEdge = navigator.userAgent.match(/Edge\\//);\n            // MS Edge pretends to be Chrome 42:\n            // https://msdn.microsoft.com/en-us/library/hh869301%28v=vs.85%29.aspx\n            resolve(matchedEdge || !matchedChrome || parseInt(matchedChrome[1], 10) >= 43);\n        };\n    })[\"catch\"](function () {\n        return false; // error, so assume unsupported\n    });\n}\n\nfunction _checkBlobSupport(idb) {\n    if (typeof supportsBlobs === 'boolean') {\n        return Promise$1.resolve(supportsBlobs);\n    }\n    return _checkBlobSupportWithoutCaching(idb).then(function (value) {\n        supportsBlobs = value;\n        return supportsBlobs;\n    });\n}\n\nfunction _deferReadiness(dbInfo) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Create a deferred object representing the current database operation.\n    var deferredOperation = {};\n\n    deferredOperation.promise = new Promise$1(function (resolve, reject) {\n        deferredOperation.resolve = resolve;\n        deferredOperation.reject = reject;\n    });\n\n    // Enqueue the deferred operation.\n    dbContext.deferredOperations.push(deferredOperation);\n\n    // Chain its promise to the database readiness.\n    if (!dbContext.dbReady) {\n        dbContext.dbReady = deferredOperation.promise;\n    } else {\n        dbContext.dbReady = dbContext.dbReady.then(function () {\n            return deferredOperation.promise;\n        });\n    }\n}\n\nfunction _advanceReadiness(dbInfo) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Dequeue a deferred operation.\n    var deferredOperation = dbContext.deferredOperations.pop();\n\n    // Resolve its promise (which is part of the database readiness\n    // chain of promises).\n    if (deferredOperation) {\n        deferredOperation.resolve();\n        return deferredOperation.promise;\n    }\n}\n\nfunction _rejectReadiness(dbInfo, err) {\n    var dbContext = dbContexts[dbInfo.name];\n\n    // Dequeue a deferred operation.\n    var deferredOperation = dbContext.deferredOperations.pop();\n\n    // Reject its promise (which is part of the database readiness\n    // chain of promises).\n    if (deferredOperation) {\n        deferredOperation.reject(err);\n        return deferredOperation.promise;\n    }\n}\n\nfunction _getConnection(dbInfo, upgradeNeeded) {\n    return new Promise$1(function (resolve, reject) {\n        dbContexts[dbInfo.name] = dbContexts[dbInfo.name] || createDbContext();\n\n        if (dbInfo.db) {\n            if (upgradeNeeded) {\n                _deferReadiness(dbInfo);\n                dbInfo.db.close();\n            } else {\n                return resolve(dbInfo.db);\n            }\n        }\n\n        var dbArgs = [dbInfo.name];\n\n        if (upgradeNeeded) {\n            dbArgs.push(dbInfo.version);\n        }\n\n        var openreq = idb.open.apply(idb, dbArgs);\n\n        if (upgradeNeeded) {\n            openreq.onupgradeneeded = function (e) {\n                var db = openreq.result;\n                try {\n                    db.createObjectStore(dbInfo.storeName);\n                    if (e.oldVersion <= 1) {\n                        // Added when support for blob shims was added\n                        db.createObjectStore(DETECT_BLOB_SUPPORT_STORE);\n                    }\n                } catch (ex) {\n                    if (ex.name === 'ConstraintError') {\n                        console.warn('The database \"' + dbInfo.name + '\"' + ' has been upgraded from version ' + e.oldVersion + ' to version ' + e.newVersion + ', but the storage \"' + dbInfo.storeName + '\" already exists.');\n                    } else {\n                        throw ex;\n                    }\n                }\n            };\n        }\n\n        openreq.onerror = function (e) {\n            e.preventDefault();\n            reject(openreq.error);\n        };\n\n        openreq.onsuccess = function () {\n            var db = openreq.result;\n            db.onversionchange = function (e) {\n                // Triggered when the database is modified (e.g. adding an objectStore) or\n                // deleted (even when initiated by other sessions in different tabs).\n                // Closing the connection here prevents those operations from being blocked.\n                // If the database is accessed again later by this instance, the connection\n                // will be reopened or the database recreated as needed.\n                e.target.close();\n            };\n            resolve(db);\n            _advanceReadiness(dbInfo);\n        };\n    });\n}\n\nfunction _getOriginalConnection(dbInfo) {\n    return _getConnection(dbInfo, false);\n}\n\nfunction _getUpgradedConnection(dbInfo) {\n    return _getConnection(dbInfo, true);\n}\n\nfunction _isUpgradeNeeded(dbInfo, defaultVersion) {\n    if (!dbInfo.db) {\n        return true;\n    }\n\n    var isNewStore = !dbInfo.db.objectStoreNames.contains(dbInfo.storeName);\n    var isDowngrade = dbInfo.version < dbInfo.db.version;\n    var isUpgrade = dbInfo.version > dbInfo.db.version;\n\n    if (isDowngrade) {\n        // If the version is not the default one\n        // then warn for impossible downgrade.\n        if (dbInfo.version !== defaultVersion) {\n            console.warn('The database \"' + dbInfo.name + '\"' + \" can't be downgraded from version \" + dbInfo.db.version + ' to version ' + dbInfo.version + '.');\n        }\n        // Align the versions to prevent errors.\n        dbInfo.version = dbInfo.db.version;\n    }\n\n    if (isUpgrade || isNewStore) {\n        // If the store is new then increment the version (if needed).\n        // This will trigger an \"upgradeneeded\" event which is required\n        // for creating a store.\n        if (isNewStore) {\n            var incVersion = dbInfo.db.version + 1;\n            if (incVersion > dbInfo.version) {\n                dbInfo.version = incVersion;\n            }\n        }\n\n        return true;\n    }\n\n    return false;\n}\n\n// encode a blob for indexeddb engines that don't support blobs\nfunction _encodeBlob(blob) {\n    return new Promise$1(function (resolve, reject) {\n        var reader = new FileReader();\n        reader.onerror = reject;\n        reader.onloadend = function (e) {\n            var base64 = btoa(e.target.result || '');\n            resolve({\n                __local_forage_encoded_blob: true,\n                data: base64,\n                type: blob.type\n            });\n        };\n        reader.readAsBinaryString(blob);\n    });\n}\n\n// decode an encoded blob\nfunction _decodeBlob(encodedBlob) {\n    var arrayBuff = _binStringToArrayBuffer(atob(encodedBlob.data));\n    return createBlob([arrayBuff], { type: encodedBlob.type });\n}\n\n// is this one of our fancy encoded blobs?\nfunction _isEncodedBlob(value) {\n    return value && value.__local_forage_encoded_blob;\n}\n\n// Specialize the default `ready()` function by making it dependent\n// on the current database operations. Thus, the driver will be actually\n// ready when it's been initialized (default) *and* there are no pending\n// operations on the database (initiated by some other instances).\nfunction _fullyReady(callback) {\n    var self = this;\n\n    var promise = self._initReady().then(function () {\n        var dbContext = dbContexts[self._dbInfo.name];\n\n        if (dbContext && dbContext.dbReady) {\n            return dbContext.dbReady;\n        }\n    });\n\n    executeTwoCallbacks(promise, callback, callback);\n    return promise;\n}\n\n// Try to establish a new db connection to replace the\n// current one which is broken (i.e. experiencing\n// InvalidStateError while creating a transaction).\nfunction _tryReconnect(dbInfo) {\n    _deferReadiness(dbInfo);\n\n    var dbContext = dbContexts[dbInfo.name];\n    var forages = dbContext.forages;\n\n    for (var i = 0; i < forages.length; i++) {\n        var forage = forages[i];\n        if (forage._dbInfo.db) {\n            forage._dbInfo.db.close();\n            forage._dbInfo.db = null;\n        }\n    }\n    dbInfo.db = null;\n\n    return _getOriginalConnection(dbInfo).then(function (db) {\n        dbInfo.db = db;\n        if (_isUpgradeNeeded(dbInfo)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n        }\n        return db;\n    }).then(function (db) {\n        // store the latest db reference\n        // in case the db was upgraded\n        dbInfo.db = dbContext.db = db;\n        for (var i = 0; i < forages.length; i++) {\n            forages[i]._dbInfo.db = db;\n        }\n    })[\"catch\"](function (err) {\n        _rejectReadiness(dbInfo, err);\n        throw err;\n    });\n}\n\n// FF doesn't like Promises (micro-tasks) and IDDB store operations,\n// so we have to do it with callbacks\nfunction createTransaction(dbInfo, mode, callback, retries) {\n    if (retries === undefined) {\n        retries = 1;\n    }\n\n    try {\n        var tx = dbInfo.db.transaction(dbInfo.storeName, mode);\n        callback(null, tx);\n    } catch (err) {\n        if (retries > 0 && (!dbInfo.db || err.name === 'InvalidStateError' || err.name === 'NotFoundError')) {\n            return Promise$1.resolve().then(function () {\n                if (!dbInfo.db || err.name === 'NotFoundError' && !dbInfo.db.objectStoreNames.contains(dbInfo.storeName) && dbInfo.version <= dbInfo.db.version) {\n                    // increase the db version, to create the new ObjectStore\n                    if (dbInfo.db) {\n                        dbInfo.version = dbInfo.db.version + 1;\n                    }\n                    // Reopen the database for upgrading.\n                    return _getUpgradedConnection(dbInfo);\n                }\n            }).then(function () {\n                return _tryReconnect(dbInfo).then(function () {\n                    createTransaction(dbInfo, mode, callback, retries - 1);\n                });\n            })[\"catch\"](callback);\n        }\n\n        callback(err);\n    }\n}\n\nfunction createDbContext() {\n    return {\n        // Running localForages sharing a database.\n        forages: [],\n        // Shared database.\n        db: null,\n        // Database readiness (promise).\n        dbReady: null,\n        // Deferred operations on the database.\n        deferredOperations: []\n    };\n}\n\n// Open the IndexedDB database (automatically creates one if one didn't\n// previously exist), using any options set in the config.\nfunction _initStorage(options) {\n    var self = this;\n    var dbInfo = {\n        db: null\n    };\n\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = options[i];\n        }\n    }\n\n    // Get the current context of the database;\n    var dbContext = dbContexts[dbInfo.name];\n\n    // ...or create a new context.\n    if (!dbContext) {\n        dbContext = createDbContext();\n        // Register the new context in the global container.\n        dbContexts[dbInfo.name] = dbContext;\n    }\n\n    // Register itself as a running localForage in the current context.\n    dbContext.forages.push(self);\n\n    // Replace the default `ready()` function with the specialized one.\n    if (!self._initReady) {\n        self._initReady = self.ready;\n        self.ready = _fullyReady;\n    }\n\n    // Create an array of initialization states of the related localForages.\n    var initPromises = [];\n\n    function ignoreErrors() {\n        // Don't handle errors here,\n        // just makes sure related localForages aren't pending.\n        return Promise$1.resolve();\n    }\n\n    for (var j = 0; j < dbContext.forages.length; j++) {\n        var forage = dbContext.forages[j];\n        if (forage !== self) {\n            // Don't wait for itself...\n            initPromises.push(forage._initReady()[\"catch\"](ignoreErrors));\n        }\n    }\n\n    // Take a snapshot of the related localForages.\n    var forages = dbContext.forages.slice(0);\n\n    // Initialize the connection process only when\n    // all the related localForages aren't pending.\n    return Promise$1.all(initPromises).then(function () {\n        dbInfo.db = dbContext.db;\n        // Get the connection or open a new one without upgrade.\n        return _getOriginalConnection(dbInfo);\n    }).then(function (db) {\n        dbInfo.db = db;\n        if (_isUpgradeNeeded(dbInfo, self._defaultConfig.version)) {\n            // Reopen the database for upgrading.\n            return _getUpgradedConnection(dbInfo);\n        }\n        return db;\n    }).then(function (db) {\n        dbInfo.db = dbContext.db = db;\n        self._dbInfo = dbInfo;\n        // Share the final connection amongst related localForages.\n        for (var k = 0; k < forages.length; k++) {\n            var forage = forages[k];\n            if (forage !== self) {\n                // Self is already up-to-date.\n                forage._dbInfo.db = dbInfo.db;\n                forage._dbInfo.version = dbInfo.version;\n            }\n        }\n    });\n}\n\nfunction getItem(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.get(key);\n\n                    req.onsuccess = function () {\n                        var value = req.result;\n                        if (value === undefined) {\n                            value = null;\n                        }\n                        if (_isEncodedBlob(value)) {\n                            value = _decodeBlob(value);\n                        }\n                        resolve(value);\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Iterate over all items stored in database.\nfunction iterate(iterator, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.openCursor();\n                    var iterationNumber = 1;\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n\n                        if (cursor) {\n                            var value = cursor.value;\n                            if (_isEncodedBlob(value)) {\n                                value = _decodeBlob(value);\n                            }\n                            var result = iterator(value, cursor.key, iterationNumber++);\n\n                            // when the iterator callback returns any\n                            // (non-`undefined`) value, then we stop\n                            // the iteration immediately\n                            if (result !== void 0) {\n                                resolve(result);\n                            } else {\n                                cursor[\"continue\"]();\n                            }\n                        } else {\n                            resolve();\n                        }\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n\n    return promise;\n}\n\nfunction setItem(key, value, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        var dbInfo;\n        self.ready().then(function () {\n            dbInfo = self._dbInfo;\n            if (toString.call(value) === '[object Blob]') {\n                return _checkBlobSupport(dbInfo.db).then(function (blobSupport) {\n                    if (blobSupport) {\n                        return value;\n                    }\n                    return _encodeBlob(value);\n                });\n            }\n            return value;\n        }).then(function (value) {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n\n                    // The reason we don't _save_ null is because IE 10 does\n                    // not support saving the `null` type in IndexedDB. How\n                    // ironic, given the bug below!\n                    // See: https://github.com/mozilla/localForage/issues/161\n                    if (value === null) {\n                        value = undefined;\n                    }\n\n                    var req = store.put(value, key);\n\n                    transaction.oncomplete = function () {\n                        // Cast to undefined so the value passed to\n                        // callback/promise is the same as what one would get out\n                        // of `getItem()` later. This leads to some weirdness\n                        // (setItem('foo', undefined) will return `null`), but\n                        // it's not my fault localStorage is our baseline and that\n                        // it's weird.\n                        if (value === undefined) {\n                            value = null;\n                        }\n\n                        resolve(value);\n                    };\n                    transaction.onabort = transaction.onerror = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction removeItem(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    // We use a Grunt task to make this safe for IE and some\n                    // versions of Android (including those used by Cordova).\n                    // Normally IE won't like `.delete()` and will insist on\n                    // using `['delete']()`, but we have a build step that\n                    // fixes this for us now.\n                    var req = store[\"delete\"](key);\n                    transaction.oncomplete = function () {\n                        resolve();\n                    };\n\n                    transaction.onerror = function () {\n                        reject(req.error);\n                    };\n\n                    // The request will be also be aborted if we've exceeded our storage\n                    // space.\n                    transaction.onabort = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction clear(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_WRITE, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.clear();\n\n                    transaction.oncomplete = function () {\n                        resolve();\n                    };\n\n                    transaction.onabort = transaction.onerror = function () {\n                        var err = req.error ? req.error : req.transaction.error;\n                        reject(err);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction length(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.count();\n\n                    req.onsuccess = function () {\n                        resolve(req.result);\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction key(n, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        if (n < 0) {\n            resolve(null);\n\n            return;\n        }\n\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var advanced = false;\n                    var req = store.openKeyCursor();\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n                        if (!cursor) {\n                            // this means there weren't enough keys\n                            resolve(null);\n\n                            return;\n                        }\n\n                        if (n === 0) {\n                            // We have the first key, return it if that's what they\n                            // wanted.\n                            resolve(cursor.key);\n                        } else {\n                            if (!advanced) {\n                                // Otherwise, ask the cursor to skip ahead n\n                                // records.\n                                advanced = true;\n                                cursor.advance(n);\n                            } else {\n                                // When we get here, we've got the nth key.\n                                resolve(cursor.key);\n                            }\n                        }\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            createTransaction(self._dbInfo, READ_ONLY, function (err, transaction) {\n                if (err) {\n                    return reject(err);\n                }\n\n                try {\n                    var store = transaction.objectStore(self._dbInfo.storeName);\n                    var req = store.openKeyCursor();\n                    var keys = [];\n\n                    req.onsuccess = function () {\n                        var cursor = req.result;\n\n                        if (!cursor) {\n                            resolve(keys);\n                            return;\n                        }\n\n                        keys.push(cursor.key);\n                        cursor[\"continue\"]();\n                    };\n\n                    req.onerror = function () {\n                        reject(req.error);\n                    };\n                } catch (e) {\n                    reject(e);\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction dropInstance(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    var currentConfig = this.config();\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        var isCurrentDb = options.name === currentConfig.name && self._dbInfo.db;\n\n        var dbPromise = isCurrentDb ? Promise$1.resolve(self._dbInfo.db) : _getOriginalConnection(options).then(function (db) {\n            var dbContext = dbContexts[options.name];\n            var forages = dbContext.forages;\n            dbContext.db = db;\n            for (var i = 0; i < forages.length; i++) {\n                forages[i]._dbInfo.db = db;\n            }\n            return db;\n        });\n\n        if (!options.storeName) {\n            promise = dbPromise.then(function (db) {\n                _deferReadiness(options);\n\n                var dbContext = dbContexts[options.name];\n                var forages = dbContext.forages;\n\n                db.close();\n                for (var i = 0; i < forages.length; i++) {\n                    var forage = forages[i];\n                    forage._dbInfo.db = null;\n                }\n\n                var dropDBPromise = new Promise$1(function (resolve, reject) {\n                    var req = idb.deleteDatabase(options.name);\n\n                    req.onerror = function () {\n                        var db = req.result;\n                        if (db) {\n                            db.close();\n                        }\n                        reject(req.error);\n                    };\n\n                    req.onblocked = function () {\n                        // Closing all open connections in onversionchange handler should prevent this situation, but if\n                        // we do get here, it just means the request remains pending - eventually it will succeed or error\n                        console.warn('dropInstance blocked for database \"' + options.name + '\" until all open connections are closed');\n                    };\n\n                    req.onsuccess = function () {\n                        var db = req.result;\n                        if (db) {\n                            db.close();\n                        }\n                        resolve(db);\n                    };\n                });\n\n                return dropDBPromise.then(function (db) {\n                    dbContext.db = db;\n                    for (var i = 0; i < forages.length; i++) {\n                        var _forage = forages[i];\n                        _advanceReadiness(_forage._dbInfo);\n                    }\n                })[\"catch\"](function (err) {\n                    (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                    throw err;\n                });\n            });\n        } else {\n            promise = dbPromise.then(function (db) {\n                if (!db.objectStoreNames.contains(options.storeName)) {\n                    return;\n                }\n\n                var newVersion = db.version + 1;\n\n                _deferReadiness(options);\n\n                var dbContext = dbContexts[options.name];\n                var forages = dbContext.forages;\n\n                db.close();\n                for (var i = 0; i < forages.length; i++) {\n                    var forage = forages[i];\n                    forage._dbInfo.db = null;\n                    forage._dbInfo.version = newVersion;\n                }\n\n                var dropObjectPromise = new Promise$1(function (resolve, reject) {\n                    var req = idb.open(options.name, newVersion);\n\n                    req.onerror = function (err) {\n                        var db = req.result;\n                        db.close();\n                        reject(err);\n                    };\n\n                    req.onupgradeneeded = function () {\n                        var db = req.result;\n                        db.deleteObjectStore(options.storeName);\n                    };\n\n                    req.onsuccess = function () {\n                        var db = req.result;\n                        db.close();\n                        resolve(db);\n                    };\n                });\n\n                return dropObjectPromise.then(function (db) {\n                    dbContext.db = db;\n                    for (var j = 0; j < forages.length; j++) {\n                        var _forage2 = forages[j];\n                        _forage2._dbInfo.db = db;\n                        _advanceReadiness(_forage2._dbInfo);\n                    }\n                })[\"catch\"](function (err) {\n                    (_rejectReadiness(options, err) || Promise$1.resolve())[\"catch\"](function () {});\n                    throw err;\n                });\n            });\n        }\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar asyncStorage = {\n    _driver: 'asyncStorage',\n    _initStorage: _initStorage,\n    _support: isIndexedDBValid(),\n    iterate: iterate,\n    getItem: getItem,\n    setItem: setItem,\n    removeItem: removeItem,\n    clear: clear,\n    length: length,\n    key: key,\n    keys: keys,\n    dropInstance: dropInstance\n};\n\nfunction isWebSQLValid() {\n    return typeof openDatabase === 'function';\n}\n\n// Sadly, the best way to save binary data in WebSQL/localStorage is serializing\n// it to Base64, so this is how we store it to prevent very strange errors with less\n// verbose ways of binary <-> string data storage.\nvar BASE_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n\nvar BLOB_TYPE_PREFIX = '~~local_forage_type~';\nvar BLOB_TYPE_PREFIX_REGEX = /^~~local_forage_type~([^~]+)~/;\n\nvar SERIALIZED_MARKER = '__lfsc__:';\nvar SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER.length;\n\n// OMG the serializations!\nvar TYPE_ARRAYBUFFER = 'arbf';\nvar TYPE_BLOB = 'blob';\nvar TYPE_INT8ARRAY = 'si08';\nvar TYPE_UINT8ARRAY = 'ui08';\nvar TYPE_UINT8CLAMPEDARRAY = 'uic8';\nvar TYPE_INT16ARRAY = 'si16';\nvar TYPE_INT32ARRAY = 'si32';\nvar TYPE_UINT16ARRAY = 'ur16';\nvar TYPE_UINT32ARRAY = 'ui32';\nvar TYPE_FLOAT32ARRAY = 'fl32';\nvar TYPE_FLOAT64ARRAY = 'fl64';\nvar TYPE_SERIALIZED_MARKER_LENGTH = SERIALIZED_MARKER_LENGTH + TYPE_ARRAYBUFFER.length;\n\nvar toString$1 = Object.prototype.toString;\n\nfunction stringToBuffer(serializedString) {\n    // Fill the string into a ArrayBuffer.\n    var bufferLength = serializedString.length * 0.75;\n    var len = serializedString.length;\n    var i;\n    var p = 0;\n    var encoded1, encoded2, encoded3, encoded4;\n\n    if (serializedString[serializedString.length - 1] === '=') {\n        bufferLength--;\n        if (serializedString[serializedString.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n\n    var buffer = new ArrayBuffer(bufferLength);\n    var bytes = new Uint8Array(buffer);\n\n    for (i = 0; i < len; i += 4) {\n        encoded1 = BASE_CHARS.indexOf(serializedString[i]);\n        encoded2 = BASE_CHARS.indexOf(serializedString[i + 1]);\n        encoded3 = BASE_CHARS.indexOf(serializedString[i + 2]);\n        encoded4 = BASE_CHARS.indexOf(serializedString[i + 3]);\n\n        /*jslint bitwise: true */\n        bytes[p++] = encoded1 << 2 | encoded2 >> 4;\n        bytes[p++] = (encoded2 & 15) << 4 | encoded3 >> 2;\n        bytes[p++] = (encoded3 & 3) << 6 | encoded4 & 63;\n    }\n    return buffer;\n}\n\n// Converts a buffer to a string to store, serialized, in the backend\n// storage library.\nfunction bufferToString(buffer) {\n    // base64-arraybuffer\n    var bytes = new Uint8Array(buffer);\n    var base64String = '';\n    var i;\n\n    for (i = 0; i < bytes.length; i += 3) {\n        /*jslint bitwise: true */\n        base64String += BASE_CHARS[bytes[i] >> 2];\n        base64String += BASE_CHARS[(bytes[i] & 3) << 4 | bytes[i + 1] >> 4];\n        base64String += BASE_CHARS[(bytes[i + 1] & 15) << 2 | bytes[i + 2] >> 6];\n        base64String += BASE_CHARS[bytes[i + 2] & 63];\n    }\n\n    if (bytes.length % 3 === 2) {\n        base64String = base64String.substring(0, base64String.length - 1) + '=';\n    } else if (bytes.length % 3 === 1) {\n        base64String = base64String.substring(0, base64String.length - 2) + '==';\n    }\n\n    return base64String;\n}\n\n// Serialize a value, afterwards executing a callback (which usually\n// instructs the `setItem()` callback/promise to be executed). This is how\n// we store binary data with localStorage.\nfunction serialize(value, callback) {\n    var valueType = '';\n    if (value) {\n        valueType = toString$1.call(value);\n    }\n\n    // Cannot use `value instanceof ArrayBuffer` or such here, as these\n    // checks fail when running the tests using casper.js...\n    //\n    // TODO: See why those tests fail and use a better solution.\n    if (value && (valueType === '[object ArrayBuffer]' || value.buffer && toString$1.call(value.buffer) === '[object ArrayBuffer]')) {\n        // Convert binary arrays to a string and prefix the string with\n        // a special marker.\n        var buffer;\n        var marker = SERIALIZED_MARKER;\n\n        if (value instanceof ArrayBuffer) {\n            buffer = value;\n            marker += TYPE_ARRAYBUFFER;\n        } else {\n            buffer = value.buffer;\n\n            if (valueType === '[object Int8Array]') {\n                marker += TYPE_INT8ARRAY;\n            } else if (valueType === '[object Uint8Array]') {\n                marker += TYPE_UINT8ARRAY;\n            } else if (valueType === '[object Uint8ClampedArray]') {\n                marker += TYPE_UINT8CLAMPEDARRAY;\n            } else if (valueType === '[object Int16Array]') {\n                marker += TYPE_INT16ARRAY;\n            } else if (valueType === '[object Uint16Array]') {\n                marker += TYPE_UINT16ARRAY;\n            } else if (valueType === '[object Int32Array]') {\n                marker += TYPE_INT32ARRAY;\n            } else if (valueType === '[object Uint32Array]') {\n                marker += TYPE_UINT32ARRAY;\n            } else if (valueType === '[object Float32Array]') {\n                marker += TYPE_FLOAT32ARRAY;\n            } else if (valueType === '[object Float64Array]') {\n                marker += TYPE_FLOAT64ARRAY;\n            } else {\n                callback(new Error('Failed to get type for BinaryArray'));\n            }\n        }\n\n        callback(marker + bufferToString(buffer));\n    } else if (valueType === '[object Blob]') {\n        // Conver the blob to a binaryArray and then to a string.\n        var fileReader = new FileReader();\n\n        fileReader.onload = function () {\n            // Backwards-compatible prefix for the blob type.\n            var str = BLOB_TYPE_PREFIX + value.type + '~' + bufferToString(this.result);\n\n            callback(SERIALIZED_MARKER + TYPE_BLOB + str);\n        };\n\n        fileReader.readAsArrayBuffer(value);\n    } else {\n        try {\n            callback(JSON.stringify(value));\n        } catch (e) {\n            console.error(\"Couldn't convert value into a JSON string: \", value);\n\n            callback(null, e);\n        }\n    }\n}\n\n// Deserialize data we've inserted into a value column/field. We place\n// special markers into our strings to mark them as encoded; this isn't\n// as nice as a meta field, but it's the only sane thing we can do whilst\n// keeping localStorage support intact.\n//\n// Oftentimes this will just deserialize JSON content, but if we have a\n// special marker (SERIALIZED_MARKER, defined above), we will extract\n// some kind of arraybuffer/binary data/typed array out of the string.\nfunction deserialize(value) {\n    // If we haven't marked this string as being specially serialized (i.e.\n    // something other than serialized JSON), we can just return it and be\n    // done with it.\n    if (value.substring(0, SERIALIZED_MARKER_LENGTH) !== SERIALIZED_MARKER) {\n        return JSON.parse(value);\n    }\n\n    // The following code deals with deserializing some kind of Blob or\n    // TypedArray. First we separate out the type of data we're dealing\n    // with from the data itself.\n    var serializedString = value.substring(TYPE_SERIALIZED_MARKER_LENGTH);\n    var type = value.substring(SERIALIZED_MARKER_LENGTH, TYPE_SERIALIZED_MARKER_LENGTH);\n\n    var blobType;\n    // Backwards-compatible blob type serialization strategy.\n    // DBs created with older versions of localForage will simply not have the blob type.\n    if (type === TYPE_BLOB && BLOB_TYPE_PREFIX_REGEX.test(serializedString)) {\n        var matcher = serializedString.match(BLOB_TYPE_PREFIX_REGEX);\n        blobType = matcher[1];\n        serializedString = serializedString.substring(matcher[0].length);\n    }\n    var buffer = stringToBuffer(serializedString);\n\n    // Return the right type based on the code/type set during\n    // serialization.\n    switch (type) {\n        case TYPE_ARRAYBUFFER:\n            return buffer;\n        case TYPE_BLOB:\n            return createBlob([buffer], { type: blobType });\n        case TYPE_INT8ARRAY:\n            return new Int8Array(buffer);\n        case TYPE_UINT8ARRAY:\n            return new Uint8Array(buffer);\n        case TYPE_UINT8CLAMPEDARRAY:\n            return new Uint8ClampedArray(buffer);\n        case TYPE_INT16ARRAY:\n            return new Int16Array(buffer);\n        case TYPE_UINT16ARRAY:\n            return new Uint16Array(buffer);\n        case TYPE_INT32ARRAY:\n            return new Int32Array(buffer);\n        case TYPE_UINT32ARRAY:\n            return new Uint32Array(buffer);\n        case TYPE_FLOAT32ARRAY:\n            return new Float32Array(buffer);\n        case TYPE_FLOAT64ARRAY:\n            return new Float64Array(buffer);\n        default:\n            throw new Error('Unkown type: ' + type);\n    }\n}\n\nvar localforageSerializer = {\n    serialize: serialize,\n    deserialize: deserialize,\n    stringToBuffer: stringToBuffer,\n    bufferToString: bufferToString\n};\n\n/*\n * Includes code from:\n *\n * base64-arraybuffer\n * https://github.com/niklasvh/base64-arraybuffer\n *\n * Copyright (c) 2012 Niklas von Hertzen\n * Licensed under the MIT license.\n */\n\nfunction createDbTable(t, dbInfo, callback, errorCallback) {\n    t.executeSql('CREATE TABLE IF NOT EXISTS ' + dbInfo.storeName + ' ' + '(id INTEGER PRIMARY KEY, key unique, value)', [], callback, errorCallback);\n}\n\n// Open the WebSQL database (automatically creates one if one didn't\n// previously exist), using any options set in the config.\nfunction _initStorage$1(options) {\n    var self = this;\n    var dbInfo = {\n        db: null\n    };\n\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = typeof options[i] !== 'string' ? options[i].toString() : options[i];\n        }\n    }\n\n    var dbInfoPromise = new Promise$1(function (resolve, reject) {\n        // Open the database; the openDatabase API will automatically\n        // create it for us if it doesn't exist.\n        try {\n            dbInfo.db = openDatabase(dbInfo.name, String(dbInfo.version), dbInfo.description, dbInfo.size);\n        } catch (e) {\n            return reject(e);\n        }\n\n        // Create our key/value table if it doesn't exist.\n        dbInfo.db.transaction(function (t) {\n            createDbTable(t, dbInfo, function () {\n                self._dbInfo = dbInfo;\n                resolve();\n            }, function (t, error) {\n                reject(error);\n            });\n        }, reject);\n    });\n\n    dbInfo.serializer = localforageSerializer;\n    return dbInfoPromise;\n}\n\nfunction tryExecuteSql(t, dbInfo, sqlStatement, args, callback, errorCallback) {\n    t.executeSql(sqlStatement, args, callback, function (t, error) {\n        if (error.code === error.SYNTAX_ERR) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name = ?\", [dbInfo.storeName], function (t, results) {\n                if (!results.rows.length) {\n                    // if the table is missing (was deleted)\n                    // re-create it table and retry\n                    createDbTable(t, dbInfo, function () {\n                        t.executeSql(sqlStatement, args, callback, errorCallback);\n                    }, errorCallback);\n                } else {\n                    errorCallback(t, error);\n                }\n            }, errorCallback);\n        } else {\n            errorCallback(t, error);\n        }\n    }, errorCallback);\n}\n\nfunction getItem$1(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName + ' WHERE key = ? LIMIT 1', [key], function (t, results) {\n                    var result = results.rows.length ? results.rows.item(0).value : null;\n\n                    // Check to see if this is serialized content we need to\n                    // unpack.\n                    if (result) {\n                        result = dbInfo.serializer.deserialize(result);\n                    }\n\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction iterate$1(iterator, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT * FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var rows = results.rows;\n                    var length = rows.length;\n\n                    for (var i = 0; i < length; i++) {\n                        var item = rows.item(i);\n                        var result = item.value;\n\n                        // Check to see if this is serialized content\n                        // we need to unpack.\n                        if (result) {\n                            result = dbInfo.serializer.deserialize(result);\n                        }\n\n                        result = iterator(result, item.key, i + 1);\n\n                        // void(0) prevents problems with redefinition\n                        // of `undefined`.\n                        if (result !== void 0) {\n                            resolve(result);\n                            return;\n                        }\n                    }\n\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction _setItem(key, value, callback, retriesLeft) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            // The localStorage API doesn't return undefined values in an\n            // \"expected\" way, so undefined is always cast to null in all\n            // drivers. See: https://github.com/mozilla/localForage/pull/42\n            if (value === undefined) {\n                value = null;\n            }\n\n            // Save the original value to pass to the callback.\n            var originalValue = value;\n\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n                if (error) {\n                    reject(error);\n                } else {\n                    dbInfo.db.transaction(function (t) {\n                        tryExecuteSql(t, dbInfo, 'INSERT OR REPLACE INTO ' + dbInfo.storeName + ' ' + '(key, value) VALUES (?, ?)', [key, value], function () {\n                            resolve(originalValue);\n                        }, function (t, error) {\n                            reject(error);\n                        });\n                    }, function (sqlError) {\n                        // The transaction failed; check\n                        // to see if it's a quota error.\n                        if (sqlError.code === sqlError.QUOTA_ERR) {\n                            // We reject the callback outright for now, but\n                            // it's worth trying to re-run the transaction.\n                            // Even if the user accepts the prompt to use\n                            // more storage on Safari, this error will\n                            // be called.\n                            //\n                            // Try to re-run the transaction.\n                            if (retriesLeft > 0) {\n                                resolve(_setItem.apply(self, [key, originalValue, callback, retriesLeft - 1]));\n                                return;\n                            }\n                            reject(sqlError);\n                        }\n                    });\n                }\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction setItem$1(key, value, callback) {\n    return _setItem.apply(this, [key, value, callback, 1]);\n}\n\nfunction removeItem$1(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName + ' WHERE key = ?', [key], function () {\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Deletes every item in the table.\n// TODO: Find out if this resets the AUTO_INCREMENT number.\nfunction clear$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'DELETE FROM ' + dbInfo.storeName, [], function () {\n                    resolve();\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Does a simple `COUNT(key)` to get the number of items stored in\n// localForage.\nfunction length$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                // Ahhh, SQL makes this one soooooo easy.\n                tryExecuteSql(t, dbInfo, 'SELECT COUNT(key) as c FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var result = results.rows.item(0).c;\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Return the key located at key index X; essentially gets the key from a\n// `WHERE id = ?`. This is the most efficient way I can think to implement\n// this rarely-used (in my experience) part of the API, but it can seem\n// inconsistent, because we do `INSERT OR REPLACE INTO` on `setItem()`, so\n// the ID of each key will change every time it's updated. Perhaps a stored\n// procedure for the `setItem()` SQL would solve this problem?\n// TODO: Don't change ID on `setItem()`.\nfunction key$1(n, callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName + ' WHERE id = ? LIMIT 1', [n + 1], function (t, results) {\n                    var result = results.rows.length ? results.rows.item(0).key : null;\n                    resolve(result);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys$1(callback) {\n    var self = this;\n\n    var promise = new Promise$1(function (resolve, reject) {\n        self.ready().then(function () {\n            var dbInfo = self._dbInfo;\n            dbInfo.db.transaction(function (t) {\n                tryExecuteSql(t, dbInfo, 'SELECT key FROM ' + dbInfo.storeName, [], function (t, results) {\n                    var keys = [];\n\n                    for (var i = 0; i < results.rows.length; i++) {\n                        keys.push(results.rows.item(i).key);\n                    }\n\n                    resolve(keys);\n                }, function (t, error) {\n                    reject(error);\n                });\n            });\n        })[\"catch\"](reject);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// https://www.w3.org/TR/webdatabase/#databases\n// > There is no way to enumerate or delete the databases available for an origin from this API.\nfunction getAllStoreNames(db) {\n    return new Promise$1(function (resolve, reject) {\n        db.transaction(function (t) {\n            t.executeSql('SELECT name FROM sqlite_master ' + \"WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'\", [], function (t, results) {\n                var storeNames = [];\n\n                for (var i = 0; i < results.rows.length; i++) {\n                    storeNames.push(results.rows.item(i).name);\n                }\n\n                resolve({\n                    db: db,\n                    storeNames: storeNames\n                });\n            }, function (t, error) {\n                reject(error);\n            });\n        }, function (sqlError) {\n            reject(sqlError);\n        });\n    });\n}\n\nfunction dropInstance$1(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    var currentConfig = this.config();\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        promise = new Promise$1(function (resolve) {\n            var db;\n            if (options.name === currentConfig.name) {\n                // use the db reference of the current instance\n                db = self._dbInfo.db;\n            } else {\n                db = openDatabase(options.name, '', '', 0);\n            }\n\n            if (!options.storeName) {\n                // drop all database tables\n                resolve(getAllStoreNames(db));\n            } else {\n                resolve({\n                    db: db,\n                    storeNames: [options.storeName]\n                });\n            }\n        }).then(function (operationInfo) {\n            return new Promise$1(function (resolve, reject) {\n                operationInfo.db.transaction(function (t) {\n                    function dropTable(storeName) {\n                        return new Promise$1(function (resolve, reject) {\n                            t.executeSql('DROP TABLE IF EXISTS ' + storeName, [], function () {\n                                resolve();\n                            }, function (t, error) {\n                                reject(error);\n                            });\n                        });\n                    }\n\n                    var operations = [];\n                    for (var i = 0, len = operationInfo.storeNames.length; i < len; i++) {\n                        operations.push(dropTable(operationInfo.storeNames[i]));\n                    }\n\n                    Promise$1.all(operations).then(function () {\n                        resolve();\n                    })[\"catch\"](function (e) {\n                        reject(e);\n                    });\n                }, function (sqlError) {\n                    reject(sqlError);\n                });\n            });\n        });\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar webSQLStorage = {\n    _driver: 'webSQLStorage',\n    _initStorage: _initStorage$1,\n    _support: isWebSQLValid(),\n    iterate: iterate$1,\n    getItem: getItem$1,\n    setItem: setItem$1,\n    removeItem: removeItem$1,\n    clear: clear$1,\n    length: length$1,\n    key: key$1,\n    keys: keys$1,\n    dropInstance: dropInstance$1\n};\n\nfunction isLocalStorageValid() {\n    try {\n        return typeof localStorage !== 'undefined' && 'setItem' in localStorage &&\n        // in IE8 typeof localStorage.setItem === 'object'\n        !!localStorage.setItem;\n    } catch (e) {\n        return false;\n    }\n}\n\nfunction _getKeyPrefix(options, defaultConfig) {\n    var keyPrefix = options.name + '/';\n\n    if (options.storeName !== defaultConfig.storeName) {\n        keyPrefix += options.storeName + '/';\n    }\n    return keyPrefix;\n}\n\n// Check if localStorage throws when saving an item\nfunction checkIfLocalStorageThrows() {\n    var localStorageTestKey = '_localforage_support_test';\n\n    try {\n        localStorage.setItem(localStorageTestKey, true);\n        localStorage.removeItem(localStorageTestKey);\n\n        return false;\n    } catch (e) {\n        return true;\n    }\n}\n\n// Check if localStorage is usable and allows to save an item\n// This method checks if localStorage is usable in Safari Private Browsing\n// mode, or in any other case where the available quota for localStorage\n// is 0 and there wasn't any saved items yet.\nfunction _isLocalStorageUsable() {\n    return !checkIfLocalStorageThrows() || localStorage.length > 0;\n}\n\n// Config the localStorage backend, using options set in the config.\nfunction _initStorage$2(options) {\n    var self = this;\n    var dbInfo = {};\n    if (options) {\n        for (var i in options) {\n            dbInfo[i] = options[i];\n        }\n    }\n\n    dbInfo.keyPrefix = _getKeyPrefix(options, self._defaultConfig);\n\n    if (!_isLocalStorageUsable()) {\n        return Promise$1.reject();\n    }\n\n    self._dbInfo = dbInfo;\n    dbInfo.serializer = localforageSerializer;\n\n    return Promise$1.resolve();\n}\n\n// Remove all keys from the datastore, effectively destroying all data in\n// the app's key/value store!\nfunction clear$2(callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var keyPrefix = self._dbInfo.keyPrefix;\n\n        for (var i = localStorage.length - 1; i >= 0; i--) {\n            var key = localStorage.key(i);\n\n            if (key.indexOf(keyPrefix) === 0) {\n                localStorage.removeItem(key);\n            }\n        }\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Retrieve an item from the store. Unlike the original async_storage\n// library in Gaia, we don't modify return values at all. If a key's value\n// is `undefined`, we pass that value to the callback function.\nfunction getItem$2(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var result = localStorage.getItem(dbInfo.keyPrefix + key);\n\n        // If a result was found, parse it from the serialized\n        // string into a JS object. If result isn't truthy, the key\n        // is likely undefined and we'll pass it straight to the\n        // callback.\n        if (result) {\n            result = dbInfo.serializer.deserialize(result);\n        }\n\n        return result;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Iterate over all items in the store.\nfunction iterate$2(iterator, callback) {\n    var self = this;\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var keyPrefix = dbInfo.keyPrefix;\n        var keyPrefixLength = keyPrefix.length;\n        var length = localStorage.length;\n\n        // We use a dedicated iterator instead of the `i` variable below\n        // so other keys we fetch in localStorage aren't counted in\n        // the `iterationNumber` argument passed to the `iterate()`\n        // callback.\n        //\n        // See: github.com/mozilla/localForage/pull/435#discussion_r38061530\n        var iterationNumber = 1;\n\n        for (var i = 0; i < length; i++) {\n            var key = localStorage.key(i);\n            if (key.indexOf(keyPrefix) !== 0) {\n                continue;\n            }\n            var value = localStorage.getItem(key);\n\n            // If a result was found, parse it from the serialized\n            // string into a JS object. If result isn't truthy, the\n            // key is likely undefined and we'll pass it straight\n            // to the iterator.\n            if (value) {\n                value = dbInfo.serializer.deserialize(value);\n            }\n\n            value = iterator(value, key.substring(keyPrefixLength), iterationNumber++);\n\n            if (value !== void 0) {\n                return value;\n            }\n        }\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Same as localStorage's key() method, except takes a callback.\nfunction key$2(n, callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var result;\n        try {\n            result = localStorage.key(n);\n        } catch (error) {\n            result = null;\n        }\n\n        // Remove the prefix from the key, if a key is found.\n        if (result) {\n            result = result.substring(dbInfo.keyPrefix.length);\n        }\n\n        return result;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction keys$2(callback) {\n    var self = this;\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        var length = localStorage.length;\n        var keys = [];\n\n        for (var i = 0; i < length; i++) {\n            var itemKey = localStorage.key(i);\n            if (itemKey.indexOf(dbInfo.keyPrefix) === 0) {\n                keys.push(itemKey.substring(dbInfo.keyPrefix.length));\n            }\n        }\n\n        return keys;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Supply the number of keys in the datastore to the callback function.\nfunction length$2(callback) {\n    var self = this;\n    var promise = self.keys().then(function (keys) {\n        return keys.length;\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Remove an item from the store, nice and simple.\nfunction removeItem$2(key, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        var dbInfo = self._dbInfo;\n        localStorage.removeItem(dbInfo.keyPrefix + key);\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\n// Set a key's value and run an optional callback once the value is set.\n// Unlike Gaia's implementation, the callback function is passed the value,\n// in case you want to operate on that value only after you're sure it\n// saved, or something like that.\nfunction setItem$2(key, value, callback) {\n    var self = this;\n\n    key = normalizeKey(key);\n\n    var promise = self.ready().then(function () {\n        // Convert undefined values to null.\n        // https://github.com/mozilla/localForage/pull/42\n        if (value === undefined) {\n            value = null;\n        }\n\n        // Save the original value to pass to the callback.\n        var originalValue = value;\n\n        return new Promise$1(function (resolve, reject) {\n            var dbInfo = self._dbInfo;\n            dbInfo.serializer.serialize(value, function (value, error) {\n                if (error) {\n                    reject(error);\n                } else {\n                    try {\n                        localStorage.setItem(dbInfo.keyPrefix + key, value);\n                        resolve(originalValue);\n                    } catch (e) {\n                        // localStorage capacity exceeded.\n                        // TODO: Make this a specific error/event.\n                        if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {\n                            reject(e);\n                        }\n                        reject(e);\n                    }\n                }\n            });\n        });\n    });\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nfunction dropInstance$2(options, callback) {\n    callback = getCallback.apply(this, arguments);\n\n    options = typeof options !== 'function' && options || {};\n    if (!options.name) {\n        var currentConfig = this.config();\n        options.name = options.name || currentConfig.name;\n        options.storeName = options.storeName || currentConfig.storeName;\n    }\n\n    var self = this;\n    var promise;\n    if (!options.name) {\n        promise = Promise$1.reject('Invalid arguments');\n    } else {\n        promise = new Promise$1(function (resolve) {\n            if (!options.storeName) {\n                resolve(options.name + '/');\n            } else {\n                resolve(_getKeyPrefix(options, self._defaultConfig));\n            }\n        }).then(function (keyPrefix) {\n            for (var i = localStorage.length - 1; i >= 0; i--) {\n                var key = localStorage.key(i);\n\n                if (key.indexOf(keyPrefix) === 0) {\n                    localStorage.removeItem(key);\n                }\n            }\n        });\n    }\n\n    executeCallback(promise, callback);\n    return promise;\n}\n\nvar localStorageWrapper = {\n    _driver: 'localStorageWrapper',\n    _initStorage: _initStorage$2,\n    _support: isLocalStorageValid(),\n    iterate: iterate$2,\n    getItem: getItem$2,\n    setItem: setItem$2,\n    removeItem: removeItem$2,\n    clear: clear$2,\n    length: length$2,\n    key: key$2,\n    keys: keys$2,\n    dropInstance: dropInstance$2\n};\n\nvar sameValue = function sameValue(x, y) {\n    return x === y || typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y);\n};\n\nvar includes = function includes(array, searchElement) {\n    var len = array.length;\n    var i = 0;\n    while (i < len) {\n        if (sameValue(array[i], searchElement)) {\n            return true;\n        }\n        i++;\n    }\n\n    return false;\n};\n\nvar isArray = Array.isArray || function (arg) {\n    return Object.prototype.toString.call(arg) === '[object Array]';\n};\n\n// Drivers are stored here when `defineDriver()` is called.\n// They are shared across all instances of localForage.\nvar DefinedDrivers = {};\n\nvar DriverSupport = {};\n\nvar DefaultDrivers = {\n    INDEXEDDB: asyncStorage,\n    WEBSQL: webSQLStorage,\n    LOCALSTORAGE: localStorageWrapper\n};\n\nvar DefaultDriverOrder = [DefaultDrivers.INDEXEDDB._driver, DefaultDrivers.WEBSQL._driver, DefaultDrivers.LOCALSTORAGE._driver];\n\nvar OptionalDriverMethods = ['dropInstance'];\n\nvar LibraryMethods = ['clear', 'getItem', 'iterate', 'key', 'keys', 'length', 'removeItem', 'setItem'].concat(OptionalDriverMethods);\n\nvar DefaultConfig = {\n    description: '',\n    driver: DefaultDriverOrder.slice(),\n    name: 'localforage',\n    // Default DB size is _JUST UNDER_ 5MB, as it's the highest size\n    // we can use without a prompt.\n    size: 4980736,\n    storeName: 'keyvaluepairs',\n    version: 1.0\n};\n\nfunction callWhenReady(localForageInstance, libraryMethod) {\n    localForageInstance[libraryMethod] = function () {\n        var _args = arguments;\n        return localForageInstance.ready().then(function () {\n            return localForageInstance[libraryMethod].apply(localForageInstance, _args);\n        });\n    };\n}\n\nfunction extend() {\n    for (var i = 1; i < arguments.length; i++) {\n        var arg = arguments[i];\n\n        if (arg) {\n            for (var _key in arg) {\n                if (arg.hasOwnProperty(_key)) {\n                    if (isArray(arg[_key])) {\n                        arguments[0][_key] = arg[_key].slice();\n                    } else {\n                        arguments[0][_key] = arg[_key];\n                    }\n                }\n            }\n        }\n    }\n\n    return arguments[0];\n}\n\nvar LocalForage = function () {\n    function LocalForage(options) {\n        _classCallCheck(this, LocalForage);\n\n        for (var driverTypeKey in DefaultDrivers) {\n            if (DefaultDrivers.hasOwnProperty(driverTypeKey)) {\n                var driver = DefaultDrivers[driverTypeKey];\n                var driverName = driver._driver;\n                this[driverTypeKey] = driverName;\n\n                if (!DefinedDrivers[driverName]) {\n                    // we don't need to wait for the promise,\n                    // since the default drivers can be defined\n                    // in a blocking manner\n                    this.defineDriver(driver);\n                }\n            }\n        }\n\n        this._defaultConfig = extend({}, DefaultConfig);\n        this._config = extend({}, this._defaultConfig, options);\n        this._driverSet = null;\n        this._initDriver = null;\n        this._ready = false;\n        this._dbInfo = null;\n\n        this._wrapLibraryMethodsWithReady();\n        this.setDriver(this._config.driver)[\"catch\"](function () {});\n    }\n\n    // Set any config values for localForage; can be called anytime before\n    // the first API call (e.g. `getItem`, `setItem`).\n    // We loop through options so we don't overwrite existing config\n    // values.\n\n\n    LocalForage.prototype.config = function config(options) {\n        // If the options argument is an object, we use it to set values.\n        // Otherwise, we return either a specified config value or all\n        // config values.\n        if ((typeof options === 'undefined' ? 'undefined' : _typeof(options)) === 'object') {\n            // If localforage is ready and fully initialized, we can't set\n            // any new configuration values. Instead, we return an error.\n            if (this._ready) {\n                return new Error(\"Can't call config() after localforage \" + 'has been used.');\n            }\n\n            for (var i in options) {\n                if (i === 'storeName') {\n                    options[i] = options[i].replace(/\\W/g, '_');\n                }\n\n                if (i === 'version' && typeof options[i] !== 'number') {\n                    return new Error('Database version must be a number.');\n                }\n\n                this._config[i] = options[i];\n            }\n\n            // after all config options are set and\n            // the driver option is used, try setting it\n            if ('driver' in options && options.driver) {\n                return this.setDriver(this._config.driver);\n            }\n\n            return true;\n        } else if (typeof options === 'string') {\n            return this._config[options];\n        } else {\n            return this._config;\n        }\n    };\n\n    // Used to define a custom driver, shared across all instances of\n    // localForage.\n\n\n    LocalForage.prototype.defineDriver = function defineDriver(driverObject, callback, errorCallback) {\n        var promise = new Promise$1(function (resolve, reject) {\n            try {\n                var driverName = driverObject._driver;\n                var complianceError = new Error('Custom driver not compliant; see ' + 'https://mozilla.github.io/localForage/#definedriver');\n\n                // A driver name should be defined and not overlap with the\n                // library-defined, default drivers.\n                if (!driverObject._driver) {\n                    reject(complianceError);\n                    return;\n                }\n\n                var driverMethods = LibraryMethods.concat('_initStorage');\n                for (var i = 0, len = driverMethods.length; i < len; i++) {\n                    var driverMethodName = driverMethods[i];\n\n                    // when the property is there,\n                    // it should be a method even when optional\n                    var isRequired = !includes(OptionalDriverMethods, driverMethodName);\n                    if ((isRequired || driverObject[driverMethodName]) && typeof driverObject[driverMethodName] !== 'function') {\n                        reject(complianceError);\n                        return;\n                    }\n                }\n\n                var configureMissingMethods = function configureMissingMethods() {\n                    var methodNotImplementedFactory = function methodNotImplementedFactory(methodName) {\n                        return function () {\n                            var error = new Error('Method ' + methodName + ' is not implemented by the current driver');\n                            var promise = Promise$1.reject(error);\n                            executeCallback(promise, arguments[arguments.length - 1]);\n                            return promise;\n                        };\n                    };\n\n                    for (var _i = 0, _len = OptionalDriverMethods.length; _i < _len; _i++) {\n                        var optionalDriverMethod = OptionalDriverMethods[_i];\n                        if (!driverObject[optionalDriverMethod]) {\n                            driverObject[optionalDriverMethod] = methodNotImplementedFactory(optionalDriverMethod);\n                        }\n                    }\n                };\n\n                configureMissingMethods();\n\n                var setDriverSupport = function setDriverSupport(support) {\n                    if (DefinedDrivers[driverName]) {\n                        console.info('Redefining LocalForage driver: ' + driverName);\n                    }\n                    DefinedDrivers[driverName] = driverObject;\n                    DriverSupport[driverName] = support;\n                    // don't use a then, so that we can define\n                    // drivers that have simple _support methods\n                    // in a blocking manner\n                    resolve();\n                };\n\n                if ('_support' in driverObject) {\n                    if (driverObject._support && typeof driverObject._support === 'function') {\n                        driverObject._support().then(setDriverSupport, reject);\n                    } else {\n                        setDriverSupport(!!driverObject._support);\n                    }\n                } else {\n                    setDriverSupport(true);\n                }\n            } catch (e) {\n                reject(e);\n            }\n        });\n\n        executeTwoCallbacks(promise, callback, errorCallback);\n        return promise;\n    };\n\n    LocalForage.prototype.driver = function driver() {\n        return this._driver || null;\n    };\n\n    LocalForage.prototype.getDriver = function getDriver(driverName, callback, errorCallback) {\n        var getDriverPromise = DefinedDrivers[driverName] ? Promise$1.resolve(DefinedDrivers[driverName]) : Promise$1.reject(new Error('Driver not found.'));\n\n        executeTwoCallbacks(getDriverPromise, callback, errorCallback);\n        return getDriverPromise;\n    };\n\n    LocalForage.prototype.getSerializer = function getSerializer(callback) {\n        var serializerPromise = Promise$1.resolve(localforageSerializer);\n        executeTwoCallbacks(serializerPromise, callback);\n        return serializerPromise;\n    };\n\n    LocalForage.prototype.ready = function ready(callback) {\n        var self = this;\n\n        var promise = self._driverSet.then(function () {\n            if (self._ready === null) {\n                self._ready = self._initDriver();\n            }\n\n            return self._ready;\n        });\n\n        executeTwoCallbacks(promise, callback, callback);\n        return promise;\n    };\n\n    LocalForage.prototype.setDriver = function setDriver(drivers, callback, errorCallback) {\n        var self = this;\n\n        if (!isArray(drivers)) {\n            drivers = [drivers];\n        }\n\n        var supportedDrivers = this._getSupportedDrivers(drivers);\n\n        function setDriverToConfig() {\n            self._config.driver = self.driver();\n        }\n\n        function extendSelfWithDriver(driver) {\n            self._extend(driver);\n            setDriverToConfig();\n\n            self._ready = self._initStorage(self._config);\n            return self._ready;\n        }\n\n        function initDriver(supportedDrivers) {\n            return function () {\n                var currentDriverIndex = 0;\n\n                function driverPromiseLoop() {\n                    while (currentDriverIndex < supportedDrivers.length) {\n                        var driverName = supportedDrivers[currentDriverIndex];\n                        currentDriverIndex++;\n\n                        self._dbInfo = null;\n                        self._ready = null;\n\n                        return self.getDriver(driverName).then(extendSelfWithDriver)[\"catch\"](driverPromiseLoop);\n                    }\n\n                    setDriverToConfig();\n                    var error = new Error('No available storage method found.');\n                    self._driverSet = Promise$1.reject(error);\n                    return self._driverSet;\n                }\n\n                return driverPromiseLoop();\n            };\n        }\n\n        // There might be a driver initialization in progress\n        // so wait for it to finish in order to avoid a possible\n        // race condition to set _dbInfo\n        var oldDriverSetDone = this._driverSet !== null ? this._driverSet[\"catch\"](function () {\n            return Promise$1.resolve();\n        }) : Promise$1.resolve();\n\n        this._driverSet = oldDriverSetDone.then(function () {\n            var driverName = supportedDrivers[0];\n            self._dbInfo = null;\n            self._ready = null;\n\n            return self.getDriver(driverName).then(function (driver) {\n                self._driver = driver._driver;\n                setDriverToConfig();\n                self._wrapLibraryMethodsWithReady();\n                self._initDriver = initDriver(supportedDrivers);\n            });\n        })[\"catch\"](function () {\n            setDriverToConfig();\n            var error = new Error('No available storage method found.');\n            self._driverSet = Promise$1.reject(error);\n            return self._driverSet;\n        });\n\n        executeTwoCallbacks(this._driverSet, callback, errorCallback);\n        return this._driverSet;\n    };\n\n    LocalForage.prototype.supports = function supports(driverName) {\n        return !!DriverSupport[driverName];\n    };\n\n    LocalForage.prototype._extend = function _extend(libraryMethodsAndProperties) {\n        extend(this, libraryMethodsAndProperties);\n    };\n\n    LocalForage.prototype._getSupportedDrivers = function _getSupportedDrivers(drivers) {\n        var supportedDrivers = [];\n        for (var i = 0, len = drivers.length; i < len; i++) {\n            var driverName = drivers[i];\n            if (this.supports(driverName)) {\n                supportedDrivers.push(driverName);\n            }\n        }\n        return supportedDrivers;\n    };\n\n    LocalForage.prototype._wrapLibraryMethodsWithReady = function _wrapLibraryMethodsWithReady() {\n        // Add a stub for each driver API method that delays the call to the\n        // corresponding driver method until localForage is ready. These stubs\n        // will be replaced by the driver methods as soon as the driver is\n        // loaded, so there is no performance impact.\n        for (var i = 0, len = LibraryMethods.length; i < len; i++) {\n            callWhenReady(this, LibraryMethods[i]);\n        }\n    };\n\n    LocalForage.prototype.createInstance = function createInstance(options) {\n        return new LocalForage(options);\n    };\n\n    return LocalForage;\n}();\n\n// The actual localForage object that we expose as a module or via a\n// global. It's extended by pulling in one of our other libraries.\n\n\nvar localforage_js = new LocalForage();\n\nmodule.exports = localforage_js;\n\n},{\"3\":3}]},{},[4])(4)\n});\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,UAASA,CAAC,EAAC;EAAC,IAAG,OAAOC,OAAO,KAAG,QAAQ,IAAE,OAAOC,MAAM,KAAG,WAAW,EAAC;IAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC;EAAA,CAAC,MAAK,IAAG,OAAOG,MAAM,KAAG,UAAU,IAAEA,MAAM,CAACC,GAAG,EAAC;IAACD,MAAM,CAAC,EAAE,EAACH,CAAC,CAAC;EAAA,CAAC,MAAI;IAAC,IAAIK,CAAC;IAAC,IAAG,OAAOC,MAAM,KAAG,WAAW,EAAC;MAACD,CAAC,GAACC,MAAM;IAAA,CAAC,MAAK,IAAG,OAAOC,MAAM,KAAG,WAAW,EAAC;MAACF,CAAC,GAACE,MAAM;IAAA,CAAC,MAAK,IAAG,OAAOC,IAAI,KAAG,WAAW,EAAC;MAACH,CAAC,GAACG,IAAI;IAAA,CAAC,MAAI;MAACH,CAAC,GAAC,IAAI;IAAA;IAACA,CAAC,CAACI,WAAW,GAAGT,CAAC,CAAC,CAAC;EAAA;AAAC,CAAC,EAAE,YAAU;EAAC,IAAIG,MAAM,EAACD,MAAM,EAACD,OAAO;EAAC,OAAQ,SAASS,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;IAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;MAAC,IAAG,CAACJ,CAAC,CAACG,CAAC,CAAC,EAAC;QAAC,IAAG,CAACJ,CAAC,CAACI,CAAC,CAAC,EAAC;UAAC,IAAIE,CAAC,GAAC,OAAOC,OAAO,IAAE,UAAU,IAAEA,OAAO;UAAC,IAAG,CAACF,CAAC,IAAEC,CAAC,EAAC,OAAOA,CAAC,CAACF,CAAC,EAAC,CAAC,CAAC,CAAC;UAAC,IAAGI,CAAC,EAAC,OAAOA,CAAC,CAACJ,CAAC,EAAC,CAAC,CAAC,CAAC;UAAC,IAAIf,CAAC,GAAC,IAAIoB,KAAK,CAAC,sBAAsB,GAACL,CAAC,GAAC,GAAG,CAAC;UAAC,MAAOf,CAAC,CAACqB,IAAI,GAAC,kBAAkB,EAAErB,CAAC;QAAC;QAAC,IAAIsB,CAAC,GAACV,CAAC,CAACG,CAAC,CAAC,GAAC;UAACd,OAAO,EAAC,CAAC;QAAC,CAAC;QAACU,CAAC,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAACQ,IAAI,CAACD,CAAC,CAACrB,OAAO,EAAC,UAASS,CAAC,EAAC;UAAC,IAAIE,CAAC,GAACD,CAAC,CAACI,CAAC,CAAC,CAAC,CAAC,CAAC,CAACL,CAAC,CAAC;UAAC,OAAOI,CAAC,CAACF,CAAC,GAACA,CAAC,GAACF,CAAC,CAAC;QAAA,CAAC,EAACY,CAAC,EAACA,CAAC,CAACrB,OAAO,EAACS,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC;MAAA;MAAC,OAAOD,CAAC,CAACG,CAAC,CAAC,CAACd,OAAO;IAAA;IAAC,IAAIkB,CAAC,GAAC,OAAOD,OAAO,IAAE,UAAU,IAAEA,OAAO;IAAC,KAAI,IAAIH,CAAC,GAAC,CAAC,EAACA,CAAC,GAACF,CAAC,CAACW,MAAM,EAACT,CAAC,EAAE,EAACD,CAAC,CAACD,CAAC,CAACE,CAAC,CAAC,CAAC;IAAC,OAAOD,CAAC;EAAA,CAAC,CAAE;IAAC,CAAC,EAAC,CAAC,UAASW,OAAO,EAACvB,MAAM,EAACD,OAAO,EAAC;MACr1B,CAAC,UAAUM,MAAM,EAAC;QAClB,YAAY;;QACZ,IAAImB,QAAQ,GAAGnB,MAAM,CAACoB,gBAAgB,IAAIpB,MAAM,CAACqB,sBAAsB;QAEvE,IAAIC,aAAa;QAEjB;UACE,IAAIH,QAAQ,EAAE;YACZ,IAAII,MAAM,GAAG,CAAC;YACd,IAAIC,QAAQ,GAAG,IAAIL,QAAQ,CAACM,QAAQ,CAAC;YACrC,IAAIC,OAAO,GAAG1B,MAAM,CAAC2B,QAAQ,CAACC,cAAc,CAAC,EAAE,CAAC;YAChDJ,QAAQ,CAACK,OAAO,CAACH,OAAO,EAAE;cACxBI,aAAa,EAAE;YACjB,CAAC,CAAC;YACFR,aAAa,GAAG,SAAAA,CAAA,EAAY;cAC1BI,OAAO,CAACK,IAAI,GAAIR,MAAM,GAAG,EAAEA,MAAM,GAAG,CAAE;YACxC,CAAC;UACH,CAAC,MAAM,IAAI,CAACvB,MAAM,CAACgC,YAAY,IAAI,OAAOhC,MAAM,CAACiC,cAAc,KAAK,WAAW,EAAE;YAC/E,IAAIC,OAAO,GAAG,IAAIlC,MAAM,CAACiC,cAAc,CAAC,CAAC;YACzCC,OAAO,CAACC,KAAK,CAACC,SAAS,GAAGX,QAAQ;YAClCH,aAAa,GAAG,SAAAA,CAAA,EAAY;cAC1BY,OAAO,CAACG,KAAK,CAACC,WAAW,CAAC,CAAC,CAAC;YAC9B,CAAC;UACH,CAAC,MAAM,IAAI,UAAU,IAAItC,MAAM,IAAI,oBAAoB,IAAIA,MAAM,CAAC2B,QAAQ,CAACY,aAAa,CAAC,QAAQ,CAAC,EAAE;YAClGjB,aAAa,GAAG,SAAAA,CAAA,EAAY;cAE1B;cACA;cACA,IAAIkB,QAAQ,GAAGxC,MAAM,CAAC2B,QAAQ,CAACY,aAAa,CAAC,QAAQ,CAAC;cACtDC,QAAQ,CAACC,kBAAkB,GAAG,YAAY;gBACxChB,QAAQ,CAAC,CAAC;gBAEVe,QAAQ,CAACC,kBAAkB,GAAG,IAAI;gBAClCD,QAAQ,CAACE,UAAU,CAACC,WAAW,CAACH,QAAQ,CAAC;gBACzCA,QAAQ,GAAG,IAAI;cACjB,CAAC;cACDxC,MAAM,CAAC2B,QAAQ,CAACiB,eAAe,CAACC,WAAW,CAACL,QAAQ,CAAC;YACvD,CAAC;UACH,CAAC,MAAM;YACLlB,aAAa,GAAG,SAAAA,CAAA,EAAY;cAC1BwB,UAAU,CAACrB,QAAQ,EAAE,CAAC,CAAC;YACzB,CAAC;UACH;QACF;QAEA,IAAIsB,QAAQ;QACZ,IAAIC,KAAK,GAAG,EAAE;QACd;QACA,SAASvB,QAAQA,CAAA,EAAG;UAClBsB,QAAQ,GAAG,IAAI;UACf,IAAInC,CAAC,EAAEqC,QAAQ;UACf,IAAIC,GAAG,GAAGF,KAAK,CAAC/B,MAAM;UACtB,OAAOiC,GAAG,EAAE;YACVD,QAAQ,GAAGD,KAAK;YAChBA,KAAK,GAAG,EAAE;YACVpC,CAAC,GAAG,CAAC,CAAC;YACN,OAAO,EAAEA,CAAC,GAAGsC,GAAG,EAAE;cAChBD,QAAQ,CAACrC,CAAC,CAAC,CAAC,CAAC;YACf;YACAsC,GAAG,GAAGF,KAAK,CAAC/B,MAAM;UACpB;UACA8B,QAAQ,GAAG,KAAK;QAClB;QAEApD,MAAM,CAACD,OAAO,GAAGyD,SAAS;QAC1B,SAASA,SAASA,CAACC,IAAI,EAAE;UACvB,IAAIJ,KAAK,CAACK,IAAI,CAACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAACL,QAAQ,EAAE;YACvCzB,aAAa,CAAC,CAAC;UACjB;QACF;MAEA,CAAC,EAAEN,IAAI,CAAC,IAAI,EAAC,OAAOhB,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,OAAOF,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC,CAAC;IACvI,CAAC,EAAC,CAAC,CAAC,CAAC;IAAC,CAAC,EAAC,CAAC,UAASmB,OAAO,EAACvB,MAAM,EAACD,OAAO,EAAC;MACzC,YAAY;;MACZ,IAAIyD,SAAS,GAAGjC,OAAO,CAAC,CAAC,CAAC;;MAE1B;MACA,SAASoC,QAAQA,CAAA,EAAG,CAAC;MAErB,IAAIC,QAAQ,GAAG,CAAC,CAAC;MAEjB,IAAIC,QAAQ,GAAG,CAAC,UAAU,CAAC;MAC3B,IAAIC,SAAS,GAAG,CAAC,WAAW,CAAC;MAC7B,IAAIC,OAAO,GAAG,CAAC,SAAS,CAAC;MAEzB/D,MAAM,CAACD,OAAO,GAAGiE,OAAO;MAExB,SAASA,OAAOA,CAACC,QAAQ,EAAE;QACzB,IAAI,OAAOA,QAAQ,KAAK,UAAU,EAAE;UAClC,MAAM,IAAIC,SAAS,CAAC,6BAA6B,CAAC;QACpD;QACA,IAAI,CAACC,KAAK,GAAGJ,OAAO;QACpB,IAAI,CAACV,KAAK,GAAG,EAAE;QACf,IAAI,CAACe,OAAO,GAAG,KAAK,CAAC;QACrB,IAAIH,QAAQ,KAAKN,QAAQ,EAAE;UACzBU,qBAAqB,CAAC,IAAI,EAAEJ,QAAQ,CAAC;QACvC;MACF;MAEAD,OAAO,CAACM,SAAS,CAAC,OAAO,CAAC,GAAG,UAAUC,UAAU,EAAE;QACjD,OAAO,IAAI,CAACC,IAAI,CAAC,IAAI,EAAED,UAAU,CAAC;MACpC,CAAC;MACDP,OAAO,CAACM,SAAS,CAACE,IAAI,GAAG,UAAUC,WAAW,EAAEF,UAAU,EAAE;QAC1D,IAAI,OAAOE,WAAW,KAAK,UAAU,IAAI,IAAI,CAACN,KAAK,KAAKL,SAAS,IAC/D,OAAOS,UAAU,KAAK,UAAU,IAAI,IAAI,CAACJ,KAAK,KAAKN,QAAQ,EAAE;UAC7D,OAAO,IAAI;QACb;QACA,IAAIa,OAAO,GAAG,IAAI,IAAI,CAACC,WAAW,CAAChB,QAAQ,CAAC;QAC5C,IAAI,IAAI,CAACQ,KAAK,KAAKJ,OAAO,EAAE;UAC1B,IAAIE,QAAQ,GAAG,IAAI,CAACE,KAAK,KAAKL,SAAS,GAAGW,WAAW,GAAGF,UAAU;UAClEK,MAAM,CAACF,OAAO,EAAET,QAAQ,EAAE,IAAI,CAACG,OAAO,CAAC;QACzC,CAAC,MAAM;UACL,IAAI,CAACf,KAAK,CAACK,IAAI,CAAC,IAAImB,SAAS,CAACH,OAAO,EAAED,WAAW,EAAEF,UAAU,CAAC,CAAC;QAClE;QAEA,OAAOG,OAAO;MAChB,CAAC;MACD,SAASG,SAASA,CAACH,OAAO,EAAED,WAAW,EAAEF,UAAU,EAAE;QACnD,IAAI,CAACG,OAAO,GAAGA,OAAO;QACtB,IAAI,OAAOD,WAAW,KAAK,UAAU,EAAE;UACrC,IAAI,CAACA,WAAW,GAAGA,WAAW;UAC9B,IAAI,CAACK,aAAa,GAAG,IAAI,CAACC,kBAAkB;QAC9C;QACA,IAAI,OAAOR,UAAU,KAAK,UAAU,EAAE;UACpC,IAAI,CAACA,UAAU,GAAGA,UAAU;UAC5B,IAAI,CAACS,YAAY,GAAG,IAAI,CAACC,iBAAiB;QAC5C;MACF;MACAJ,SAAS,CAACP,SAAS,CAACQ,aAAa,GAAG,UAAUI,KAAK,EAAE;QACnDtB,QAAQ,CAACuB,OAAO,CAAC,IAAI,CAACT,OAAO,EAAEQ,KAAK,CAAC;MACvC,CAAC;MACDL,SAAS,CAACP,SAAS,CAACS,kBAAkB,GAAG,UAAUG,KAAK,EAAE;QACxDN,MAAM,CAAC,IAAI,CAACF,OAAO,EAAE,IAAI,CAACD,WAAW,EAAES,KAAK,CAAC;MAC/C,CAAC;MACDL,SAAS,CAACP,SAAS,CAACU,YAAY,GAAG,UAAUE,KAAK,EAAE;QAClDtB,QAAQ,CAACwB,MAAM,CAAC,IAAI,CAACV,OAAO,EAAEQ,KAAK,CAAC;MACtC,CAAC;MACDL,SAAS,CAACP,SAAS,CAACW,iBAAiB,GAAG,UAAUC,KAAK,EAAE;QACvDN,MAAM,CAAC,IAAI,CAACF,OAAO,EAAE,IAAI,CAACH,UAAU,EAAEW,KAAK,CAAC;MAC9C,CAAC;MAED,SAASN,MAAMA,CAACF,OAAO,EAAEW,IAAI,EAAEH,KAAK,EAAE;QACpC1B,SAAS,CAAC,YAAY;UACpB,IAAI8B,WAAW;UACf,IAAI;YACFA,WAAW,GAAGD,IAAI,CAACH,KAAK,CAAC;UAC3B,CAAC,CAAC,OAAO1E,CAAC,EAAE;YACV,OAAOoD,QAAQ,CAACwB,MAAM,CAACV,OAAO,EAAElE,CAAC,CAAC;UACpC;UACA,IAAI8E,WAAW,KAAKZ,OAAO,EAAE;YAC3Bd,QAAQ,CAACwB,MAAM,CAACV,OAAO,EAAE,IAAIR,SAAS,CAAC,oCAAoC,CAAC,CAAC;UAC/E,CAAC,MAAM;YACLN,QAAQ,CAACuB,OAAO,CAACT,OAAO,EAAEY,WAAW,CAAC;UACxC;QACF,CAAC,CAAC;MACJ;MAEA1B,QAAQ,CAACuB,OAAO,GAAG,UAAU7E,IAAI,EAAE4E,KAAK,EAAE;QACxC,IAAIK,MAAM,GAAGC,QAAQ,CAACC,OAAO,EAAEP,KAAK,CAAC;QACrC,IAAIK,MAAM,CAACG,MAAM,KAAK,OAAO,EAAE;UAC7B,OAAO9B,QAAQ,CAACwB,MAAM,CAAC9E,IAAI,EAAEiF,MAAM,CAACL,KAAK,CAAC;QAC5C;QACA,IAAIS,QAAQ,GAAGJ,MAAM,CAACL,KAAK;QAE3B,IAAIS,QAAQ,EAAE;UACZtB,qBAAqB,CAAC/D,IAAI,EAAEqF,QAAQ,CAAC;QACvC,CAAC,MAAM;UACLrF,IAAI,CAAC6D,KAAK,GAAGL,SAAS;UACtBxD,IAAI,CAAC8D,OAAO,GAAGc,KAAK;UACpB,IAAIjE,CAAC,GAAG,CAAC,CAAC;UACV,IAAIsC,GAAG,GAAGjD,IAAI,CAAC+C,KAAK,CAAC/B,MAAM;UAC3B,OAAO,EAAEL,CAAC,GAAGsC,GAAG,EAAE;YAChBjD,IAAI,CAAC+C,KAAK,CAACpC,CAAC,CAAC,CAAC6D,aAAa,CAACI,KAAK,CAAC;UACpC;QACF;QACA,OAAO5E,IAAI;MACb,CAAC;MACDsD,QAAQ,CAACwB,MAAM,GAAG,UAAU9E,IAAI,EAAEsF,KAAK,EAAE;QACvCtF,IAAI,CAAC6D,KAAK,GAAGN,QAAQ;QACrBvD,IAAI,CAAC8D,OAAO,GAAGwB,KAAK;QACpB,IAAI3E,CAAC,GAAG,CAAC,CAAC;QACV,IAAIsC,GAAG,GAAGjD,IAAI,CAAC+C,KAAK,CAAC/B,MAAM;QAC3B,OAAO,EAAEL,CAAC,GAAGsC,GAAG,EAAE;UAChBjD,IAAI,CAAC+C,KAAK,CAACpC,CAAC,CAAC,CAAC+D,YAAY,CAACY,KAAK,CAAC;QACnC;QACA,OAAOtF,IAAI;MACb,CAAC;MAED,SAASmF,OAAOA,CAACI,GAAG,EAAE;QACpB;QACA,IAAIrB,IAAI,GAAGqB,GAAG,IAAIA,GAAG,CAACrB,IAAI;QAC1B,IAAIqB,GAAG,KAAK,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IAAI,OAAOrB,IAAI,KAAK,UAAU,EAAE;UAC/F,OAAO,SAASsB,QAAQA,CAAA,EAAG;YACzBtB,IAAI,CAACuB,KAAK,CAACF,GAAG,EAAEG,SAAS,CAAC;UAC5B,CAAC;QACH;MACF;MAEA,SAAS3B,qBAAqBA,CAAC/D,IAAI,EAAEqF,QAAQ,EAAE;QAC7C;QACA,IAAI/D,MAAM,GAAG,KAAK;QAClB,SAASqE,OAAOA,CAACf,KAAK,EAAE;UACtB,IAAItD,MAAM,EAAE;YACV;UACF;UACAA,MAAM,GAAG,IAAI;UACbgC,QAAQ,CAACwB,MAAM,CAAC9E,IAAI,EAAE4E,KAAK,CAAC;QAC9B;QAEA,SAASgB,SAASA,CAAChB,KAAK,EAAE;UACxB,IAAItD,MAAM,EAAE;YACV;UACF;UACAA,MAAM,GAAG,IAAI;UACbgC,QAAQ,CAACuB,OAAO,CAAC7E,IAAI,EAAE4E,KAAK,CAAC;QAC/B;QAEA,SAASiB,WAAWA,CAAA,EAAG;UACrBR,QAAQ,CAACO,SAAS,EAAED,OAAO,CAAC;QAC9B;QAEA,IAAIV,MAAM,GAAGC,QAAQ,CAACW,WAAW,CAAC;QAClC,IAAIZ,MAAM,CAACG,MAAM,KAAK,OAAO,EAAE;UAC7BO,OAAO,CAACV,MAAM,CAACL,KAAK,CAAC;QACvB;MACF;MAEA,SAASM,QAAQA,CAACH,IAAI,EAAEH,KAAK,EAAE;QAC7B,IAAIkB,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI;UACFA,GAAG,CAAClB,KAAK,GAAGG,IAAI,CAACH,KAAK,CAAC;UACvBkB,GAAG,CAACV,MAAM,GAAG,SAAS;QACxB,CAAC,CAAC,OAAOlF,CAAC,EAAE;UACV4F,GAAG,CAACV,MAAM,GAAG,OAAO;UACpBU,GAAG,CAAClB,KAAK,GAAG1E,CAAC;QACf;QACA,OAAO4F,GAAG;MACZ;MAEApC,OAAO,CAACmB,OAAO,GAAGA,OAAO;MACzB,SAASA,OAAOA,CAACD,KAAK,EAAE;QACtB,IAAIA,KAAK,YAAY,IAAI,EAAE;UACzB,OAAOA,KAAK;QACd;QACA,OAAOtB,QAAQ,CAACuB,OAAO,CAAC,IAAI,IAAI,CAACxB,QAAQ,CAAC,EAAEuB,KAAK,CAAC;MACpD;MAEAlB,OAAO,CAACoB,MAAM,GAAGA,MAAM;MACvB,SAASA,MAAMA,CAACiB,MAAM,EAAE;QACtB,IAAI3B,OAAO,GAAG,IAAI,IAAI,CAACf,QAAQ,CAAC;QAChC,OAAOC,QAAQ,CAACwB,MAAM,CAACV,OAAO,EAAE2B,MAAM,CAAC;MACzC;MAEArC,OAAO,CAACsC,GAAG,GAAGA,GAAG;MACjB,SAASA,GAAGA,CAACC,QAAQ,EAAE;QACrB,IAAIjG,IAAI,GAAG,IAAI;QACf,IAAIkG,MAAM,CAAClC,SAAS,CAACmC,QAAQ,CAACpF,IAAI,CAACkF,QAAQ,CAAC,KAAK,gBAAgB,EAAE;UACjE,OAAO,IAAI,CAACnB,MAAM,CAAC,IAAIlB,SAAS,CAAC,kBAAkB,CAAC,CAAC;QACvD;QAEA,IAAIX,GAAG,GAAGgD,QAAQ,CAACjF,MAAM;QACzB,IAAIM,MAAM,GAAG,KAAK;QAClB,IAAI,CAAC2B,GAAG,EAAE;UACR,OAAO,IAAI,CAAC4B,OAAO,CAAC,EAAE,CAAC;QACzB;QAEA,IAAIuB,MAAM,GAAG,IAAIC,KAAK,CAACpD,GAAG,CAAC;QAC3B,IAAIqD,QAAQ,GAAG,CAAC;QAChB,IAAI3F,CAAC,GAAG,CAAC,CAAC;QACV,IAAIyD,OAAO,GAAG,IAAI,IAAI,CAACf,QAAQ,CAAC;QAEhC,OAAO,EAAE1C,CAAC,GAAGsC,GAAG,EAAE;UAChBsD,WAAW,CAACN,QAAQ,CAACtF,CAAC,CAAC,EAAEA,CAAC,CAAC;QAC7B;QACA,OAAOyD,OAAO;QACd,SAASmC,WAAWA,CAAC3B,KAAK,EAAEjE,CAAC,EAAE;UAC7BX,IAAI,CAAC6E,OAAO,CAACD,KAAK,CAAC,CAACV,IAAI,CAACsC,cAAc,EAAE,UAAUlB,KAAK,EAAE;YACxD,IAAI,CAAChE,MAAM,EAAE;cACXA,MAAM,GAAG,IAAI;cACbgC,QAAQ,CAACwB,MAAM,CAACV,OAAO,EAAEkB,KAAK,CAAC;YACjC;UACF,CAAC,CAAC;UACF,SAASkB,cAAcA,CAACC,QAAQ,EAAE;YAChCL,MAAM,CAACzF,CAAC,CAAC,GAAG8F,QAAQ;YACpB,IAAI,EAAEH,QAAQ,KAAKrD,GAAG,IAAI,CAAC3B,MAAM,EAAE;cACjCA,MAAM,GAAG,IAAI;cACbgC,QAAQ,CAACuB,OAAO,CAACT,OAAO,EAAEgC,MAAM,CAAC;YACnC;UACF;QACF;MACF;MAEA1C,OAAO,CAACgD,IAAI,GAAGA,IAAI;MACnB,SAASA,IAAIA,CAACT,QAAQ,EAAE;QACtB,IAAIjG,IAAI,GAAG,IAAI;QACf,IAAIkG,MAAM,CAAClC,SAAS,CAACmC,QAAQ,CAACpF,IAAI,CAACkF,QAAQ,CAAC,KAAK,gBAAgB,EAAE;UACjE,OAAO,IAAI,CAACnB,MAAM,CAAC,IAAIlB,SAAS,CAAC,kBAAkB,CAAC,CAAC;QACvD;QAEA,IAAIX,GAAG,GAAGgD,QAAQ,CAACjF,MAAM;QACzB,IAAIM,MAAM,GAAG,KAAK;QAClB,IAAI,CAAC2B,GAAG,EAAE;UACR,OAAO,IAAI,CAAC4B,OAAO,CAAC,EAAE,CAAC;QACzB;QAEA,IAAIlE,CAAC,GAAG,CAAC,CAAC;QACV,IAAIyD,OAAO,GAAG,IAAI,IAAI,CAACf,QAAQ,CAAC;QAEhC,OAAO,EAAE1C,CAAC,GAAGsC,GAAG,EAAE;UAChBU,QAAQ,CAACsC,QAAQ,CAACtF,CAAC,CAAC,CAAC;QACvB;QACA,OAAOyD,OAAO;QACd,SAAST,QAAQA,CAACiB,KAAK,EAAE;UACvB5E,IAAI,CAAC6E,OAAO,CAACD,KAAK,CAAC,CAACV,IAAI,CAAC,UAAUyC,QAAQ,EAAE;YAC3C,IAAI,CAACrF,MAAM,EAAE;cACXA,MAAM,GAAG,IAAI;cACbgC,QAAQ,CAACuB,OAAO,CAACT,OAAO,EAAEuC,QAAQ,CAAC;YACrC;UACF,CAAC,EAAE,UAAUrB,KAAK,EAAE;YAClB,IAAI,CAAChE,MAAM,EAAE;cACXA,MAAM,GAAG,IAAI;cACbgC,QAAQ,CAACwB,MAAM,CAACV,OAAO,EAAEkB,KAAK,CAAC;YACjC;UACF,CAAC,CAAC;QACJ;MACF;IAEA,CAAC,EAAC;MAAC,GAAG,EAAC;IAAC,CAAC,CAAC;IAAC,CAAC,EAAC,CAAC,UAASrE,OAAO,EAACvB,MAAM,EAACD,OAAO,EAAC;MAC9C,CAAC,UAAUM,MAAM,EAAC;QAClB,YAAY;;QACZ,IAAI,OAAOA,MAAM,CAAC2D,OAAO,KAAK,UAAU,EAAE;UACxC3D,MAAM,CAAC2D,OAAO,GAAGzC,OAAO,CAAC,CAAC,CAAC;QAC7B;MAEA,CAAC,EAAEF,IAAI,CAAC,IAAI,EAAC,OAAOhB,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,OAAOC,IAAI,KAAK,WAAW,GAAGA,IAAI,GAAG,OAAOF,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC,CAAC;IACvI,CAAC,EAAC;MAAC,GAAG,EAAC;IAAC,CAAC,CAAC;IAAC,CAAC,EAAC,CAAC,UAASmB,OAAO,EAACvB,MAAM,EAACD,OAAO,EAAC;MAC9C,YAAY;;MAEZ,IAAImH,OAAO,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,GAAG,UAAUvB,GAAG,EAAE;QAAE,OAAO,OAAOA,GAAG;MAAE,CAAC,GAAG,UAAUA,GAAG,EAAE;QAAE,OAAOA,GAAG,IAAI,OAAOsB,MAAM,KAAK,UAAU,IAAItB,GAAG,CAAClB,WAAW,KAAKwC,MAAM,IAAItB,GAAG,KAAKsB,MAAM,CAAC7C,SAAS,GAAG,QAAQ,GAAG,OAAOuB,GAAG;MAAE,CAAC;MAE5Q,SAASwB,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;QAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;UAAE,MAAM,IAAIrD,SAAS,CAAC,mCAAmC,CAAC;QAAE;MAAE;MAExJ,SAASsD,MAAMA,CAAA,EAAG;QACd;QACA,IAAI;UACA,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;YAClC,OAAOA,SAAS;UACpB;UACA,IAAI,OAAOC,eAAe,KAAK,WAAW,EAAE;YACxC,OAAOA,eAAe;UAC1B;UACA,IAAI,OAAOC,YAAY,KAAK,WAAW,EAAE;YACrC,OAAOA,YAAY;UACvB;UACA,IAAI,OAAOC,UAAU,KAAK,WAAW,EAAE;YACnC,OAAOA,UAAU;UACrB;UACA,IAAI,OAAOC,WAAW,KAAK,WAAW,EAAE;YACpC,OAAOA,WAAW;UACtB;QACJ,CAAC,CAAC,OAAOrH,CAAC,EAAE;UACR;QACJ;MACJ;MAEA,IAAIsH,GAAG,GAAGN,MAAM,CAAC,CAAC;MAElB,SAASO,gBAAgBA,CAAA,EAAG;QACxB,IAAI;UACA;UACA;UACA,IAAI,CAACD,GAAG,IAAI,CAACA,GAAG,CAACE,IAAI,EAAE;YACnB,OAAO,KAAK;UAChB;UACA;UACA;UACA;UACA;UACA,IAAIC,QAAQ,GAAG,OAAOC,YAAY,KAAK,WAAW,IAAI,2BAA2B,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAACF,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC,YAAY,CAACF,IAAI,CAACC,SAAS,CAACE,QAAQ,CAAC;UAE5L,IAAIC,QAAQ,GAAG,OAAOC,KAAK,KAAK,UAAU,IAAIA,KAAK,CAAC/B,QAAQ,CAAC,CAAC,CAACgC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;;UAE7F;UACA;UACA;UACA;UACA;UACA;UACA,OAAO,CAAC,CAACR,QAAQ,IAAIM,QAAQ,KAAK,OAAOd,SAAS,KAAK,WAAW;UAClE;UACA;UACA;UACA;UACA,OAAOiB,WAAW,KAAK,WAAW;QACtC,CAAC,CAAC,OAAOlI,CAAC,EAAE;UACR,OAAO,KAAK;QAChB;MACJ;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA,SAASmI,UAAUA,CAACC,KAAK,EAAEC,UAAU,EAAE;QACnC;QACAD,KAAK,GAAGA,KAAK,IAAI,EAAE;QACnBC,UAAU,GAAGA,UAAU,IAAI,CAAC,CAAC;QAC7B,IAAI;UACA,OAAO,IAAIC,IAAI,CAACF,KAAK,EAAEC,UAAU,CAAC;QACtC,CAAC,CAAC,OAAOrI,CAAC,EAAE;UACR,IAAIA,CAAC,CAACuI,IAAI,KAAK,WAAW,EAAE;YACxB,MAAMvI,CAAC;UACX;UACA,IAAIwI,OAAO,GAAG,OAAOC,WAAW,KAAK,WAAW,GAAGA,WAAW,GAAG,OAAOC,aAAa,KAAK,WAAW,GAAGA,aAAa,GAAG,OAAOC,cAAc,KAAK,WAAW,GAAGA,cAAc,GAAGC,iBAAiB;UAClM,IAAIC,OAAO,GAAG,IAAIL,OAAO,CAAC,CAAC;UAC3B,KAAK,IAAI/H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2H,KAAK,CAACtH,MAAM,EAAEL,CAAC,IAAI,CAAC,EAAE;YACtCoI,OAAO,CAACC,MAAM,CAACV,KAAK,CAAC3H,CAAC,CAAC,CAAC;UAC5B;UACA,OAAOoI,OAAO,CAACE,OAAO,CAACV,UAAU,CAACW,IAAI,CAAC;QAC3C;MACJ;;MAEA;MACA;MACA,IAAI,OAAOxF,OAAO,KAAK,WAAW,EAAE;QAChC;QACA;QACAzC,OAAO,CAAC,CAAC,CAAC;MACd;MACA,IAAIkI,SAAS,GAAGzF,OAAO;MAEvB,SAAS0F,eAAeA,CAAChF,OAAO,EAAEiF,QAAQ,EAAE;QACxC,IAAIA,QAAQ,EAAE;UACVjF,OAAO,CAACF,IAAI,CAAC,UAAUe,MAAM,EAAE;YAC3BoE,QAAQ,CAAC,IAAI,EAAEpE,MAAM,CAAC;UAC1B,CAAC,EAAE,UAAUK,KAAK,EAAE;YAChB+D,QAAQ,CAAC/D,KAAK,CAAC;UACnB,CAAC,CAAC;QACN;MACJ;MAEA,SAASgE,mBAAmBA,CAAClF,OAAO,EAAEiF,QAAQ,EAAEE,aAAa,EAAE;QAC3D,IAAI,OAAOF,QAAQ,KAAK,UAAU,EAAE;UAChCjF,OAAO,CAACF,IAAI,CAACmF,QAAQ,CAAC;QAC1B;QAEA,IAAI,OAAOE,aAAa,KAAK,UAAU,EAAE;UACrCnF,OAAO,CAAC,OAAO,CAAC,CAACmF,aAAa,CAAC;QACnC;MACJ;MAEA,SAASC,YAAYA,CAACC,GAAG,EAAE;QACvB;QACA,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;UACzBC,OAAO,CAACC,IAAI,CAACF,GAAG,GAAG,yCAAyC,CAAC;UAC7DA,GAAG,GAAGG,MAAM,CAACH,GAAG,CAAC;QACrB;QAEA,OAAOA,GAAG;MACd;MAEA,SAASI,WAAWA,CAAA,EAAG;QACnB,IAAInE,SAAS,CAAC1E,MAAM,IAAI,OAAO0E,SAAS,CAACA,SAAS,CAAC1E,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;UAC3E,OAAO0E,SAAS,CAACA,SAAS,CAAC1E,MAAM,GAAG,CAAC,CAAC;QAC1C;MACJ;;MAEA;MACA;;MAEA,IAAI8I,yBAAyB,GAAG,kCAAkC;MAClE,IAAIC,aAAa,GAAG,KAAK,CAAC;MAC1B,IAAIC,UAAU,GAAG,CAAC,CAAC;MACnB,IAAI7D,QAAQ,GAAGD,MAAM,CAAClC,SAAS,CAACmC,QAAQ;;MAExC;MACA,IAAI8D,SAAS,GAAG,UAAU;MAC1B,IAAIC,UAAU,GAAG,WAAW;;MAE5B;MACA;MACA;MACA;MACA;MACA,SAASC,uBAAuBA,CAACC,GAAG,EAAE;QAClC,IAAIpJ,MAAM,GAAGoJ,GAAG,CAACpJ,MAAM;QACvB,IAAIqJ,GAAG,GAAG,IAAIC,WAAW,CAACtJ,MAAM,CAAC;QACjC,IAAIuJ,GAAG,GAAG,IAAIC,UAAU,CAACH,GAAG,CAAC;QAC7B,KAAK,IAAI1J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,EAAEL,CAAC,EAAE,EAAE;UAC7B4J,GAAG,CAAC5J,CAAC,CAAC,GAAGyJ,GAAG,CAACK,UAAU,CAAC9J,CAAC,CAAC;QAC9B;QACA,OAAO0J,GAAG;MACd;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,SAASK,+BAA+BA,CAAClD,GAAG,EAAE;QAC1C,OAAO,IAAI2B,SAAS,CAAC,UAAUtE,OAAO,EAAE;UACpC,IAAI8F,GAAG,GAAGnD,GAAG,CAACoD,WAAW,CAACd,yBAAyB,EAAEI,UAAU,CAAC;UAChE,IAAIW,IAAI,GAAGxC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;UAC3BsC,GAAG,CAACG,WAAW,CAAChB,yBAAyB,CAAC,CAACiB,GAAG,CAACF,IAAI,EAAE,KAAK,CAAC;UAE3DF,GAAG,CAACK,OAAO,GAAG,UAAU9K,CAAC,EAAE;YACvB;YACA;YACAA,CAAC,CAAC+K,cAAc,CAAC,CAAC;YAClB/K,CAAC,CAACgL,eAAe,CAAC,CAAC;YACnBrG,OAAO,CAAC,KAAK,CAAC;UAClB,CAAC;UAED8F,GAAG,CAACQ,UAAU,GAAG,YAAY;YACzB,IAAIC,aAAa,GAAGtD,SAAS,CAACC,SAAS,CAACsD,KAAK,CAAC,eAAe,CAAC;YAC9D,IAAIC,WAAW,GAAGxD,SAAS,CAACC,SAAS,CAACsD,KAAK,CAAC,QAAQ,CAAC;YACrD;YACA;YACAxG,OAAO,CAACyG,WAAW,IAAI,CAACF,aAAa,IAAIG,QAAQ,CAACH,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;UAClF,CAAC;QACL,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY;UACpB,OAAO,KAAK,CAAC,CAAC;QAClB,CAAC,CAAC;MACN;MAEA,SAASI,iBAAiBA,CAAChE,GAAG,EAAE;QAC5B,IAAI,OAAOuC,aAAa,KAAK,SAAS,EAAE;UACpC,OAAOZ,SAAS,CAACtE,OAAO,CAACkF,aAAa,CAAC;QAC3C;QACA,OAAOW,+BAA+B,CAAClD,GAAG,CAAC,CAACtD,IAAI,CAAC,UAAUU,KAAK,EAAE;UAC9DmF,aAAa,GAAGnF,KAAK;UACrB,OAAOmF,aAAa;QACxB,CAAC,CAAC;MACN;MAEA,SAAS0B,eAAeA,CAACC,MAAM,EAAE;QAC7B,IAAIC,SAAS,GAAG3B,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC;;QAEvC;QACA,IAAImD,iBAAiB,GAAG,CAAC,CAAC;QAE1BA,iBAAiB,CAACxH,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACjE8G,iBAAiB,CAAC/G,OAAO,GAAGA,OAAO;UACnC+G,iBAAiB,CAAC9G,MAAM,GAAGA,MAAM;QACrC,CAAC,CAAC;;QAEF;QACA6G,SAAS,CAACE,kBAAkB,CAACzI,IAAI,CAACwI,iBAAiB,CAAC;;QAEpD;QACA,IAAI,CAACD,SAAS,CAACG,OAAO,EAAE;UACpBH,SAAS,CAACG,OAAO,GAAGF,iBAAiB,CAACxH,OAAO;QACjD,CAAC,MAAM;UACHuH,SAAS,CAACG,OAAO,GAAGH,SAAS,CAACG,OAAO,CAAC5H,IAAI,CAAC,YAAY;YACnD,OAAO0H,iBAAiB,CAACxH,OAAO;UACpC,CAAC,CAAC;QACN;MACJ;MAEA,SAAS2H,iBAAiBA,CAACL,MAAM,EAAE;QAC/B,IAAIC,SAAS,GAAG3B,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC;;QAEvC;QACA,IAAImD,iBAAiB,GAAGD,SAAS,CAACE,kBAAkB,CAACG,GAAG,CAAC,CAAC;;QAE1D;QACA;QACA,IAAIJ,iBAAiB,EAAE;UACnBA,iBAAiB,CAAC/G,OAAO,CAAC,CAAC;UAC3B,OAAO+G,iBAAiB,CAACxH,OAAO;QACpC;MACJ;MAEA,SAAS6H,gBAAgBA,CAACP,MAAM,EAAEQ,GAAG,EAAE;QACnC,IAAIP,SAAS,GAAG3B,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC;;QAEvC;QACA,IAAImD,iBAAiB,GAAGD,SAAS,CAACE,kBAAkB,CAACG,GAAG,CAAC,CAAC;;QAE1D;QACA;QACA,IAAIJ,iBAAiB,EAAE;UACnBA,iBAAiB,CAAC9G,MAAM,CAACoH,GAAG,CAAC;UAC7B,OAAON,iBAAiB,CAACxH,OAAO;QACpC;MACJ;MAEA,SAAS+H,cAAcA,CAACT,MAAM,EAAEU,aAAa,EAAE;QAC3C,OAAO,IAAIjD,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UAC5CkF,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC,GAAGuB,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC,IAAI4D,eAAe,CAAC,CAAC;UAEtE,IAAIX,MAAM,CAACY,EAAE,EAAE;YACX,IAAIF,aAAa,EAAE;cACfX,eAAe,CAACC,MAAM,CAAC;cACvBA,MAAM,CAACY,EAAE,CAACC,KAAK,CAAC,CAAC;YACrB,CAAC,MAAM;cACH,OAAO1H,OAAO,CAAC6G,MAAM,CAACY,EAAE,CAAC;YAC7B;UACJ;UAEA,IAAIE,MAAM,GAAG,CAACd,MAAM,CAACjD,IAAI,CAAC;UAE1B,IAAI2D,aAAa,EAAE;YACfI,MAAM,CAACpJ,IAAI,CAACsI,MAAM,CAACe,OAAO,CAAC;UAC/B;UAEA,IAAIC,OAAO,GAAGlF,GAAG,CAACE,IAAI,CAACjC,KAAK,CAAC+B,GAAG,EAAEgF,MAAM,CAAC;UAEzC,IAAIJ,aAAa,EAAE;YACfM,OAAO,CAACC,eAAe,GAAG,UAAUzM,CAAC,EAAE;cACnC,IAAIoM,EAAE,GAAGI,OAAO,CAACzH,MAAM;cACvB,IAAI;gBACAqH,EAAE,CAACM,iBAAiB,CAAClB,MAAM,CAACmB,SAAS,CAAC;gBACtC,IAAI3M,CAAC,CAAC4M,UAAU,IAAI,CAAC,EAAE;kBACnB;kBACAR,EAAE,CAACM,iBAAiB,CAAC9C,yBAAyB,CAAC;gBACnD;cACJ,CAAC,CAAC,OAAOiD,EAAE,EAAE;gBACT,IAAIA,EAAE,CAACtE,IAAI,KAAK,iBAAiB,EAAE;kBAC/BiB,OAAO,CAACC,IAAI,CAAC,gBAAgB,GAAG+B,MAAM,CAACjD,IAAI,GAAG,GAAG,GAAG,kCAAkC,GAAGvI,CAAC,CAAC4M,UAAU,GAAG,cAAc,GAAG5M,CAAC,CAAC8M,UAAU,GAAG,qBAAqB,GAAGtB,MAAM,CAACmB,SAAS,GAAG,mBAAmB,CAAC;gBAC3M,CAAC,MAAM;kBACH,MAAME,EAAE;gBACZ;cACJ;YACJ,CAAC;UACL;UAEAL,OAAO,CAACO,OAAO,GAAG,UAAU/M,CAAC,EAAE;YAC3BA,CAAC,CAAC+K,cAAc,CAAC,CAAC;YAClBnG,MAAM,CAAC4H,OAAO,CAACpH,KAAK,CAAC;UACzB,CAAC;UAEDoH,OAAO,CAACQ,SAAS,GAAG,YAAY;YAC5B,IAAIZ,EAAE,GAAGI,OAAO,CAACzH,MAAM;YACvBqH,EAAE,CAACa,eAAe,GAAG,UAAUjN,CAAC,EAAE;cAC9B;cACA;cACA;cACA;cACA;cACAA,CAAC,CAACkN,MAAM,CAACb,KAAK,CAAC,CAAC;YACpB,CAAC;YACD1H,OAAO,CAACyH,EAAE,CAAC;YACXP,iBAAiB,CAACL,MAAM,CAAC;UAC7B,CAAC;QACL,CAAC,CAAC;MACN;MAEA,SAAS2B,sBAAsBA,CAAC3B,MAAM,EAAE;QACpC,OAAOS,cAAc,CAACT,MAAM,EAAE,KAAK,CAAC;MACxC;MAEA,SAAS4B,sBAAsBA,CAAC5B,MAAM,EAAE;QACpC,OAAOS,cAAc,CAACT,MAAM,EAAE,IAAI,CAAC;MACvC;MAEA,SAAS6B,gBAAgBA,CAAC7B,MAAM,EAAE8B,cAAc,EAAE;QAC9C,IAAI,CAAC9B,MAAM,CAACY,EAAE,EAAE;UACZ,OAAO,IAAI;QACf;QAEA,IAAImB,UAAU,GAAG,CAAC/B,MAAM,CAACY,EAAE,CAACoB,gBAAgB,CAACC,QAAQ,CAACjC,MAAM,CAACmB,SAAS,CAAC;QACvE,IAAIe,WAAW,GAAGlC,MAAM,CAACe,OAAO,GAAGf,MAAM,CAACY,EAAE,CAACG,OAAO;QACpD,IAAIoB,SAAS,GAAGnC,MAAM,CAACe,OAAO,GAAGf,MAAM,CAACY,EAAE,CAACG,OAAO;QAElD,IAAImB,WAAW,EAAE;UACb;UACA;UACA,IAAIlC,MAAM,CAACe,OAAO,KAAKe,cAAc,EAAE;YACnC9D,OAAO,CAACC,IAAI,CAAC,gBAAgB,GAAG+B,MAAM,CAACjD,IAAI,GAAG,GAAG,GAAG,oCAAoC,GAAGiD,MAAM,CAACY,EAAE,CAACG,OAAO,GAAG,cAAc,GAAGf,MAAM,CAACe,OAAO,GAAG,GAAG,CAAC;UACzJ;UACA;UACAf,MAAM,CAACe,OAAO,GAAGf,MAAM,CAACY,EAAE,CAACG,OAAO;QACtC;QAEA,IAAIoB,SAAS,IAAIJ,UAAU,EAAE;UACzB;UACA;UACA;UACA,IAAIA,UAAU,EAAE;YACZ,IAAIK,UAAU,GAAGpC,MAAM,CAACY,EAAE,CAACG,OAAO,GAAG,CAAC;YACtC,IAAIqB,UAAU,GAAGpC,MAAM,CAACe,OAAO,EAAE;cAC7Bf,MAAM,CAACe,OAAO,GAAGqB,UAAU;YAC/B;UACJ;UAEA,OAAO,IAAI;QACf;QAEA,OAAO,KAAK;MAChB;;MAEA;MACA,SAASC,WAAWA,CAAClD,IAAI,EAAE;QACvB,OAAO,IAAI1B,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UAC5C,IAAIkJ,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;UAC7BD,MAAM,CAACf,OAAO,GAAGnI,MAAM;UACvBkJ,MAAM,CAACE,SAAS,GAAG,UAAUhO,CAAC,EAAE;YAC5B,IAAIiO,MAAM,GAAGC,IAAI,CAAClO,CAAC,CAACkN,MAAM,CAACnI,MAAM,IAAI,EAAE,CAAC;YACxCJ,OAAO,CAAC;cACJwJ,2BAA2B,EAAE,IAAI;cACjCvM,IAAI,EAAEqM,MAAM;cACZjF,IAAI,EAAE2B,IAAI,CAAC3B;YACf,CAAC,CAAC;UACN,CAAC;UACD8E,MAAM,CAACM,kBAAkB,CAACzD,IAAI,CAAC;QACnC,CAAC,CAAC;MACN;;MAEA;MACA,SAAS0D,WAAWA,CAACC,WAAW,EAAE;QAC9B,IAAIC,SAAS,GAAGtE,uBAAuB,CAACuE,IAAI,CAACF,WAAW,CAAC1M,IAAI,CAAC,CAAC;QAC/D,OAAOuG,UAAU,CAAC,CAACoG,SAAS,CAAC,EAAE;UAAEvF,IAAI,EAAEsF,WAAW,CAACtF;QAAK,CAAC,CAAC;MAC9D;;MAEA;MACA,SAASyF,cAAcA,CAAC/J,KAAK,EAAE;QAC3B,OAAOA,KAAK,IAAIA,KAAK,CAACyJ,2BAA2B;MACrD;;MAEA;MACA;MACA;MACA;MACA,SAASO,WAAWA,CAACvF,QAAQ,EAAE;QAC3B,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAGpE,IAAI,CAAC6O,UAAU,CAAC,CAAC,CAAC3K,IAAI,CAAC,YAAY;UAC7C,IAAIyH,SAAS,GAAG3B,UAAU,CAAChK,IAAI,CAAC8O,OAAO,CAACrG,IAAI,CAAC;UAE7C,IAAIkD,SAAS,IAAIA,SAAS,CAACG,OAAO,EAAE;YAChC,OAAOH,SAAS,CAACG,OAAO;UAC5B;QACJ,CAAC,CAAC;QAEFxC,mBAAmB,CAAClF,OAAO,EAAEiF,QAAQ,EAAEA,QAAQ,CAAC;QAChD,OAAOjF,OAAO;MAClB;;MAEA;MACA;MACA;MACA,SAAS2K,aAAaA,CAACrD,MAAM,EAAE;QAC3BD,eAAe,CAACC,MAAM,CAAC;QAEvB,IAAIC,SAAS,GAAG3B,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC;QACvC,IAAIuG,OAAO,GAAGrD,SAAS,CAACqD,OAAO;QAE/B,KAAK,IAAIrO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,OAAO,CAAChO,MAAM,EAAEL,CAAC,EAAE,EAAE;UACrC,IAAIsO,MAAM,GAAGD,OAAO,CAACrO,CAAC,CAAC;UACvB,IAAIsO,MAAM,CAACH,OAAO,CAACxC,EAAE,EAAE;YACnB2C,MAAM,CAACH,OAAO,CAACxC,EAAE,CAACC,KAAK,CAAC,CAAC;YACzB0C,MAAM,CAACH,OAAO,CAACxC,EAAE,GAAG,IAAI;UAC5B;QACJ;QACAZ,MAAM,CAACY,EAAE,GAAG,IAAI;QAEhB,OAAOe,sBAAsB,CAAC3B,MAAM,CAAC,CAACxH,IAAI,CAAC,UAAUoI,EAAE,EAAE;UACrDZ,MAAM,CAACY,EAAE,GAAGA,EAAE;UACd,IAAIiB,gBAAgB,CAAC7B,MAAM,CAAC,EAAE;YAC1B;YACA,OAAO4B,sBAAsB,CAAC5B,MAAM,CAAC;UACzC;UACA,OAAOY,EAAE;QACb,CAAC,CAAC,CAACpI,IAAI,CAAC,UAAUoI,EAAE,EAAE;UAClB;UACA;UACAZ,MAAM,CAACY,EAAE,GAAGX,SAAS,CAACW,EAAE,GAAGA,EAAE;UAC7B,KAAK,IAAI3L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,OAAO,CAAChO,MAAM,EAAEL,CAAC,EAAE,EAAE;YACrCqO,OAAO,CAACrO,CAAC,CAAC,CAACmO,OAAO,CAACxC,EAAE,GAAGA,EAAE;UAC9B;QACJ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAUJ,GAAG,EAAE;UACvBD,gBAAgB,CAACP,MAAM,EAAEQ,GAAG,CAAC;UAC7B,MAAMA,GAAG;QACb,CAAC,CAAC;MACN;;MAEA;MACA;MACA,SAASgD,iBAAiBA,CAACxD,MAAM,EAAEyD,IAAI,EAAE9F,QAAQ,EAAE+F,OAAO,EAAE;QACxD,IAAIA,OAAO,KAAKC,SAAS,EAAE;UACvBD,OAAO,GAAG,CAAC;QACf;QAEA,IAAI;UACA,IAAIE,EAAE,GAAG5D,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAACc,MAAM,CAACmB,SAAS,EAAEsC,IAAI,CAAC;UACtD9F,QAAQ,CAAC,IAAI,EAAEiG,EAAE,CAAC;QACtB,CAAC,CAAC,OAAOpD,GAAG,EAAE;UACV,IAAIkD,OAAO,GAAG,CAAC,KAAK,CAAC1D,MAAM,CAACY,EAAE,IAAIJ,GAAG,CAACzD,IAAI,KAAK,mBAAmB,IAAIyD,GAAG,CAACzD,IAAI,KAAK,eAAe,CAAC,EAAE;YACjG,OAAOU,SAAS,CAACtE,OAAO,CAAC,CAAC,CAACX,IAAI,CAAC,YAAY;cACxC,IAAI,CAACwH,MAAM,CAACY,EAAE,IAAIJ,GAAG,CAACzD,IAAI,KAAK,eAAe,IAAI,CAACiD,MAAM,CAACY,EAAE,CAACoB,gBAAgB,CAACC,QAAQ,CAACjC,MAAM,CAACmB,SAAS,CAAC,IAAInB,MAAM,CAACe,OAAO,IAAIf,MAAM,CAACY,EAAE,CAACG,OAAO,EAAE;gBAC7I;gBACA,IAAIf,MAAM,CAACY,EAAE,EAAE;kBACXZ,MAAM,CAACe,OAAO,GAAGf,MAAM,CAACY,EAAE,CAACG,OAAO,GAAG,CAAC;gBAC1C;gBACA;gBACA,OAAOa,sBAAsB,CAAC5B,MAAM,CAAC;cACzC;YACJ,CAAC,CAAC,CAACxH,IAAI,CAAC,YAAY;cAChB,OAAO6K,aAAa,CAACrD,MAAM,CAAC,CAACxH,IAAI,CAAC,YAAY;gBAC1CgL,iBAAiB,CAACxD,MAAM,EAAEyD,IAAI,EAAE9F,QAAQ,EAAE+F,OAAO,GAAG,CAAC,CAAC;cAC1D,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC/F,QAAQ,CAAC;UACzB;UAEAA,QAAQ,CAAC6C,GAAG,CAAC;QACjB;MACJ;MAEA,SAASG,eAAeA,CAAA,EAAG;QACvB,OAAO;UACH;UACA2C,OAAO,EAAE,EAAE;UACX;UACA1C,EAAE,EAAE,IAAI;UACR;UACAR,OAAO,EAAE,IAAI;UACb;UACAD,kBAAkB,EAAE;QACxB,CAAC;MACL;;MAEA;MACA;MACA,SAAS0D,YAAYA,CAACC,OAAO,EAAE;QAC3B,IAAIxP,IAAI,GAAG,IAAI;QACf,IAAI0L,MAAM,GAAG;UACTY,EAAE,EAAE;QACR,CAAC;QAED,IAAIkD,OAAO,EAAE;UACT,KAAK,IAAI7O,CAAC,IAAI6O,OAAO,EAAE;YACnB9D,MAAM,CAAC/K,CAAC,CAAC,GAAG6O,OAAO,CAAC7O,CAAC,CAAC;UAC1B;QACJ;;QAEA;QACA,IAAIgL,SAAS,GAAG3B,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC;;QAEvC;QACA,IAAI,CAACkD,SAAS,EAAE;UACZA,SAAS,GAAGU,eAAe,CAAC,CAAC;UAC7B;UACArC,UAAU,CAAC0B,MAAM,CAACjD,IAAI,CAAC,GAAGkD,SAAS;QACvC;;QAEA;QACAA,SAAS,CAACqD,OAAO,CAAC5L,IAAI,CAACpD,IAAI,CAAC;;QAE5B;QACA,IAAI,CAACA,IAAI,CAAC6O,UAAU,EAAE;UAClB7O,IAAI,CAAC6O,UAAU,GAAG7O,IAAI,CAACyP,KAAK;UAC5BzP,IAAI,CAACyP,KAAK,GAAGb,WAAW;QAC5B;;QAEA;QACA,IAAIc,YAAY,GAAG,EAAE;QAErB,SAASC,YAAYA,CAAA,EAAG;UACpB;UACA;UACA,OAAOxG,SAAS,CAACtE,OAAO,CAAC,CAAC;QAC9B;QAEA,KAAK,IAAI+K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjE,SAAS,CAACqD,OAAO,CAAChO,MAAM,EAAE4O,CAAC,EAAE,EAAE;UAC/C,IAAIX,MAAM,GAAGtD,SAAS,CAACqD,OAAO,CAACY,CAAC,CAAC;UACjC,IAAIX,MAAM,KAAKjP,IAAI,EAAE;YACjB;YACA0P,YAAY,CAACtM,IAAI,CAAC6L,MAAM,CAACJ,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAACc,YAAY,CAAC,CAAC;UACjE;QACJ;;QAEA;QACA,IAAIX,OAAO,GAAGrD,SAAS,CAACqD,OAAO,CAACa,KAAK,CAAC,CAAC,CAAC;;QAExC;QACA;QACA,OAAO1G,SAAS,CAACnD,GAAG,CAAC0J,YAAY,CAAC,CAACxL,IAAI,CAAC,YAAY;UAChDwH,MAAM,CAACY,EAAE,GAAGX,SAAS,CAACW,EAAE;UACxB;UACA,OAAOe,sBAAsB,CAAC3B,MAAM,CAAC;QACzC,CAAC,CAAC,CAACxH,IAAI,CAAC,UAAUoI,EAAE,EAAE;UAClBZ,MAAM,CAACY,EAAE,GAAGA,EAAE;UACd,IAAIiB,gBAAgB,CAAC7B,MAAM,EAAE1L,IAAI,CAAC8P,cAAc,CAACrD,OAAO,CAAC,EAAE;YACvD;YACA,OAAOa,sBAAsB,CAAC5B,MAAM,CAAC;UACzC;UACA,OAAOY,EAAE;QACb,CAAC,CAAC,CAACpI,IAAI,CAAC,UAAUoI,EAAE,EAAE;UAClBZ,MAAM,CAACY,EAAE,GAAGX,SAAS,CAACW,EAAE,GAAGA,EAAE;UAC7BtM,IAAI,CAAC8O,OAAO,GAAGpD,MAAM;UACrB;UACA,KAAK,IAAIqE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,OAAO,CAAChO,MAAM,EAAE+O,CAAC,EAAE,EAAE;YACrC,IAAId,MAAM,GAAGD,OAAO,CAACe,CAAC,CAAC;YACvB,IAAId,MAAM,KAAKjP,IAAI,EAAE;cACjB;cACAiP,MAAM,CAACH,OAAO,CAACxC,EAAE,GAAGZ,MAAM,CAACY,EAAE;cAC7B2C,MAAM,CAACH,OAAO,CAACrC,OAAO,GAAGf,MAAM,CAACe,OAAO;YAC3C;UACJ;QACJ,CAAC,CAAC;MACN;MAEA,SAASuD,OAAOA,CAACvG,GAAG,EAAEJ,QAAQ,EAAE;QAC5B,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BgL,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE7E,SAAS,EAAE,UAAUiC,GAAG,EAAEtB,WAAW,EAAE;cACnE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;gBAC3D,IAAIqD,GAAG,GAAGD,KAAK,CAACE,GAAG,CAAC1G,GAAG,CAAC;gBAExByG,GAAG,CAAChD,SAAS,GAAG,YAAY;kBACxB,IAAItI,KAAK,GAAGsL,GAAG,CAACjL,MAAM;kBACtB,IAAIL,KAAK,KAAKyK,SAAS,EAAE;oBACrBzK,KAAK,GAAG,IAAI;kBAChB;kBACA,IAAI+J,cAAc,CAAC/J,KAAK,CAAC,EAAE;oBACvBA,KAAK,GAAG2J,WAAW,CAAC3J,KAAK,CAAC;kBAC9B;kBACAC,OAAO,CAACD,KAAK,CAAC;gBAClB,CAAC;gBAEDsL,GAAG,CAACjD,OAAO,GAAG,YAAY;kBACtBnI,MAAM,CAACoL,GAAG,CAAC5K,KAAK,CAAC;gBACrB,CAAC;cACL,CAAC,CAAC,OAAOpF,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA,SAASgM,OAAOA,CAACtJ,QAAQ,EAAEuC,QAAQ,EAAE;QACjC,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BgL,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE7E,SAAS,EAAE,UAAUiC,GAAG,EAAEtB,WAAW,EAAE;cACnE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;gBAC3D,IAAIqD,GAAG,GAAGD,KAAK,CAACI,UAAU,CAAC,CAAC;gBAC5B,IAAIC,eAAe,GAAG,CAAC;gBAEvBJ,GAAG,CAAChD,SAAS,GAAG,YAAY;kBACxB,IAAIqD,MAAM,GAAGL,GAAG,CAACjL,MAAM;kBAEvB,IAAIsL,MAAM,EAAE;oBACR,IAAI3L,KAAK,GAAG2L,MAAM,CAAC3L,KAAK;oBACxB,IAAI+J,cAAc,CAAC/J,KAAK,CAAC,EAAE;sBACvBA,KAAK,GAAG2J,WAAW,CAAC3J,KAAK,CAAC;oBAC9B;oBACA,IAAIK,MAAM,GAAG6B,QAAQ,CAAClC,KAAK,EAAE2L,MAAM,CAAC9G,GAAG,EAAE6G,eAAe,EAAE,CAAC;;oBAE3D;oBACA;oBACA;oBACA,IAAIrL,MAAM,KAAK,KAAK,CAAC,EAAE;sBACnBJ,OAAO,CAACI,MAAM,CAAC;oBACnB,CAAC,MAAM;sBACHsL,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;oBACxB;kBACJ,CAAC,MAAM;oBACH1L,OAAO,CAAC,CAAC;kBACb;gBACJ,CAAC;gBAEDqL,GAAG,CAACjD,OAAO,GAAG,YAAY;kBACtBnI,MAAM,CAACoL,GAAG,CAAC5K,KAAK,CAAC;gBACrB,CAAC;cACL,CAAC,CAAC,OAAOpF,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAElC,OAAOjF,OAAO;MAClB;MAEA,SAASoM,OAAOA,CAAC/G,GAAG,EAAE7E,KAAK,EAAEyE,QAAQ,EAAE;QACnC,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD,IAAI4G,MAAM;UACV1L,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACrB,IAAI3I,QAAQ,CAACpF,IAAI,CAAC6D,KAAK,CAAC,KAAK,eAAe,EAAE;cAC1C,OAAO4G,iBAAiB,CAACE,MAAM,CAACY,EAAE,CAAC,CAACpI,IAAI,CAAC,UAAUuM,WAAW,EAAE;gBAC5D,IAAIA,WAAW,EAAE;kBACb,OAAO7L,KAAK;gBAChB;gBACA,OAAOmJ,WAAW,CAACnJ,KAAK,CAAC;cAC7B,CAAC,CAAC;YACN;YACA,OAAOA,KAAK;UAChB,CAAC,CAAC,CAACV,IAAI,CAAC,UAAUU,KAAK,EAAE;YACrBsK,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE5E,UAAU,EAAE,UAAUgC,GAAG,EAAEtB,WAAW,EAAE;cACpE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;;gBAE3D;gBACA;gBACA;gBACA;gBACA,IAAIjI,KAAK,KAAK,IAAI,EAAE;kBAChBA,KAAK,GAAGyK,SAAS;gBACrB;gBAEA,IAAIa,GAAG,GAAGD,KAAK,CAAClF,GAAG,CAACnG,KAAK,EAAE6E,GAAG,CAAC;gBAE/BmB,WAAW,CAACO,UAAU,GAAG,YAAY;kBACjC;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA,IAAIvG,KAAK,KAAKyK,SAAS,EAAE;oBACrBzK,KAAK,GAAG,IAAI;kBAChB;kBAEAC,OAAO,CAACD,KAAK,CAAC;gBAClB,CAAC;gBACDgG,WAAW,CAACI,OAAO,GAAGJ,WAAW,CAACqC,OAAO,GAAG,YAAY;kBACpD,IAAIf,GAAG,GAAGgE,GAAG,CAAC5K,KAAK,GAAG4K,GAAG,CAAC5K,KAAK,GAAG4K,GAAG,CAACtF,WAAW,CAACtF,KAAK;kBACvDR,MAAM,CAACoH,GAAG,CAAC;gBACf,CAAC;cACL,CAAC,CAAC,OAAOhM,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASsM,UAAUA,CAACjH,GAAG,EAAEJ,QAAQ,EAAE;QAC/B,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BgL,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE5E,UAAU,EAAE,UAAUgC,GAAG,EAAEtB,WAAW,EAAE;cACpE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;gBAC3D;gBACA;gBACA;gBACA;gBACA;gBACA,IAAIqD,GAAG,GAAGD,KAAK,CAAC,QAAQ,CAAC,CAACxG,GAAG,CAAC;gBAC9BmB,WAAW,CAACO,UAAU,GAAG,YAAY;kBACjCtG,OAAO,CAAC,CAAC;gBACb,CAAC;gBAED+F,WAAW,CAACqC,OAAO,GAAG,YAAY;kBAC9BnI,MAAM,CAACoL,GAAG,CAAC5K,KAAK,CAAC;gBACrB,CAAC;;gBAED;gBACA;gBACAsF,WAAW,CAACI,OAAO,GAAG,YAAY;kBAC9B,IAAIkB,GAAG,GAAGgE,GAAG,CAAC5K,KAAK,GAAG4K,GAAG,CAAC5K,KAAK,GAAG4K,GAAG,CAACtF,WAAW,CAACtF,KAAK;kBACvDR,MAAM,CAACoH,GAAG,CAAC;gBACf,CAAC;cACL,CAAC,CAAC,OAAOhM,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASuM,KAAKA,CAACtH,QAAQ,EAAE;QACrB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BgL,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE5E,UAAU,EAAE,UAAUgC,GAAG,EAAEtB,WAAW,EAAE;cACpE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;gBAC3D,IAAIqD,GAAG,GAAGD,KAAK,CAACU,KAAK,CAAC,CAAC;gBAEvB/F,WAAW,CAACO,UAAU,GAAG,YAAY;kBACjCtG,OAAO,CAAC,CAAC;gBACb,CAAC;gBAED+F,WAAW,CAACI,OAAO,GAAGJ,WAAW,CAACqC,OAAO,GAAG,YAAY;kBACpD,IAAIf,GAAG,GAAGgE,GAAG,CAAC5K,KAAK,GAAG4K,GAAG,CAAC5K,KAAK,GAAG4K,GAAG,CAACtF,WAAW,CAACtF,KAAK;kBACvDR,MAAM,CAACoH,GAAG,CAAC;gBACf,CAAC;cACL,CAAC,CAAC,OAAOhM,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASpD,MAAMA,CAACqI,QAAQ,EAAE;QACtB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BgL,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE7E,SAAS,EAAE,UAAUiC,GAAG,EAAEtB,WAAW,EAAE;cACnE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;gBAC3D,IAAIqD,GAAG,GAAGD,KAAK,CAACW,KAAK,CAAC,CAAC;gBAEvBV,GAAG,CAAChD,SAAS,GAAG,YAAY;kBACxBrI,OAAO,CAACqL,GAAG,CAACjL,MAAM,CAAC;gBACvB,CAAC;gBAEDiL,GAAG,CAACjD,OAAO,GAAG,YAAY;kBACtBnI,MAAM,CAACoL,GAAG,CAAC5K,KAAK,CAAC;gBACrB,CAAC;cACL,CAAC,CAAC,OAAOpF,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASqF,GAAGA,CAACrJ,CAAC,EAAEiJ,QAAQ,EAAE;QACtB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD,IAAI1E,CAAC,GAAG,CAAC,EAAE;YACPyE,OAAO,CAAC,IAAI,CAAC;YAEb;UACJ;UAEA7E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BgL,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE7E,SAAS,EAAE,UAAUiC,GAAG,EAAEtB,WAAW,EAAE;cACnE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;gBAC3D,IAAIgE,QAAQ,GAAG,KAAK;gBACpB,IAAIX,GAAG,GAAGD,KAAK,CAACa,aAAa,CAAC,CAAC;gBAE/BZ,GAAG,CAAChD,SAAS,GAAG,YAAY;kBACxB,IAAIqD,MAAM,GAAGL,GAAG,CAACjL,MAAM;kBACvB,IAAI,CAACsL,MAAM,EAAE;oBACT;oBACA1L,OAAO,CAAC,IAAI,CAAC;oBAEb;kBACJ;kBAEA,IAAIzE,CAAC,KAAK,CAAC,EAAE;oBACT;oBACA;oBACAyE,OAAO,CAAC0L,MAAM,CAAC9G,GAAG,CAAC;kBACvB,CAAC,MAAM;oBACH,IAAI,CAACoH,QAAQ,EAAE;sBACX;sBACA;sBACAA,QAAQ,GAAG,IAAI;sBACfN,MAAM,CAACQ,OAAO,CAAC3Q,CAAC,CAAC;oBACrB,CAAC,MAAM;sBACH;sBACAyE,OAAO,CAAC0L,MAAM,CAAC9G,GAAG,CAAC;oBACvB;kBACJ;gBACJ,CAAC;gBAEDyG,GAAG,CAACjD,OAAO,GAAG,YAAY;kBACtBnI,MAAM,CAACoL,GAAG,CAAC5K,KAAK,CAAC;gBACrB,CAAC;cACL,CAAC,CAAC,OAAOpF,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAAS4M,IAAIA,CAAC3H,QAAQ,EAAE;QACpB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1BgL,iBAAiB,CAAClP,IAAI,CAAC8O,OAAO,EAAE7E,SAAS,EAAE,UAAUiC,GAAG,EAAEtB,WAAW,EAAE;cACnE,IAAIsB,GAAG,EAAE;gBACL,OAAOpH,MAAM,CAACoH,GAAG,CAAC;cACtB;cAEA,IAAI;gBACA,IAAI+D,KAAK,GAAGrF,WAAW,CAACE,WAAW,CAAC9K,IAAI,CAAC8O,OAAO,CAACjC,SAAS,CAAC;gBAC3D,IAAIqD,GAAG,GAAGD,KAAK,CAACa,aAAa,CAAC,CAAC;gBAC/B,IAAIE,IAAI,GAAG,EAAE;gBAEbd,GAAG,CAAChD,SAAS,GAAG,YAAY;kBACxB,IAAIqD,MAAM,GAAGL,GAAG,CAACjL,MAAM;kBAEvB,IAAI,CAACsL,MAAM,EAAE;oBACT1L,OAAO,CAACmM,IAAI,CAAC;oBACb;kBACJ;kBAEAA,IAAI,CAAC5N,IAAI,CAACmN,MAAM,CAAC9G,GAAG,CAAC;kBACrB8G,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;gBACxB,CAAC;gBAEDL,GAAG,CAACjD,OAAO,GAAG,YAAY;kBACtBnI,MAAM,CAACoL,GAAG,CAAC5K,KAAK,CAAC;gBACrB,CAAC;cACL,CAAC,CAAC,OAAOpF,CAAC,EAAE;gBACR4E,MAAM,CAAC5E,CAAC,CAAC;cACb;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC4E,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAAS6M,YAAYA,CAACzB,OAAO,EAAEnG,QAAQ,EAAE;QACrCA,QAAQ,GAAGQ,WAAW,CAACpE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAE7C,IAAIwL,aAAa,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;QACjC3B,OAAO,GAAG,OAAOA,OAAO,KAAK,UAAU,IAAIA,OAAO,IAAI,CAAC,CAAC;QACxD,IAAI,CAACA,OAAO,CAAC/G,IAAI,EAAE;UACf+G,OAAO,CAAC/G,IAAI,GAAG+G,OAAO,CAAC/G,IAAI,IAAIyI,aAAa,CAACzI,IAAI;UACjD+G,OAAO,CAAC3C,SAAS,GAAG2C,OAAO,CAAC3C,SAAS,IAAIqE,aAAa,CAACrE,SAAS;QACpE;QAEA,IAAI7M,IAAI,GAAG,IAAI;QACf,IAAIoE,OAAO;QACX,IAAI,CAACoL,OAAO,CAAC/G,IAAI,EAAE;UACfrE,OAAO,GAAG+E,SAAS,CAACrE,MAAM,CAAC,mBAAmB,CAAC;QACnD,CAAC,MAAM;UACH,IAAIsM,WAAW,GAAG5B,OAAO,CAAC/G,IAAI,KAAKyI,aAAa,CAACzI,IAAI,IAAIzI,IAAI,CAAC8O,OAAO,CAACxC,EAAE;UAExE,IAAI+E,SAAS,GAAGD,WAAW,GAAGjI,SAAS,CAACtE,OAAO,CAAC7E,IAAI,CAAC8O,OAAO,CAACxC,EAAE,CAAC,GAAGe,sBAAsB,CAACmC,OAAO,CAAC,CAACtL,IAAI,CAAC,UAAUoI,EAAE,EAAE;YAClH,IAAIX,SAAS,GAAG3B,UAAU,CAACwF,OAAO,CAAC/G,IAAI,CAAC;YACxC,IAAIuG,OAAO,GAAGrD,SAAS,CAACqD,OAAO;YAC/BrD,SAAS,CAACW,EAAE,GAAGA,EAAE;YACjB,KAAK,IAAI3L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,OAAO,CAAChO,MAAM,EAAEL,CAAC,EAAE,EAAE;cACrCqO,OAAO,CAACrO,CAAC,CAAC,CAACmO,OAAO,CAACxC,EAAE,GAAGA,EAAE;YAC9B;YACA,OAAOA,EAAE;UACb,CAAC,CAAC;UAEF,IAAI,CAACkD,OAAO,CAAC3C,SAAS,EAAE;YACpBzI,OAAO,GAAGiN,SAAS,CAACnN,IAAI,CAAC,UAAUoI,EAAE,EAAE;cACnCb,eAAe,CAAC+D,OAAO,CAAC;cAExB,IAAI7D,SAAS,GAAG3B,UAAU,CAACwF,OAAO,CAAC/G,IAAI,CAAC;cACxC,IAAIuG,OAAO,GAAGrD,SAAS,CAACqD,OAAO;cAE/B1C,EAAE,CAACC,KAAK,CAAC,CAAC;cACV,KAAK,IAAI5L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,OAAO,CAAChO,MAAM,EAAEL,CAAC,EAAE,EAAE;gBACrC,IAAIsO,MAAM,GAAGD,OAAO,CAACrO,CAAC,CAAC;gBACvBsO,MAAM,CAACH,OAAO,CAACxC,EAAE,GAAG,IAAI;cAC5B;cAEA,IAAIgF,aAAa,GAAG,IAAInI,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;gBACzD,IAAIoL,GAAG,GAAG1I,GAAG,CAAC+J,cAAc,CAAC/B,OAAO,CAAC/G,IAAI,CAAC;gBAE1CyH,GAAG,CAACjD,OAAO,GAAG,YAAY;kBACtB,IAAIX,EAAE,GAAG4D,GAAG,CAACjL,MAAM;kBACnB,IAAIqH,EAAE,EAAE;oBACJA,EAAE,CAACC,KAAK,CAAC,CAAC;kBACd;kBACAzH,MAAM,CAACoL,GAAG,CAAC5K,KAAK,CAAC;gBACrB,CAAC;gBAED4K,GAAG,CAACsB,SAAS,GAAG,YAAY;kBACxB;kBACA;kBACA9H,OAAO,CAACC,IAAI,CAAC,qCAAqC,GAAG6F,OAAO,CAAC/G,IAAI,GAAG,yCAAyC,CAAC;gBAClH,CAAC;gBAEDyH,GAAG,CAAChD,SAAS,GAAG,YAAY;kBACxB,IAAIZ,EAAE,GAAG4D,GAAG,CAACjL,MAAM;kBACnB,IAAIqH,EAAE,EAAE;oBACJA,EAAE,CAACC,KAAK,CAAC,CAAC;kBACd;kBACA1H,OAAO,CAACyH,EAAE,CAAC;gBACf,CAAC;cACL,CAAC,CAAC;cAEF,OAAOgF,aAAa,CAACpN,IAAI,CAAC,UAAUoI,EAAE,EAAE;gBACpCX,SAAS,CAACW,EAAE,GAAGA,EAAE;gBACjB,KAAK,IAAI3L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,OAAO,CAAChO,MAAM,EAAEL,CAAC,EAAE,EAAE;kBACrC,IAAI8Q,OAAO,GAAGzC,OAAO,CAACrO,CAAC,CAAC;kBACxBoL,iBAAiB,CAAC0F,OAAO,CAAC3C,OAAO,CAAC;gBACtC;cACJ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU5C,GAAG,EAAE;gBACvB,CAACD,gBAAgB,CAACuD,OAAO,EAAEtD,GAAG,CAAC,IAAI/C,SAAS,CAACtE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;gBAChF,MAAMqH,GAAG;cACb,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,MAAM;YACH9H,OAAO,GAAGiN,SAAS,CAACnN,IAAI,CAAC,UAAUoI,EAAE,EAAE;cACnC,IAAI,CAACA,EAAE,CAACoB,gBAAgB,CAACC,QAAQ,CAAC6B,OAAO,CAAC3C,SAAS,CAAC,EAAE;gBAClD;cACJ;cAEA,IAAIG,UAAU,GAAGV,EAAE,CAACG,OAAO,GAAG,CAAC;cAE/BhB,eAAe,CAAC+D,OAAO,CAAC;cAExB,IAAI7D,SAAS,GAAG3B,UAAU,CAACwF,OAAO,CAAC/G,IAAI,CAAC;cACxC,IAAIuG,OAAO,GAAGrD,SAAS,CAACqD,OAAO;cAE/B1C,EAAE,CAACC,KAAK,CAAC,CAAC;cACV,KAAK,IAAI5L,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,OAAO,CAAChO,MAAM,EAAEL,CAAC,EAAE,EAAE;gBACrC,IAAIsO,MAAM,GAAGD,OAAO,CAACrO,CAAC,CAAC;gBACvBsO,MAAM,CAACH,OAAO,CAACxC,EAAE,GAAG,IAAI;gBACxB2C,MAAM,CAACH,OAAO,CAACrC,OAAO,GAAGO,UAAU;cACvC;cAEA,IAAI0E,iBAAiB,GAAG,IAAIvI,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;gBAC7D,IAAIoL,GAAG,GAAG1I,GAAG,CAACE,IAAI,CAAC8H,OAAO,CAAC/G,IAAI,EAAEuE,UAAU,CAAC;gBAE5CkD,GAAG,CAACjD,OAAO,GAAG,UAAUf,GAAG,EAAE;kBACzB,IAAII,EAAE,GAAG4D,GAAG,CAACjL,MAAM;kBACnBqH,EAAE,CAACC,KAAK,CAAC,CAAC;kBACVzH,MAAM,CAACoH,GAAG,CAAC;gBACf,CAAC;gBAEDgE,GAAG,CAACvD,eAAe,GAAG,YAAY;kBAC9B,IAAIL,EAAE,GAAG4D,GAAG,CAACjL,MAAM;kBACnBqH,EAAE,CAACqF,iBAAiB,CAACnC,OAAO,CAAC3C,SAAS,CAAC;gBAC3C,CAAC;gBAEDqD,GAAG,CAAChD,SAAS,GAAG,YAAY;kBACxB,IAAIZ,EAAE,GAAG4D,GAAG,CAACjL,MAAM;kBACnBqH,EAAE,CAACC,KAAK,CAAC,CAAC;kBACV1H,OAAO,CAACyH,EAAE,CAAC;gBACf,CAAC;cACL,CAAC,CAAC;cAEF,OAAOoF,iBAAiB,CAACxN,IAAI,CAAC,UAAUoI,EAAE,EAAE;gBACxCX,SAAS,CAACW,EAAE,GAAGA,EAAE;gBACjB,KAAK,IAAIsD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,OAAO,CAAChO,MAAM,EAAE4O,CAAC,EAAE,EAAE;kBACrC,IAAIgC,QAAQ,GAAG5C,OAAO,CAACY,CAAC,CAAC;kBACzBgC,QAAQ,CAAC9C,OAAO,CAACxC,EAAE,GAAGA,EAAE;kBACxBP,iBAAiB,CAAC6F,QAAQ,CAAC9C,OAAO,CAAC;gBACvC;cACJ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU5C,GAAG,EAAE;gBACvB,CAACD,gBAAgB,CAACuD,OAAO,EAAEtD,GAAG,CAAC,IAAI/C,SAAS,CAACtE,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;gBAChF,MAAMqH,GAAG;cACb,CAAC,CAAC;YACN,CAAC,CAAC;UACN;QACJ;QAEA9C,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,IAAIyN,YAAY,GAAG;QACfC,OAAO,EAAE,cAAc;QACvBvC,YAAY,EAAEA,YAAY;QAC1BwC,QAAQ,EAAEtK,gBAAgB,CAAC,CAAC;QAC5B2I,OAAO,EAAEA,OAAO;QAChBJ,OAAO,EAAEA,OAAO;QAChBQ,OAAO,EAAEA,OAAO;QAChBE,UAAU,EAAEA,UAAU;QACtBC,KAAK,EAAEA,KAAK;QACZ3P,MAAM,EAAEA,MAAM;QACdyI,GAAG,EAAEA,GAAG;QACRuH,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA;MAClB,CAAC;MAED,SAASe,aAAaA,CAAA,EAAG;QACrB,OAAO,OAAOpK,YAAY,KAAK,UAAU;MAC7C;;MAEA;MACA;MACA;MACA,IAAIqK,UAAU,GAAG,kEAAkE;MAEnF,IAAIC,gBAAgB,GAAG,sBAAsB;MAC7C,IAAIC,sBAAsB,GAAG,+BAA+B;MAE5D,IAAIC,iBAAiB,GAAG,WAAW;MACnC,IAAIC,wBAAwB,GAAGD,iBAAiB,CAACpR,MAAM;;MAEvD;MACA,IAAIsR,gBAAgB,GAAG,MAAM;MAC7B,IAAIC,SAAS,GAAG,MAAM;MACtB,IAAIC,cAAc,GAAG,MAAM;MAC3B,IAAIC,eAAe,GAAG,MAAM;MAC5B,IAAIC,sBAAsB,GAAG,MAAM;MACnC,IAAIC,eAAe,GAAG,MAAM;MAC5B,IAAIC,eAAe,GAAG,MAAM;MAC5B,IAAIC,gBAAgB,GAAG,MAAM;MAC7B,IAAIC,gBAAgB,GAAG,MAAM;MAC7B,IAAIC,iBAAiB,GAAG,MAAM;MAC9B,IAAIC,iBAAiB,GAAG,MAAM;MAC9B,IAAIC,6BAA6B,GAAGZ,wBAAwB,GAAGC,gBAAgB,CAACtR,MAAM;MAEtF,IAAIkS,UAAU,GAAGhN,MAAM,CAAClC,SAAS,CAACmC,QAAQ;MAE1C,SAASgN,cAAcA,CAACC,gBAAgB,EAAE;QACtC;QACA,IAAIC,YAAY,GAAGD,gBAAgB,CAACpS,MAAM,GAAG,IAAI;QACjD,IAAIiC,GAAG,GAAGmQ,gBAAgB,CAACpS,MAAM;QACjC,IAAIL,CAAC;QACL,IAAI2S,CAAC,GAAG,CAAC;QACT,IAAIC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ;QAE1C,IAAIN,gBAAgB,CAACA,gBAAgB,CAACpS,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UACvDqS,YAAY,EAAE;UACd,IAAID,gBAAgB,CAACA,gBAAgB,CAACpS,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;YACvDqS,YAAY,EAAE;UAClB;QACJ;QAEA,IAAIM,MAAM,GAAG,IAAIrJ,WAAW,CAAC+I,YAAY,CAAC;QAC1C,IAAIO,KAAK,GAAG,IAAIpJ,UAAU,CAACmJ,MAAM,CAAC;QAElC,KAAKhT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,IAAI,CAAC,EAAE;UACzB4S,QAAQ,GAAGtB,UAAU,CAAC9J,OAAO,CAACiL,gBAAgB,CAACzS,CAAC,CAAC,CAAC;UAClD6S,QAAQ,GAAGvB,UAAU,CAAC9J,OAAO,CAACiL,gBAAgB,CAACzS,CAAC,GAAG,CAAC,CAAC,CAAC;UACtD8S,QAAQ,GAAGxB,UAAU,CAAC9J,OAAO,CAACiL,gBAAgB,CAACzS,CAAC,GAAG,CAAC,CAAC,CAAC;UACtD+S,QAAQ,GAAGzB,UAAU,CAAC9J,OAAO,CAACiL,gBAAgB,CAACzS,CAAC,GAAG,CAAC,CAAC,CAAC;;UAEtD;UACAiT,KAAK,CAACN,CAAC,EAAE,CAAC,GAAGC,QAAQ,IAAI,CAAC,GAAGC,QAAQ,IAAI,CAAC;UAC1CI,KAAK,CAACN,CAAC,EAAE,CAAC,GAAG,CAACE,QAAQ,GAAG,EAAE,KAAK,CAAC,GAAGC,QAAQ,IAAI,CAAC;UACjDG,KAAK,CAACN,CAAC,EAAE,CAAC,GAAG,CAACG,QAAQ,GAAG,CAAC,KAAK,CAAC,GAAGC,QAAQ,GAAG,EAAE;QACpD;QACA,OAAOC,MAAM;MACjB;;MAEA;MACA;MACA,SAASE,cAAcA,CAACF,MAAM,EAAE;QAC5B;QACA,IAAIC,KAAK,GAAG,IAAIpJ,UAAU,CAACmJ,MAAM,CAAC;QAClC,IAAIG,YAAY,GAAG,EAAE;QACrB,IAAInT,CAAC;QAEL,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiT,KAAK,CAAC5S,MAAM,EAAEL,CAAC,IAAI,CAAC,EAAE;UAClC;UACAmT,YAAY,IAAI7B,UAAU,CAAC2B,KAAK,CAACjT,CAAC,CAAC,IAAI,CAAC,CAAC;UACzCmT,YAAY,IAAI7B,UAAU,CAAC,CAAC2B,KAAK,CAACjT,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGiT,KAAK,CAACjT,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UACnEmT,YAAY,IAAI7B,UAAU,CAAC,CAAC2B,KAAK,CAACjT,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,GAAGiT,KAAK,CAACjT,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;UACxEmT,YAAY,IAAI7B,UAAU,CAAC2B,KAAK,CAACjT,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACjD;QAEA,IAAIiT,KAAK,CAAC5S,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;UACxB8S,YAAY,GAAGA,YAAY,CAACC,SAAS,CAAC,CAAC,EAAED,YAAY,CAAC9S,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG;QAC3E,CAAC,MAAM,IAAI4S,KAAK,CAAC5S,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;UAC/B8S,YAAY,GAAGA,YAAY,CAACC,SAAS,CAAC,CAAC,EAAED,YAAY,CAAC9S,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;QAC5E;QAEA,OAAO8S,YAAY;MACvB;;MAEA;MACA;MACA;MACA,SAASE,SAASA,CAACpP,KAAK,EAAEyE,QAAQ,EAAE;QAChC,IAAI4K,SAAS,GAAG,EAAE;QAClB,IAAIrP,KAAK,EAAE;UACPqP,SAAS,GAAGf,UAAU,CAACnS,IAAI,CAAC6D,KAAK,CAAC;QACtC;;QAEA;QACA;QACA;QACA;QACA,IAAIA,KAAK,KAAKqP,SAAS,KAAK,sBAAsB,IAAIrP,KAAK,CAAC+O,MAAM,IAAIT,UAAU,CAACnS,IAAI,CAAC6D,KAAK,CAAC+O,MAAM,CAAC,KAAK,sBAAsB,CAAC,EAAE;UAC7H;UACA;UACA,IAAIA,MAAM;UACV,IAAIO,MAAM,GAAG9B,iBAAiB;UAE9B,IAAIxN,KAAK,YAAY0F,WAAW,EAAE;YAC9BqJ,MAAM,GAAG/O,KAAK;YACdsP,MAAM,IAAI5B,gBAAgB;UAC9B,CAAC,MAAM;YACHqB,MAAM,GAAG/O,KAAK,CAAC+O,MAAM;YAErB,IAAIM,SAAS,KAAK,oBAAoB,EAAE;cACpCC,MAAM,IAAI1B,cAAc;YAC5B,CAAC,MAAM,IAAIyB,SAAS,KAAK,qBAAqB,EAAE;cAC5CC,MAAM,IAAIzB,eAAe;YAC7B,CAAC,MAAM,IAAIwB,SAAS,KAAK,4BAA4B,EAAE;cACnDC,MAAM,IAAIxB,sBAAsB;YACpC,CAAC,MAAM,IAAIuB,SAAS,KAAK,qBAAqB,EAAE;cAC5CC,MAAM,IAAIvB,eAAe;YAC7B,CAAC,MAAM,IAAIsB,SAAS,KAAK,sBAAsB,EAAE;cAC7CC,MAAM,IAAIrB,gBAAgB;YAC9B,CAAC,MAAM,IAAIoB,SAAS,KAAK,qBAAqB,EAAE;cAC5CC,MAAM,IAAItB,eAAe;YAC7B,CAAC,MAAM,IAAIqB,SAAS,KAAK,sBAAsB,EAAE;cAC7CC,MAAM,IAAIpB,gBAAgB;YAC9B,CAAC,MAAM,IAAImB,SAAS,KAAK,uBAAuB,EAAE;cAC9CC,MAAM,IAAInB,iBAAiB;YAC/B,CAAC,MAAM,IAAIkB,SAAS,KAAK,uBAAuB,EAAE;cAC9CC,MAAM,IAAIlB,iBAAiB;YAC/B,CAAC,MAAM;cACH3J,QAAQ,CAAC,IAAIzI,KAAK,CAAC,oCAAoC,CAAC,CAAC;YAC7D;UACJ;UAEAyI,QAAQ,CAAC6K,MAAM,GAAGL,cAAc,CAACF,MAAM,CAAC,CAAC;QAC7C,CAAC,MAAM,IAAIM,SAAS,KAAK,eAAe,EAAE;UACtC;UACA,IAAIE,UAAU,GAAG,IAAIlG,UAAU,CAAC,CAAC;UAEjCkG,UAAU,CAACC,MAAM,GAAG,YAAY;YAC5B;YACA,IAAIC,GAAG,GAAGnC,gBAAgB,GAAGtN,KAAK,CAACsE,IAAI,GAAG,GAAG,GAAG2K,cAAc,CAAC,IAAI,CAAC5O,MAAM,CAAC;YAE3EoE,QAAQ,CAAC+I,iBAAiB,GAAGG,SAAS,GAAG8B,GAAG,CAAC;UACjD,CAAC;UAEDF,UAAU,CAACG,iBAAiB,CAAC1P,KAAK,CAAC;QACvC,CAAC,MAAM;UACH,IAAI;YACAyE,QAAQ,CAACkL,IAAI,CAACC,SAAS,CAAC5P,KAAK,CAAC,CAAC;UACnC,CAAC,CAAC,OAAO1E,CAAC,EAAE;YACRwJ,OAAO,CAACpE,KAAK,CAAC,6CAA6C,EAAEV,KAAK,CAAC;YAEnEyE,QAAQ,CAAC,IAAI,EAAEnJ,CAAC,CAAC;UACrB;QACJ;MACJ;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,SAASuU,WAAWA,CAAC7P,KAAK,EAAE;QACxB;QACA;QACA;QACA,IAAIA,KAAK,CAACmP,SAAS,CAAC,CAAC,EAAE1B,wBAAwB,CAAC,KAAKD,iBAAiB,EAAE;UACpE,OAAOmC,IAAI,CAACG,KAAK,CAAC9P,KAAK,CAAC;QAC5B;;QAEA;QACA;QACA;QACA,IAAIwO,gBAAgB,GAAGxO,KAAK,CAACmP,SAAS,CAACd,6BAA6B,CAAC;QACrE,IAAI/J,IAAI,GAAGtE,KAAK,CAACmP,SAAS,CAAC1B,wBAAwB,EAAEY,6BAA6B,CAAC;QAEnF,IAAI0B,QAAQ;QACZ;QACA;QACA,IAAIzL,IAAI,KAAKqJ,SAAS,IAAIJ,sBAAsB,CAACtK,IAAI,CAACuL,gBAAgB,CAAC,EAAE;UACrE,IAAIwB,OAAO,GAAGxB,gBAAgB,CAAC/H,KAAK,CAAC8G,sBAAsB,CAAC;UAC5DwC,QAAQ,GAAGC,OAAO,CAAC,CAAC,CAAC;UACrBxB,gBAAgB,GAAGA,gBAAgB,CAACW,SAAS,CAACa,OAAO,CAAC,CAAC,CAAC,CAAC5T,MAAM,CAAC;QACpE;QACA,IAAI2S,MAAM,GAAGR,cAAc,CAACC,gBAAgB,CAAC;;QAE7C;QACA;QACA,QAAQlK,IAAI;UACR,KAAKoJ,gBAAgB;YACjB,OAAOqB,MAAM;UACjB,KAAKpB,SAAS;YACV,OAAOlK,UAAU,CAAC,CAACsL,MAAM,CAAC,EAAE;cAAEzK,IAAI,EAAEyL;YAAS,CAAC,CAAC;UACnD,KAAKnC,cAAc;YACf,OAAO,IAAIqC,SAAS,CAAClB,MAAM,CAAC;UAChC,KAAKlB,eAAe;YAChB,OAAO,IAAIjI,UAAU,CAACmJ,MAAM,CAAC;UACjC,KAAKjB,sBAAsB;YACvB,OAAO,IAAIoC,iBAAiB,CAACnB,MAAM,CAAC;UACxC,KAAKhB,eAAe;YAChB,OAAO,IAAIoC,UAAU,CAACpB,MAAM,CAAC;UACjC,KAAKd,gBAAgB;YACjB,OAAO,IAAImC,WAAW,CAACrB,MAAM,CAAC;UAClC,KAAKf,eAAe;YAChB,OAAO,IAAIqC,UAAU,CAACtB,MAAM,CAAC;UACjC,KAAKb,gBAAgB;YACjB,OAAO,IAAIoC,WAAW,CAACvB,MAAM,CAAC;UAClC,KAAKZ,iBAAiB;YAClB,OAAO,IAAIoC,YAAY,CAACxB,MAAM,CAAC;UACnC,KAAKX,iBAAiB;YAClB,OAAO,IAAIoC,YAAY,CAACzB,MAAM,CAAC;UACnC;YACI,MAAM,IAAI/S,KAAK,CAAC,eAAe,GAAGsI,IAAI,CAAC;QAC/C;MACJ;MAEA,IAAImM,qBAAqB,GAAG;QACxBrB,SAAS,EAAEA,SAAS;QACpBS,WAAW,EAAEA,WAAW;QACxBtB,cAAc,EAAEA,cAAc;QAC9BU,cAAc,EAAEA;MACpB,CAAC;;MAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MAEA,SAASyB,aAAaA,CAACnV,CAAC,EAAEuL,MAAM,EAAErC,QAAQ,EAAEE,aAAa,EAAE;QACvDpJ,CAAC,CAACoV,UAAU,CAAC,6BAA6B,GAAG7J,MAAM,CAACmB,SAAS,GAAG,GAAG,GAAG,6CAA6C,EAAE,EAAE,EAAExD,QAAQ,EAAEE,aAAa,CAAC;MACrJ;;MAEA;MACA;MACA,SAASiM,cAAcA,CAAChG,OAAO,EAAE;QAC7B,IAAIxP,IAAI,GAAG,IAAI;QACf,IAAI0L,MAAM,GAAG;UACTY,EAAE,EAAE;QACR,CAAC;QAED,IAAIkD,OAAO,EAAE;UACT,KAAK,IAAI7O,CAAC,IAAI6O,OAAO,EAAE;YACnB9D,MAAM,CAAC/K,CAAC,CAAC,GAAG,OAAO6O,OAAO,CAAC7O,CAAC,CAAC,KAAK,QAAQ,GAAG6O,OAAO,CAAC7O,CAAC,CAAC,CAACwF,QAAQ,CAAC,CAAC,GAAGqJ,OAAO,CAAC7O,CAAC,CAAC;UACnF;QACJ;QAEA,IAAI8U,aAAa,GAAG,IAAItM,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACzD;UACA;UACA,IAAI;YACA4G,MAAM,CAACY,EAAE,GAAG1E,YAAY,CAAC8D,MAAM,CAACjD,IAAI,EAAEmB,MAAM,CAAC8B,MAAM,CAACe,OAAO,CAAC,EAAEf,MAAM,CAACgK,WAAW,EAAEhK,MAAM,CAACiK,IAAI,CAAC;UAClG,CAAC,CAAC,OAAOzV,CAAC,EAAE;YACR,OAAO4E,MAAM,CAAC5E,CAAC,CAAC;UACpB;;UAEA;UACAwL,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;YAC/BmV,aAAa,CAACnV,CAAC,EAAEuL,MAAM,EAAE,YAAY;cACjC1L,IAAI,CAAC8O,OAAO,GAAGpD,MAAM;cACrB7G,OAAO,CAAC,CAAC;YACb,CAAC,EAAE,UAAU1E,CAAC,EAAEmF,KAAK,EAAE;cACnBR,MAAM,CAACQ,KAAK,CAAC;YACjB,CAAC,CAAC;UACN,CAAC,EAAER,MAAM,CAAC;QACd,CAAC,CAAC;QAEF4G,MAAM,CAACkK,UAAU,GAAGP,qBAAqB;QACzC,OAAOI,aAAa;MACxB;MAEA,SAASI,aAAaA,CAAC1V,CAAC,EAAEuL,MAAM,EAAEoK,YAAY,EAAEC,IAAI,EAAE1M,QAAQ,EAAEE,aAAa,EAAE;QAC3EpJ,CAAC,CAACoV,UAAU,CAACO,YAAY,EAAEC,IAAI,EAAE1M,QAAQ,EAAE,UAAUlJ,CAAC,EAAEmF,KAAK,EAAE;UAC3D,IAAIA,KAAK,CAACzE,IAAI,KAAKyE,KAAK,CAAC0Q,UAAU,EAAE;YACjC7V,CAAC,CAACoV,UAAU,CAAC,iCAAiC,GAAG,iCAAiC,EAAE,CAAC7J,MAAM,CAACmB,SAAS,CAAC,EAAE,UAAU1M,CAAC,EAAE8V,OAAO,EAAE;cAC1H,IAAI,CAACA,OAAO,CAACC,IAAI,CAAClV,MAAM,EAAE;gBACtB;gBACA;gBACAsU,aAAa,CAACnV,CAAC,EAAEuL,MAAM,EAAE,YAAY;kBACjCvL,CAAC,CAACoV,UAAU,CAACO,YAAY,EAAEC,IAAI,EAAE1M,QAAQ,EAAEE,aAAa,CAAC;gBAC7D,CAAC,EAAEA,aAAa,CAAC;cACrB,CAAC,MAAM;gBACHA,aAAa,CAACpJ,CAAC,EAAEmF,KAAK,CAAC;cAC3B;YACJ,CAAC,EAAEiE,aAAa,CAAC;UACrB,CAAC,MAAM;YACHA,aAAa,CAACpJ,CAAC,EAAEmF,KAAK,CAAC;UAC3B;QACJ,CAAC,EAAEiE,aAAa,CAAC;MACrB;MAEA,SAAS4M,SAASA,CAAC1M,GAAG,EAAEJ,QAAQ,EAAE;QAC9B,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;cAC/B0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,gBAAgB,GAAGA,MAAM,CAACmB,SAAS,GAAG,wBAAwB,EAAE,CAACpD,GAAG,CAAC,EAAE,UAAUtJ,CAAC,EAAE8V,OAAO,EAAE;gBAClH,IAAIhR,MAAM,GAAGgR,OAAO,CAACC,IAAI,CAAClV,MAAM,GAAGiV,OAAO,CAACC,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAACxR,KAAK,GAAG,IAAI;;gBAEpE;gBACA;gBACA,IAAIK,MAAM,EAAE;kBACRA,MAAM,GAAGyG,MAAM,CAACkK,UAAU,CAACnB,WAAW,CAACxP,MAAM,CAAC;gBAClD;gBAEAJ,OAAO,CAACI,MAAM,CAAC;cACnB,CAAC,EAAE,UAAU9E,CAAC,EAAEmF,KAAK,EAAE;gBACnBR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAACR,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASiS,SAASA,CAACvP,QAAQ,EAAEuC,QAAQ,EAAE;QACnC,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YAEzBpD,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;cAC/B0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,gBAAgB,GAAGA,MAAM,CAACmB,SAAS,EAAE,EAAE,EAAE,UAAU1M,CAAC,EAAE8V,OAAO,EAAE;gBACpF,IAAIC,IAAI,GAAGD,OAAO,CAACC,IAAI;gBACvB,IAAIlV,MAAM,GAAGkV,IAAI,CAAClV,MAAM;gBAExB,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,EAAEL,CAAC,EAAE,EAAE;kBAC7B,IAAIyV,IAAI,GAAGF,IAAI,CAACE,IAAI,CAACzV,CAAC,CAAC;kBACvB,IAAIsE,MAAM,GAAGmR,IAAI,CAACxR,KAAK;;kBAEvB;kBACA;kBACA,IAAIK,MAAM,EAAE;oBACRA,MAAM,GAAGyG,MAAM,CAACkK,UAAU,CAACnB,WAAW,CAACxP,MAAM,CAAC;kBAClD;kBAEAA,MAAM,GAAG6B,QAAQ,CAAC7B,MAAM,EAAEmR,IAAI,CAAC3M,GAAG,EAAE9I,CAAC,GAAG,CAAC,CAAC;;kBAE1C;kBACA;kBACA,IAAIsE,MAAM,KAAK,KAAK,CAAC,EAAE;oBACnBJ,OAAO,CAACI,MAAM,CAAC;oBACf;kBACJ;gBACJ;gBAEAJ,OAAO,CAAC,CAAC;cACb,CAAC,EAAE,UAAU1E,CAAC,EAAEmF,KAAK,EAAE;gBACnBR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAACR,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASkS,QAAQA,CAAC7M,GAAG,EAAE7E,KAAK,EAAEyE,QAAQ,EAAEkN,WAAW,EAAE;QACjD,IAAIvW,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B;YACA;YACA;YACA,IAAIU,KAAK,KAAKyK,SAAS,EAAE;cACrBzK,KAAK,GAAG,IAAI;YAChB;;YAEA;YACA,IAAI4R,aAAa,GAAG5R,KAAK;YAEzB,IAAI8G,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACkK,UAAU,CAAC5B,SAAS,CAACpP,KAAK,EAAE,UAAUA,KAAK,EAAEU,KAAK,EAAE;cACvD,IAAIA,KAAK,EAAE;gBACPR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,MAAM;gBACHoG,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;kBAC/B0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,yBAAyB,GAAGA,MAAM,CAACmB,SAAS,GAAG,GAAG,GAAG,4BAA4B,EAAE,CAACpD,GAAG,EAAE7E,KAAK,CAAC,EAAE,YAAY;oBAClIC,OAAO,CAAC2R,aAAa,CAAC;kBAC1B,CAAC,EAAE,UAAUrW,CAAC,EAAEmF,KAAK,EAAE;oBACnBR,MAAM,CAACQ,KAAK,CAAC;kBACjB,CAAC,CAAC;gBACN,CAAC,EAAE,UAAUmR,QAAQ,EAAE;kBACnB;kBACA;kBACA,IAAIA,QAAQ,CAAC5V,IAAI,KAAK4V,QAAQ,CAACC,SAAS,EAAE;oBACtC;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,IAAIH,WAAW,GAAG,CAAC,EAAE;sBACjB1R,OAAO,CAACyR,QAAQ,CAAC7Q,KAAK,CAACzF,IAAI,EAAE,CAACyJ,GAAG,EAAE+M,aAAa,EAAEnN,QAAQ,EAAEkN,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;sBAC9E;oBACJ;oBACAzR,MAAM,CAAC2R,QAAQ,CAAC;kBACpB;gBACJ,CAAC,CAAC;cACN;YACJ,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC3R,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASuS,SAASA,CAAClN,GAAG,EAAE7E,KAAK,EAAEyE,QAAQ,EAAE;QACrC,OAAOiN,QAAQ,CAAC7Q,KAAK,CAAC,IAAI,EAAE,CAACgE,GAAG,EAAE7E,KAAK,EAAEyE,QAAQ,EAAE,CAAC,CAAC,CAAC;MAC1D;MAEA,SAASuN,YAAYA,CAACnN,GAAG,EAAEJ,QAAQ,EAAE;QACjC,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;cAC/B0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,cAAc,GAAGA,MAAM,CAACmB,SAAS,GAAG,gBAAgB,EAAE,CAACpD,GAAG,CAAC,EAAE,YAAY;gBAC9F5E,OAAO,CAAC,CAAC;cACb,CAAC,EAAE,UAAU1E,CAAC,EAAEmF,KAAK,EAAE;gBACnBR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAACR,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA;MACA,SAASyS,OAAOA,CAACxN,QAAQ,EAAE;QACvB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;cAC/B0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,cAAc,GAAGA,MAAM,CAACmB,SAAS,EAAE,EAAE,EAAE,YAAY;gBACxEhI,OAAO,CAAC,CAAC;cACb,CAAC,EAAE,UAAU1E,CAAC,EAAEmF,KAAK,EAAE;gBACnBR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAACR,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA;MACA,SAAS0S,QAAQA,CAACzN,QAAQ,EAAE;QACxB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;cAC/B;cACA0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,8BAA8B,GAAGA,MAAM,CAACmB,SAAS,EAAE,EAAE,EAAE,UAAU1M,CAAC,EAAE8V,OAAO,EAAE;gBAClG,IAAIhR,MAAM,GAAGgR,OAAO,CAACC,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAACW,CAAC;gBACnClS,OAAO,CAACI,MAAM,CAAC;cACnB,CAAC,EAAE,UAAU9E,CAAC,EAAEmF,KAAK,EAAE;gBACnBR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAACR,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,SAAS4S,KAAKA,CAAC5W,CAAC,EAAEiJ,QAAQ,EAAE;QACxB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;cAC/B0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,kBAAkB,GAAGA,MAAM,CAACmB,SAAS,GAAG,uBAAuB,EAAE,CAACzM,CAAC,GAAG,CAAC,CAAC,EAAE,UAAUD,CAAC,EAAE8V,OAAO,EAAE;gBACrH,IAAIhR,MAAM,GAAGgR,OAAO,CAACC,IAAI,CAAClV,MAAM,GAAGiV,OAAO,CAACC,IAAI,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC3M,GAAG,GAAG,IAAI;gBAClE5E,OAAO,CAACI,MAAM,CAAC;cACnB,CAAC,EAAE,UAAU9E,CAAC,EAAEmF,KAAK,EAAE;gBACnBR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAACR,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAAS6S,MAAMA,CAAC5N,QAAQ,EAAE;QACtB,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UACnD9E,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAC1B,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACY,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;cAC/B0V,aAAa,CAAC1V,CAAC,EAAEuL,MAAM,EAAE,kBAAkB,GAAGA,MAAM,CAACmB,SAAS,EAAE,EAAE,EAAE,UAAU1M,CAAC,EAAE8V,OAAO,EAAE;gBACtF,IAAIjF,IAAI,GAAG,EAAE;gBAEb,KAAK,IAAIrQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsV,OAAO,CAACC,IAAI,CAAClV,MAAM,EAAEL,CAAC,EAAE,EAAE;kBAC1CqQ,IAAI,CAAC5N,IAAI,CAAC6S,OAAO,CAACC,IAAI,CAACE,IAAI,CAACzV,CAAC,CAAC,CAAC8I,GAAG,CAAC;gBACvC;gBAEA5E,OAAO,CAACmM,IAAI,CAAC;cACjB,CAAC,EAAE,UAAU7Q,CAAC,EAAEmF,KAAK,EAAE;gBACnBR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAACR,MAAM,CAAC;QACvB,CAAC,CAAC;QAEFsE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA;MACA,SAAS8S,gBAAgBA,CAAC5K,EAAE,EAAE;QAC1B,OAAO,IAAInD,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;UAC5CwH,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;YACxBA,CAAC,CAACoV,UAAU,CAAC,iCAAiC,GAAG,8DAA8D,EAAE,EAAE,EAAE,UAAUpV,CAAC,EAAE8V,OAAO,EAAE;cACvI,IAAIkB,UAAU,GAAG,EAAE;cAEnB,KAAK,IAAIxW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsV,OAAO,CAACC,IAAI,CAAClV,MAAM,EAAEL,CAAC,EAAE,EAAE;gBAC1CwW,UAAU,CAAC/T,IAAI,CAAC6S,OAAO,CAACC,IAAI,CAACE,IAAI,CAACzV,CAAC,CAAC,CAAC8H,IAAI,CAAC;cAC9C;cAEA5D,OAAO,CAAC;gBACJyH,EAAE,EAAEA,EAAE;gBACN6K,UAAU,EAAEA;cAChB,CAAC,CAAC;YACN,CAAC,EAAE,UAAUhX,CAAC,EAAEmF,KAAK,EAAE;cACnBR,MAAM,CAACQ,KAAK,CAAC;YACjB,CAAC,CAAC;UACN,CAAC,EAAE,UAAUmR,QAAQ,EAAE;YACnB3R,MAAM,CAAC2R,QAAQ,CAAC;UACpB,CAAC,CAAC;QACN,CAAC,CAAC;MACN;MAEA,SAASW,cAAcA,CAAC5H,OAAO,EAAEnG,QAAQ,EAAE;QACvCA,QAAQ,GAAGQ,WAAW,CAACpE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAE7C,IAAIwL,aAAa,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;QACjC3B,OAAO,GAAG,OAAOA,OAAO,KAAK,UAAU,IAAIA,OAAO,IAAI,CAAC,CAAC;QACxD,IAAI,CAACA,OAAO,CAAC/G,IAAI,EAAE;UACf+G,OAAO,CAAC/G,IAAI,GAAG+G,OAAO,CAAC/G,IAAI,IAAIyI,aAAa,CAACzI,IAAI;UACjD+G,OAAO,CAAC3C,SAAS,GAAG2C,OAAO,CAAC3C,SAAS,IAAIqE,aAAa,CAACrE,SAAS;QACpE;QAEA,IAAI7M,IAAI,GAAG,IAAI;QACf,IAAIoE,OAAO;QACX,IAAI,CAACoL,OAAO,CAAC/G,IAAI,EAAE;UACfrE,OAAO,GAAG+E,SAAS,CAACrE,MAAM,CAAC,mBAAmB,CAAC;QACnD,CAAC,MAAM;UACHV,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAE;YACvC,IAAIyH,EAAE;YACN,IAAIkD,OAAO,CAAC/G,IAAI,KAAKyI,aAAa,CAACzI,IAAI,EAAE;cACrC;cACA6D,EAAE,GAAGtM,IAAI,CAAC8O,OAAO,CAACxC,EAAE;YACxB,CAAC,MAAM;cACHA,EAAE,GAAG1E,YAAY,CAAC4H,OAAO,CAAC/G,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9C;YAEA,IAAI,CAAC+G,OAAO,CAAC3C,SAAS,EAAE;cACpB;cACAhI,OAAO,CAACqS,gBAAgB,CAAC5K,EAAE,CAAC,CAAC;YACjC,CAAC,MAAM;cACHzH,OAAO,CAAC;gBACJyH,EAAE,EAAEA,EAAE;gBACN6K,UAAU,EAAE,CAAC3H,OAAO,CAAC3C,SAAS;cAClC,CAAC,CAAC;YACN;UACJ,CAAC,CAAC,CAAC3I,IAAI,CAAC,UAAUmT,aAAa,EAAE;YAC7B,OAAO,IAAIlO,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;cAC5CuS,aAAa,CAAC/K,EAAE,CAAC1B,WAAW,CAAC,UAAUzK,CAAC,EAAE;gBACtC,SAASmX,SAASA,CAACzK,SAAS,EAAE;kBAC1B,OAAO,IAAI1D,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;oBAC5C3E,CAAC,CAACoV,UAAU,CAAC,uBAAuB,GAAG1I,SAAS,EAAE,EAAE,EAAE,YAAY;sBAC9DhI,OAAO,CAAC,CAAC;oBACb,CAAC,EAAE,UAAU1E,CAAC,EAAEmF,KAAK,EAAE;sBACnBR,MAAM,CAACQ,KAAK,CAAC;oBACjB,CAAC,CAAC;kBACN,CAAC,CAAC;gBACN;gBAEA,IAAIiS,UAAU,GAAG,EAAE;gBACnB,KAAK,IAAI5W,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAGoU,aAAa,CAACF,UAAU,CAACnW,MAAM,EAAEL,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;kBACjE4W,UAAU,CAACnU,IAAI,CAACkU,SAAS,CAACD,aAAa,CAACF,UAAU,CAACxW,CAAC,CAAC,CAAC,CAAC;gBAC3D;gBAEAwI,SAAS,CAACnD,GAAG,CAACuR,UAAU,CAAC,CAACrT,IAAI,CAAC,YAAY;kBACvCW,OAAO,CAAC,CAAC;gBACb,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU3E,CAAC,EAAE;kBACrB4E,MAAM,CAAC5E,CAAC,CAAC;gBACb,CAAC,CAAC;cACN,CAAC,EAAE,UAAUuW,QAAQ,EAAE;gBACnB3R,MAAM,CAAC2R,QAAQ,CAAC;cACpB,CAAC,CAAC;YACN,CAAC,CAAC;UACN,CAAC,CAAC;QACN;QAEArN,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,IAAIoT,aAAa,GAAG;QAChB1F,OAAO,EAAE,eAAe;QACxBvC,YAAY,EAAEiG,cAAc;QAC5BzD,QAAQ,EAAEC,aAAa,CAAC,CAAC;QACzB5B,OAAO,EAAEiG,SAAS;QAClBrG,OAAO,EAAEmG,SAAS;QAClB3F,OAAO,EAAEmG,SAAS;QAClBjG,UAAU,EAAEkG,YAAY;QACxBjG,KAAK,EAAEkG,OAAO;QACd7V,MAAM,EAAE8V,QAAQ;QAChBrN,GAAG,EAAEuN,KAAK;QACVhG,IAAI,EAAEiG,MAAM;QACZhG,YAAY,EAAEmG;MAClB,CAAC;MAED,SAASK,mBAAmBA,CAAA,EAAG;QAC3B,IAAI;UACA,OAAO,OAAOC,YAAY,KAAK,WAAW,IAAI,SAAS,IAAIA,YAAY;UACvE;UACA,CAAC,CAACA,YAAY,CAAClH,OAAO;QAC1B,CAAC,CAAC,OAAOtQ,CAAC,EAAE;UACR,OAAO,KAAK;QAChB;MACJ;MAEA,SAASyX,aAAaA,CAACnI,OAAO,EAAEoI,aAAa,EAAE;QAC3C,IAAIC,SAAS,GAAGrI,OAAO,CAAC/G,IAAI,GAAG,GAAG;QAElC,IAAI+G,OAAO,CAAC3C,SAAS,KAAK+K,aAAa,CAAC/K,SAAS,EAAE;UAC/CgL,SAAS,IAAIrI,OAAO,CAAC3C,SAAS,GAAG,GAAG;QACxC;QACA,OAAOgL,SAAS;MACpB;;MAEA;MACA,SAASC,yBAAyBA,CAAA,EAAG;QACjC,IAAIC,mBAAmB,GAAG,2BAA2B;QAErD,IAAI;UACAL,YAAY,CAAClH,OAAO,CAACuH,mBAAmB,EAAE,IAAI,CAAC;UAC/CL,YAAY,CAAChH,UAAU,CAACqH,mBAAmB,CAAC;UAE5C,OAAO,KAAK;QAChB,CAAC,CAAC,OAAO7X,CAAC,EAAE;UACR,OAAO,IAAI;QACf;MACJ;;MAEA;MACA;MACA;MACA;MACA,SAAS8X,qBAAqBA,CAAA,EAAG;QAC7B,OAAO,CAACF,yBAAyB,CAAC,CAAC,IAAIJ,YAAY,CAAC1W,MAAM,GAAG,CAAC;MAClE;;MAEA;MACA,SAASiX,cAAcA,CAACzI,OAAO,EAAE;QAC7B,IAAIxP,IAAI,GAAG,IAAI;QACf,IAAI0L,MAAM,GAAG,CAAC,CAAC;QACf,IAAI8D,OAAO,EAAE;UACT,KAAK,IAAI7O,CAAC,IAAI6O,OAAO,EAAE;YACnB9D,MAAM,CAAC/K,CAAC,CAAC,GAAG6O,OAAO,CAAC7O,CAAC,CAAC;UAC1B;QACJ;QAEA+K,MAAM,CAACmM,SAAS,GAAGF,aAAa,CAACnI,OAAO,EAAExP,IAAI,CAAC8P,cAAc,CAAC;QAE9D,IAAI,CAACkI,qBAAqB,CAAC,CAAC,EAAE;UAC1B,OAAO7O,SAAS,CAACrE,MAAM,CAAC,CAAC;QAC7B;QAEA9E,IAAI,CAAC8O,OAAO,GAAGpD,MAAM;QACrBA,MAAM,CAACkK,UAAU,GAAGP,qBAAqB;QAEzC,OAAOlM,SAAS,CAACtE,OAAO,CAAC,CAAC;MAC9B;;MAEA;MACA;MACA,SAASqT,OAAOA,CAAC7O,QAAQ,EAAE;QACvB,IAAIrJ,IAAI,GAAG,IAAI;QACf,IAAIoE,OAAO,GAAGpE,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;UACxC,IAAI2T,SAAS,GAAG7X,IAAI,CAAC8O,OAAO,CAAC+I,SAAS;UAEtC,KAAK,IAAIlX,CAAC,GAAG+W,YAAY,CAAC1W,MAAM,GAAG,CAAC,EAAEL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC/C,IAAI8I,GAAG,GAAGiO,YAAY,CAACjO,GAAG,CAAC9I,CAAC,CAAC;YAE7B,IAAI8I,GAAG,CAACtB,OAAO,CAAC0P,SAAS,CAAC,KAAK,CAAC,EAAE;cAC9BH,YAAY,CAAChH,UAAU,CAACjH,GAAG,CAAC;YAChC;UACJ;QACJ,CAAC,CAAC;QAEFL,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA;MACA;MACA,SAAS+T,SAASA,CAAC1O,GAAG,EAAEJ,QAAQ,EAAE;QAC9B,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAGpE,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;UACxC,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;UACzB,IAAI7J,MAAM,GAAGyS,YAAY,CAAC1H,OAAO,CAACtE,MAAM,CAACmM,SAAS,GAAGpO,GAAG,CAAC;;UAEzD;UACA;UACA;UACA;UACA,IAAIxE,MAAM,EAAE;YACRA,MAAM,GAAGyG,MAAM,CAACkK,UAAU,CAACnB,WAAW,CAACxP,MAAM,CAAC;UAClD;UAEA,OAAOA,MAAM;QACjB,CAAC,CAAC;QAEFmE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA,SAASgU,SAASA,CAACtR,QAAQ,EAAEuC,QAAQ,EAAE;QACnC,IAAIrJ,IAAI,GAAG,IAAI;QAEf,IAAIoE,OAAO,GAAGpE,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;UACxC,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;UACzB,IAAI+I,SAAS,GAAGnM,MAAM,CAACmM,SAAS;UAChC,IAAIQ,eAAe,GAAGR,SAAS,CAAC7W,MAAM;UACtC,IAAIA,MAAM,GAAG0W,YAAY,CAAC1W,MAAM;;UAEhC;UACA;UACA;UACA;UACA;UACA;UACA,IAAIsP,eAAe,GAAG,CAAC;UAEvB,KAAK,IAAI3P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,EAAEL,CAAC,EAAE,EAAE;YAC7B,IAAI8I,GAAG,GAAGiO,YAAY,CAACjO,GAAG,CAAC9I,CAAC,CAAC;YAC7B,IAAI8I,GAAG,CAACtB,OAAO,CAAC0P,SAAS,CAAC,KAAK,CAAC,EAAE;cAC9B;YACJ;YACA,IAAIjT,KAAK,GAAG8S,YAAY,CAAC1H,OAAO,CAACvG,GAAG,CAAC;;YAErC;YACA;YACA;YACA;YACA,IAAI7E,KAAK,EAAE;cACPA,KAAK,GAAG8G,MAAM,CAACkK,UAAU,CAACnB,WAAW,CAAC7P,KAAK,CAAC;YAChD;YAEAA,KAAK,GAAGkC,QAAQ,CAAClC,KAAK,EAAE6E,GAAG,CAACsK,SAAS,CAACsE,eAAe,CAAC,EAAE/H,eAAe,EAAE,CAAC;YAE1E,IAAI1L,KAAK,KAAK,KAAK,CAAC,EAAE;cAClB,OAAOA,KAAK;YAChB;UACJ;QACJ,CAAC,CAAC;QAEFwE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA,SAASkU,KAAKA,CAAClY,CAAC,EAAEiJ,QAAQ,EAAE;QACxB,IAAIrJ,IAAI,GAAG,IAAI;QACf,IAAIoE,OAAO,GAAGpE,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;UACxC,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;UACzB,IAAI7J,MAAM;UACV,IAAI;YACAA,MAAM,GAAGyS,YAAY,CAACjO,GAAG,CAACrJ,CAAC,CAAC;UAChC,CAAC,CAAC,OAAOkF,KAAK,EAAE;YACZL,MAAM,GAAG,IAAI;UACjB;;UAEA;UACA,IAAIA,MAAM,EAAE;YACRA,MAAM,GAAGA,MAAM,CAAC8O,SAAS,CAACrI,MAAM,CAACmM,SAAS,CAAC7W,MAAM,CAAC;UACtD;UAEA,OAAOiE,MAAM;QACjB,CAAC,CAAC;QAEFmE,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASmU,MAAMA,CAAClP,QAAQ,EAAE;QACtB,IAAIrJ,IAAI,GAAG,IAAI;QACf,IAAIoE,OAAO,GAAGpE,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;UACxC,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;UACzB,IAAI9N,MAAM,GAAG0W,YAAY,CAAC1W,MAAM;UAChC,IAAIgQ,IAAI,GAAG,EAAE;UAEb,KAAK,IAAIrQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGK,MAAM,EAAEL,CAAC,EAAE,EAAE;YAC7B,IAAI6X,OAAO,GAAGd,YAAY,CAACjO,GAAG,CAAC9I,CAAC,CAAC;YACjC,IAAI6X,OAAO,CAACrQ,OAAO,CAACuD,MAAM,CAACmM,SAAS,CAAC,KAAK,CAAC,EAAE;cACzC7G,IAAI,CAAC5N,IAAI,CAACoV,OAAO,CAACzE,SAAS,CAACrI,MAAM,CAACmM,SAAS,CAAC7W,MAAM,CAAC,CAAC;YACzD;UACJ;UAEA,OAAOgQ,IAAI;QACf,CAAC,CAAC;QAEF5H,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA,SAASqU,QAAQA,CAACpP,QAAQ,EAAE;QACxB,IAAIrJ,IAAI,GAAG,IAAI;QACf,IAAIoE,OAAO,GAAGpE,IAAI,CAACgR,IAAI,CAAC,CAAC,CAAC9M,IAAI,CAAC,UAAU8M,IAAI,EAAE;UAC3C,OAAOA,IAAI,CAAChQ,MAAM;QACtB,CAAC,CAAC;QAEFoI,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA,SAASsU,YAAYA,CAACjP,GAAG,EAAEJ,QAAQ,EAAE;QACjC,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAGpE,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;UACxC,IAAIwH,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;UACzB4I,YAAY,CAAChH,UAAU,CAAChF,MAAM,CAACmM,SAAS,GAAGpO,GAAG,CAAC;QACnD,CAAC,CAAC;QAEFL,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;;MAEA;MACA;MACA;MACA;MACA,SAASuU,SAASA,CAAClP,GAAG,EAAE7E,KAAK,EAAEyE,QAAQ,EAAE;QACrC,IAAIrJ,IAAI,GAAG,IAAI;QAEfyJ,GAAG,GAAGD,YAAY,CAACC,GAAG,CAAC;QAEvB,IAAIrF,OAAO,GAAGpE,IAAI,CAACyP,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;UACxC;UACA;UACA,IAAIU,KAAK,KAAKyK,SAAS,EAAE;YACrBzK,KAAK,GAAG,IAAI;UAChB;;UAEA;UACA,IAAI4R,aAAa,GAAG5R,KAAK;UAEzB,OAAO,IAAIuE,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;YAC5C,IAAI4G,MAAM,GAAG1L,IAAI,CAAC8O,OAAO;YACzBpD,MAAM,CAACkK,UAAU,CAAC5B,SAAS,CAACpP,KAAK,EAAE,UAAUA,KAAK,EAAEU,KAAK,EAAE;cACvD,IAAIA,KAAK,EAAE;gBACPR,MAAM,CAACQ,KAAK,CAAC;cACjB,CAAC,MAAM;gBACH,IAAI;kBACAoS,YAAY,CAAClH,OAAO,CAAC9E,MAAM,CAACmM,SAAS,GAAGpO,GAAG,EAAE7E,KAAK,CAAC;kBACnDC,OAAO,CAAC2R,aAAa,CAAC;gBAC1B,CAAC,CAAC,OAAOtW,CAAC,EAAE;kBACR;kBACA;kBACA,IAAIA,CAAC,CAACuI,IAAI,KAAK,oBAAoB,IAAIvI,CAAC,CAACuI,IAAI,KAAK,4BAA4B,EAAE;oBAC5E3D,MAAM,CAAC5E,CAAC,CAAC;kBACb;kBACA4E,MAAM,CAAC5E,CAAC,CAAC;gBACb;cACJ;YACJ,CAAC,CAAC;UACN,CAAC,CAAC;QACN,CAAC,CAAC;QAEFkJ,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,SAASwU,cAAcA,CAACpJ,OAAO,EAAEnG,QAAQ,EAAE;QACvCA,QAAQ,GAAGQ,WAAW,CAACpE,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;QAE7C8J,OAAO,GAAG,OAAOA,OAAO,KAAK,UAAU,IAAIA,OAAO,IAAI,CAAC,CAAC;QACxD,IAAI,CAACA,OAAO,CAAC/G,IAAI,EAAE;UACf,IAAIyI,aAAa,GAAG,IAAI,CAACC,MAAM,CAAC,CAAC;UACjC3B,OAAO,CAAC/G,IAAI,GAAG+G,OAAO,CAAC/G,IAAI,IAAIyI,aAAa,CAACzI,IAAI;UACjD+G,OAAO,CAAC3C,SAAS,GAAG2C,OAAO,CAAC3C,SAAS,IAAIqE,aAAa,CAACrE,SAAS;QACpE;QAEA,IAAI7M,IAAI,GAAG,IAAI;QACf,IAAIoE,OAAO;QACX,IAAI,CAACoL,OAAO,CAAC/G,IAAI,EAAE;UACfrE,OAAO,GAAG+E,SAAS,CAACrE,MAAM,CAAC,mBAAmB,CAAC;QACnD,CAAC,MAAM;UACHV,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAE;YACvC,IAAI,CAAC2K,OAAO,CAAC3C,SAAS,EAAE;cACpBhI,OAAO,CAAC2K,OAAO,CAAC/G,IAAI,GAAG,GAAG,CAAC;YAC/B,CAAC,MAAM;cACH5D,OAAO,CAAC8S,aAAa,CAACnI,OAAO,EAAExP,IAAI,CAAC8P,cAAc,CAAC,CAAC;YACxD;UACJ,CAAC,CAAC,CAAC5L,IAAI,CAAC,UAAU2T,SAAS,EAAE;YACzB,KAAK,IAAIlX,CAAC,GAAG+W,YAAY,CAAC1W,MAAM,GAAG,CAAC,EAAEL,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;cAC/C,IAAI8I,GAAG,GAAGiO,YAAY,CAACjO,GAAG,CAAC9I,CAAC,CAAC;cAE7B,IAAI8I,GAAG,CAACtB,OAAO,CAAC0P,SAAS,CAAC,KAAK,CAAC,EAAE;gBAC9BH,YAAY,CAAChH,UAAU,CAACjH,GAAG,CAAC;cAChC;YACJ;UACJ,CAAC,CAAC;QACN;QAEAL,eAAe,CAAChF,OAAO,EAAEiF,QAAQ,CAAC;QAClC,OAAOjF,OAAO;MAClB;MAEA,IAAIyU,mBAAmB,GAAG;QACtB/G,OAAO,EAAE,qBAAqB;QAC9BvC,YAAY,EAAE0I,cAAc;QAC5BlG,QAAQ,EAAE0F,mBAAmB,CAAC,CAAC;QAC/BrH,OAAO,EAAEgI,SAAS;QAClBpI,OAAO,EAAEmI,SAAS;QAClB3H,OAAO,EAAEmI,SAAS;QAClBjI,UAAU,EAAEgI,YAAY;QACxB/H,KAAK,EAAEuH,OAAO;QACdlX,MAAM,EAAEyX,QAAQ;QAChBhP,GAAG,EAAE6O,KAAK;QACVtH,IAAI,EAAEuH,MAAM;QACZtH,YAAY,EAAE2H;MAClB,CAAC;MAED,IAAIE,SAAS,GAAG,SAASA,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;QACrC,OAAOD,CAAC,KAAKC,CAAC,IAAI,OAAOD,CAAC,KAAK,QAAQ,IAAI,OAAOC,CAAC,KAAK,QAAQ,IAAIC,KAAK,CAACF,CAAC,CAAC,IAAIE,KAAK,CAACD,CAAC,CAAC;MAC5F,CAAC;MAED,IAAIE,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAEC,aAAa,EAAE;QACnD,IAAInW,GAAG,GAAGkW,KAAK,CAACnY,MAAM;QACtB,IAAIL,CAAC,GAAG,CAAC;QACT,OAAOA,CAAC,GAAGsC,GAAG,EAAE;UACZ,IAAI6V,SAAS,CAACK,KAAK,CAACxY,CAAC,CAAC,EAAEyY,aAAa,CAAC,EAAE;YACpC,OAAO,IAAI;UACf;UACAzY,CAAC,EAAE;QACP;QAEA,OAAO,KAAK;MAChB,CAAC;MAED,IAAI0Y,OAAO,GAAGhT,KAAK,CAACgT,OAAO,IAAI,UAAUC,GAAG,EAAE;QAC1C,OAAOpT,MAAM,CAAClC,SAAS,CAACmC,QAAQ,CAACpF,IAAI,CAACuY,GAAG,CAAC,KAAK,gBAAgB;MACnE,CAAC;;MAED;MACA;MACA,IAAIC,cAAc,GAAG,CAAC,CAAC;MAEvB,IAAIC,aAAa,GAAG,CAAC,CAAC;MAEtB,IAAIC,cAAc,GAAG;QACjBC,SAAS,EAAE7H,YAAY;QACvB8H,MAAM,EAAEnC,aAAa;QACrBoC,YAAY,EAAEf;MAClB,CAAC;MAED,IAAIgB,kBAAkB,GAAG,CAACJ,cAAc,CAACC,SAAS,CAAC5H,OAAO,EAAE2H,cAAc,CAACE,MAAM,CAAC7H,OAAO,EAAE2H,cAAc,CAACG,YAAY,CAAC9H,OAAO,CAAC;MAE/H,IAAIgI,qBAAqB,GAAG,CAAC,cAAc,CAAC;MAE5C,IAAIC,cAAc,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,SAAS,CAAC,CAACC,MAAM,CAACF,qBAAqB,CAAC;MAEpI,IAAIG,aAAa,GAAG;QAChBvE,WAAW,EAAE,EAAE;QACfwE,MAAM,EAAEL,kBAAkB,CAAChK,KAAK,CAAC,CAAC;QAClCpH,IAAI,EAAE,aAAa;QACnB;QACA;QACAkN,IAAI,EAAE,OAAO;QACb9I,SAAS,EAAE,eAAe;QAC1BJ,OAAO,EAAE;MACb,CAAC;MAED,SAAS0N,aAAaA,CAACC,mBAAmB,EAAEC,aAAa,EAAE;QACvDD,mBAAmB,CAACC,aAAa,CAAC,GAAG,YAAY;UAC7C,IAAIC,KAAK,GAAG5U,SAAS;UACrB,OAAO0U,mBAAmB,CAAC3K,KAAK,CAAC,CAAC,CAACvL,IAAI,CAAC,YAAY;YAChD,OAAOkW,mBAAmB,CAACC,aAAa,CAAC,CAAC5U,KAAK,CAAC2U,mBAAmB,EAAEE,KAAK,CAAC;UAC/E,CAAC,CAAC;QACN,CAAC;MACL;MAEA,SAASC,MAAMA,CAAA,EAAG;QACd,KAAK,IAAI5Z,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+E,SAAS,CAAC1E,MAAM,EAAEL,CAAC,EAAE,EAAE;UACvC,IAAI2Y,GAAG,GAAG5T,SAAS,CAAC/E,CAAC,CAAC;UAEtB,IAAI2Y,GAAG,EAAE;YACL,KAAK,IAAIkB,IAAI,IAAIlB,GAAG,EAAE;cAClB,IAAIA,GAAG,CAACmB,cAAc,CAACD,IAAI,CAAC,EAAE;gBAC1B,IAAInB,OAAO,CAACC,GAAG,CAACkB,IAAI,CAAC,CAAC,EAAE;kBACpB9U,SAAS,CAAC,CAAC,CAAC,CAAC8U,IAAI,CAAC,GAAGlB,GAAG,CAACkB,IAAI,CAAC,CAAC3K,KAAK,CAAC,CAAC;gBAC1C,CAAC,MAAM;kBACHnK,SAAS,CAAC,CAAC,CAAC,CAAC8U,IAAI,CAAC,GAAGlB,GAAG,CAACkB,IAAI,CAAC;gBAClC;cACJ;YACJ;UACJ;QACJ;QAEA,OAAO9U,SAAS,CAAC,CAAC,CAAC;MACvB;MAEA,IAAIgV,WAAW,GAAG,YAAY;QAC1B,SAASA,WAAWA,CAAClL,OAAO,EAAE;UAC1BzI,eAAe,CAAC,IAAI,EAAE2T,WAAW,CAAC;UAElC,KAAK,IAAIC,aAAa,IAAIlB,cAAc,EAAE;YACtC,IAAIA,cAAc,CAACgB,cAAc,CAACE,aAAa,CAAC,EAAE;cAC9C,IAAIT,MAAM,GAAGT,cAAc,CAACkB,aAAa,CAAC;cAC1C,IAAIC,UAAU,GAAGV,MAAM,CAACpI,OAAO;cAC/B,IAAI,CAAC6I,aAAa,CAAC,GAAGC,UAAU;cAEhC,IAAI,CAACrB,cAAc,CAACqB,UAAU,CAAC,EAAE;gBAC7B;gBACA;gBACA;gBACA,IAAI,CAACC,YAAY,CAACX,MAAM,CAAC;cAC7B;YACJ;UACJ;UAEA,IAAI,CAACpK,cAAc,GAAGyK,MAAM,CAAC,CAAC,CAAC,EAAEN,aAAa,CAAC;UAC/C,IAAI,CAACa,OAAO,GAAGP,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACzK,cAAc,EAAEN,OAAO,CAAC;UACvD,IAAI,CAACuL,UAAU,GAAG,IAAI;UACtB,IAAI,CAACC,WAAW,GAAG,IAAI;UACvB,IAAI,CAACC,MAAM,GAAG,KAAK;UACnB,IAAI,CAACnM,OAAO,GAAG,IAAI;UAEnB,IAAI,CAACoM,4BAA4B,CAAC,CAAC;UACnC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACL,OAAO,CAACZ,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;QAChE;;QAEA;QACA;QACA;QACA;;QAGAQ,WAAW,CAAC1W,SAAS,CAACmN,MAAM,GAAG,SAASA,MAAMA,CAAC3B,OAAO,EAAE;UACpD;UACA;UACA;UACA,IAAI,CAAC,OAAOA,OAAO,KAAK,WAAW,GAAG,WAAW,GAAG5I,OAAO,CAAC4I,OAAO,CAAC,MAAM,QAAQ,EAAE;YAChF;YACA;YACA,IAAI,IAAI,CAACyL,MAAM,EAAE;cACb,OAAO,IAAIra,KAAK,CAAC,wCAAwC,GAAG,gBAAgB,CAAC;YACjF;YAEA,KAAK,IAAID,CAAC,IAAI6O,OAAO,EAAE;cACnB,IAAI7O,CAAC,KAAK,WAAW,EAAE;gBACnB6O,OAAO,CAAC7O,CAAC,CAAC,GAAG6O,OAAO,CAAC7O,CAAC,CAAC,CAACya,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;cAC/C;cAEA,IAAIza,CAAC,KAAK,SAAS,IAAI,OAAO6O,OAAO,CAAC7O,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACnD,OAAO,IAAIC,KAAK,CAAC,oCAAoC,CAAC;cAC1D;cAEA,IAAI,CAACka,OAAO,CAACna,CAAC,CAAC,GAAG6O,OAAO,CAAC7O,CAAC,CAAC;YAChC;;YAEA;YACA;YACA,IAAI,QAAQ,IAAI6O,OAAO,IAAIA,OAAO,CAAC0K,MAAM,EAAE;cACvC,OAAO,IAAI,CAACiB,SAAS,CAAC,IAAI,CAACL,OAAO,CAACZ,MAAM,CAAC;YAC9C;YAEA,OAAO,IAAI;UACf,CAAC,MAAM,IAAI,OAAO1K,OAAO,KAAK,QAAQ,EAAE;YACpC,OAAO,IAAI,CAACsL,OAAO,CAACtL,OAAO,CAAC;UAChC,CAAC,MAAM;YACH,OAAO,IAAI,CAACsL,OAAO;UACvB;QACJ,CAAC;;QAED;QACA;;QAGAJ,WAAW,CAAC1W,SAAS,CAAC6W,YAAY,GAAG,SAASA,YAAYA,CAACQ,YAAY,EAAEhS,QAAQ,EAAEE,aAAa,EAAE;UAC9F,IAAInF,OAAO,GAAG,IAAI+E,SAAS,CAAC,UAAUtE,OAAO,EAAEC,MAAM,EAAE;YACnD,IAAI;cACA,IAAI8V,UAAU,GAAGS,YAAY,CAACvJ,OAAO;cACrC,IAAIwJ,eAAe,GAAG,IAAI1a,KAAK,CAAC,mCAAmC,GAAG,qDAAqD,CAAC;;cAE5H;cACA;cACA,IAAI,CAACya,YAAY,CAACvJ,OAAO,EAAE;gBACvBhN,MAAM,CAACwW,eAAe,CAAC;gBACvB;cACJ;cAEA,IAAIC,aAAa,GAAGxB,cAAc,CAACC,MAAM,CAAC,cAAc,CAAC;cACzD,KAAK,IAAIrZ,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAGsY,aAAa,CAACva,MAAM,EAAEL,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;gBACtD,IAAI6a,gBAAgB,GAAGD,aAAa,CAAC5a,CAAC,CAAC;;gBAEvC;gBACA;gBACA,IAAI8a,UAAU,GAAG,CAACvC,QAAQ,CAACY,qBAAqB,EAAE0B,gBAAgB,CAAC;gBACnE,IAAI,CAACC,UAAU,IAAIJ,YAAY,CAACG,gBAAgB,CAAC,KAAK,OAAOH,YAAY,CAACG,gBAAgB,CAAC,KAAK,UAAU,EAAE;kBACxG1W,MAAM,CAACwW,eAAe,CAAC;kBACvB;gBACJ;cACJ;cAEA,IAAII,uBAAuB,GAAG,SAASA,uBAAuBA,CAAA,EAAG;gBAC7D,IAAIC,2BAA2B,GAAG,SAASA,2BAA2BA,CAACC,UAAU,EAAE;kBAC/E,OAAO,YAAY;oBACf,IAAItW,KAAK,GAAG,IAAI1E,KAAK,CAAC,SAAS,GAAGgb,UAAU,GAAG,2CAA2C,CAAC;oBAC3F,IAAIxX,OAAO,GAAG+E,SAAS,CAACrE,MAAM,CAACQ,KAAK,CAAC;oBACrC8D,eAAe,CAAChF,OAAO,EAAEsB,SAAS,CAACA,SAAS,CAAC1E,MAAM,GAAG,CAAC,CAAC,CAAC;oBACzD,OAAOoD,OAAO;kBAClB,CAAC;gBACL,CAAC;gBAED,KAAK,IAAIyX,EAAE,GAAG,CAAC,EAAEC,IAAI,GAAGhC,qBAAqB,CAAC9Y,MAAM,EAAE6a,EAAE,GAAGC,IAAI,EAAED,EAAE,EAAE,EAAE;kBACnE,IAAIE,oBAAoB,GAAGjC,qBAAqB,CAAC+B,EAAE,CAAC;kBACpD,IAAI,CAACR,YAAY,CAACU,oBAAoB,CAAC,EAAE;oBACrCV,YAAY,CAACU,oBAAoB,CAAC,GAAGJ,2BAA2B,CAACI,oBAAoB,CAAC;kBAC1F;gBACJ;cACJ,CAAC;cAEDL,uBAAuB,CAAC,CAAC;cAEzB,IAAIM,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,OAAO,EAAE;gBACtD,IAAI1C,cAAc,CAACqB,UAAU,CAAC,EAAE;kBAC5BlR,OAAO,CAACwS,IAAI,CAAC,iCAAiC,GAAGtB,UAAU,CAAC;gBAChE;gBACArB,cAAc,CAACqB,UAAU,CAAC,GAAGS,YAAY;gBACzC7B,aAAa,CAACoB,UAAU,CAAC,GAAGqB,OAAO;gBACnC;gBACA;gBACA;gBACApX,OAAO,CAAC,CAAC;cACb,CAAC;cAED,IAAI,UAAU,IAAIwW,YAAY,EAAE;gBAC5B,IAAIA,YAAY,CAACtJ,QAAQ,IAAI,OAAOsJ,YAAY,CAACtJ,QAAQ,KAAK,UAAU,EAAE;kBACtEsJ,YAAY,CAACtJ,QAAQ,CAAC,CAAC,CAAC7N,IAAI,CAAC8X,gBAAgB,EAAElX,MAAM,CAAC;gBAC1D,CAAC,MAAM;kBACHkX,gBAAgB,CAAC,CAAC,CAACX,YAAY,CAACtJ,QAAQ,CAAC;gBAC7C;cACJ,CAAC,MAAM;gBACHiK,gBAAgB,CAAC,IAAI,CAAC;cAC1B;YACJ,CAAC,CAAC,OAAO9b,CAAC,EAAE;cACR4E,MAAM,CAAC5E,CAAC,CAAC;YACb;UACJ,CAAC,CAAC;UAEFoJ,mBAAmB,CAAClF,OAAO,EAAEiF,QAAQ,EAAEE,aAAa,CAAC;UACrD,OAAOnF,OAAO;QAClB,CAAC;QAEDsW,WAAW,CAAC1W,SAAS,CAACkW,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;UAC7C,OAAO,IAAI,CAACpI,OAAO,IAAI,IAAI;QAC/B,CAAC;QAED4I,WAAW,CAAC1W,SAAS,CAACmY,SAAS,GAAG,SAASA,SAASA,CAACvB,UAAU,EAAEvR,QAAQ,EAAEE,aAAa,EAAE;UACtF,IAAI6S,gBAAgB,GAAG7C,cAAc,CAACqB,UAAU,CAAC,GAAGzR,SAAS,CAACtE,OAAO,CAAC0U,cAAc,CAACqB,UAAU,CAAC,CAAC,GAAGzR,SAAS,CAACrE,MAAM,CAAC,IAAIlE,KAAK,CAAC,mBAAmB,CAAC,CAAC;UAEpJ0I,mBAAmB,CAAC8S,gBAAgB,EAAE/S,QAAQ,EAAEE,aAAa,CAAC;UAC9D,OAAO6S,gBAAgB;QAC3B,CAAC;QAED1B,WAAW,CAAC1W,SAAS,CAACqY,aAAa,GAAG,SAASA,aAAaA,CAAChT,QAAQ,EAAE;UACnE,IAAIiT,iBAAiB,GAAGnT,SAAS,CAACtE,OAAO,CAACwQ,qBAAqB,CAAC;UAChE/L,mBAAmB,CAACgT,iBAAiB,EAAEjT,QAAQ,CAAC;UAChD,OAAOiT,iBAAiB;QAC5B,CAAC;QAED5B,WAAW,CAAC1W,SAAS,CAACyL,KAAK,GAAG,SAASA,KAAKA,CAACpG,QAAQ,EAAE;UACnD,IAAIrJ,IAAI,GAAG,IAAI;UAEf,IAAIoE,OAAO,GAAGpE,IAAI,CAAC+a,UAAU,CAAC7W,IAAI,CAAC,YAAY;YAC3C,IAAIlE,IAAI,CAACib,MAAM,KAAK,IAAI,EAAE;cACtBjb,IAAI,CAACib,MAAM,GAAGjb,IAAI,CAACgb,WAAW,CAAC,CAAC;YACpC;YAEA,OAAOhb,IAAI,CAACib,MAAM;UACtB,CAAC,CAAC;UAEF3R,mBAAmB,CAAClF,OAAO,EAAEiF,QAAQ,EAAEA,QAAQ,CAAC;UAChD,OAAOjF,OAAO;QAClB,CAAC;QAEDsW,WAAW,CAAC1W,SAAS,CAACmX,SAAS,GAAG,SAASA,SAASA,CAACoB,OAAO,EAAElT,QAAQ,EAAEE,aAAa,EAAE;UACnF,IAAIvJ,IAAI,GAAG,IAAI;UAEf,IAAI,CAACqZ,OAAO,CAACkD,OAAO,CAAC,EAAE;YACnBA,OAAO,GAAG,CAACA,OAAO,CAAC;UACvB;UAEA,IAAIC,gBAAgB,GAAG,IAAI,CAACC,oBAAoB,CAACF,OAAO,CAAC;UAEzD,SAASG,iBAAiBA,CAAA,EAAG;YACzB1c,IAAI,CAAC8a,OAAO,CAACZ,MAAM,GAAGla,IAAI,CAACka,MAAM,CAAC,CAAC;UACvC;UAEA,SAASyC,oBAAoBA,CAACzC,MAAM,EAAE;YAClCla,IAAI,CAAC4c,OAAO,CAAC1C,MAAM,CAAC;YACpBwC,iBAAiB,CAAC,CAAC;YAEnB1c,IAAI,CAACib,MAAM,GAAGjb,IAAI,CAACuP,YAAY,CAACvP,IAAI,CAAC8a,OAAO,CAAC;YAC7C,OAAO9a,IAAI,CAACib,MAAM;UACtB;UAEA,SAAS4B,UAAUA,CAACL,gBAAgB,EAAE;YAClC,OAAO,YAAY;cACf,IAAIM,kBAAkB,GAAG,CAAC;cAE1B,SAASC,iBAAiBA,CAAA,EAAG;gBACzB,OAAOD,kBAAkB,GAAGN,gBAAgB,CAACxb,MAAM,EAAE;kBACjD,IAAI4Z,UAAU,GAAG4B,gBAAgB,CAACM,kBAAkB,CAAC;kBACrDA,kBAAkB,EAAE;kBAEpB9c,IAAI,CAAC8O,OAAO,GAAG,IAAI;kBACnB9O,IAAI,CAACib,MAAM,GAAG,IAAI;kBAElB,OAAOjb,IAAI,CAACmc,SAAS,CAACvB,UAAU,CAAC,CAAC1W,IAAI,CAACyY,oBAAoB,CAAC,CAAC,OAAO,CAAC,CAACI,iBAAiB,CAAC;gBAC5F;gBAEAL,iBAAiB,CAAC,CAAC;gBACnB,IAAIpX,KAAK,GAAG,IAAI1E,KAAK,CAAC,oCAAoC,CAAC;gBAC3DZ,IAAI,CAAC+a,UAAU,GAAG5R,SAAS,CAACrE,MAAM,CAACQ,KAAK,CAAC;gBACzC,OAAOtF,IAAI,CAAC+a,UAAU;cAC1B;cAEA,OAAOgC,iBAAiB,CAAC,CAAC;YAC9B,CAAC;UACL;;UAEA;UACA;UACA;UACA,IAAIC,gBAAgB,GAAG,IAAI,CAACjC,UAAU,KAAK,IAAI,GAAG,IAAI,CAACA,UAAU,CAAC,OAAO,CAAC,CAAC,YAAY;YACnF,OAAO5R,SAAS,CAACtE,OAAO,CAAC,CAAC;UAC9B,CAAC,CAAC,GAAGsE,SAAS,CAACtE,OAAO,CAAC,CAAC;UAExB,IAAI,CAACkW,UAAU,GAAGiC,gBAAgB,CAAC9Y,IAAI,CAAC,YAAY;YAChD,IAAI0W,UAAU,GAAG4B,gBAAgB,CAAC,CAAC,CAAC;YACpCxc,IAAI,CAAC8O,OAAO,GAAG,IAAI;YACnB9O,IAAI,CAACib,MAAM,GAAG,IAAI;YAElB,OAAOjb,IAAI,CAACmc,SAAS,CAACvB,UAAU,CAAC,CAAC1W,IAAI,CAAC,UAAUgW,MAAM,EAAE;cACrDla,IAAI,CAAC8R,OAAO,GAAGoI,MAAM,CAACpI,OAAO;cAC7B4K,iBAAiB,CAAC,CAAC;cACnB1c,IAAI,CAACkb,4BAA4B,CAAC,CAAC;cACnClb,IAAI,CAACgb,WAAW,GAAG6B,UAAU,CAACL,gBAAgB,CAAC;YACnD,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,YAAY;YACpBE,iBAAiB,CAAC,CAAC;YACnB,IAAIpX,KAAK,GAAG,IAAI1E,KAAK,CAAC,oCAAoC,CAAC;YAC3DZ,IAAI,CAAC+a,UAAU,GAAG5R,SAAS,CAACrE,MAAM,CAACQ,KAAK,CAAC;YACzC,OAAOtF,IAAI,CAAC+a,UAAU;UAC1B,CAAC,CAAC;UAEFzR,mBAAmB,CAAC,IAAI,CAACyR,UAAU,EAAE1R,QAAQ,EAAEE,aAAa,CAAC;UAC7D,OAAO,IAAI,CAACwR,UAAU;QAC1B,CAAC;QAEDL,WAAW,CAAC1W,SAAS,CAACiZ,QAAQ,GAAG,SAASA,QAAQA,CAACrC,UAAU,EAAE;UAC3D,OAAO,CAAC,CAACpB,aAAa,CAACoB,UAAU,CAAC;QACtC,CAAC;QAEDF,WAAW,CAAC1W,SAAS,CAAC4Y,OAAO,GAAG,SAASA,OAAOA,CAACM,2BAA2B,EAAE;UAC1E3C,MAAM,CAAC,IAAI,EAAE2C,2BAA2B,CAAC;QAC7C,CAAC;QAEDxC,WAAW,CAAC1W,SAAS,CAACyY,oBAAoB,GAAG,SAASA,oBAAoBA,CAACF,OAAO,EAAE;UAChF,IAAIC,gBAAgB,GAAG,EAAE;UACzB,KAAK,IAAI7b,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAGsZ,OAAO,CAACvb,MAAM,EAAEL,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;YAChD,IAAIia,UAAU,GAAG2B,OAAO,CAAC5b,CAAC,CAAC;YAC3B,IAAI,IAAI,CAACsc,QAAQ,CAACrC,UAAU,CAAC,EAAE;cAC3B4B,gBAAgB,CAACpZ,IAAI,CAACwX,UAAU,CAAC;YACrC;UACJ;UACA,OAAO4B,gBAAgB;QAC3B,CAAC;QAED9B,WAAW,CAAC1W,SAAS,CAACkX,4BAA4B,GAAG,SAASA,4BAA4BA,CAAA,EAAG;UACzF;UACA;UACA;UACA;UACA,KAAK,IAAIva,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAG8W,cAAc,CAAC/Y,MAAM,EAAEL,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;YACvDwZ,aAAa,CAAC,IAAI,EAAEJ,cAAc,CAACpZ,CAAC,CAAC,CAAC;UAC1C;QACJ,CAAC;QAED+Z,WAAW,CAAC1W,SAAS,CAACmZ,cAAc,GAAG,SAASA,cAAcA,CAAC3N,OAAO,EAAE;UACpE,OAAO,IAAIkL,WAAW,CAAClL,OAAO,CAAC;QACnC,CAAC;QAED,OAAOkL,WAAW;MACtB,CAAC,CAAC,CAAC;;MAEH;MACA;;MAGA,IAAI0C,cAAc,GAAG,IAAI1C,WAAW,CAAC,CAAC;MAEtChb,MAAM,CAACD,OAAO,GAAG2d,cAAc;IAE/B,CAAC,EAAC;MAAC,GAAG,EAAC;IAAC,CAAC;EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}