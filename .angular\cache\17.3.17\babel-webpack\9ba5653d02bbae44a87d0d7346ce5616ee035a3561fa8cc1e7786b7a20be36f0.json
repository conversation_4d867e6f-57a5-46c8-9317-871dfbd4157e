{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport const asyncScheduler = new AsyncScheduler(AsyncAction);\nexport const async = asyncScheduler;", "map": {"version": 3, "names": ["AsyncAction", "AsyncScheduler", "asyncScheduler", "async"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/rxjs/dist/esm/internal/scheduler/async.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { AsyncScheduler } from './AsyncScheduler';\nexport const asyncScheduler = new AsyncScheduler(AsyncAction);\nexport const async = asyncScheduler;\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,MAAMC,cAAc,GAAG,IAAID,cAAc,CAACD,WAAW,CAAC;AAC7D,OAAO,MAAMG,KAAK,GAAGD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}