{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport LocalForage from 'localforage';\n/** @hidden */\nexport const Drivers = {\n  SecureStorage: 'ionicSecureStorage',\n  IndexedDB: LocalForage.INDEXEDDB,\n  LocalStorage: LocalForage.LOCALSTORAGE\n};\nconst defaultConfig = {\n  name: '_ionicstorage',\n  storeName: '_ionickv',\n  dbKey: '_ionickey',\n  driverOrder: [Drivers.SecureStorage, Drivers.IndexedDB, Drivers.LocalStorage]\n};\nexport class Storage {\n  /**\n   * Create a new Storage instance using the order of drivers and any additional config\n   * options to pass to LocalForage.\n   *\n   * Possible default driverOrder options are: ['indexeddb', 'localstorage'] and the\n   * default is that exact ordering.\n   *\n   * When using Ionic Secure Storage (enterprise only), use ['ionicSecureStorage', 'indexeddb', 'localstorage'] to ensure\n   * Secure Storage is used when available, or fall back to IndexedDB or LocalStorage on the web.\n   */\n  constructor(config = defaultConfig) {\n    this._db = null;\n    this._secureStorageDriver = null;\n    const actualConfig = Object.assign({}, defaultConfig, config || {});\n    this._config = actualConfig;\n  }\n  create() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const db = LocalForage.createInstance(_this._config);\n      _this._db = db;\n      yield db.setDriver(_this._config.driverOrder || []);\n      return _this;\n    })();\n  }\n  /**\n   * Define a new Driver. Must be called before\n   * initializing the database. Example:\n   *\n   * await storage.defineDriver(myDriver);\n   * await storage.create();\n   */\n  defineDriver(driver) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (driver._driver === Drivers.SecureStorage) {\n        _this2._secureStorageDriver = driver;\n      }\n      return LocalForage.defineDriver(driver);\n    })();\n  }\n  /**\n   * Get the name of the driver being used.\n   * @returns Name of the driver\n   */\n  get driver() {\n    var _a;\n    return ((_a = this._db) === null || _a === void 0 ? void 0 : _a.driver()) || null;\n  }\n  assertDb() {\n    if (!this._db) {\n      throw new Error('Database not created. Must call create() first');\n    }\n    return this._db;\n  }\n  /**\n   * Get the value associated with the given key.\n   * @param key the key to identify this value\n   * @returns Returns a promise with the value of the given key\n   */\n  get(key) {\n    const db = this.assertDb();\n    return db.getItem(key);\n  }\n  /**\n   * Set the value for the given key.\n   * @param key the key to identify this value\n   * @param value the value for this key\n   * @returns Returns a promise that resolves when the key and value are set\n   */\n  set(key, value) {\n    const db = this.assertDb();\n    return db.setItem(key, value);\n  }\n  /**\n   * Remove any value associated with this key.\n   * @param key the key to identify this value\n   * @returns Returns a promise that resolves when the value is removed\n   */\n  remove(key) {\n    const db = this.assertDb();\n    return db.removeItem(key);\n  }\n  /**\n   * Clear the entire key value store. WARNING: HOT!\n   * @returns Returns a promise that resolves when the store is cleared\n   */\n  clear() {\n    const db = this.assertDb();\n    return db.clear();\n  }\n  /**\n   * @returns Returns a promise that resolves with the number of keys stored.\n   */\n  length() {\n    const db = this.assertDb();\n    return db.length();\n  }\n  /**\n   * @returns Returns a promise that resolves with the keys in the store.\n   */\n  keys() {\n    const db = this.assertDb();\n    return db.keys();\n  }\n  /**\n   * Iterate through each key,value pair.\n   * @param iteratorCallback a callback of the form (value, key, iterationNumber)\n   * @returns Returns a promise that resolves when the iteration has finished.\n   */\n  forEach(iteratorCallback) {\n    const db = this.assertDb();\n    return db.iterate(iteratorCallback);\n  }\n  setEncryptionKey(key) {\n    var _a;\n    if (!this._secureStorageDriver) {\n      throw new Error('@ionic-enterprise/secure-storage not installed. Encryption support not available');\n    } else {\n      (_a = this._secureStorageDriver) === null || _a === void 0 ? void 0 : _a.setEncryptionKey(key);\n    }\n  }\n}", "map": {"version": 3, "names": ["LocalForage", "Drivers", "SecureStorage", "IndexedDB", "INDEXEDDB", "LocalStorage", "LOCALSTORAGE", "defaultConfig", "name", "storeName", "db<PERSON><PERSON>", "driver<PERSON><PERSON><PERSON>", "Storage", "constructor", "config", "_db", "_secureStorageDriver", "actualConfig", "Object", "assign", "_config", "create", "_this", "_asyncToGenerator", "db", "createInstance", "setDriver", "defineDriver", "driver", "_this2", "_driver", "_a", "assertDb", "Error", "get", "key", "getItem", "set", "value", "setItem", "remove", "removeItem", "clear", "length", "keys", "for<PERSON>ach", "iteratorCallback", "iterate", "setEncryptionKey"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/storage/dist/esm/index.js"], "sourcesContent": ["import LocalForage from 'localforage';\n/** @hidden */\nexport const Drivers = {\n    SecureStorage: 'ionicSecureStorage',\n    IndexedDB: LocalForage.INDEXEDDB,\n    LocalStorage: LocalForage.LOCALSTORAGE\n};\nconst defaultConfig = {\n    name: '_ionicstorage',\n    storeName: '_ionickv',\n    dbKey: '_ionickey',\n    driverOrder: [\n        Drivers.SecureStorage,\n        Drivers.IndexedDB,\n        Drivers.LocalStorage\n    ]\n};\nexport class Storage {\n    /**\n     * Create a new Storage instance using the order of drivers and any additional config\n     * options to pass to LocalForage.\n     *\n     * Possible default driverOrder options are: ['indexeddb', 'localstorage'] and the\n     * default is that exact ordering.\n     *\n     * When using Ionic Secure Storage (enterprise only), use ['ionicSecureStorage', 'indexeddb', 'localstorage'] to ensure\n     * Secure Storage is used when available, or fall back to IndexedDB or LocalStorage on the web.\n     */\n    constructor(config = defaultConfig) {\n        this._db = null;\n        this._secureStorageDriver = null;\n        const actualConfig = Object.assign({}, defaultConfig, config || {});\n        this._config = actualConfig;\n    }\n    async create() {\n        const db = LocalForage.createInstance(this._config);\n        this._db = db;\n        await db.setDriver(this._config.driverOrder || []);\n        return this;\n    }\n    /**\n     * Define a new Driver. Must be called before\n     * initializing the database. Example:\n     *\n     * await storage.defineDriver(myDriver);\n     * await storage.create();\n     */\n    async defineDriver(driver) {\n        if (driver._driver === Drivers.SecureStorage) {\n            this._secureStorageDriver = driver;\n        }\n        return LocalForage.defineDriver(driver);\n    }\n    /**\n     * Get the name of the driver being used.\n     * @returns Name of the driver\n     */\n    get driver() {\n        var _a;\n        return ((_a = this._db) === null || _a === void 0 ? void 0 : _a.driver()) || null;\n    }\n    assertDb() {\n        if (!this._db) {\n            throw new Error('Database not created. Must call create() first');\n        }\n        return this._db;\n    }\n    /**\n     * Get the value associated with the given key.\n     * @param key the key to identify this value\n     * @returns Returns a promise with the value of the given key\n     */\n    get(key) {\n        const db = this.assertDb();\n        return db.getItem(key);\n    }\n    /**\n     * Set the value for the given key.\n     * @param key the key to identify this value\n     * @param value the value for this key\n     * @returns Returns a promise that resolves when the key and value are set\n     */\n    set(key, value) {\n        const db = this.assertDb();\n        return db.setItem(key, value);\n    }\n    /**\n     * Remove any value associated with this key.\n     * @param key the key to identify this value\n     * @returns Returns a promise that resolves when the value is removed\n     */\n    remove(key) {\n        const db = this.assertDb();\n        return db.removeItem(key);\n    }\n    /**\n     * Clear the entire key value store. WARNING: HOT!\n     * @returns Returns a promise that resolves when the store is cleared\n     */\n    clear() {\n        const db = this.assertDb();\n        return db.clear();\n    }\n    /**\n     * @returns Returns a promise that resolves with the number of keys stored.\n     */\n    length() {\n        const db = this.assertDb();\n        return db.length();\n    }\n    /**\n     * @returns Returns a promise that resolves with the keys in the store.\n     */\n    keys() {\n        const db = this.assertDb();\n        return db.keys();\n    }\n    /**\n     * Iterate through each key,value pair.\n     * @param iteratorCallback a callback of the form (value, key, iterationNumber)\n     * @returns Returns a promise that resolves when the iteration has finished.\n     */\n    forEach(iteratorCallback) {\n        const db = this.assertDb();\n        return db.iterate(iteratorCallback);\n    }\n    setEncryptionKey(key) {\n        var _a;\n        if (!this._secureStorageDriver) {\n            throw new Error('@ionic-enterprise/secure-storage not installed. Encryption support not available');\n        }\n        else {\n            (_a = this._secureStorageDriver) === null || _a === void 0 ? void 0 : _a.setEncryptionKey(key);\n        }\n    }\n}\n"], "mappings": ";AAAA,OAAOA,WAAW,MAAM,aAAa;AACrC;AACA,OAAO,MAAMC,OAAO,GAAG;EACnBC,aAAa,EAAE,oBAAoB;EACnCC,SAAS,EAAEH,WAAW,CAACI,SAAS;EAChCC,YAAY,EAAEL,WAAW,CAACM;AAC9B,CAAC;AACD,MAAMC,aAAa,GAAG;EAClBC,IAAI,EAAE,eAAe;EACrBC,SAAS,EAAE,UAAU;EACrBC,KAAK,EAAE,WAAW;EAClBC,WAAW,EAAE,CACTV,OAAO,CAACC,aAAa,EACrBD,OAAO,CAACE,SAAS,EACjBF,OAAO,CAACI,YAAY;AAE5B,CAAC;AACD,OAAO,MAAMO,OAAO,CAAC;EACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,MAAM,GAAGP,aAAa,EAAE;IAChC,IAAI,CAACQ,GAAG,GAAG,IAAI;IACf,IAAI,CAACC,oBAAoB,GAAG,IAAI;IAChC,MAAMC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,aAAa,EAAEO,MAAM,IAAI,CAAC,CAAC,CAAC;IACnE,IAAI,CAACM,OAAO,GAAGH,YAAY;EAC/B;EACMI,MAAMA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACX,MAAMC,EAAE,GAAGxB,WAAW,CAACyB,cAAc,CAACH,KAAI,CAACF,OAAO,CAAC;MACnDE,KAAI,CAACP,GAAG,GAAGS,EAAE;MACb,MAAMA,EAAE,CAACE,SAAS,CAACJ,KAAI,CAACF,OAAO,CAACT,WAAW,IAAI,EAAE,CAAC;MAClD,OAAOW,KAAI;IAAC;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACUK,YAAYA,CAACC,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAAN,iBAAA;MACvB,IAAIK,MAAM,CAACE,OAAO,KAAK7B,OAAO,CAACC,aAAa,EAAE;QAC1C2B,MAAI,CAACb,oBAAoB,GAAGY,MAAM;MACtC;MACA,OAAO5B,WAAW,CAAC2B,YAAY,CAACC,MAAM,CAAC;IAAC;EAC5C;EACA;AACJ;AACA;AACA;EACI,IAAIA,MAAMA,CAAA,EAAG;IACT,IAAIG,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAG,IAAI,CAAChB,GAAG,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACH,MAAM,CAAC,CAAC,KAAK,IAAI;EACrF;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACjB,GAAG,EAAE;MACX,MAAM,IAAIkB,KAAK,CAAC,gDAAgD,CAAC;IACrE;IACA,OAAO,IAAI,CAAClB,GAAG;EACnB;EACA;AACJ;AACA;AACA;AACA;EACImB,GAAGA,CAACC,GAAG,EAAE;IACL,MAAMX,EAAE,GAAG,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC1B,OAAOR,EAAE,CAACY,OAAO,CAACD,GAAG,CAAC;EAC1B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,GAAGA,CAACF,GAAG,EAAEG,KAAK,EAAE;IACZ,MAAMd,EAAE,GAAG,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC1B,OAAOR,EAAE,CAACe,OAAO,CAACJ,GAAG,EAAEG,KAAK,CAAC;EACjC;EACA;AACJ;AACA;AACA;AACA;EACIE,MAAMA,CAACL,GAAG,EAAE;IACR,MAAMX,EAAE,GAAG,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC1B,OAAOR,EAAE,CAACiB,UAAU,CAACN,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACIO,KAAKA,CAAA,EAAG;IACJ,MAAMlB,EAAE,GAAG,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC1B,OAAOR,EAAE,CAACkB,KAAK,CAAC,CAAC;EACrB;EACA;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,MAAMnB,EAAE,GAAG,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC1B,OAAOR,EAAE,CAACmB,MAAM,CAAC,CAAC;EACtB;EACA;AACJ;AACA;EACIC,IAAIA,CAAA,EAAG;IACH,MAAMpB,EAAE,GAAG,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC1B,OAAOR,EAAE,CAACoB,IAAI,CAAC,CAAC;EACpB;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAACC,gBAAgB,EAAE;IACtB,MAAMtB,EAAE,GAAG,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC1B,OAAOR,EAAE,CAACuB,OAAO,CAACD,gBAAgB,CAAC;EACvC;EACAE,gBAAgBA,CAACb,GAAG,EAAE;IAClB,IAAIJ,EAAE;IACN,IAAI,CAAC,IAAI,CAACf,oBAAoB,EAAE;MAC5B,MAAM,IAAIiB,KAAK,CAAC,kFAAkF,CAAC;IACvG,CAAC,MACI;MACD,CAACF,EAAE,GAAG,IAAI,CAACf,oBAAoB,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,gBAAgB,CAACb,GAAG,CAAC;IAClG;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}