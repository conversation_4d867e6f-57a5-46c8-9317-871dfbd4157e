{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/checkbox\";\nimport * as i10 from \"@angular/material/progress-spinner\";\nfunction RegisterPage_div_34_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_34_div_8_Template_div_click_0_listener() {\n      const type_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectComplaintType(type_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 25);\n    i0.ɵɵelement(2, \"ion-icon\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 28);\n    i0.ɵɵelement(9, \"ion-radio\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const type_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r2.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_3_0.value) === type_r2.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", type_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(type_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(type_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", type_r2.value);\n  }\n}\nfunction RegisterPage_div_34_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_34_div_9_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.isStepCompleted(1) ? null : null);\n    });\n    i0.ɵɵtext(2, \" Continue to Invoice Search \");\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"arrow_forward\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction RegisterPage_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"form\", 18)(2, \"div\", 19)(3, \"h3\");\n    i0.ɵɵtext(4, \"Select Complaint Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6, \"Choose the category that best describes your complaint\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 21);\n    i0.ɵɵtemplate(8, RegisterPage_div_34_div_8_Template, 10, 6, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, RegisterPage_div_34_div_9_Template, 5, 0, \"div\", 23);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintTypeForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.complaintTypes);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r2.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_3_0.value);\n  }\n}\nfunction RegisterPage_div_35_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"h4\");\n    i0.ɵɵtext(2, \"Selected Complaint Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 50);\n    i0.ɵɵelement(4, \"ion-icon\", 26);\n    i0.ɵɵelementStart(5, \"div\")(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"name\", (tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_3_0.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_4_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_4_0.description);\n  }\n}\nfunction RegisterPage_div_35_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactPersonName\"), \" \");\n  }\n}\nfunction RegisterPage_div_35_mat_error_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactNumber\"), \" \");\n  }\n}\nfunction RegisterPage_div_35_mat_error_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"complaintDescription\"), \" \");\n  }\n}\nfunction RegisterPage_div_35_div_52_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"mat-icon\", 61);\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 62)(4, \"span\", 63);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 64);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 65);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_35_div_52_div_16_div_4_Template_button_click_8_listener() {\n      const i_r8 = i0.ɵɵrestoreView(_r7).index;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r8));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r9 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(file_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", (file_r9.size / 1024 / 1024).toFixed(2), \" MB)\");\n  }\n}\nfunction RegisterPage_div_35_div_52_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 58);\n    i0.ɵɵtemplate(4, RegisterPage_div_35_div_52_div_16_div_4_Template, 11, 2, \"div\", 59);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Selected Files (\", ctx_r2.selectedFiles.length, \"):\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedFiles);\n  }\n}\nfunction RegisterPage_div_35_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 51)(1, \"h5\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Upload Supporting Documents \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 52)(6, \"input\", 53, 0);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_35_div_52_Template_input_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_35_div_52_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const fileInput_r6 = i0.ɵɵreference(7);\n      return i0.ɵɵresetView(fileInput_r6.click());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Choose Files \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 55)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, RegisterPage_div_35_div_52_div_16_Template, 5, 2, \"div\", 56);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFiles.length > 0);\n  }\n}\nfunction RegisterPage_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"form\", 18)(2, \"div\", 19)(3, \"h3\");\n    i0.ɵɵtext(4, \"Complaint Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6, \"Provide detailed information about your complaint\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, RegisterPage_div_35_div_7_Template, 10, 3, \"div\", 32);\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"h4\")(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Complaint Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 34)(14, \"div\", 35)(15, \"mat-form-field\", 36)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"Contact Person Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 37);\n    i0.ɵɵelementStart(19, \"mat-icon\", 38);\n    i0.ɵɵtext(20, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, RegisterPage_div_35_mat_error_21_Template, 2, 1, \"mat-error\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"mat-form-field\", 36)(23, \"mat-label\");\n    i0.ɵɵtext(24, \"Contact Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 40);\n    i0.ɵɵelementStart(26, \"mat-icon\", 38);\n    i0.ɵɵtext(27, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, RegisterPage_div_35_mat_error_28_Template, 2, 1, \"mat-error\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-form-field\", 41)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"Complaint Description *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"textarea\", 42);\n    i0.ɵɵelementStart(33, \"mat-icon\", 38);\n    i0.ɵɵtext(34, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"mat-hint\");\n    i0.ɵɵtext(36, \"Please provide a detailed description of the issue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(37, RegisterPage_div_35_mat_error_37_Template, 2, 1, \"mat-error\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"mat-form-field\", 41)(39, \"mat-label\");\n    i0.ɵɵtext(40, \"Additional Comments (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(41, \"textarea\", 43);\n    i0.ɵɵelementStart(42, \"mat-icon\", 38);\n    i0.ɵɵtext(43, \"comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"mat-hint\");\n    i0.ɵɵtext(45, \"Optional: Add any additional information that might be helpful\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 44)(47, \"mat-checkbox\", 45)(48, \"strong\");\n    i0.ɵɵtext(49, \"Do you have complaint letters to attach?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(50, \"p\", 46);\n    i0.ɵɵtext(51, \"Check this box if you have supporting documents, photos, or letters related to your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(52, RegisterPage_div_35_div_52_Template, 17, 1, \"div\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 30)(54, \"button\", 48);\n    i0.ɵɵtext(55, \" Continue to Invoice Selection \");\n    i0.ɵɵelementStart(56, \"mat-icon\");\n    i0.ɵɵtext(57, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintDetailsForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getSelectedComplaintType());\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_3_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_3_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r2.complaintDetailsForm.get(\"complaintDescription\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r2.complaintDetailsForm.get(\"complaintDescription\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r2.complaintDetailsForm.get(\"hasComplaintLetters\")) == null ? null : tmp_6_0.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.complaintDetailsForm.valid);\n  }\n}\nfunction RegisterPage_div_36_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"label\");\n    i0.ɵɵtext(2, \"Complaint Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79);\n    i0.ɵɵelement(4, \"ion-icon\", 26);\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"name\", (tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_3_0.label);\n  }\n}\nfunction RegisterPage_div_36_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"label\");\n    i0.ɵɵtext(2, \"Contact Person:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_2_0.value);\n  }\n}\nfunction RegisterPage_div_36_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"label\");\n    i0.ɵɵtext(2, \"Contact Number:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_2_0.value);\n  }\n}\nfunction RegisterPage_div_36_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"label\");\n    i0.ɵɵtext(2, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 80)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r2.complaintDetailsForm.get(\"complaintDescription\")) == null ? null : tmp_2_0.value);\n  }\n}\nfunction RegisterPage_div_36_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 78)(1, \"label\");\n    i0.ɵɵtext(2, \"Attachments:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 79)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.selectedFiles.length, \" file(s) attached\");\n  }\n}\nfunction RegisterPage_div_36_mat_error_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.invoiceSearchForm, \"searchTerm\"), \" \");\n  }\n}\nfunction RegisterPage_div_36_div_30_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Search Results (\", ctx_r2.invoiceSearchResults.length, \" found) \");\n  }\n}\nfunction RegisterPage_div_36_div_30_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" All Available Invoices (\", ctx_r2.invoiceSearchResults.length, \" total) \");\n  }\n}\nfunction RegisterPage_div_36_div_30_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 84);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_36_div_30_div_5_Template_div_click_0_listener() {\n      const invoice_r12 = i0.ɵɵrestoreView(_r11).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectInvoice(invoice_r12));\n    });\n    i0.ɵɵelementStart(1, \"div\", 85)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 86);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 87);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 88);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const invoice_r12 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(invoice_r12.invoiceNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 5, invoice_r12.invoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(invoice_r12.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", invoice_r12.zone, \" - \", invoice_r12.operatingUnit, \"\");\n  }\n}\nfunction RegisterPage_div_36_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 81)(1, \"h5\");\n    i0.ɵɵtemplate(2, RegisterPage_div_36_div_30_span_2_Template, 2, 1, \"span\", 39)(3, RegisterPage_div_36_div_30_span_3_Template, 2, 1, \"span\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 82);\n    i0.ɵɵtemplate(5, RegisterPage_div_36_div_30_div_5_Template, 11, 8, \"div\", 83);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_2_0.value) && ((tmp_2_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_2_0.value.trim()) !== \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !((tmp_3_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_3_0.value) || ((tmp_3_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_3_0.value.trim()) === \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.invoiceSearchResults);\n  }\n}\nfunction RegisterPage_div_36_div_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"p\");\n    i0.ɵɵtext(2, \"No invoices found matching your search criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 90);\n    i0.ɵɵtext(4, \"Try searching with different keywords or clear the search to see all invoices.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterPage_div_36_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"h4\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"receipt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Selected Invoice Details (Read-Only) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 92)(6, \"div\", 93)(7, \"div\", 94)(8, \"div\", 95)(9, \"label\");\n    i0.ɵɵtext(10, \"Invoice Number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 96)(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"confirmation_number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 95)(17, \"label\");\n    i0.ɵɵtext(18, \"Invoice Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 96)(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\");\n    i0.ɵɵtext(23);\n    i0.ɵɵpipe(24, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 94)(26, \"div\", 95)(27, \"label\");\n    i0.ɵɵtext(28, \"Customer Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 96)(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(34, \"div\", 95)(35, \"label\");\n    i0.ɵɵtext(36, \"Customer Address\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 96)(38, \"mat-icon\");\n    i0.ɵɵtext(39, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\");\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(42, \"div\", 94)(43, \"div\", 95)(44, \"label\");\n    i0.ɵɵtext(45, \"Zone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(46, \"div\", 96)(47, \"mat-icon\");\n    i0.ɵɵtext(48, \"map\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"span\");\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 95)(52, \"label\");\n    i0.ɵɵtext(53, \"Operating Unit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"div\", 96)(55, \"mat-icon\");\n    i0.ɵɵtext(56, \"domain\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\");\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(59, \"div\", 94)(60, \"div\", 95)(61, \"label\");\n    i0.ɵɵtext(62, \"Organization\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 96)(64, \"mat-icon\");\n    i0.ɵɵtext(65, \"corporate_fare\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\");\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(68, \"div\", 95)(69, \"label\");\n    i0.ɵɵtext(70, \"Bill To Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(71, \"div\", 96)(72, \"mat-icon\");\n    i0.ɵɵtext(73, \"account_balance\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"span\");\n    i0.ɵɵtext(75);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(76, \"div\", 94)(77, \"div\", 97)(78, \"label\");\n    i0.ɵɵtext(79, \"Ship To Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"div\", 96)(81, \"mat-icon\");\n    i0.ɵɵtext(82, \"local_shipping\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"span\");\n    i0.ɵɵtext(84);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(85, \"div\", 98)(86, \"button\", 99);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_36_div_32_Template_button_click_86_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearInvoiceSelection());\n    });\n    i0.ɵɵelementStart(87, \"mat-icon\");\n    i0.ɵɵtext(88, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(89, \" Change Invoice \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.invoiceNumber);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(24, 9, ctx_r2.selectedInvoice.invoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerName);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerAddress);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.zone);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.operatingUnit);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.organization);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.billToLocation);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.shipToLocation);\n  }\n}\nfunction RegisterPage_div_36_mat_icon_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_36_mat_spinner_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 100);\n  }\n}\nfunction RegisterPage_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 19)(2, \"h3\");\n    i0.ɵɵtext(3, \"Invoice Selection\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5, \"Search and select the invoice related to your complaint\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 66)(7, \"h4\")(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"assignment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Your Complaint Summary \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 67);\n    i0.ɵɵtemplate(12, RegisterPage_div_36_div_12_Template, 7, 2, \"div\", 68)(13, RegisterPage_div_36_div_13_Template, 8, 1, \"div\", 68)(14, RegisterPage_div_36_div_14_Template, 8, 1, \"div\", 68)(15, RegisterPage_div_36_div_15_Template, 8, 1, \"div\", 68)(16, RegisterPage_div_36_div_16_Template, 8, 1, \"div\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"form\", 18)(18, \"div\", 69)(19, \"h4\")(20, \"mat-icon\");\n    i0.ɵɵtext(21, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22, \" Search for Related Invoice \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"mat-form-field\", 70)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Search Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 71);\n    i0.ɵɵlistener(\"input\", function RegisterPage_div_36_Template_input_input_26_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInvoiceSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-icon\", 38);\n    i0.ɵɵtext(28, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, RegisterPage_div_36_mat_error_29_Template, 2, 1, \"mat-error\", 39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, RegisterPage_div_36_div_30_Template, 6, 3, \"div\", 72)(31, RegisterPage_div_36_div_31_Template, 5, 0, \"div\", 73);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(32, RegisterPage_div_36_div_32_Template, 90, 12, \"div\", 74);\n    i0.ɵɵelementStart(33, \"div\", 30)(34, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_36_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBackToComplaintDetails());\n    });\n    i0.ɵɵelementStart(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" Back to Complaint Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 76);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_36_Template_button_click_38_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmitComplaint());\n    });\n    i0.ɵɵtemplate(39, RegisterPage_div_36_mat_icon_39_Template, 2, 0, \"mat-icon\", 39)(40, RegisterPage_div_36_mat_spinner_40_Template, 1, 0, \"mat-spinner\", 77);\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_7_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getSelectedComplaintType());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_2_0.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_3_0.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r2.complaintDetailsForm.get(\"complaintDescription\")) == null ? null : tmp_4_0.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFiles.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.invoiceSearchForm);\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading || !ctx_r2.selectedInvoice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isLoading ? \"Submit Complaint\" : \"Submit Complaint\", \" \");\n  }\n}\nexport class RegisterPage {\n  constructor(formBuilder, router, loadingController, toastController) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.isLoading = false;\n    this.selectedFiles = [];\n    this.selectedInvoice = null;\n    this.invoiceSearchResults = [];\n    this.showInvoiceResults = false;\n    this.complaintTypes = [{\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    }, {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    }, {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    }, {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    }, {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    }, {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }];\n    // Sample invoice data for demonstration\n    this.sampleInvoices = [{\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    }, {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    }, {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    }, {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai'\n    }, {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata'\n    }, {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad'\n    }, {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat'\n    }, {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi'\n    }];\n    this.createForms();\n  }\n  ngOnInit() {\n    // Don't show invoices initially - wait until step 3\n  }\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      complaintDescription: ['', [Validators.required, Validators.minLength(10)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n  selectComplaintType(type) {\n    this.complaintTypeForm.patchValue({\n      selectedType: type.value\n    });\n  }\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice => invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase()));\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n  selectInvoice(invoice) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: invoice.invoiceNumber\n    });\n  }\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: ''\n    });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n  onSubmitComplaint() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.complaintTypeForm.valid && _this.selectedInvoice && _this.complaintDetailsForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Registering complaint...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate registration process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          const toast = yield _this.toastController.create({\n            message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n            duration: 4000,\n            color: 'success',\n            position: 'top'\n          });\n          yield toast.present();\n          // Navigate to track page\n          _this.router.navigate(['/track']);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please complete all required steps and fill in all required fields.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({\n        attachedFile: files[0]\n      });\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({\n        attachedFile: null\n      });\n    }\n  }\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n  isStepCompleted(step) {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDetailsForm.valid;\n      case 3:\n        return this.selectedInvoice !== null;\n      default:\n        return false;\n    }\n  }\n  getErrorMessage(form, field) {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      selectedType: 'Complaint Type',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      complaintDescription: 'Complaint Description',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function RegisterPage_Factory(t) {\n      return new (t || RegisterPage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.LoadingController), i0.ɵɵdirectiveInject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterPage,\n      selectors: [[\"app-register\"]],\n      decls: 37,\n      vars: 21,\n      consts: [[\"fileInput\", \"\"], [3, \"translucent\"], [\"color\", \"primary\"], [\"slot\", \"start\"], [3, \"click\"], [\"name\", \"arrow-back\"], [1, \"register-content\", 3, \"fullscreen\"], [1, \"container\"], [1, \"header-section\"], [1, \"header-content\"], [1, \"progress-section\"], [1, \"progress-steps\"], [1, \"step\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step-connector\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"step-content\"], [3, \"formGroup\"], [1, \"form-section\"], [1, \"section-description\"], [1, \"complaint-types-grid\"], [\"class\", \"complaint-type-card\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"step-actions\", 4, \"ngIf\"], [1, \"complaint-type-card\", 3, \"click\"], [1, \"card-icon\"], [3, \"name\"], [1, \"card-content\"], [1, \"card-radio\"], [\"formControlName\", \"selectedType\", 3, \"value\"], [1, \"step-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"selected-type-display\", 4, \"ngIf\"], [1, \"complaint-form-section\"], [1, \"editable-form-card\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonName\", \"placeholder\", \"Enter contact person name\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"contactNumber\", \"placeholder\", \"Enter 10-digit contact number\", \"type\", \"tel\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"complaintDescription\", \"rows\", \"4\", \"placeholder\", \"Describe your complaint in detail (minimum 10 characters)\"], [\"matInput\", \"\", \"formControlName\", \"comments\", \"rows\", \"3\", \"placeholder\", \"Any additional comments or information\"], [1, \"complaint-letters-section\"], [\"formControlName\", \"hasComplaintLetters\", \"color\", \"primary\"], [1, \"checkbox-hint\"], [\"class\", \"file-upload-section\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"disabled\"], [1, \"selected-type-display\"], [1, \"type-display-card\"], [1, \"file-upload-section\"], [1, \"upload-area\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \".pdf,.jpg,.jpeg,.png,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [1, \"upload-hint\"], [\"class\", \"selected-files\", 4, \"ngIf\"], [1, \"selected-files\"], [1, \"file-list\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-icon\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-size\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 1, \"remove-file\", 3, \"click\"], [1, \"complaint-summary-section\"], [1, \"summary-card\"], [\"class\", \"summary-item\", 4, \"ngIf\"], [1, \"search-section\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"formControlName\", \"searchTerm\", \"placeholder\", \"Enter invoice number, customer name, or leave empty to see all\", 3, \"input\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"invoice-details-section\", 4, \"ngIf\"], [\"mat-button\", \"\", 1, \"back-button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"summary-item\"], [1, \"summary-value\"], [1, \"summary-value\", \"description\"], [1, \"search-results\"], [1, \"invoice-list\"], [\"class\", \"invoice-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"invoice-item\", 3, \"click\"], [1, \"invoice-header\"], [1, \"invoice-date\"], [1, \"invoice-customer\"], [1, \"invoice-zone\"], [1, \"no-results\"], [1, \"search-hint\"], [1, \"invoice-details-section\"], [1, \"readonly-invoice-card\"], [1, \"invoice-details-grid\"], [1, \"detail-group\"], [1, \"detail-item\"], [1, \"readonly-field\"], [1, \"detail-item\", \"full-width\"], [1, \"invoice-actions\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [\"diameter\", \"20\"]],\n      template: function RegisterPage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-header\", 1)(1, \"ion-toolbar\", 2)(2, \"ion-buttons\", 3)(3, \"ion-button\", 4);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_ion_button_click_3_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelement(4, \"ion-icon\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"ion-title\");\n          i0.ɵɵtext(6, \"Register Complaint\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"ion-content\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"h1\");\n          i0.ɵɵtext(12, \"Register New Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\");\n          i0.ɵɵtext(14, \"Follow the steps below to register your complaint\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13);\n          i0.ɵɵtext(19, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 14);\n          i0.ɵɵtext(21, \"Complaint Type\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(22, \"div\", 15);\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"div\", 13);\n          i0.ɵɵtext(25, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14);\n          i0.ɵɵtext(27, \"Complaint Description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(28, \"div\", 15);\n          i0.ɵɵelementStart(29, \"div\", 12)(30, \"div\", 13);\n          i0.ɵɵtext(31, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 14);\n          i0.ɵɵtext(33, \"Invoice Selection\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(34, RegisterPage_div_34_Template, 10, 3, \"div\", 16)(35, RegisterPage_div_35_Template, 58, 7, \"div\", 16)(36, RegisterPage_div_36_Template, 42, 14, \"div\", 16);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"translucent\", true);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"fullscreen\", true);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"active\", true)(\"completed\", ctx.isStepCompleted(1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isStepCompleted(1))(\"completed\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isStepCompleted(2))(\"completed\", ctx.isStepCompleted(3));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(1) && !ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(2) && !ctx.isStepCompleted(3));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.IonButton, i3.IonButtons, i3.IonContent, i3.IonHeader, i3.IonIcon, i3.IonRadio, i3.IonTitle, i3.IonToolbar, i3.RadioValueAccessor, i5.MatFormField, i5.MatLabel, i5.MatHint, i5.MatError, i5.MatSuffix, i6.MatInput, i7.MatButton, i7.MatIconButton, i8.MatIcon, i9.MatCheckbox, i10.MatProgressSpinner, i4.DatePipe],\n      styles: [\".register-content[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n}\\n.register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  color: #999;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.active[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: #e0e0e0;\\n  margin: 0 16px;\\n  margin-bottom: 32px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 32px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0 0 32px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 12px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card.selected[_ngcontent-%COMP%] {\\n  border-color: #1976d2;\\n  background: #e3f2fd;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]   ion-radio[_ngcontent-%COMP%] {\\n  --color: #1976d2;\\n  --color-checked: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   .invoice-date[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-customer[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-zone[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p.search-hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  font-style: italic;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #666;\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e0e0e0;\\n  padding-top: 16px;\\n  text-align: right;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-size: 14px;\\n  font-weight: 500;\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"EDITABLE\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  padding: 16px;\\n  background: #f3e5f5;\\n  border: 1px solid #9c27b0;\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   .checkbox-hint[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 32px;\\n  color: #666;\\n  font-size: 13px;\\n  font-style: italic;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%] {\\n  margin: 24px 0 0 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border: 2px dashed #9c27b0;\\n  border-radius: 8px;\\n  background: #fce4ec;\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  border-color: #9c27b0;\\n  color: #7b1fa2;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(156, 39, 176, 0.04);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n  margin: 8px 0 0 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: white;\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  border: 1px solid #e1bee7;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  border-color: #9c27b0;\\n  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-size[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: space-between;\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  border-color: #ccc;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #999;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);\\n  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);\\n  transform: translateY(-1px);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  color: #999;\\n  box-shadow: none;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n    padding: 24px 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    margin: 0 8px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵlistener", "RegisterPage_div_34_div_8_Template_div_click_0_listener", "type_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectComplaintType", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵclassProp", "tmp_3_0", "complaintTypeForm", "get", "value", "ɵɵadvance", "ɵɵproperty", "icon", "ɵɵtextInterpolate", "label", "description", "RegisterPage_div_34_div_9_Template_button_click_1_listener", "_r4", "isStepCompleted", "ɵɵtemplate", "RegisterPage_div_34_div_8_Template", "RegisterPage_div_34_div_9_Template", "complaintTypes", "tmp_2_0", "getSelectedComplaintType", "tmp_4_0", "ɵɵtextInterpolate1", "getErrorMessage", "complaintDetailsForm", "RegisterPage_div_35_div_52_div_16_div_4_Template_button_click_8_listener", "i_r8", "_r7", "index", "removeFile", "file_r9", "name", "size", "toFixed", "RegisterPage_div_35_div_52_div_16_div_4_Template", "selectedFiles", "length", "RegisterPage_div_35_div_52_Template_input_change_6_listener", "$event", "_r5", "onFileSelected", "RegisterPage_div_35_div_52_Template_button_click_8_listener", "fileInput_r6", "ɵɵreference", "click", "RegisterPage_div_35_div_52_div_16_Template", "RegisterPage_div_35_div_7_Template", "RegisterPage_div_35_mat_error_21_Template", "RegisterPage_div_35_mat_error_28_Template", "RegisterPage_div_35_mat_error_37_Template", "RegisterPage_div_35_div_52_Template", "invalid", "touched", "tmp_5_0", "tmp_6_0", "valid", "invoiceSearchForm", "invoiceSearchResults", "RegisterPage_div_36_div_30_div_5_Template_div_click_0_listener", "invoice_r12", "_r11", "selectInvoice", "invoiceNumber", "ɵɵpipeBind2", "invoiceDate", "customerName", "ɵɵtextInterpolate2", "zone", "operatingUnit", "RegisterPage_div_36_div_30_span_2_Template", "RegisterPage_div_36_div_30_span_3_Template", "RegisterPage_div_36_div_30_div_5_Template", "trim", "RegisterPage_div_36_div_32_Template_button_click_86_listener", "_r13", "clearInvoiceSelection", "selectedInvoice", "customerAddress", "organization", "billToLocation", "shipToLocation", "RegisterPage_div_36_div_12_Template", "RegisterPage_div_36_div_13_Template", "RegisterPage_div_36_div_14_Template", "RegisterPage_div_36_div_15_Template", "RegisterPage_div_36_div_16_Template", "RegisterPage_div_36_Template_input_input_26_listener", "_r10", "onInvoiceSearch", "RegisterPage_div_36_mat_error_29_Template", "RegisterPage_div_36_div_30_Template", "RegisterPage_div_36_div_31_Template", "RegisterPage_div_36_div_32_Template", "RegisterPage_div_36_Template_button_click_34_listener", "goBackToComplaintDetails", "RegisterPage_div_36_Template_button_click_38_listener", "onSubmitComplaint", "RegisterPage_div_36_mat_icon_39_Template", "RegisterPage_div_36_mat_spinner_40_Template", "tmp_7_0", "showInvoiceResults", "isLoading", "RegisterPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "sampleInvoices", "Date", "createForms", "ngOnInit", "group", "selectedType", "required", "searchTerm", "contactPersonName", "<PERSON><PERSON><PERSON><PERSON>", "contactNumber", "pattern", "complaintDescription", "comments", "hasComplaintLetters", "attachedFile", "type", "patchValue", "showAllInvoices", "setTimeout", "filter", "invoice", "toLowerCase", "includes", "_this", "_asyncToGenerator", "loading", "create", "message", "duration", "present", "dismiss", "toast", "now", "color", "position", "navigate", "event", "files", "target", "Array", "from", "splice", "selected<PERSON><PERSON><PERSON>", "find", "step", "form", "field", "control", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "goBack", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "LoadingController", "ToastController", "selectors", "decls", "vars", "consts", "template", "RegisterPage_Template", "rf", "ctx", "RegisterPage_Template_ion_button_click_3_listener", "RegisterPage_div_34_Template", "RegisterPage_div_35_Template", "RegisterPage_div_36_Template"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\n\nexport interface InvoiceData {\n  invoiceNumber: string;\n  invoiceDate: Date;\n  customerName: string;\n  customerAddress: string;\n  zone: string;\n  operatingUnit: string;\n  organization: string;\n  billToLocation: string;\n  shipToLocation: string;\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss'],\n})\nexport class RegisterPage implements OnInit {\n\n  complaintTypeForm!: FormGroup;\n  invoiceSearchForm!: FormGroup;\n  complaintDetailsForm!: FormGroup;\n  isLoading = false;\n  selectedFiles: File[] = [];\n  selectedInvoice: InvoiceData | null = null;\n  invoiceSearchResults: InvoiceData[] = [];\n  showInvoiceResults = false;\n\n  complaintTypes = [\n    {\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    },\n    {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    },\n    {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    },\n    {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    },\n    {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    },\n    {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }\n  ];\n\n  // Sample invoice data for demonstration\n  sampleInvoices: InvoiceData[] = [\n    {\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    },\n    {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    },\n    {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    },\n    {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai'\n    },\n    {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata'\n    },\n    {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad'\n    },\n    {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat'\n    },\n    {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi'\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController\n  ) {\n    this.createForms();\n  }\n\n  ngOnInit() {\n    // Don't show invoices initially - wait until step 3\n  }\n\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      complaintDescription: ['', [Validators.required, Validators.minLength(10)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n\n  selectComplaintType(type: any) {\n    this.complaintTypeForm.patchValue({ selectedType: type.value });\n  }\n\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice =>\n          invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n\n  selectInvoice(invoice: InvoiceData) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({ searchTerm: invoice.invoiceNumber });\n  }\n\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({ searchTerm: '' });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n\n  async onSubmitComplaint() {\n    if (this.complaintTypeForm.valid && this.selectedInvoice && this.complaintDetailsForm.valid) {\n      this.isLoading = true;\n\n      const loading = await this.loadingController.create({\n        message: 'Registering complaint...',\n        duration: 3000\n      });\n\n      await loading.present();\n\n      // Simulate registration process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n\n        const toast = await this.toastController.create({\n          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n          duration: 4000,\n          color: 'success',\n          position: 'top'\n        });\n        await toast.present();\n\n        // Navigate to track page\n        this.router.navigate(['/track']);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please complete all required steps and fill in all required fields.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({ attachedFile: files[0] });\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({ attachedFile: null });\n    }\n  }\n\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDetailsForm.valid;\n      case 3:\n        return this.selectedInvoice !== null;\n      default:\n        return false;\n    }\n  }\n\n  getErrorMessage(form: FormGroup, field: string): string {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      selectedType: 'Complaint Type',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      complaintDescription: 'Complaint Description',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n}\n", "<ion-header [translucent]=\"true\">\n  <ion-toolbar color=\"primary\">\n    <ion-buttons slot=\"start\">\n      <ion-button (click)=\"goBack()\">\n        <ion-icon name=\"arrow-back\"></ion-icon>\n      </ion-button>\n    </ion-buttons>\n    <ion-title>Register Complaint</ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\" class=\"register-content\">\n  <div class=\"container\">\n    <!-- Header Section -->\n    <div class=\"header-section\">\n      <div class=\"header-content\">\n        <h1>Register New Complaint</h1>\n        <p>Follow the steps below to register your complaint</p>\n      </div>\n    </div>\n\n    <!-- Progress Indicator -->\n    <div class=\"progress-section\">\n      <div class=\"progress-steps\">\n        <div class=\"step\" [class.active]=\"true\" [class.completed]=\"isStepCompleted(1)\">\n          <div class=\"step-number\">1</div>\n          <div class=\"step-label\">Complaint Type</div>\n        </div>\n        <div class=\"step-connector\" [class.completed]=\"isStepCompleted(1)\"></div>\n        <div class=\"step\" [class.active]=\"isStepCompleted(1)\" [class.completed]=\"isStepCompleted(2)\">\n          <div class=\"step-number\">2</div>\n          <div class=\"step-label\">Complaint Description</div>\n        </div>\n        <div class=\"step-connector\" [class.completed]=\"isStepCompleted(2)\"></div>\n        <div class=\"step\" [class.active]=\"isStepCompleted(2)\" [class.completed]=\"isStepCompleted(3)\">\n          <div class=\"step-number\">3</div>\n          <div class=\"step-label\">Invoice Selection</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Step 1: Complaint Type Selection -->\n    <div class=\"step-content\" *ngIf=\"!isStepCompleted(1)\">\n      <form [formGroup]=\"complaintTypeForm\">\n        <div class=\"form-section\">\n          <h3>Select Complaint Type</h3>\n          <p class=\"section-description\">Choose the category that best describes your complaint</p>\n\n          <div class=\"complaint-types-grid\">\n            <div class=\"complaint-type-card\"\n                 *ngFor=\"let type of complaintTypes\"\n                 [class.selected]=\"complaintTypeForm.get('selectedType')?.value === type.value\"\n                 (click)=\"selectComplaintType(type)\">\n              <div class=\"card-icon\">\n                <ion-icon [name]=\"type.icon\"></ion-icon>\n              </div>\n              <div class=\"card-content\">\n                <h4>{{ type.label }}</h4>\n                <p>{{ type.description }}</p>\n              </div>\n              <div class=\"card-radio\">\n                <ion-radio [value]=\"type.value\" formControlName=\"selectedType\"></ion-radio>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"step-actions\" *ngIf=\"complaintTypeForm.get('selectedType')?.value\">\n            <button mat-raised-button color=\"primary\" (click)=\"isStepCompleted(1) ? null : null\">\n              Continue to Invoice Search\n              <mat-icon>arrow_forward</mat-icon>\n            </button>\n          </div>\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 2: Complaint Description -->\n    <div class=\"step-content\" *ngIf=\"isStepCompleted(1) && !isStepCompleted(2)\">\n      <form [formGroup]=\"complaintDetailsForm\">\n        <div class=\"form-section\">\n          <h3>Complaint Description</h3>\n          <p class=\"section-description\">Provide detailed information about your complaint</p>\n\n          <!-- Selected Complaint Type Display -->\n          <div class=\"selected-type-display\" *ngIf=\"getSelectedComplaintType()\">\n            <h4>Selected Complaint Type</h4>\n            <div class=\"type-display-card\">\n              <ion-icon [name]=\"getSelectedComplaintType()?.icon\"></ion-icon>\n              <div>\n                <strong>{{ getSelectedComplaintType()?.label }}</strong>\n                <p>{{ getSelectedComplaintType()?.description }}</p>\n              </div>\n            </div>\n          </div>\n\n          <!-- Complaint Description Form -->\n          <div class=\"complaint-form-section\">\n            <h4>\n              <mat-icon>edit</mat-icon>\n              Complaint Information\n            </h4>\n\n            <div class=\"editable-form-card\">\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Contact Person Name *</mat-label>\n                  <input matInput formControlName=\"contactPersonName\" placeholder=\"Enter contact person name\">\n                  <mat-icon matSuffix>person</mat-icon>\n                  <mat-error *ngIf=\"complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched\">\n                    {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Contact Number *</mat-label>\n                  <input matInput formControlName=\"contactNumber\" placeholder=\"Enter 10-digit contact number\" type=\"tel\">\n                  <mat-icon matSuffix>phone</mat-icon>\n                  <mat-error *ngIf=\"complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched\">\n                    {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Complaint Description *</mat-label>\n                <textarea matInput formControlName=\"complaintDescription\" rows=\"4\" placeholder=\"Describe your complaint in detail (minimum 10 characters)\"></textarea>\n                <mat-icon matSuffix>description</mat-icon>\n                <mat-hint>Please provide a detailed description of the issue</mat-hint>\n                <mat-error *ngIf=\"complaintDetailsForm.get('complaintDescription')?.invalid && complaintDetailsForm.get('complaintDescription')?.touched\">\n                  {{ getErrorMessage(complaintDetailsForm, 'complaintDescription') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Additional Comments (Optional)</mat-label>\n                <textarea matInput formControlName=\"comments\" rows=\"3\" placeholder=\"Any additional comments or information\"></textarea>\n                <mat-icon matSuffix>comment</mat-icon>\n                <mat-hint>Optional: Add any additional information that might be helpful</mat-hint>\n              </mat-form-field>\n\n              <!-- Complaint Letters Checkbox -->\n              <div class=\"complaint-letters-section\">\n                <mat-checkbox formControlName=\"hasComplaintLetters\" color=\"primary\">\n                  <strong>Do you have complaint letters to attach?</strong>\n                </mat-checkbox>\n                <p class=\"checkbox-hint\">Check this box if you have supporting documents, photos, or letters related to your complaint</p>\n              </div>\n\n              <!-- File Upload Section -->\n              <div class=\"file-upload-section\" *ngIf=\"complaintDetailsForm.get('hasComplaintLetters')?.value\">\n                <h5>\n                  <mat-icon>attach_file</mat-icon>\n                  Upload Supporting Documents\n                </h5>\n                <div class=\"upload-area\">\n                  <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" multiple accept=\".pdf,.jpg,.jpeg,.png,.doc,.docx\" style=\"display: none;\">\n\n                  <button mat-stroked-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                    <mat-icon>cloud_upload</mat-icon>\n                    Choose Files\n                  </button>\n                  <p class=\"upload-hint\">\n                    <mat-icon>info</mat-icon>\n                    Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each)\n                  </p>\n                </div>\n\n                <div class=\"selected-files\" *ngIf=\"selectedFiles.length > 0\">\n                  <h6>Selected Files ({{ selectedFiles.length }}):</h6>\n                  <div class=\"file-list\">\n                    <div class=\"file-item\" *ngFor=\"let file of selectedFiles; let i = index\">\n                      <mat-icon class=\"file-icon\">description</mat-icon>\n                      <div class=\"file-info\">\n                        <span class=\"file-name\">{{ file.name }}</span>\n                        <span class=\"file-size\">({{ (file.size / 1024 / 1024).toFixed(2) }} MB)</span>\n                      </div>\n                      <button mat-icon-button (click)=\"removeFile(i)\" class=\"remove-file\" color=\"warn\">\n                        <mat-icon>delete</mat-icon>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"step-actions\">\n            <button mat-raised-button color=\"primary\" [disabled]=\"!complaintDetailsForm.valid\">\n              Continue to Invoice Selection\n              <mat-icon>arrow_forward</mat-icon>\n            </button>\n          </div>\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 3: Invoice Selection -->\n    <div class=\"step-content\" *ngIf=\"isStepCompleted(2) && !isStepCompleted(3)\">\n      <div class=\"form-section\">\n        <h3>Invoice Selection</h3>\n        <p class=\"section-description\">Search and select the invoice related to your complaint</p>\n\n        <!-- Complaint Summary Display -->\n        <div class=\"complaint-summary-section\">\n          <h4>\n            <mat-icon>assignment</mat-icon>\n            Your Complaint Summary\n          </h4>\n          <div class=\"summary-card\">\n            <div class=\"summary-item\" *ngIf=\"getSelectedComplaintType()\">\n              <label>Complaint Type:</label>\n              <div class=\"summary-value\">\n                <ion-icon [name]=\"getSelectedComplaintType()?.icon\"></ion-icon>\n                <span>{{ getSelectedComplaintType()?.label }}</span>\n              </div>\n            </div>\n            <div class=\"summary-item\" *ngIf=\"complaintDetailsForm.get('contactPersonName')?.value\">\n              <label>Contact Person:</label>\n              <div class=\"summary-value\">\n                <mat-icon>person</mat-icon>\n                <span>{{ complaintDetailsForm.get('contactPersonName')?.value }}</span>\n              </div>\n            </div>\n            <div class=\"summary-item\" *ngIf=\"complaintDetailsForm.get('contactNumber')?.value\">\n              <label>Contact Number:</label>\n              <div class=\"summary-value\">\n                <mat-icon>phone</mat-icon>\n                <span>{{ complaintDetailsForm.get('contactNumber')?.value }}</span>\n              </div>\n            </div>\n            <div class=\"summary-item\" *ngIf=\"complaintDetailsForm.get('complaintDescription')?.value\">\n              <label>Description:</label>\n              <div class=\"summary-value description\">\n                <mat-icon>description</mat-icon>\n                <span>{{ complaintDetailsForm.get('complaintDescription')?.value }}</span>\n              </div>\n            </div>\n            <div class=\"summary-item\" *ngIf=\"selectedFiles.length > 0\">\n              <label>Attachments:</label>\n              <div class=\"summary-value\">\n                <mat-icon>attach_file</mat-icon>\n                <span>{{ selectedFiles.length }} file(s) attached</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Invoice Search Section -->\n        <form [formGroup]=\"invoiceSearchForm\">\n          <div class=\"search-section\">\n            <h4>\n              <mat-icon>search</mat-icon>\n              Search for Related Invoice\n            </h4>\n            <mat-form-field appearance=\"outline\" class=\"search-field\">\n              <mat-label>Search Invoice</mat-label>\n              <input matInput formControlName=\"searchTerm\" placeholder=\"Enter invoice number, customer name, or leave empty to see all\" (input)=\"onInvoiceSearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n              <mat-error *ngIf=\"invoiceSearchForm.get('searchTerm')?.invalid && invoiceSearchForm.get('searchTerm')?.touched\">\n                {{ getErrorMessage(invoiceSearchForm, 'searchTerm') }}\n              </mat-error>\n            </mat-form-field>\n\n            <div class=\"search-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length > 0\">\n              <h5>\n                <span *ngIf=\"invoiceSearchForm.get('searchTerm')?.value && invoiceSearchForm.get('searchTerm')?.value.trim() !== ''\">\n                  Search Results ({{ invoiceSearchResults.length }} found)\n                </span>\n                <span *ngIf=\"!invoiceSearchForm.get('searchTerm')?.value || invoiceSearchForm.get('searchTerm')?.value.trim() === ''\">\n                  All Available Invoices ({{ invoiceSearchResults.length }} total)\n                </span>\n              </h5>\n              <div class=\"invoice-list\">\n                <div class=\"invoice-item\" *ngFor=\"let invoice of invoiceSearchResults\" (click)=\"selectInvoice(invoice)\">\n                  <div class=\"invoice-header\">\n                    <strong>{{ invoice.invoiceNumber }}</strong>\n                    <span class=\"invoice-date\">{{ invoice.invoiceDate | date:'dd/MM/yyyy' }}</span>\n                  </div>\n                  <div class=\"invoice-customer\">{{ invoice.customerName }}</div>\n                  <div class=\"invoice-zone\">{{ invoice.zone }} - {{ invoice.operatingUnit }}</div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"no-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length === 0\">\n              <p>No invoices found matching your search criteria.</p>\n              <p class=\"search-hint\">Try searching with different keywords or clear the search to see all invoices.</p>\n            </div>\n          </div>\n        </form>\n\n        <!-- Selected Invoice Details (Read-Only) -->\n        <div class=\"invoice-details-section\" *ngIf=\"selectedInvoice\">\n          <h4>\n            <mat-icon>receipt</mat-icon>\n            Selected Invoice Details (Read-Only)\n          </h4>\n          <div class=\"readonly-invoice-card\">\n            <div class=\"invoice-details-grid\">\n              <div class=\"detail-group\">\n                <div class=\"detail-item\">\n                  <label>Invoice Number</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>confirmation_number</mat-icon>\n                    <span>{{ selectedInvoice.invoiceNumber }}</span>\n                  </div>\n                </div>\n                <div class=\"detail-item\">\n                  <label>Invoice Date</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>calendar_today</mat-icon>\n                    <span>{{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"detail-group\">\n                <div class=\"detail-item\">\n                  <label>Customer Name</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>business</mat-icon>\n                    <span>{{ selectedInvoice.customerName }}</span>\n                  </div>\n                </div>\n                <div class=\"detail-item\">\n                  <label>Customer Address</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>location_on</mat-icon>\n                    <span>{{ selectedInvoice.customerAddress }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"detail-group\">\n                <div class=\"detail-item\">\n                  <label>Zone</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>map</mat-icon>\n                    <span>{{ selectedInvoice.zone }}</span>\n                  </div>\n                </div>\n                <div class=\"detail-item\">\n                  <label>Operating Unit</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>domain</mat-icon>\n                    <span>{{ selectedInvoice.operatingUnit }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"detail-group\">\n                <div class=\"detail-item\">\n                  <label>Organization</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>corporate_fare</mat-icon>\n                    <span>{{ selectedInvoice.organization }}</span>\n                  </div>\n                </div>\n                <div class=\"detail-item\">\n                  <label>Bill To Location</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>account_balance</mat-icon>\n                    <span>{{ selectedInvoice.billToLocation }}</span>\n                  </div>\n                </div>\n              </div>\n\n              <div class=\"detail-group\">\n                <div class=\"detail-item full-width\">\n                  <label>Ship To Location</label>\n                  <div class=\"readonly-field\">\n                    <mat-icon>local_shipping</mat-icon>\n                    <span>{{ selectedInvoice.shipToLocation }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div class=\"invoice-actions\">\n              <button mat-button color=\"warn\" (click)=\"clearInvoiceSelection()\">\n                <mat-icon>clear</mat-icon>\n                Change Invoice\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"step-actions\">\n          <button mat-button (click)=\"goBackToComplaintDetails()\" class=\"back-button\">\n            <mat-icon>arrow_back</mat-icon>\n            Back to Complaint Details\n          </button>\n          <button mat-raised-button color=\"primary\" (click)=\"onSubmitComplaint()\" [disabled]=\"isLoading || !selectedInvoice\" class=\"submit-button\">\n            <mat-icon *ngIf=\"!isLoading\">send</mat-icon>\n            <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n            {{ isLoading ? 'Submit Complaint' : 'Submit Complaint' }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</ion-content>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;ICgDvDC,EAAA,CAAAC,cAAA,cAGyC;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,OAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,mBAAA,CAAAP,OAAA,CAAyB;IAAA,EAAC;IACtCJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAY,SAAA,mBAAwC;IAC1CZ,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAc,MAAA,GAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IACzBb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,GAAsB;IAC3Bd,EAD2B,CAAAa,YAAA,EAAI,EACzB;IACNb,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAY,SAAA,oBAA2E;IAE/EZ,EADE,CAAAa,YAAA,EAAM,EACF;;;;;;IAZDb,EAAA,CAAAe,WAAA,eAAAC,OAAA,GAAAR,MAAA,CAAAS,iBAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,KAAA,MAAAf,OAAA,CAAAe,KAAA,CAA8E;IAGrEnB,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAqB,UAAA,SAAAjB,OAAA,CAAAkB,IAAA,CAAkB;IAGxBtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAuB,iBAAA,CAAAnB,OAAA,CAAAoB,KAAA,CAAgB;IACjBxB,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAuB,iBAAA,CAAAnB,OAAA,CAAAqB,WAAA,CAAsB;IAGdzB,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAqB,UAAA,UAAAjB,OAAA,CAAAe,KAAA,CAAoB;;;;;;IAMnCnB,EADF,CAAAC,cAAA,cAA+E,iBACQ;IAA3CD,EAAA,CAAAE,UAAA,mBAAAwB,2DAAA;MAAA1B,EAAA,CAAAK,aAAA,CAAAsB,GAAA;MAAA,MAAAnB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoB,eAAA,CAAgB,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;IAAA,EAAC;IAClF5B,EAAA,CAAAc,MAAA,mCACA;IAAAd,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,oBAAa;IAE3Bd,EAF2B,CAAAa,YAAA,EAAW,EAC3B,EACL;;;;;IA1BNb,EAHN,CAAAC,cAAA,cAAsD,eACd,cACV,SACpB;IAAAD,EAAA,CAAAc,MAAA,4BAAqB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAC9Bb,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAc,MAAA,6DAAsD;IAAAd,EAAA,CAAAa,YAAA,EAAI;IAEzFb,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAA6B,UAAA,IAAAC,kCAAA,mBAGyC;IAY3C9B,EAAA,CAAAa,YAAA,EAAM;IAENb,EAAA,CAAA6B,UAAA,IAAAE,kCAAA,kBAA+E;IAQrF/B,EAFI,CAAAa,YAAA,EAAM,EACD,EACH;;;;;IA/BEb,EAAA,CAAAoB,SAAA,EAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAAS,iBAAA,CAA+B;IAOTjB,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAAwB,cAAA,CAAiB;IAgBdhC,EAAA,CAAAoB,SAAA,EAAkD;IAAlDpB,EAAA,CAAAqB,UAAA,UAAAL,OAAA,GAAAR,MAAA,CAAAS,iBAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,KAAA,CAAkD;;;;;IAmB3EnB,EADF,CAAAC,cAAA,cAAsE,SAChE;IAAAD,EAAA,CAAAc,MAAA,8BAAuB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAChCb,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAY,SAAA,mBAA+D;IAE7DZ,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAc,MAAA,GAAuC;IAAAd,EAAA,CAAAa,YAAA,EAAS;IACxDb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,GAA6C;IAGtDd,EAHsD,CAAAa,YAAA,EAAI,EAChD,EACF,EACF;;;;;;;IANQb,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAAqB,UAAA,UAAAY,OAAA,GAAAzB,MAAA,CAAA0B,wBAAA,qBAAAD,OAAA,CAAAX,IAAA,CAAyC;IAEzCtB,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAuB,iBAAA,EAAAP,OAAA,GAAAR,MAAA,CAAA0B,wBAAA,qBAAAlB,OAAA,CAAAQ,KAAA,CAAuC;IAC5CxB,EAAA,CAAAoB,SAAA,GAA6C;IAA7CpB,EAAA,CAAAuB,iBAAA,EAAAY,OAAA,GAAA3B,MAAA,CAAA0B,wBAAA,qBAAAC,OAAA,CAAAV,WAAA,CAA6C;;;;;IAkB9CzB,EAAA,CAAAC,cAAA,gBAAoI;IAClID,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IADVb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAoC,kBAAA,MAAA5B,MAAA,CAAA6B,eAAA,CAAA7B,MAAA,CAAA8B,oBAAA,4BACF;;;;;IAOAtC,EAAA,CAAAC,cAAA,gBAA4H;IAC1HD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IADVb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAoC,kBAAA,MAAA5B,MAAA,CAAA6B,eAAA,CAAA7B,MAAA,CAAA8B,oBAAA,wBACF;;;;;IASFtC,EAAA,CAAAC,cAAA,gBAA0I;IACxID,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IADVb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAoC,kBAAA,MAAA5B,MAAA,CAAA6B,eAAA,CAAA7B,MAAA,CAAA8B,oBAAA,+BACF;;;;;;IAyCMtC,EADF,CAAAC,cAAA,cAAyE,mBAC3C;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAEhDb,EADF,CAAAC,cAAA,cAAuB,eACG;IAAAD,EAAA,CAAAc,MAAA,GAAe;IAAAd,EAAA,CAAAa,YAAA,EAAO;IAC9Cb,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAc,MAAA,GAA+C;IACzEd,EADyE,CAAAa,YAAA,EAAO,EAC1E;IACNb,EAAA,CAAAC,cAAA,iBAAiF;IAAzDD,EAAA,CAAAE,UAAA,mBAAAqC,yEAAA;MAAA,MAAAC,IAAA,GAAAxC,EAAA,CAAAK,aAAA,CAAAoC,GAAA,EAAAC,KAAA;MAAA,MAAAlC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmC,UAAA,CAAAH,IAAA,CAAa;IAAA,EAAC;IAC7CxC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAEpBd,EAFoB,CAAAa,YAAA,EAAW,EACpB,EACL;;;;IANsBb,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAAuB,iBAAA,CAAAqB,OAAA,CAAAC,IAAA,CAAe;IACf7C,EAAA,CAAAoB,SAAA,GAA+C;IAA/CpB,EAAA,CAAAoC,kBAAA,OAAAQ,OAAA,CAAAE,IAAA,gBAAAC,OAAA,YAA+C;;;;;IAN7E/C,EADF,CAAAC,cAAA,cAA6D,SACvD;IAAAD,EAAA,CAAAc,MAAA,GAA4C;IAAAd,EAAA,CAAAa,YAAA,EAAK;IACrDb,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAA6B,UAAA,IAAAmB,gDAAA,mBAAyE;IAW7EhD,EADE,CAAAa,YAAA,EAAM,EACF;;;;IAbAb,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAAoC,kBAAA,qBAAA5B,MAAA,CAAAyC,aAAA,CAAAC,MAAA,OAA4C;IAENlD,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAAyC,aAAA,CAAkB;;;;;;IAnB5DjD,EAFJ,CAAAC,cAAA,cAAgG,SAC1F,eACQ;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAChCb,EAAA,CAAAc,MAAA,oCACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAEHb,EADF,CAAAC,cAAA,cAAyB,mBACkH;IAA3GD,EAAA,CAAAE,UAAA,oBAAAiD,4DAAAC,MAAA;MAAApD,EAAA,CAAAK,aAAA,CAAAgD,GAAA;MAAA,MAAA7C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAA8C,cAAA,CAAAF,MAAA,CAAsB;IAAA,EAAC;IAA/DpD,EAAA,CAAAa,YAAA,EAAyI;IAEzIb,EAAA,CAAAC,cAAA,iBAA6F;IAAlDD,EAAA,CAAAE,UAAA,mBAAAqD,4DAAA;MAAAvD,EAAA,CAAAK,aAAA,CAAAgD,GAAA;MAAA,MAAAG,YAAA,GAAAxD,EAAA,CAAAyD,WAAA;MAAA,OAAAzD,EAAA,CAAAU,WAAA,CAAS8C,YAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IACpE1D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACjCb,EAAA,CAAAc,MAAA,sBACF;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAEPb,EADF,CAAAC,cAAA,aAAuB,gBACX;IAAAD,EAAA,CAAAc,MAAA,YAAI;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACzBb,EAAA,CAAAc,MAAA,oEACF;IACFd,EADE,CAAAa,YAAA,EAAI,EACA;IAENb,EAAA,CAAA6B,UAAA,KAAA8B,0CAAA,kBAA6D;IAe/D3D,EAAA,CAAAa,YAAA,EAAM;;;;IAfyBb,EAAA,CAAAoB,SAAA,IAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAyC,aAAA,CAAAC,MAAA,KAA8B;;;;;IAvFjElD,EAHN,CAAAC,cAAA,cAA4E,eACjC,cACb,SACpB;IAAAD,EAAA,CAAAc,MAAA,4BAAqB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAC9Bb,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAc,MAAA,wDAAiD;IAAAd,EAAA,CAAAa,YAAA,EAAI;IAGpFb,EAAA,CAAA6B,UAAA,IAAA+B,kCAAA,mBAAsE;IAclE5D,EAFJ,CAAAC,cAAA,cAAoC,SAC9B,gBACQ;IAAAD,EAAA,CAAAc,MAAA,YAAI;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACzBb,EAAA,CAAAc,MAAA,+BACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAKCb,EAHN,CAAAC,cAAA,eAAgC,eACR,0BACoC,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,6BAAqB;IAAAd,EAAA,CAAAa,YAAA,EAAY;IAC5Cb,EAAA,CAAAY,SAAA,iBAA4F;IAC5FZ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACrCb,EAAA,CAAA6B,UAAA,KAAAgC,yCAAA,wBAAoI;IAGtI7D,EAAA,CAAAa,YAAA,EAAiB;IAGfb,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,wBAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAY;IACvCb,EAAA,CAAAY,SAAA,iBAAuG;IACvGZ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACpCb,EAAA,CAAA6B,UAAA,KAAAiC,yCAAA,wBAA4H;IAIhI9D,EADE,CAAAa,YAAA,EAAiB,EACb;IAGJb,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,+BAAuB;IAAAd,EAAA,CAAAa,YAAA,EAAY;IAC9Cb,EAAA,CAAAY,SAAA,oBAAsJ;IACtJZ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,mBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC1Cb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,0DAAkD;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACvEb,EAAA,CAAA6B,UAAA,KAAAkC,yCAAA,wBAA0I;IAG5I/D,EAAA,CAAAa,YAAA,EAAiB;IAGfb,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,sCAA8B;IAAAd,EAAA,CAAAa,YAAA,EAAY;IACrDb,EAAA,CAAAY,SAAA,oBAAuH;IACvHZ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,eAAO;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACtCb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,sEAA8D;IAC1Ed,EAD0E,CAAAa,YAAA,EAAW,EACpE;IAKbb,EAFJ,CAAAC,cAAA,eAAuC,wBAC+B,cAC1D;IAAAD,EAAA,CAAAc,MAAA,gDAAwC;IAClDd,EADkD,CAAAa,YAAA,EAAS,EAC5C;IACfb,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAc,MAAA,qGAA6F;IACxHd,EADwH,CAAAa,YAAA,EAAI,EACtH;IAGNb,EAAA,CAAA6B,UAAA,KAAAmC,mCAAA,mBAAgG;IAmCpGhE,EADE,CAAAa,YAAA,EAAM,EACF;IAGJb,EADF,CAAAC,cAAA,eAA0B,kBAC2D;IACjFD,EAAA,CAAAc,MAAA,uCACA;IAAAd,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,qBAAa;IAKjCd,EALiC,CAAAa,YAAA,EAAW,EAC3B,EACL,EACF,EACD,EACH;;;;;;;;IApHEb,EAAA,CAAAoB,SAAA,EAAkC;IAAlCpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAA8B,oBAAA,CAAkC;IAMAtC,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAA0B,wBAAA,GAAgC;IAwBhDlC,EAAA,CAAAoB,SAAA,IAAsH;IAAtHpB,EAAA,CAAAqB,UAAA,WAAAL,OAAA,GAAAR,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,wCAAAF,OAAA,CAAAiD,OAAA,OAAAjD,OAAA,GAAAR,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,wCAAAF,OAAA,CAAAkD,OAAA,EAAsH;IAStHlE,EAAA,CAAAoB,SAAA,GAA8G;IAA9GpB,EAAA,CAAAqB,UAAA,WAAAc,OAAA,GAAA3B,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,oCAAAiB,OAAA,CAAA8B,OAAA,OAAA9B,OAAA,GAAA3B,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,oCAAAiB,OAAA,CAAA+B,OAAA,EAA8G;IAWhHlE,EAAA,CAAAoB,SAAA,GAA4H;IAA5HpB,EAAA,CAAAqB,UAAA,WAAA8C,OAAA,GAAA3D,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,2CAAAiD,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA3D,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,2CAAAiD,OAAA,CAAAD,OAAA,EAA4H;IAqBxGlE,EAAA,CAAAoB,SAAA,IAA4D;IAA5DpB,EAAA,CAAAqB,UAAA,UAAA+C,OAAA,GAAA5D,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,0CAAAkD,OAAA,CAAAjD,KAAA,CAA4D;IAsCtDnB,EAAA,CAAAoB,SAAA,GAAwC;IAAxCpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAA8B,oBAAA,CAAA+B,KAAA,CAAwC;;;;;IAuBhFrE,EADF,CAAAC,cAAA,cAA6D,YACpD;IAAAD,EAAA,CAAAc,MAAA,sBAAe;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC9Bb,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAAY,SAAA,mBAA+D;IAC/DZ,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,GAAuC;IAEjDd,EAFiD,CAAAa,YAAA,EAAO,EAChD,EACF;;;;;;IAHQb,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAAqB,UAAA,UAAAY,OAAA,GAAAzB,MAAA,CAAA0B,wBAAA,qBAAAD,OAAA,CAAAX,IAAA,CAAyC;IAC7CtB,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAuB,iBAAA,EAAAP,OAAA,GAAAR,MAAA,CAAA0B,wBAAA,qBAAAlB,OAAA,CAAAQ,KAAA,CAAuC;;;;;IAI/CxB,EADF,CAAAC,cAAA,cAAuF,YAC9E;IAAAD,EAAA,CAAAc,MAAA,sBAAe;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE5Bb,EADF,CAAAC,cAAA,cAA2B,eACf;IAAAD,EAAA,CAAAc,MAAA,aAAM;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC3Bb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,GAA0D;IAEpEd,EAFoE,CAAAa,YAAA,EAAO,EACnE,EACF;;;;;IAFIb,EAAA,CAAAoB,SAAA,GAA0D;IAA1DpB,EAAA,CAAAuB,iBAAA,EAAAU,OAAA,GAAAzB,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,wCAAAe,OAAA,CAAAd,KAAA,CAA0D;;;;;IAIlEnB,EADF,CAAAC,cAAA,cAAmF,YAC1E;IAAAD,EAAA,CAAAc,MAAA,sBAAe;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE5Bb,EADF,CAAAC,cAAA,cAA2B,eACf;IAAAD,EAAA,CAAAc,MAAA,YAAK;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC1Bb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,GAAsD;IAEhEd,EAFgE,CAAAa,YAAA,EAAO,EAC/D,EACF;;;;;IAFIb,EAAA,CAAAoB,SAAA,GAAsD;IAAtDpB,EAAA,CAAAuB,iBAAA,EAAAU,OAAA,GAAAzB,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,oCAAAe,OAAA,CAAAd,KAAA,CAAsD;;;;;IAI9DnB,EADF,CAAAC,cAAA,cAA0F,YACjF;IAAAD,EAAA,CAAAc,MAAA,mBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAEzBb,EADF,CAAAC,cAAA,cAAuC,eAC3B;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAChCb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,GAA6D;IAEvEd,EAFuE,CAAAa,YAAA,EAAO,EACtE,EACF;;;;;IAFIb,EAAA,CAAAoB,SAAA,GAA6D;IAA7DpB,EAAA,CAAAuB,iBAAA,EAAAU,OAAA,GAAAzB,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,2CAAAe,OAAA,CAAAd,KAAA,CAA6D;;;;;IAIrEnB,EADF,CAAAC,cAAA,cAA2D,YAClD;IAAAD,EAAA,CAAAc,MAAA,mBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAEzBb,EADF,CAAAC,cAAA,cAA2B,eACf;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAChCb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,GAA2C;IAErDd,EAFqD,CAAAa,YAAA,EAAO,EACpD,EACF;;;;IAFIb,EAAA,CAAAoB,SAAA,GAA2C;IAA3CpB,EAAA,CAAAoC,kBAAA,KAAA5B,MAAA,CAAAyC,aAAA,CAAAC,MAAA,sBAA2C;;;;;IAiBnDlD,EAAA,CAAAC,cAAA,gBAAgH;IAC9GD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IADVb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAoC,kBAAA,MAAA5B,MAAA,CAAA6B,eAAA,CAAA7B,MAAA,CAAA8D,iBAAA,qBACF;;;;;IAKEtE,EAAA,CAAAC,cAAA,WAAqH;IACnHD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAO;;;;IADLb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAoC,kBAAA,sBAAA5B,MAAA,CAAA+D,oBAAA,CAAArB,MAAA,aACF;;;;;IACAlD,EAAA,CAAAC,cAAA,WAAsH;IACpHD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAO;;;;IADLb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAoC,kBAAA,8BAAA5B,MAAA,CAAA+D,oBAAA,CAAArB,MAAA,aACF;;;;;;IAGAlD,EAAA,CAAAC,cAAA,cAAwG;IAAjCD,EAAA,CAAAE,UAAA,mBAAAsE,+DAAA;MAAA,MAAAC,WAAA,GAAAzE,EAAA,CAAAK,aAAA,CAAAqE,IAAA,EAAAnE,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAmE,aAAA,CAAAF,WAAA,CAAsB;IAAA,EAAC;IAEnGzE,EADF,CAAAC,cAAA,cAA4B,aAClB;IAAAD,EAAA,CAAAc,MAAA,GAA2B;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAC5Cb,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAc,MAAA,GAA6C;;IAC1Ed,EAD0E,CAAAa,YAAA,EAAO,EAC3E;IACNb,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAc,MAAA,GAA0B;IAAAd,EAAA,CAAAa,YAAA,EAAM;IAC9Db,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAc,MAAA,IAAgD;IAC5Ed,EAD4E,CAAAa,YAAA,EAAM,EAC5E;;;;IALMb,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAAuB,iBAAA,CAAAkD,WAAA,CAAAG,aAAA,CAA2B;IACR5E,EAAA,CAAAoB,SAAA,GAA6C;IAA7CpB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAA6E,WAAA,OAAAJ,WAAA,CAAAK,WAAA,gBAA6C;IAE5C9E,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAuB,iBAAA,CAAAkD,WAAA,CAAAM,YAAA,CAA0B;IAC9B/E,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAgF,kBAAA,KAAAP,WAAA,CAAAQ,IAAA,SAAAR,WAAA,CAAAS,aAAA,KAAgD;;;;;IAf9ElF,EADF,CAAAC,cAAA,cAA0F,SACpF;IAIFD,EAHA,CAAA6B,UAAA,IAAAsD,0CAAA,mBAAqH,IAAAC,0CAAA,mBAGC;IAGxHpF,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA6B,UAAA,IAAAwD,yCAAA,mBAAwG;IAS5GrF,EADE,CAAAa,YAAA,EAAM,EACF;;;;;;IAjBKb,EAAA,CAAAoB,SAAA,GAA4G;IAA5GpB,EAAA,CAAAqB,UAAA,WAAAY,OAAA,GAAAzB,MAAA,CAAA8D,iBAAA,CAAApD,GAAA,iCAAAe,OAAA,CAAAd,KAAA,OAAAc,OAAA,GAAAzB,MAAA,CAAA8D,iBAAA,CAAApD,GAAA,iCAAAe,OAAA,CAAAd,KAAA,CAAAmE,IAAA,WAA4G;IAG5GtF,EAAA,CAAAoB,SAAA,EAA6G;IAA7GpB,EAAA,CAAAqB,UAAA,YAAAL,OAAA,GAAAR,MAAA,CAAA8D,iBAAA,CAAApD,GAAA,iCAAAF,OAAA,CAAAG,KAAA,OAAAH,OAAA,GAAAR,MAAA,CAAA8D,iBAAA,CAAApD,GAAA,iCAAAF,OAAA,CAAAG,KAAA,CAAAmE,IAAA,WAA6G;IAKtEtF,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAA+D,oBAAA,CAAuB;;;;;IAYvEvE,EADF,CAAAC,cAAA,cAAwF,QACnF;IAAAD,EAAA,CAAAc,MAAA,uDAAgD;IAAAd,EAAA,CAAAa,YAAA,EAAI;IACvDb,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAc,MAAA,qFAA8E;IACvGd,EADuG,CAAAa,YAAA,EAAI,EACrG;;;;;;IAONb,EAFJ,CAAAC,cAAA,cAA6D,SACvD,eACQ;IAAAD,EAAA,CAAAc,MAAA,cAAO;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC5Bb,EAAA,CAAAc,MAAA,6CACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAKGb,EAJR,CAAAC,cAAA,cAAmC,cACC,cACN,cACC,YAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE3Bb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,2BAAmB;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACxCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAmC;IAE7Cd,EAF6C,CAAAa,YAAA,EAAO,EAC5C,EACF;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAEzBb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACnCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAqD;;IAGjEd,EAHiE,CAAAa,YAAA,EAAO,EAC9D,EACF,EACF;IAIFb,EAFJ,CAAAC,cAAA,eAA0B,eACC,aAChB;IAAAD,EAAA,CAAAc,MAAA,qBAAa;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE1Bb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,gBAAQ;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC7Bb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAkC;IAE5Cd,EAF4C,CAAAa,YAAA,EAAO,EAC3C,EACF;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,wBAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE7Bb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,mBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAChCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAqC;IAGjDd,EAHiD,CAAAa,YAAA,EAAO,EAC9C,EACF,EACF;IAIFb,EAFJ,CAAAC,cAAA,eAA0B,eACC,aAChB;IAAAD,EAAA,CAAAc,MAAA,YAAI;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAEjBb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,WAAG;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACxBb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAA0B;IAEpCd,EAFoC,CAAAa,YAAA,EAAO,EACnC,EACF;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE3Bb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC3Bb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAmC;IAG/Cd,EAH+C,CAAAa,YAAA,EAAO,EAC5C,EACF,EACF;IAIFb,EAFJ,CAAAC,cAAA,eAA0B,eACC,aAChB;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAEzBb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACnCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAkC;IAE5Cd,EAF4C,CAAAa,YAAA,EAAO,EAC3C,EACF;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,wBAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE7Bb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,uBAAe;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACpCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAoC;IAGhDd,EAHgD,CAAAa,YAAA,EAAO,EAC7C,EACF,EACF;IAIFb,EAFJ,CAAAC,cAAA,eAA0B,eACY,aAC3B;IAAAD,EAAA,CAAAc,MAAA,wBAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAE7Bb,EADF,CAAAC,cAAA,eAA4B,gBAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACnCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAoC;IAIlDd,EAJkD,CAAAa,YAAA,EAAO,EAC7C,EACF,EACF,EACF;IAEJb,EADF,CAAAC,cAAA,eAA6B,kBACuC;IAAlCD,EAAA,CAAAE,UAAA,mBAAAqF,6DAAA;MAAAvF,EAAA,CAAAK,aAAA,CAAAmF,IAAA;MAAA,MAAAhF,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiF,qBAAA,EAAuB;IAAA,EAAC;IAC/DzF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC1Bb,EAAA,CAAAc,MAAA,wBACF;IAGNd,EAHM,CAAAa,YAAA,EAAS,EACL,EACF,EACF;;;;IAhFYb,EAAA,CAAAoB,SAAA,IAAmC;IAAnCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAd,aAAA,CAAmC;IAOnC5E,EAAA,CAAAoB,SAAA,GAAqD;IAArDpB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAA6E,WAAA,QAAArE,MAAA,CAAAkF,eAAA,CAAAZ,WAAA,gBAAqD;IAUrD9E,EAAA,CAAAoB,SAAA,IAAkC;IAAlCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAX,YAAA,CAAkC;IAOlC/E,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAC,eAAA,CAAqC;IAUrC3F,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAT,IAAA,CAA0B;IAO1BjF,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAR,aAAA,CAAmC;IAUnClF,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAE,YAAA,CAAkC;IAOlC5F,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAG,cAAA,CAAoC;IAUpC7F,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAkF,eAAA,CAAAI,cAAA,CAAoC;;;;;IAoBlD9F,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAc,MAAA,WAAI;IAAAd,EAAA,CAAAa,YAAA,EAAW;;;;;IAC5Cb,EAAA,CAAAY,SAAA,uBAA2D;;;;;;IAlM/DZ,EAFJ,CAAAC,cAAA,cAA4E,cAChD,SACpB;IAAAD,EAAA,CAAAc,MAAA,wBAAiB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAC1Bb,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAc,MAAA,8DAAuD;IAAAd,EAAA,CAAAa,YAAA,EAAI;IAKtFb,EAFJ,CAAAC,cAAA,cAAuC,SACjC,eACQ;IAAAD,EAAA,CAAAc,MAAA,iBAAU;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC/Bb,EAAA,CAAAc,MAAA,gCACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,eAA0B;IA6BxBD,EA5BA,CAAA6B,UAAA,KAAAkE,mCAAA,kBAA6D,KAAAC,mCAAA,kBAO0B,KAAAC,mCAAA,kBAOJ,KAAAC,mCAAA,kBAOO,KAAAC,mCAAA,kBAO/B;IAQ/DnG,EADE,CAAAa,YAAA,EAAM,EACF;IAMAb,EAHN,CAAAC,cAAA,gBAAsC,eACR,UACtB,gBACQ;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC3Bb,EAAA,CAAAc,MAAA,oCACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAEHb,EADF,CAAAC,cAAA,0BAA0D,iBAC7C;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAY;IACrCb,EAAA,CAAAC,cAAA,iBAAsJ;IAA5BD,EAAA,CAAAE,UAAA,mBAAAkG,qDAAA;MAAApG,EAAA,CAAAK,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA8F,eAAA,EAAiB;IAAA,EAAC;IAArJtG,EAAA,CAAAa,YAAA,EAAsJ;IACtJb,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACrCb,EAAA,CAAA6B,UAAA,KAAA0E,yCAAA,wBAAgH;IAGlHvG,EAAA,CAAAa,YAAA,EAAiB;IAuBjBb,EArBA,CAAA6B,UAAA,KAAA2E,mCAAA,kBAA0F,KAAAC,mCAAA,kBAqBF;IAK5FzG,EADE,CAAAa,YAAA,EAAM,EACD;IAGPb,EAAA,CAAA6B,UAAA,KAAA6E,mCAAA,oBAA6D;IA+F3D1G,EADF,CAAAC,cAAA,eAA0B,kBACoD;IAAzDD,EAAA,CAAAE,UAAA,mBAAAyG,sDAAA;MAAA3G,EAAA,CAAAK,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoG,wBAAA,EAA0B;IAAA,EAAC;IACrD5G,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,kBAAU;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC/Bb,EAAA,CAAAc,MAAA,mCACF;IAAAd,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,kBAAyI;IAA/FD,EAAA,CAAAE,UAAA,mBAAA2G,sDAAA;MAAA7G,EAAA,CAAAK,aAAA,CAAAgG,IAAA;MAAA,MAAA7F,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsG,iBAAA,EAAmB;IAAA,EAAC;IAErE9G,EADA,CAAA6B,UAAA,KAAAkF,wCAAA,uBAA6B,KAAAC,2CAAA,0BACgB;IAC7ChH,EAAA,CAAAc,MAAA,IACF;IAGNd,EAHM,CAAAa,YAAA,EAAS,EACL,EACF,EACF;;;;;;;;IA7L6Bb,EAAA,CAAAoB,SAAA,IAAgC;IAAhCpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAA0B,wBAAA,GAAgC;IAOhClC,EAAA,CAAAoB,SAAA,EAA0D;IAA1DpB,EAAA,CAAAqB,UAAA,UAAAY,OAAA,GAAAzB,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,wCAAAe,OAAA,CAAAd,KAAA,CAA0D;IAO1DnB,EAAA,CAAAoB,SAAA,EAAsD;IAAtDpB,EAAA,CAAAqB,UAAA,UAAAL,OAAA,GAAAR,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,oCAAAF,OAAA,CAAAG,KAAA,CAAsD;IAOtDnB,EAAA,CAAAoB,SAAA,EAA6D;IAA7DpB,EAAA,CAAAqB,UAAA,UAAAc,OAAA,GAAA3B,MAAA,CAAA8B,oBAAA,CAAApB,GAAA,2CAAAiB,OAAA,CAAAhB,KAAA,CAA6D;IAO7DnB,EAAA,CAAAoB,SAAA,EAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAyC,aAAA,CAAAC,MAAA,KAA8B;IAWvDlD,EAAA,CAAAoB,SAAA,EAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAA8D,iBAAA,CAA+B;IAUnBtE,EAAA,CAAAoB,SAAA,IAAkG;IAAlGpB,EAAA,CAAAqB,UAAA,WAAA4F,OAAA,GAAAzG,MAAA,CAAA8D,iBAAA,CAAApD,GAAA,iCAAA+F,OAAA,CAAAhD,OAAA,OAAAgD,OAAA,GAAAzG,MAAA,CAAA8D,iBAAA,CAAApD,GAAA,iCAAA+F,OAAA,CAAA/C,OAAA,EAAkG;IAKnFlE,EAAA,CAAAoB,SAAA,EAA2D;IAA3DpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAA0G,kBAAA,IAAA1G,MAAA,CAAA+D,oBAAA,CAAArB,MAAA,KAA2D;IAqB/DlD,EAAA,CAAAoB,SAAA,EAA6D;IAA7DpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAA0G,kBAAA,IAAA1G,MAAA,CAAA+D,oBAAA,CAAArB,MAAA,OAA6D;IAQpDlD,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAkF,eAAA,CAAqB;IAmGe1F,EAAA,CAAAoB,SAAA,GAA0C;IAA1CpB,EAAA,CAAAqB,UAAA,aAAAb,MAAA,CAAA2G,SAAA,KAAA3G,MAAA,CAAAkF,eAAA,CAA0C;IACrG1F,EAAA,CAAAoB,SAAA,EAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,UAAAb,MAAA,CAAA2G,SAAA,CAAgB;IACbnH,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAA2G,SAAA,CAAe;IAC7BnH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAoC,kBAAA,MAAA5B,MAAA,CAAA2G,SAAA,gDACF;;;ADrXV,OAAM,MAAOC,YAAY;EA8IvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IA7IzB,KAAAN,SAAS,GAAG,KAAK;IACjB,KAAAlE,aAAa,GAAW,EAAE;IAC1B,KAAAyC,eAAe,GAAuB,IAAI;IAC1C,KAAAnB,oBAAoB,GAAkB,EAAE;IACxC,KAAA2C,kBAAkB,GAAG,KAAK;IAE1B,KAAAlF,cAAc,GAAG,CACf;MACEb,KAAK,EAAE,eAAe;MACtBK,KAAK,EAAE,sBAAsB;MAC7BF,IAAI,EAAE,iBAAiB;MACvBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,cAAc;MACrBK,KAAK,EAAE,uBAAuB;MAC9BF,IAAI,EAAE,mBAAmB;MACzBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,iBAAiB;MACxBK,KAAK,EAAE,2BAA2B;MAClCF,IAAI,EAAE,aAAa;MACnBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,aAAa;MACpBK,KAAK,EAAE,oBAAoB;MAC3BF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,SAAS;MAChBK,KAAK,EAAE,iBAAiB;MACxBF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,SAAS;MAChBK,KAAK,EAAE,yBAAyB;MAChCF,IAAI,EAAE,uBAAuB;MAC7BG,WAAW,EAAE;KACd,CACF;IAED;IACA,KAAAiG,cAAc,GAAkB,CAC9B;MACE9C,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,uBAAuB;MACrCY,eAAe,EAAE,yDAAyD;MAC1EV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,uBAAuB;MACvCC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,wBAAwB;MACtCY,eAAe,EAAE,mDAAmD;MACpEV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,uBAAuB;MACtCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,8BAA8B;MAC9CC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,wBAAwB;MACtCY,eAAe,EAAE,oDAAoD;MACrEV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,6BAA6B;MAC3CY,eAAe,EAAE,0DAA0D;MAC3EV,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,mBAAmB;MAClCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,+BAA+B;MAC/CC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,kBAAkB;MAChCY,eAAe,EAAE,0CAA0C;MAC3DV,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,oBAAoB;MACnCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,gCAAgC;MAChDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,oBAAoB;MAClCY,eAAe,EAAE,wDAAwD;MACzEV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,mBAAmB;MACjCY,eAAe,EAAE,2CAA2C;MAC5DV,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,sBAAsB;MACrCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,kCAAkC;MAClDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI6C,IAAI,CAAC,YAAY,CAAC;MACnC5C,YAAY,EAAE,wBAAwB;MACtCY,eAAe,EAAE,0DAA0D;MAC3EV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,yBAAyB;MACzCC,cAAc,EAAE;KACjB,CACF;IAQC,IAAI,CAAC8B,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN;EAAA;EAGFD,WAAWA,CAAA;IACT,IAAI,CAAC3G,iBAAiB,GAAG,IAAI,CAACqG,WAAW,CAACQ,KAAK,CAAC;MAC9CC,YAAY,EAAE,CAAC,EAAE,EAAEhI,UAAU,CAACiI,QAAQ;KACvC,CAAC;IAEF,IAAI,CAAC1D,iBAAiB,GAAG,IAAI,CAACgD,WAAW,CAACQ,KAAK,CAAC;MAC9CG,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;KAClB,CAAC;IAEF,IAAI,CAAC3F,oBAAoB,GAAG,IAAI,CAACgF,WAAW,CAACQ,KAAK,CAAC;MACjDI,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACnI,UAAU,CAACiI,QAAQ,EAAEjI,UAAU,CAACoI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACrI,UAAU,CAACiI,QAAQ,EAAEjI,UAAU,CAACsI,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAC7EC,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAACvI,UAAU,CAACiI,QAAQ,EAAEjI,UAAU,CAACoI,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3EI,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,YAAY,EAAE,CAAC,IAAI;KACpB,CAAC;EACJ;EAEA9H,mBAAmBA,CAAC+H,IAAS;IAC3B,IAAI,CAACzH,iBAAiB,CAAC0H,UAAU,CAAC;MAAEZ,YAAY,EAAEW,IAAI,CAACvH;IAAK,CAAE,CAAC;EACjE;EAEAyH,eAAeA,CAAA;IACb,IAAI,CAACrE,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAACmD,cAAc,CAAC;IACpD,IAAI,CAACR,kBAAkB,GAAG,IAAI;EAChC;EAEAZ,eAAeA,CAAA;IACb,MAAM2B,UAAU,GAAG,IAAI,CAAC3D,iBAAiB,CAACpD,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAElE,IAAI,CAAC8G,UAAU,IAAIA,UAAU,CAAC3C,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3C;MACA,IAAI,CAACsD,eAAe,EAAE;MACtB;;IAGF,IAAIX,UAAU,CAAC/E,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACiE,SAAS,GAAG,IAAI;MAErB;MACA0B,UAAU,CAAC,MAAK;QACd,IAAI,CAACtE,oBAAoB,GAAG,IAAI,CAACmD,cAAc,CAACoB,MAAM,CAACC,OAAO,IAC5DA,OAAO,CAACnE,aAAa,CAACoE,WAAW,EAAE,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,EAAE,CAAC,IACtED,OAAO,CAAChE,YAAY,CAACiE,WAAW,EAAE,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,EAAE,CAAC,IACrED,OAAO,CAAC9D,IAAI,CAAC+D,WAAW,EAAE,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,EAAE,CAAC,IAC7DD,OAAO,CAAC7D,aAAa,CAAC8D,WAAW,EAAE,CAACC,QAAQ,CAAChB,UAAU,CAACe,WAAW,EAAE,CAAC,CACvE;QACD,IAAI,CAAC9B,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACC,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEAxC,aAAaA,CAACoE,OAAoB;IAChC,IAAI,CAACrD,eAAe,GAAGqD,OAAO;IAC9B,IAAI,CAAC7B,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAAC5C,iBAAiB,CAACqE,UAAU,CAAC;MAAEV,UAAU,EAAEc,OAAO,CAACnE;IAAa,CAAE,CAAC;EAC1E;EAEAa,qBAAqBA,CAAA;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACpB,iBAAiB,CAACqE,UAAU,CAAC;MAAEV,UAAU,EAAE;IAAE,CAAE,CAAC;IACrD;IACA,IAAI,CAACW,eAAe,EAAE;EACxB;EAEM9B,iBAAiBA,CAAA;IAAA,IAAAoC,KAAA;IAAA,OAAAC,iBAAA;MACrB,IAAID,KAAI,CAACjI,iBAAiB,CAACoD,KAAK,IAAI6E,KAAI,CAACxD,eAAe,IAAIwD,KAAI,CAAC5G,oBAAoB,CAAC+B,KAAK,EAAE;QAC3F6E,KAAI,CAAC/B,SAAS,GAAG,IAAI;QAErB,MAAMiC,OAAO,SAASF,KAAI,CAAC1B,iBAAiB,CAAC6B,MAAM,CAAC;UAClDC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB;QACAX,UAAU,cAAAM,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAAC/B,SAAS,GAAG,KAAK;UACtB,MAAMiC,OAAO,CAACK,OAAO,EAAE;UAEvB,MAAMC,KAAK,SAASR,KAAI,CAACzB,eAAe,CAAC4B,MAAM,CAAC;YAC9CC,OAAO,EAAE,uDAAuD,GAAG3B,IAAI,CAACgC,GAAG,EAAE;YAC7EJ,QAAQ,EAAE,IAAI;YACdK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,MAAMH,KAAK,CAACF,OAAO,EAAE;UAErB;UACAN,KAAI,CAAC3B,MAAM,CAACuC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMJ,KAAK,SAASR,KAAI,CAACzB,eAAe,CAAC4B,MAAM,CAAC;UAC9CC,OAAO,EAAE,qEAAqE;UAC9EC,QAAQ,EAAE,IAAI;UACdK,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;SACX,CAAC;QACF,MAAMH,KAAK,CAACF,OAAO,EAAE;;IACtB;EACH;EAEAlG,cAAcA,CAACyG,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAAC9G,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACD,aAAa,GAAGiH,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACtC,IAAI,CAAC1H,oBAAoB,CAACqG,UAAU,CAAC;QAAEF,YAAY,EAAEuB,KAAK,CAAC,CAAC;MAAC,CAAE,CAAC;;EAEpE;EAEArH,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACO,aAAa,CAACmH,MAAM,CAAC1H,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,IAAI,CAACO,aAAa,CAACC,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACZ,oBAAoB,CAACqG,UAAU,CAAC;QAAEF,YAAY,EAAE;MAAI,CAAE,CAAC;;EAEhE;EAEAvG,wBAAwBA,CAAA;IACtB,MAAMmI,aAAa,GAAG,IAAI,CAACpJ,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACvE,OAAO,IAAI,CAACa,cAAc,CAACsI,IAAI,CAAC5B,IAAI,IAAIA,IAAI,CAACvH,KAAK,KAAKkJ,aAAa,CAAC;EACvE;EAEAzI,eAAeA,CAAC2I,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO,IAAI,CAACtJ,iBAAiB,CAACoD,KAAK;MACrC,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC/B,oBAAoB,CAAC+B,KAAK;MACxC,KAAK,CAAC;QACJ,OAAO,IAAI,CAACqB,eAAe,KAAK,IAAI;MACtC;QACE,OAAO,KAAK;;EAElB;EAEArD,eAAeA,CAACmI,IAAe,EAAEC,KAAa;IAC5C,MAAMC,OAAO,GAAGF,IAAI,CAACtJ,GAAG,CAACuJ,KAAK,CAAC;IAC/B,IAAIC,OAAO,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMxC,SAAS,GAAGuC,OAAO,CAACG,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACH,KAAK,CAAC,qBAAqBtC,SAAS,aAAa;;IAEhF,IAAIuC,OAAO,EAAEC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACH,KAAa;IACjC,MAAMM,MAAM,GAA8B;MACxChD,YAAY,EAAE,gBAAgB;MAC9BE,UAAU,EAAE,aAAa;MACzBC,iBAAiB,EAAE,qBAAqB;MACxCE,aAAa,EAAE,gBAAgB;MAC/BE,oBAAoB,EAAE,uBAAuB;MAC7CC,QAAQ,EAAE;KACX;IACD,OAAOwC,MAAM,CAACN,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAO,MAAMA,CAAA;IACJ,IAAI,CAACzD,MAAM,CAACuC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBAlUW1C,YAAY,EAAApH,EAAA,CAAAiL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnL,EAAA,CAAAiL,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAArL,EAAA,CAAAiL,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAvL,EAAA,CAAAiL,iBAAA,CAAAK,EAAA,CAAAE,eAAA;IAAA;EAAA;;;YAAZpE,YAAY;MAAAqE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBnB/L,EAHN,CAAAC,cAAA,oBAAiC,qBACF,qBACD,oBACO;UAAnBD,EAAA,CAAAE,UAAA,mBAAA+L,kDAAA;YAAA,OAASD,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UAC5BhL,EAAA,CAAAY,SAAA,kBAAuC;UAE3CZ,EADE,CAAAa,YAAA,EAAa,EACD;UACdb,EAAA,CAAAC,cAAA,gBAAW;UAAAD,EAAA,CAAAc,MAAA,yBAAkB;UAEjCd,EAFiC,CAAAa,YAAA,EAAY,EAC7B,EACH;UAOLb,EALR,CAAAC,cAAA,qBAA0D,aACjC,aAEO,cACE,UACtB;UAAAD,EAAA,CAAAc,MAAA,8BAAsB;UAAAd,EAAA,CAAAa,YAAA,EAAK;UAC/Bb,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,yDAAiD;UAExDd,EAFwD,CAAAa,YAAA,EAAI,EACpD,EACF;UAMAb,EAHN,CAAAC,cAAA,eAA8B,eACA,eACqD,eACpD;UAAAD,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAa,YAAA,EAAM;UAChCb,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAc,MAAA,sBAAc;UACxCd,EADwC,CAAAa,YAAA,EAAM,EACxC;UACNb,EAAA,CAAAY,SAAA,eAAyE;UAEvEZ,EADF,CAAAC,cAAA,eAA6F,eAClE;UAAAD,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAa,YAAA,EAAM;UAChCb,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAc,MAAA,6BAAqB;UAC/Cd,EAD+C,CAAAa,YAAA,EAAM,EAC/C;UACNb,EAAA,CAAAY,SAAA,eAAyE;UAEvEZ,EADF,CAAAC,cAAA,eAA6F,eAClE;UAAAD,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAa,YAAA,EAAM;UAChCb,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAc,MAAA,yBAAiB;UAG/Cd,EAH+C,CAAAa,YAAA,EAAM,EAC3C,EACF,EACF;UA8JNb,EA3JA,CAAA6B,UAAA,KAAAqK,4BAAA,mBAAsD,KAAAC,4BAAA,mBAmCsB,KAAAC,4BAAA,oBAwHA;UA2MhFpM,EADE,CAAAa,YAAA,EAAM,EACM;;;UAhZFb,EAAA,CAAAqB,UAAA,qBAAoB;UAWnBrB,EAAA,CAAAoB,SAAA,GAAmB;UAAnBpB,EAAA,CAAAqB,UAAA,oBAAmB;UAaNrB,EAAA,CAAAoB,SAAA,IAAqB;UAACpB,EAAtB,CAAAe,WAAA,gBAAqB,cAAAiL,GAAA,CAAApK,eAAA,IAAuC;UAIlD5B,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAiL,GAAA,CAAApK,eAAA,IAAsC;UAChD5B,EAAA,CAAAoB,SAAA,EAAmC;UAACpB,EAApC,CAAAe,WAAA,WAAAiL,GAAA,CAAApK,eAAA,IAAmC,cAAAoK,GAAA,CAAApK,eAAA,IAAuC;UAIhE5B,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAiL,GAAA,CAAApK,eAAA,IAAsC;UAChD5B,EAAA,CAAAoB,SAAA,EAAmC;UAACpB,EAApC,CAAAe,WAAA,WAAAiL,GAAA,CAAApK,eAAA,IAAmC,cAAAoK,GAAA,CAAApK,eAAA,IAAuC;UAQrE5B,EAAA,CAAAoB,SAAA,GAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAA2K,GAAA,CAAApK,eAAA,IAAyB;UAmCzB5B,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAA2K,GAAA,CAAApK,eAAA,QAAAoK,GAAA,CAAApK,eAAA,IAA+C;UAwH/C5B,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAA2K,GAAA,CAAApK,eAAA,QAAAoK,GAAA,CAAApK,eAAA,IAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}