{"ast": null, "code": "/**\n * This file includes polyfills needed by <PERSON><PERSON> and is loaded before the app.\n * You can add your own extra polyfills to this file.\n *\n * This file is divided into 2 sections:\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\n *      file.\n *\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\n * automatically update themselves. This includes recent versions of Safari, Chrome (including\n * Opera), Edge on the desktop, and iOS and Chrome on mobile.\n *\n * Learn more in https://angular.io/guide/browser-support\n */\n/***************************************************************************************************\n * BROWSER POLYFILLS\n */\n/***************************************************************************************************\n * Zone JS is required by default for Angular itself.\n */\nimport 'zone.js'; // Included with Angular CLI.\n/***************************************************************************************************\n * APPLICATION IMPORTS\n */", "map": {"version": 3, "names": [], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\polyfills.ts"], "sourcesContent": ["/**\n * This file includes polyfills needed by <PERSON><PERSON> and is loaded before the app.\n * You can add your own extra polyfills to this file.\n *\n * This file is divided into 2 sections:\n *   1. Browser polyfills. These are applied before loading ZoneJS and are sorted by browsers.\n *   2. Application imports. Files imported after ZoneJS that should be loaded before your main\n *      file.\n *\n * The current setup is for so-called \"evergreen\" browsers; the last versions of browsers that\n * automatically update themselves. This includes recent versions of Safari, Chrome (including\n * Opera), Edge on the desktop, and iOS and Chrome on mobile.\n *\n * Learn more in https://angular.io/guide/browser-support\n */\n\n/***************************************************************************************************\n * BROWSER POLYFILLS\n */\n\n/***************************************************************************************************\n * Zone JS is required by default for Angular itself.\n */\nimport 'zone.js';  // Included with Angular CLI.\n\n\n/***************************************************************************************************\n * APPLICATION IMPORTS\n */\n"], "mappings": "AAAA;;;;;;;;;;;;;;;AAgBA;;;AAIA;;;AAGA,OAAO,SAAS,CAAC,CAAE;AAGnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}