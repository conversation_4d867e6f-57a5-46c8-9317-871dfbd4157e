{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { RouteReuseStrategy } from '@angular/router';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { HttpClientModule } from '@angular/common/http';\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\nimport { IonicStorageModule } from '@ionic/storage-angular';\n// Material Design Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatListModule } from '@angular/material/list';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { AppComponent } from './app.component';\nimport { AppRoutingModule } from './app-routing.module';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ionic/angular\";\nimport * as i2 from \"@ionic/storage-angular\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: RouteReuseStrategy,\n        useClass: IonicRouteStrategy\n      }],\n      imports: [BrowserModule, BrowserAnimationsModule, IonicModule.forRoot(), IonicStorageModule.forRoot(), AppRoutingModule, HttpClientModule,\n      // Material Design Modules\n      MatToolbarModule, MatButtonModule, MatCardModule, MatFormFieldModule, MatInputModule, MatIconModule, MatProgressSpinnerModule, MatSnackBarModule, MatTabsModule, MatListModule, MatSidenavModule, MatMenuModule, MatDialogModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatStepperModule, MatTableModule, MatPaginatorModule, MatSortModule, MatChipsModule, MatBadgeModule, MatTooltipModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, BrowserAnimationsModule, i1.IonicModule, i2.IonicStorageModule, AppRoutingModule, HttpClientModule,\n    // Material Design Modules\n    MatToolbarModule, MatButtonModule, MatCardModule, MatFormFieldModule, MatInputModule, MatIconModule, MatProgressSpinnerModule, MatSnackBarModule, MatTabsModule, MatListModule, MatSidenavModule, MatMenuModule, MatDialogModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatStepperModule, MatTableModule, MatPaginatorModule, MatSortModule, MatChipsModule, MatBadgeModule, MatTooltipModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "RouteReuseStrategy", "BrowserAnimationsModule", "HttpClientModule", "IonicModule", "IonicRouteStrategy", "IonicStorageModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatIconModule", "MatProgressSpinnerModule", "MatSnackBarModule", "MatTabsModule", "MatListModule", "MatSidenavModule", "MatMenuModule", "MatDialogModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatStepperModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatChipsModule", "MatBadgeModule", "MatTooltipModule", "AppComponent", "AppRoutingModule", "AppModule", "bootstrap", "provide", "useClass", "imports", "forRoot", "declarations", "i1", "i2"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { BrowserModule } from '@angular/platform-browser';\r\nimport { RouteReuseStrategy } from '@angular/router';\r\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\r\nimport { HttpClientModule } from '@angular/common/http';\r\n\r\nimport { IonicModule, IonicRouteStrategy } from '@ionic/angular';\r\nimport { IonicStorageModule } from '@ionic/storage-angular';\r\n\r\n// Material Design Modules\r\nimport { MatToolbarModule } from '@angular/material/toolbar';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\r\nimport { MatTabsModule } from '@angular/material/tabs';\r\nimport { MatListModule } from '@angular/material/list';\r\nimport { MatSidenavModule } from '@angular/material/sidenav';\r\nimport { MatMenuModule } from '@angular/material/menu';\r\nimport { MatDialogModule } from '@angular/material/dialog';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { MatDatepickerModule } from '@angular/material/datepicker';\r\nimport { MatNativeDateModule } from '@angular/material/core';\r\nimport { MatStepperModule } from '@angular/material/stepper';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSortModule } from '@angular/material/sort';\r\nimport { MatChipsModule } from '@angular/material/chips';\r\nimport { MatBadgeModule } from '@angular/material/badge';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\n\r\n// Capacitor Plugins\r\nimport { Camera } from '@capacitor/camera';\r\nimport { Network } from '@capacitor/network';\r\nimport { StatusBar } from '@capacitor/status-bar';\r\nimport { SplashScreen } from '@capacitor/splash-screen';\r\n\r\nimport { AppComponent } from './app.component';\r\nimport { AppRoutingModule } from './app-routing.module';\r\n\r\n@NgModule({\r\n  declarations: [AppComponent],\r\n  imports: [\r\n    BrowserModule,\r\n    BrowserAnimationsModule,\r\n    IonicModule.forRoot(),\r\n    IonicStorageModule.forRoot(),\r\n    AppRoutingModule,\r\n    HttpClientModule,\r\n\r\n    // Material Design Modules\r\n    MatToolbarModule,\r\n    MatButtonModule,\r\n    MatCardModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatIconModule,\r\n    MatProgressSpinnerModule,\r\n    MatSnackBarModule,\r\n    MatTabsModule,\r\n    MatListModule,\r\n    MatSidenavModule,\r\n    MatMenuModule,\r\n    MatDialogModule,\r\n    MatSelectModule,\r\n    MatDatepickerModule,\r\n    MatNativeDateModule,\r\n    MatStepperModule,\r\n    MatTableModule,\r\n    MatPaginatorModule,\r\n    MatSortModule,\r\n    MatChipsModule,\r\n    MatBadgeModule,\r\n    MatTooltipModule\r\n  ],\r\n  providers: [\r\n    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy }\r\n  ],\r\n  bootstrap: [AppComponent],\r\n})\r\nexport class AppModule {}\r\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,gBAAgB,QAAQ,sBAAsB;AAEvD,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,gBAAgB;AAChE,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAQ5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,sBAAsB;;;;AA0CvD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRH,YAAY;IAAA;EAAA;;;iBAHb,CACT;QAAEI,OAAO,EAAEjC,kBAAkB;QAAEkC,QAAQ,EAAE9B;MAAkB,CAAE,CAC9D;MAAA+B,OAAA,GAlCCpC,aAAa,EACbE,uBAAuB,EACvBE,WAAW,CAACiC,OAAO,EAAE,EACrB/B,kBAAkB,CAAC+B,OAAO,EAAE,EAC5BN,gBAAgB,EAChB5B,gBAAgB;MAEhB;MACAI,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,aAAa,EACbC,wBAAwB,EACxBC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB;IAAA;EAAA;;;2EAOPG,SAAS;IAAAM,YAAA,GAvCLR,YAAY;IAAAM,OAAA,GAEzBpC,aAAa,EACbE,uBAAuB,EAAAqC,EAAA,CAAAnC,WAAA,EAAAoC,EAAA,CAAAlC,kBAAA,EAGvByB,gBAAgB,EAChB5B,gBAAgB;IAEhB;IACAI,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,aAAa,EACbC,wBAAwB,EACxBC,iBAAiB,EACjBC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,eAAe,EACfC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAChBC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}