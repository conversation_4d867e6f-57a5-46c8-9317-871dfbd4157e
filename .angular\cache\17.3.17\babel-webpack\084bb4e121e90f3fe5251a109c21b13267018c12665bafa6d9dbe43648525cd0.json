{"ast": null, "code": "import { registerPlugin } from '@capacitor/core';\nconst StatusBar = registerPlugin('StatusBar');\nexport * from './definitions';\nexport { StatusBar };", "map": {"version": 3, "names": ["registerPlugin", "StatusBar"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@capacitor/status-bar/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst StatusBar = registerPlugin('StatusBar');\nexport * from './definitions';\nexport { StatusBar };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iBAAiB;AAChD,MAAMC,SAAS,GAAGD,cAAc,CAAC,WAAW,CAAC;AAC7C,cAAc,eAAe;AAC7B,SAASC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}