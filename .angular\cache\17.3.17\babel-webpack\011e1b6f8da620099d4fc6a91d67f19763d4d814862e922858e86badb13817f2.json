{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\n// Material Design Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { CreditnotePageRoutingModule } from './creditnote-routing.module';\nimport { CreditnotePage } from './creditnote.page';\nimport * as i0 from \"@angular/core\";\nexport class CreditnotePageModule {\n  static {\n    this.ɵfac = function CreditnotePageModule_Factory(t) {\n      return new (t || CreditnotePageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: CreditnotePageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, CreditnotePageRoutingModule, MatToolbarModule, MatButtonModule, MatCardModule, MatIconModule, MatFormFieldModule, MatInputModule, MatTableModule, MatPaginatorModule, MatSortModule, MatChipsModule, MatBadgeModule, MatProgressSpinnerModule, MatExpansionModule, MatDividerModule, MatTabsModule, MatDatepickerModule, MatNativeDateModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(CreditnotePageModule, {\n    declarations: [CreditnotePage],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, CreditnotePageRoutingModule, MatToolbarModule, MatButtonModule, MatCardModule, MatIconModule, MatFormFieldModule, MatInputModule, MatTableModule, MatPaginatorModule, MatSortModule, MatChipsModule, MatBadgeModule, MatProgressSpinnerModule, MatExpansionModule, MatDividerModule, MatTabsModule, MatDatepickerModule, MatNativeDateModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "MatToolbarModule", "MatButtonModule", "MatCardModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatTableModule", "MatPaginatorModule", "MatSortModule", "MatChipsModule", "MatBadgeModule", "MatProgressSpinnerModule", "MatExpansionModule", "MatDividerModule", "MatTabsModule", "MatDatepickerModule", "MatNativeDateModule", "CreditnotePageRoutingModule", "CreditnotePage", "CreditnotePageModule", "declarations", "imports"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\creditnote\\creditnote.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\n\n// Material Design Modules\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSortModule } from '@angular/material/sort';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule, MatOptionModule } from '@angular/material/core';\n\nimport { CreditnotePageRoutingModule } from './creditnote-routing.module';\nimport { CreditnotePage } from './creditnote.page';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    IonicModule,\n    CreditnotePageRoutingModule,\n    MatToolbarModule,\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatTableModule,\n    MatPaginatorModule,\n    MatSortModule,\n    MatChipsModule,\n    MatBadgeModule,\n    MatProgressSpinnerModule,\n    MatExpansionModule,\n    MatDividerModule,\n    MatTabsModule,\n    MatDatepickerModule,\n    MatNativeDateModule\n  ],\n  declarations: [CreditnotePage]\n})\nexport class CreditnotePageModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,gBAAgB;AAE5C;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAyB,wBAAwB;AAE7E,SAASC,2BAA2B,QAAQ,6BAA6B;AACzE,SAASC,cAAc,QAAQ,mBAAmB;;AA6BlD,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAzB7BvB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXkB,2BAA2B,EAC3BjB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,aAAa,EACbC,mBAAmB,EACnBC,mBAAmB;IAAA;EAAA;;;2EAIVG,oBAAoB;IAAAC,YAAA,GAFhBF,cAAc;IAAAG,OAAA,GAvB3BzB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXkB,2BAA2B,EAC3BjB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,cAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,cAAc,EACdC,cAAc,EACdC,wBAAwB,EACxBC,kBAAkB,EAClBC,gBAAgB,EAChBC,aAAa,EACbC,mBAAmB,EACnBC,mBAAmB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}