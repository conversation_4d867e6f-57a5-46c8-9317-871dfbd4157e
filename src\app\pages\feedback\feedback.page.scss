/* Material Design Feedback Page Styles */

.material-header {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
              0px 4px 5px 0px rgba(0, 0, 0, 0.14),
              0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.material-toolbar {
  height: 64px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  
  .toolbar-title {
    font-size: 20px;
    font-weight: 500;
    margin-left: 16px;
  }
  
  .spacer {
    flex: 1 1 auto;
  }
}

.material-content {
  --background: #f5f5f5;
  padding: 20px;
}

.feedback-container {
  max-width: 800px;
  margin: 0 auto;
}

/* Header Section */
.header-section {
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  padding: 40px 20px;
  text-align: center;
  border-radius: 12px;
  margin-bottom: 24px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }
  
  .header-content {
    position: relative;
    z-index: 1;
  }
  
  .header-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.9;
  }
  
  .header-title {
    font-size: 28px;
    font-weight: 300;
    margin: 0 0 8px 0;
    letter-spacing: 0.5px;
  }
  
  .header-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
    font-weight: 400;
  }
}

/* Feedback Card */
.feedback-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
  overflow: hidden;
  
  .mat-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px;
    
    .form-title {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: 500;
      color: #333;
      margin: 0;
      
      .title-icon {
        margin-right: 12px;
        font-size: 28px;
        color: #1976d2;
      }
    }
    
    .mat-card-subtitle {
      color: #666;
      font-size: 14px;
      margin-top: 8px;
    }
  }
  
  .mat-card-content {
    padding: 24px;
  }
}

/* Form Styles */
.feedback-form {
  .form-section {
    margin-bottom: 32px;
    
    .section-title {
      display: flex;
      align-items: center;
      font-size: 18px;
      font-weight: 500;
      color: #333;
      margin: 0 0 20px 0;
      
      mat-icon {
        margin-right: 8px;
        color: #1976d2;
        font-size: 20px;
      }
    }
  }
  
  .section-divider {
    margin: 32px 0;
    border-color: #e0e0e0;
  }
  
  .form-row {
    display: flex;
    gap: 16px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 0;
    }
  }
  
  .full-width {
    width: 100%;
  }
  
  .half-width {
    flex: 1;
    
    @media (max-width: 768px) {
      width: 100%;
    }
  }
  
  .mat-form-field {
    margin-bottom: 16px;
    
    .mat-form-field-outline {
      border-radius: 8px;
    }
    
    &.mat-focused .mat-form-field-outline-thick {
      border-color: #1976d2;
    }
  }
}

/* Category Option */
.category-option {
  display: flex;
  align-items: center;
  
  mat-icon {
    margin-right: 8px;
    font-size: 18px;
  }
}

/* Rating Section */
.rating-section {
  text-align: center;
  
  .rating-label {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
  }
  
  .rating-stars {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;
    
    .rating-star {
      font-size: 32px;
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.1);
      }
      
      &.active {
        transform: scale(1.1);
      }
      
      mat-icon {
        font-size: 32px;
      }
    }
  }
  
  .rating-info {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    
    mat-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  
  .checkbox-item {
    .checkbox-content {
      display: flex;
      flex-direction: column;
      
      .checkbox-label {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
      
      .checkbox-description {
        font-size: 14px;
        color: #666;
        margin-top: 4px;
      }
    }
  }
}

/* Submit Section */
.submit-section {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e0e0e0;
  
  .submit-button {
    padding: 12px 32px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
    color: white;
    
    .spinning {
      animation: spin 1s linear infinite;
    }
    
    mat-icon {
      margin-right: 8px;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Thank You Card */
.thank-you-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #e8f5e8 0%, #f1f8e9 100%);
  
  .thank-you-content {
    text-align: center;
    padding: 20px;
    
    .thank-you-icon {
      font-size: 48px;
      color: #4caf50;
      margin-bottom: 16px;
    }
    
    h3 {
      font-size: 20px;
      font-weight: 500;
      color: #2e7d32;
      margin: 0 0 12px 0;
    }
    
    p {
      color: #388e3c;
      font-size: 14px;
      line-height: 1.5;
      margin: 0;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .material-content {
    padding: 12px;
  }
  
  .header-section {
    padding: 32px 16px;
    margin-bottom: 16px;
    border-radius: 8px;
    
    .header-icon {
      font-size: 40px;
    }
    
    .header-title {
      font-size: 24px;
    }
    
    .header-subtitle {
      font-size: 14px;
    }
  }
  
  .feedback-card {
    border-radius: 8px;
    
    .mat-card-header {
      padding: 16px;
      
      .form-title {
        font-size: 20px;
        
        .title-icon {
          font-size: 24px;
        }
      }
    }
    
    .mat-card-content {
      padding: 16px;
    }
  }
  
  .feedback-form .form-section .section-title {
    font-size: 16px;
  }
  
  .rating-section .rating-stars .rating-star {
    font-size: 28px;
    
    mat-icon {
      font-size: 28px;
    }
  }
}

@media (max-width: 480px) {
  .material-toolbar .toolbar-title {
    font-size: 18px;
  }
  
  .header-section {
    padding: 24px 12px;
    
    .header-title {
      font-size: 20px;
    }
  }
  
  .feedback-card .mat-card-header .form-title {
    font-size: 18px;
    flex-direction: column;
    text-align: center;
    
    .title-icon {
      margin-right: 0;
      margin-bottom: 8px;
    }
  }
  
  .submit-section .submit-button {
    width: 100%;
    padding: 16px;
  }
}

/* Success Snackbar */
::ng-deep .success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;
}
