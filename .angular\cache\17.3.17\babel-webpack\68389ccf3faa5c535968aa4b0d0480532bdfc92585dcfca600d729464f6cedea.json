{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-1bf57181.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-6107a37c.js';\nimport { p as isEndSide, i as inheritAriaAttributes, o as assert, l as clamp } from './helpers-be245865.js';\nimport { m as menuController } from './index-6e05b96e.js';\nimport { o as getPresentedOverlay } from './overlays-b874c3c3.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { u as menuOutline, v as menuSharp } from './index-f7dc70ba.js';\nimport './index-a5d50daf.js';\nimport './index-9b0d46f4.js';\nimport './animation-6a0c5338.js';\nimport './framework-delegate-ed4ba327.js';\nconst menuIosCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}}@supports not (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{left:0;right:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{left:unset;right:unset;left:auto;right:0}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{left:unset;right:unset;left:auto;right:0}}}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}}@supports not (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{left:auto;right:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{left:unset;right:unset;left:0;right:auto}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{left:unset;right:unset;left:0;right:auto}}}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){width:var(--width);min-width:var(--min-width);max-width:var(--max-width)}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}\";\nconst IonMenuIosStyle0 = menuIosCss;\nconst menuMdCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}}@supports not (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{left:0;right:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{left:unset;right:unset;left:auto;right:0}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{left:unset;right:unset;left:auto;right:0}}}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}}@supports not (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{left:auto;right:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{left:unset;right:unset;left:0;right:auto}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{left:unset;right:unset;left:0;right:auto}}}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){width:var(--width);min-width:var(--min-width);max-width:var(--max-width)}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}\";\nconst IonMenuMdStyle0 = menuMdCss;\nconst iosEasing = 'cubic-bezier(0.32,0.72,0,1)';\nconst mdEasing = 'cubic-bezier(0.0,0.0,0.2,1)';\nconst iosEasingReverse = 'cubic-bezier(1, 0, 0.68, 0.28)';\nconst mdEasingReverse = 'cubic-bezier(0.4, 0, 0.6, 1)';\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]), input:not([type=hidden]):not([tabindex^=\"-\"]), textarea:not([tabindex^=\"-\"]), button:not([tabindex^=\"-\"]), select:not([tabindex^=\"-\"]), .ion-focusable:not([tabindex^=\"-\"])';\nconst Menu = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionWillOpen = createEvent(this, \"ionWillOpen\", 7);\n    this.ionWillClose = createEvent(this, \"ionWillClose\", 7);\n    this.ionDidOpen = createEvent(this, \"ionDidOpen\", 7);\n    this.ionDidClose = createEvent(this, \"ionDidClose\", 7);\n    this.ionMenuChange = createEvent(this, \"ionMenuChange\", 7);\n    this.lastOnEnd = 0;\n    this.blocker = GESTURE_CONTROLLER.createBlocker({\n      disableScroll: true\n    });\n    this.didLoad = false;\n    /**\n     * Flag used to determine if an open/close\n     * operation was cancelled. For example, if\n     * an app calls \"menu.open\" then disables the menu\n     * part way through the animation, then this would\n     * be considered a cancelled operation.\n     */\n    this.operationCancelled = false;\n    this.isAnimating = false;\n    this._isOpen = false;\n    this.inheritedAttributes = {};\n    this.handleFocus = ev => {\n      /**\n       * Overlays have their own focus trapping listener\n       * so we do not want the two listeners to conflict\n       * with each other. If the top-most overlay that is\n       * open does not contain this ion-menu, then ion-menu's\n       * focus trapping should not run.\n       */\n      const lastOverlay = getPresentedOverlay(document);\n      if (lastOverlay && !lastOverlay.contains(this.el)) {\n        return;\n      }\n      this.trapKeyboardFocus(ev, document);\n    };\n    this.isPaneVisible = false;\n    this.isEndSide = false;\n    this.contentId = undefined;\n    this.menuId = undefined;\n    this.type = undefined;\n    this.disabled = false;\n    this.side = 'start';\n    this.swipeGesture = true;\n    this.maxEdgeStart = 50;\n  }\n  typeChanged(type, oldType) {\n    const contentEl = this.contentEl;\n    if (contentEl) {\n      if (oldType !== undefined) {\n        contentEl.classList.remove(`menu-content-${oldType}`);\n      }\n      contentEl.classList.add(`menu-content-${type}`);\n      contentEl.removeAttribute('style');\n    }\n    if (this.menuInnerEl) {\n      // Remove effects of previous animations\n      this.menuInnerEl.removeAttribute('style');\n    }\n    this.animation = undefined;\n  }\n  disabledChanged() {\n    this.updateState();\n    this.ionMenuChange.emit({\n      disabled: this.disabled,\n      open: this._isOpen\n    });\n  }\n  sideChanged() {\n    this.isEndSide = isEndSide(this.side);\n    /**\n     * Menu direction animation is calculated based on the document direction.\n     * If the document direction changes, we need to create a new animation.\n     */\n    this.animation = undefined;\n  }\n  swipeGestureChanged() {\n    this.updateState();\n  }\n  connectedCallback() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // TODO: connectedCallback is fired in CE build\n      // before WC is defined. This needs to be fixed in Stencil.\n      if (typeof customElements !== 'undefined' && customElements != null) {\n        yield customElements.whenDefined('ion-menu');\n      }\n      if (_this.type === undefined) {\n        _this.type = config.get('menuType', 'overlay');\n      }\n      const content = _this.contentId !== undefined ? document.getElementById(_this.contentId) : null;\n      if (content === null) {\n        console.error('Menu: must have a \"content\" element to listen for drag events on.');\n        return;\n      }\n      if (_this.el.contains(content)) {\n        console.error(`Menu: \"contentId\" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`);\n      }\n      _this.contentEl = content;\n      // add menu's content classes\n      content.classList.add('menu-content');\n      _this.typeChanged(_this.type, undefined);\n      _this.sideChanged();\n      // register this menu with the app's menu controller\n      menuController._register(_this);\n      _this.menuChanged();\n      _this.gesture = (yield import('./index-2cf77112.js')).createGesture({\n        el: document,\n        gestureName: 'menu-swipe',\n        gesturePriority: 30,\n        threshold: 10,\n        blurOnStart: true,\n        canStart: ev => _this.canStart(ev),\n        onWillStart: () => _this.onWillStart(),\n        onStart: () => _this.onStart(),\n        onMove: ev => _this.onMove(ev),\n        onEnd: ev => _this.onEnd(ev)\n      });\n      _this.updateState();\n    })();\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.didLoad = true;\n      _this2.menuChanged();\n      _this2.updateState();\n    })();\n  }\n  menuChanged() {\n    /**\n     * Inform dependent components such as ion-menu-button\n     * that the menu is ready. Note that we only want to do this\n     * once the menu has been rendered which is why we check for didLoad.\n     */\n    if (this.didLoad) {\n      this.ionMenuChange.emit({\n        disabled: this.disabled,\n        open: this._isOpen\n      });\n    }\n  }\n  disconnectedCallback() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      /**\n       * The menu should be closed when it is\n       * unmounted from the DOM.\n       * This is an async call, so we need to wait for\n       * this to finish otherwise contentEl\n       * will not have MENU_CONTENT_OPEN removed.\n       */\n      yield _this3.close(false);\n      _this3.blocker.destroy();\n      menuController._unregister(_this3);\n      if (_this3.animation) {\n        _this3.animation.destroy();\n      }\n      if (_this3.gesture) {\n        _this3.gesture.destroy();\n        _this3.gesture = undefined;\n      }\n      _this3.animation = undefined;\n      _this3.contentEl = undefined;\n    })();\n  }\n  onSplitPaneChanged(ev) {\n    const {\n      target\n    } = ev;\n    const closestSplitPane = this.el.closest('ion-split-pane');\n    /**\n     * Menu listens on the body for \"ionSplitPaneVisible\".\n     * However, this means the callback will run any time\n     * a SplitPane changes visibility. As a result, we only want\n     * Menu's visibility state to update if its parent SplitPane\n     * changes visibility.\n     */\n    if (target !== closestSplitPane) {\n      return;\n    }\n    this.isPaneVisible = ev.detail.isPane(this.el);\n    this.updateState();\n  }\n  onBackdropClick(ev) {\n    // TODO(FW-2832): type (CustomEvent triggers errors which should be sorted)\n    if (this._isOpen && this.lastOnEnd < ev.timeStamp - 100) {\n      const shouldClose = ev.composedPath ? !ev.composedPath().includes(this.menuInnerEl) : false;\n      if (shouldClose) {\n        ev.preventDefault();\n        ev.stopPropagation();\n        this.close();\n      }\n    }\n  }\n  onKeydown(ev) {\n    if (ev.key === 'Escape') {\n      this.close();\n    }\n  }\n  /**\n   * Returns `true` is the menu is open.\n   */\n  isOpen() {\n    return Promise.resolve(this._isOpen);\n  }\n  /**\n   * Returns `true` is the menu is active.\n   *\n   * A menu is active when it can be opened or closed, meaning it's enabled\n   * and it's not part of a `ion-split-pane`.\n   */\n  isActive() {\n    return Promise.resolve(this._isActive());\n  }\n  /**\n   * Opens the menu. If the menu is already open or it can't be opened,\n   * it returns `false`.\n   */\n  open(animated = true) {\n    return this.setOpen(true, animated);\n  }\n  /**\n   * Closes the menu. If the menu is already closed or it can't be closed,\n   * it returns `false`.\n   */\n  close(animated = true) {\n    return this.setOpen(false, animated);\n  }\n  /**\n   * Toggles the menu. If the menu is already open, it will try to close, otherwise it will try to open it.\n   * If the operation can't be completed successfully, it returns `false`.\n   */\n  toggle(animated = true) {\n    return this.setOpen(!this._isOpen, animated);\n  }\n  /**\n   * Opens or closes the button.\n   * If the operation can't be completed successfully, it returns `false`.\n   */\n  setOpen(shouldOpen, animated = true) {\n    return menuController._setOpen(this, shouldOpen, animated);\n  }\n  focusFirstDescendant() {\n    const {\n      el\n    } = this;\n    const firstInput = el.querySelector(focusableQueryString);\n    if (firstInput) {\n      firstInput.focus();\n    } else {\n      el.focus();\n    }\n  }\n  focusLastDescendant() {\n    const {\n      el\n    } = this;\n    const inputs = Array.from(el.querySelectorAll(focusableQueryString));\n    const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n    if (lastInput) {\n      lastInput.focus();\n    } else {\n      el.focus();\n    }\n  }\n  trapKeyboardFocus(ev, doc) {\n    const target = ev.target;\n    if (!target) {\n      return;\n    }\n    /**\n     * If the target is inside the menu contents, let the browser\n     * focus as normal and keep a log of the last focused element.\n     */\n    if (this.el.contains(target)) {\n      this.lastFocus = target;\n    } else {\n      /**\n       * Otherwise, we are about to have focus go out of the menu.\n       * Wrap the focus to either the first or last element.\n       */\n      /**\n       * Once we call `focusFirstDescendant`, another focus event\n       * will fire, which will cause `lastFocus` to be updated\n       * before we can run the code after that. We cache the value\n       * here to avoid that.\n       */\n      this.focusFirstDescendant();\n      /**\n       * If the cached last focused element is the same as the now-\n       * active element, that means the user was on the first element\n       * already and pressed Shift + Tab, so we need to wrap to the\n       * last descendant.\n       */\n      if (this.lastFocus === doc.activeElement) {\n        this.focusLastDescendant();\n      }\n    }\n  }\n  _setOpen(_x) {\n    var _this4 = this;\n    return _asyncToGenerator(function* (shouldOpen, animated = true) {\n      // If the menu is disabled or it is currently being animated, let's do nothing\n      if (!_this4._isActive() || _this4.isAnimating || shouldOpen === _this4._isOpen) {\n        return false;\n      }\n      _this4.beforeAnimation(shouldOpen);\n      yield _this4.loadAnimation();\n      yield _this4.startAnimation(shouldOpen, animated);\n      /**\n       * If the animation was cancelled then\n       * return false because the operation\n       * did not succeed.\n       */\n      if (_this4.operationCancelled) {\n        _this4.operationCancelled = false;\n        return false;\n      }\n      _this4.afterAnimation(shouldOpen);\n      return true;\n    }).apply(this, arguments);\n  }\n  loadAnimation() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      // Menu swipe animation takes the menu's inner width as parameter,\n      // If `offsetWidth` changes, we need to create a new animation.\n      const width = _this5.menuInnerEl.offsetWidth;\n      /**\n       * Menu direction animation is calculated based on the document direction.\n       * If the document direction changes, we need to create a new animation.\n       */\n      const isEndSide$1 = isEndSide(_this5.side);\n      if (width === _this5.width && _this5.animation !== undefined && isEndSide$1 === _this5.isEndSide) {\n        return;\n      }\n      _this5.width = width;\n      _this5.isEndSide = isEndSide$1;\n      // Destroy existing animation\n      if (_this5.animation) {\n        _this5.animation.destroy();\n        _this5.animation = undefined;\n      }\n      // Create new animation\n      const animation = _this5.animation = yield menuController._createAnimation(_this5.type, _this5);\n      if (!config.getBoolean('animated', true)) {\n        animation.duration(0);\n      }\n      animation.fill('both');\n    })();\n  }\n  startAnimation(shouldOpen, animated) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const isReversed = !shouldOpen;\n      const mode = getIonMode(_this6);\n      const easing = mode === 'ios' ? iosEasing : mdEasing;\n      const easingReverse = mode === 'ios' ? iosEasingReverse : mdEasingReverse;\n      const ani = _this6.animation.direction(isReversed ? 'reverse' : 'normal').easing(isReversed ? easingReverse : easing);\n      if (animated) {\n        yield ani.play();\n      } else {\n        ani.play({\n          sync: true\n        });\n      }\n      /**\n       * We run this after the play invocation\n       * instead of using ani.onFinish so that\n       * multiple onFinish callbacks do not get\n       * run if an animation is played, stopped,\n       * and then played again.\n       */\n      if (ani.getDirection() === 'reverse') {\n        ani.direction('normal');\n      }\n    })();\n  }\n  _isActive() {\n    return !this.disabled && !this.isPaneVisible;\n  }\n  canSwipe() {\n    return this.swipeGesture && !this.isAnimating && this._isActive();\n  }\n  canStart(detail) {\n    // Do not allow swipe gesture if a modal is open\n    const isModalPresented = !!document.querySelector('ion-modal.show-modal');\n    if (isModalPresented || !this.canSwipe()) {\n      return false;\n    }\n    if (this._isOpen) {\n      return true;\n    } else if (menuController._getOpenSync()) {\n      return false;\n    }\n    return checkEdgeSide(window, detail.currentX, this.isEndSide, this.maxEdgeStart);\n  }\n  onWillStart() {\n    this.beforeAnimation(!this._isOpen);\n    return this.loadAnimation();\n  }\n  onStart() {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    // the cloned animation should not use an easing curve during seek\n    this.animation.progressStart(true, this._isOpen ? 1 : 0);\n  }\n  onMove(detail) {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    const delta = computeDelta(detail.deltaX, this._isOpen, this.isEndSide);\n    const stepValue = delta / this.width;\n    this.animation.progressStep(this._isOpen ? 1 - stepValue : stepValue);\n  }\n  onEnd(detail) {\n    if (!this.isAnimating || !this.animation) {\n      assert(false, 'isAnimating has to be true');\n      return;\n    }\n    const isOpen = this._isOpen;\n    const isEndSide = this.isEndSide;\n    const delta = computeDelta(detail.deltaX, isOpen, isEndSide);\n    const width = this.width;\n    const stepValue = delta / width;\n    const velocity = detail.velocityX;\n    const z = width / 2.0;\n    const shouldCompleteRight = velocity >= 0 && (velocity > 0.2 || detail.deltaX > z);\n    const shouldCompleteLeft = velocity <= 0 && (velocity < -0.2 || detail.deltaX < -z);\n    const shouldComplete = isOpen ? isEndSide ? shouldCompleteRight : shouldCompleteLeft : isEndSide ? shouldCompleteLeft : shouldCompleteRight;\n    let shouldOpen = !isOpen && shouldComplete;\n    if (isOpen && !shouldComplete) {\n      shouldOpen = true;\n    }\n    this.lastOnEnd = detail.currentTime;\n    // Account for rounding errors in JS\n    let newStepValue = shouldComplete ? 0.001 : -0.001;\n    /**\n     * stepValue can sometimes return a negative\n     * value, but you can't have a negative time value\n     * for the cubic bezier curve (at least with web animations)\n     */\n    const adjustedStepValue = stepValue < 0 ? 0.01 : stepValue;\n    /**\n     * Animation will be reversed here, so need to\n     * reverse the easing curve as well\n     *\n     * Additionally, we need to account for the time relative\n     * to the new easing curve, as `stepValue` is going to be given\n     * in terms of a linear curve.\n     */\n    newStepValue += getTimeGivenProgression([0, 0], [0.4, 0], [0.6, 1], [1, 1], clamp(0, adjustedStepValue, 0.9999))[0] || 0;\n    const playTo = this._isOpen ? !shouldComplete : shouldComplete;\n    this.animation.easing('cubic-bezier(0.4, 0.0, 0.6, 1)').onFinish(() => this.afterAnimation(shouldOpen), {\n      oneTimeCallback: true\n    }).progressEnd(playTo ? 1 : 0, this._isOpen ? 1 - newStepValue : newStepValue, 300);\n  }\n  beforeAnimation(shouldOpen) {\n    assert(!this.isAnimating, '_before() should not be called while animating');\n    // this places the menu into the correct location before it animates in\n    // this css class doesn't actually kick off any animations\n    this.el.classList.add(SHOW_MENU);\n    /**\n     * We add a tabindex here so that focus trapping\n     * still works even if the menu does not have\n     * any focusable elements slotted inside. The\n     * focus trapping utility will fallback to focusing\n     * the menu so focus does not leave when the menu\n     * is open.\n     */\n    this.el.setAttribute('tabindex', '0');\n    if (this.backdropEl) {\n      this.backdropEl.classList.add(SHOW_BACKDROP);\n    }\n    // add css class and hide content behind menu from screen readers\n    if (this.contentEl) {\n      this.contentEl.classList.add(MENU_CONTENT_OPEN);\n      /**\n       * When the menu is open and overlaying the main\n       * content, the main content should not be announced\n       * by the screenreader as the menu is the main\n       * focus. This is useful with screenreaders that have\n       * \"read from top\" gestures that read the entire\n       * page from top to bottom when activated.\n       * This should be done before the animation starts\n       * so that users cannot accidentally scroll\n       * the content while dragging a menu open.\n       */\n      this.contentEl.setAttribute('aria-hidden', 'true');\n    }\n    this.blocker.block();\n    this.isAnimating = true;\n    if (shouldOpen) {\n      this.ionWillOpen.emit();\n    } else {\n      this.ionWillClose.emit();\n    }\n  }\n  afterAnimation(isOpen) {\n    var _a;\n    // keep opening/closing the menu disabled for a touch more yet\n    // only add listeners/css if it's enabled and isOpen\n    // and only remove listeners/css if it's not open\n    // emit opened/closed events\n    this._isOpen = isOpen;\n    this.isAnimating = false;\n    if (!this._isOpen) {\n      this.blocker.unblock();\n    }\n    if (isOpen) {\n      // emit open event\n      this.ionDidOpen.emit();\n      /**\n       * Move focus to the menu to prepare focus trapping, as long as\n       * it isn't already focused. Use the host element instead of the\n       * first descendant to avoid the scroll position jumping around.\n       */\n      const focusedMenu = (_a = document.activeElement) === null || _a === void 0 ? void 0 : _a.closest('ion-menu');\n      if (focusedMenu !== this.el) {\n        this.el.focus();\n      }\n      // start focus trapping\n      document.addEventListener('focus', this.handleFocus, true);\n    } else {\n      // remove css classes and unhide content from screen readers\n      this.el.classList.remove(SHOW_MENU);\n      /**\n       * Remove tabindex from the menu component\n       * so that is cannot be tabbed to.\n       */\n      this.el.removeAttribute('tabindex');\n      if (this.contentEl) {\n        this.contentEl.classList.remove(MENU_CONTENT_OPEN);\n        /**\n         * Remove aria-hidden so screen readers\n         * can announce the main content again\n         * now that the menu is not the main focus.\n         */\n        this.contentEl.removeAttribute('aria-hidden');\n      }\n      if (this.backdropEl) {\n        this.backdropEl.classList.remove(SHOW_BACKDROP);\n      }\n      if (this.animation) {\n        this.animation.stop();\n      }\n      // emit close event\n      this.ionDidClose.emit();\n      // undo focus trapping so multiple menus don't collide\n      document.removeEventListener('focus', this.handleFocus, true);\n    }\n  }\n  updateState() {\n    const isActive = this._isActive();\n    if (this.gesture) {\n      this.gesture.enable(isActive && this.swipeGesture);\n    }\n    /**\n     * If the menu is disabled but it is still open\n     * then we should close the menu immediately.\n     * Additionally, if the menu is in the process\n     * of animating {open, close} and the menu is disabled\n     * then it should still be closed immediately.\n     */\n    if (!isActive) {\n      /**\n       * It is possible to disable the menu while\n       * it is mid-animation. When this happens, we\n       * need to set the operationCancelled flag\n       * so that this._setOpen knows to return false\n       * and not run the \"afterAnimation\" callback.\n       */\n      if (this.isAnimating) {\n        this.operationCancelled = true;\n      }\n      /**\n       * If the menu is disabled then we should\n       * forcibly close the menu even if it is open.\n       */\n      this.afterAnimation(false);\n    }\n  }\n  render() {\n    const {\n      type,\n      disabled,\n      isPaneVisible,\n      inheritedAttributes,\n      side\n    } = this;\n    const mode = getIonMode(this);\n    /**\n     * If the Close Watcher is enabled then\n     * the ionBackButton listener in the menu controller\n     * will handle closing the menu when Escape is pressed.\n     */\n    return h(Host, {\n      key: '7443f67fbe5122052025bab862136044fc942401',\n      onKeyDown: shouldUseCloseWatcher() ? null : this.onKeydown,\n      role: \"navigation\",\n      \"aria-label\": inheritedAttributes['aria-label'] || 'menu',\n      class: {\n        [mode]: true,\n        [`menu-type-${type}`]: true,\n        'menu-enabled': !disabled,\n        [`menu-side-${side}`]: true,\n        'menu-pane-visible': isPaneVisible\n      }\n    }, h(\"div\", {\n      key: '45c7d37ace20f663a4bea89cb38bbc798f88dfbd',\n      class: \"menu-inner\",\n      part: \"container\",\n      ref: el => this.menuInnerEl = el\n    }, h(\"slot\", {\n      key: '975437a5d4029cc200b6dbc2d47a16b4318c00aa'\n    })), h(\"ion-backdrop\", {\n      key: 'acc8a1f5dc1b1e2a34757bf797e794017f545bdc',\n      ref: el => this.backdropEl = el,\n      class: \"menu-backdrop\",\n      tappable: false,\n      stopPropagation: false,\n      part: \"backdrop\"\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"type\": [\"typeChanged\"],\n      \"disabled\": [\"disabledChanged\"],\n      \"side\": [\"sideChanged\"],\n      \"swipeGesture\": [\"swipeGestureChanged\"]\n    };\n  }\n};\nconst computeDelta = (deltaX, isOpen, isEndSide) => {\n  return Math.max(0, isOpen !== isEndSide ? -deltaX : deltaX);\n};\nconst checkEdgeSide = (win, posX, isEndSide, maxEdgeStart) => {\n  if (isEndSide) {\n    return posX >= win.innerWidth - maxEdgeStart;\n  } else {\n    return posX <= maxEdgeStart;\n  }\n};\nconst SHOW_MENU = 'show-menu';\nconst SHOW_BACKDROP = 'show-backdrop';\nconst MENU_CONTENT_OPEN = 'menu-content-open';\nMenu.style = {\n  ios: IonMenuIosStyle0,\n  md: IonMenuMdStyle0\n};\n\n// Given a menu, return whether or not the menu toggle should be visible\nconst updateVisibility = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(function* (menu) {\n    const menuEl = yield menuController.get(menu);\n    return !!(menuEl && (yield menuEl.isActive()));\n  });\n  return function updateVisibility(_x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst menuButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #3880ff);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst IonMenuButtonIosStyle0 = menuButtonIosCss;\nconst menuButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}\";\nconst IonMenuButtonMdStyle0 = menuButtonMdCss;\nconst MenuButton = class {\n  constructor(hostRef) {\n    var _this7 = this;\n    registerInstance(this, hostRef);\n    this.inheritedAttributes = {};\n    this.onClick = /*#__PURE__*/_asyncToGenerator(function* () {\n      return menuController.toggle(_this7.menu);\n    });\n    this.visible = false;\n    this.color = undefined;\n    this.disabled = false;\n    this.menu = undefined;\n    this.autoHide = true;\n    this.type = 'button';\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  componentDidLoad() {\n    this.visibilityChanged();\n  }\n  visibilityChanged() {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      _this8.visible = yield updateVisibility(_this8.menu);\n    })();\n  }\n  render() {\n    const {\n      color,\n      disabled,\n      inheritedAttributes\n    } = this;\n    const mode = getIonMode(this);\n    const menuIcon = config.get('menuIcon', mode === 'ios' ? menuOutline : menuSharp);\n    const hidden = this.autoHide && !this.visible;\n    const attrs = {\n      type: this.type\n    };\n    const ariaLabel = inheritedAttributes['aria-label'] || 'menu';\n    return h(Host, {\n      key: '7a4543dfcbf559f0d3a473683f8e0bd1d4c3542a',\n      onClick: this.onClick,\n      \"aria-disabled\": disabled ? 'true' : null,\n      \"aria-hidden\": hidden ? 'true' : null,\n      class: createColorClasses(color, {\n        [mode]: true,\n        button: true,\n        // ion-buttons target .button\n        'menu-button-hidden': hidden,\n        'menu-button-disabled': disabled,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': true,\n        'ion-focusable': true\n      })\n    }, h(\"button\", Object.assign({\n      key: '2b6944dc130fa765ac7559077254555583529ec3'\n    }, attrs, {\n      disabled: disabled,\n      class: \"button-native\",\n      part: \"native\",\n      \"aria-label\": ariaLabel\n    }), h(\"span\", {\n      key: 'b4d1006bec8c9e761c64ae3e2fb64848dfc30307',\n      class: \"button-inner\"\n    }, h(\"slot\", {\n      key: 'eaf1d57cd2e841c70095821576c52062dc76500b'\n    }, h(\"ion-icon\", {\n      key: '105ddb806aae2e6add6cb3989fd4a5cf5ee7d952',\n      part: \"icon\",\n      icon: menuIcon,\n      mode: mode,\n      lazy: false,\n      \"aria-hidden\": \"true\"\n    }))), mode === 'md' && h(\"ion-ripple-effect\", {\n      key: '8a312aab747de2bdd6adee74fb0bfcbbde12c191',\n      type: \"unbounded\"\n    })));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nMenuButton.style = {\n  ios: IonMenuButtonIosStyle0,\n  md: IonMenuButtonMdStyle0\n};\nconst menuToggleCss = \":host(.menu-toggle-hidden){display:none}\";\nconst IonMenuToggleStyle0 = menuToggleCss;\nconst MenuToggle = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.onClick = () => {\n      return menuController.toggle(this.menu);\n    };\n    this.visible = false;\n    this.menu = undefined;\n    this.autoHide = true;\n  }\n  connectedCallback() {\n    this.visibilityChanged();\n  }\n  visibilityChanged() {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      _this9.visible = yield updateVisibility(_this9.menu);\n    })();\n  }\n  render() {\n    const mode = getIonMode(this);\n    const hidden = this.autoHide && !this.visible;\n    return h(Host, {\n      key: '94a0815a634c6fb1991854bfbcf5b2b4b61d7710',\n      onClick: this.onClick,\n      \"aria-hidden\": hidden ? 'true' : null,\n      class: {\n        [mode]: true,\n        'menu-toggle-hidden': hidden\n      }\n    }, h(\"slot\", {\n      key: 'f3ac6d17d5421390ab05f3f31ad00ec4f2ca5c7c'\n    }));\n  }\n};\nMenuToggle.style = IonMenuToggleStyle0;\nexport { Menu as ion_menu, MenuButton as ion_menu_button, MenuToggle as ion_menu_toggle };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "g", "getTimeGivenProgression", "G", "GESTURE_CONTROLLER", "shouldUseCloseWatcher", "p", "isEndSide", "i", "inheritAriaAttributes", "o", "assert", "l", "clamp", "m", "menuController", "getPresentedOverlay", "c", "config", "b", "getIonMode", "createColorClasses", "hostContext", "u", "menuOutline", "v", "menuSharp", "menuIosCss", "IonMenuIosStyle0", "menuMdCss", "IonMenuMdStyle0", "iosEasing", "mdEasing", "iosEasingReverse", "mdEasingReverse", "focusableQueryString", "<PERSON><PERSON>", "constructor", "hostRef", "ionWillOpen", "ionWillClose", "ionDidOpen", "ionDidClose", "ionMenuChange", "lastOnEnd", "blocker", "createBlocker", "disableScroll", "didLoad", "operationCancelled", "isAnimating", "_isOpen", "inheritedAttributes", "handleFocus", "ev", "lastOverlay", "document", "contains", "el", "trapKeyboardFocus", "isPaneVisible", "contentId", "undefined", "menuId", "type", "disabled", "side", "swipeGesture", "maxEdgeStart", "typeChanged", "oldType", "contentEl", "classList", "remove", "add", "removeAttribute", "menuInnerEl", "animation", "disabled<PERSON><PERSON>ed", "updateState", "emit", "open", "sideChanged", "swipeGestureChanged", "connectedCallback", "_this", "_asyncToGenerator", "customElements", "whenDefined", "get", "content", "getElementById", "console", "error", "_register", "menuChanged", "gesture", "createGesture", "<PERSON><PERSON><PERSON>", "gesturePriority", "threshold", "blurOnStart", "canStart", "onWillStart", "onStart", "onMove", "onEnd", "componentWillLoad", "componentDidLoad", "_this2", "disconnectedCallback", "_this3", "close", "destroy", "_unregister", "onSplitPaneChanged", "target", "closestSplitPane", "closest", "detail", "isPane", "onBackdropClick", "timeStamp", "shouldClose", "<PERSON><PERSON><PERSON>", "includes", "preventDefault", "stopPropagation", "onKeydown", "key", "isOpen", "Promise", "resolve", "isActive", "_isActive", "animated", "<PERSON><PERSON><PERSON>", "toggle", "shouldOpen", "_setOpen", "focusFirstDescendant", "firstInput", "querySelector", "focus", "focusLastDescendant", "inputs", "Array", "from", "querySelectorAll", "lastInput", "length", "doc", "lastFocus", "activeElement", "_x", "_this4", "beforeAnimation", "loadAnimation", "startAnimation", "afterAnimation", "apply", "arguments", "_this5", "width", "offsetWidth", "isEndSide$1", "_createAnimation", "getBoolean", "duration", "fill", "_this6", "isReversed", "mode", "easing", "easingReverse", "ani", "direction", "play", "sync", "getDirection", "canSwipe", "isModalPresented", "_getOpenSync", "checkEdgeSide", "window", "currentX", "progressStart", "delta", "computeDelta", "deltaX", "<PERSON><PERSON><PERSON><PERSON>", "progressStep", "velocity", "velocityX", "z", "shouldCompleteRight", "shouldCompleteLeft", "shouldComplete", "currentTime", "newStepValue", "adjustedStepValue", "playTo", "onFinish", "oneTimeCallback", "progressEnd", "SHOW_MENU", "setAttribute", "backdropEl", "SHOW_BACKDROP", "MENU_CONTENT_OPEN", "block", "_a", "unblock", "focusedMenu", "addEventListener", "stop", "removeEventListener", "enable", "render", "onKeyDown", "role", "class", "part", "ref", "tappable", "watchers", "Math", "max", "win", "posX", "innerWidth", "style", "ios", "md", "updateVisibility", "_ref", "menu", "menuEl", "_x2", "menuButtonIosCss", "IonMenuButtonIosStyle0", "menuButtonMdCss", "IonMenuButtonMdStyle0", "MenuButton", "_this7", "onClick", "visible", "color", "autoHide", "visibilityChanged", "_this8", "menuIcon", "hidden", "attrs", "aria<PERSON><PERSON><PERSON>", "button", "Object", "assign", "icon", "lazy", "menuToggleCss", "IonMenuToggleStyle0", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_this9", "ion_menu", "ion_menu_button", "ion_menu_toggle"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/ion-menu_3.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { g as getTimeGivenProgression } from './cubic-bezier-fe2083dc.js';\nimport { G as GESTURE_CONTROLLER } from './gesture-controller-1bf57181.js';\nimport { shouldUseCloseWatcher } from './hardware-back-button-6107a37c.js';\nimport { p as isEndSide, i as inheritAriaAttributes, o as assert, l as clamp } from './helpers-be245865.js';\nimport { m as menuController } from './index-6e05b96e.js';\nimport { o as getPresentedOverlay } from './overlays-b874c3c3.js';\nimport { c as config, b as getIonMode } from './ionic-global-94f25d1b.js';\nimport { c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { u as menuOutline, v as menuSharp } from './index-f7dc70ba.js';\nimport './index-a5d50daf.js';\nimport './index-9b0d46f4.js';\nimport './animation-6a0c5338.js';\nimport './framework-delegate-ed4ba327.js';\n\nconst menuIosCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}}@supports not (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{left:0;right:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{left:unset;right:unset;left:auto;right:0}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{left:unset;right:unset;left:auto;right:0}}}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}}@supports not (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{left:auto;right:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{left:unset;right:unset;left:0;right:auto}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{left:unset;right:unset;left:0;right:auto}}}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){width:var(--width);min-width:var(--min-width);max-width:var(--max-width)}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-type-push){z-index:1000}:host(.menu-type-push) .show-backdrop{display:block}\";\nconst IonMenuIosStyle0 = menuIosCss;\n\nconst menuMdCss = \":host{--width:304px;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--background:var(--ion-background-color, #fff);left:0;right:0;top:0;bottom:0;display:none;position:absolute;contain:strict}:host(.show-menu){display:block}.menu-inner{-webkit-transform:translateX(-9999px);transform:translateX(-9999px);display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;-ms-flex-pack:justify;justify-content:space-between;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);contain:strict}:host(.menu-side-start) .menu-inner{--ion-safe-area-right:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{inset-inline-start:0;inset-inline-end:auto}}@supports not (inset-inline-start: 0){:host(.menu-side-start) .menu-inner{left:0;right:auto}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{left:unset;right:unset;left:auto;right:0}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{left:unset;right:unset;left:auto;right:0}}}:host-context([dir=rtl]):host(.menu-side-start) .menu-inner,:host-context([dir=rtl]).menu-side-start .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}@supports selector(:dir(rtl)){:host(.menu-side-start:dir(rtl)) .menu-inner{--ion-safe-area-right:unset;--ion-safe-area-left:0px}}:host(.menu-side-end) .menu-inner{--ion-safe-area-left:0px;top:0;bottom:0}@supports (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{inset-inline-start:auto;inset-inline-end:0}}@supports not (inset-inline-start: 0){:host(.menu-side-end) .menu-inner{left:auto;right:0}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{left:unset;right:unset;left:0;right:auto}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{left:unset;right:unset;left:0;right:auto}}}:host-context([dir=rtl]):host(.menu-side-end) .menu-inner,:host-context([dir=rtl]).menu-side-end .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}@supports selector(:dir(rtl)){:host(.menu-side-end:dir(rtl)) .menu-inner{--ion-safe-area-left:unset;--ion-safe-area-right:0px}}ion-backdrop{display:none;opacity:0.01;z-index:-1}@media (max-width: 340px){.menu-inner{--width:264px}}:host(.menu-type-reveal){z-index:0}:host(.menu-type-reveal.show-menu) .menu-inner{-webkit-transform:translate3d(0,  0,  0);transform:translate3d(0,  0,  0)}:host(.menu-type-overlay){z-index:1000}:host(.menu-type-overlay) .show-backdrop{display:block;cursor:pointer}:host(.menu-pane-visible){width:var(--width);min-width:var(--min-width);max-width:var(--max-width)}:host(.menu-pane-visible) .menu-inner{left:0;right:0;width:auto;-webkit-transform:none;transform:none;-webkit-box-shadow:none;box-shadow:none}:host(.menu-pane-visible) ion-backdrop{display:hidden !important}:host(.menu-type-overlay) .menu-inner{-webkit-box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18);box-shadow:4px 0px 16px rgba(0, 0, 0, 0.18)}\";\nconst IonMenuMdStyle0 = menuMdCss;\n\nconst iosEasing = 'cubic-bezier(0.32,0.72,0,1)';\nconst mdEasing = 'cubic-bezier(0.0,0.0,0.2,1)';\nconst iosEasingReverse = 'cubic-bezier(1, 0, 0.68, 0.28)';\nconst mdEasingReverse = 'cubic-bezier(0.4, 0, 0.6, 1)';\nconst focusableQueryString = '[tabindex]:not([tabindex^=\"-\"]), input:not([type=hidden]):not([tabindex^=\"-\"]), textarea:not([tabindex^=\"-\"]), button:not([tabindex^=\"-\"]), select:not([tabindex^=\"-\"]), .ion-focusable:not([tabindex^=\"-\"])';\nconst Menu = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionWillOpen = createEvent(this, \"ionWillOpen\", 7);\n        this.ionWillClose = createEvent(this, \"ionWillClose\", 7);\n        this.ionDidOpen = createEvent(this, \"ionDidOpen\", 7);\n        this.ionDidClose = createEvent(this, \"ionDidClose\", 7);\n        this.ionMenuChange = createEvent(this, \"ionMenuChange\", 7);\n        this.lastOnEnd = 0;\n        this.blocker = GESTURE_CONTROLLER.createBlocker({ disableScroll: true });\n        this.didLoad = false;\n        /**\n         * Flag used to determine if an open/close\n         * operation was cancelled. For example, if\n         * an app calls \"menu.open\" then disables the menu\n         * part way through the animation, then this would\n         * be considered a cancelled operation.\n         */\n        this.operationCancelled = false;\n        this.isAnimating = false;\n        this._isOpen = false;\n        this.inheritedAttributes = {};\n        this.handleFocus = (ev) => {\n            /**\n             * Overlays have their own focus trapping listener\n             * so we do not want the two listeners to conflict\n             * with each other. If the top-most overlay that is\n             * open does not contain this ion-menu, then ion-menu's\n             * focus trapping should not run.\n             */\n            const lastOverlay = getPresentedOverlay(document);\n            if (lastOverlay && !lastOverlay.contains(this.el)) {\n                return;\n            }\n            this.trapKeyboardFocus(ev, document);\n        };\n        this.isPaneVisible = false;\n        this.isEndSide = false;\n        this.contentId = undefined;\n        this.menuId = undefined;\n        this.type = undefined;\n        this.disabled = false;\n        this.side = 'start';\n        this.swipeGesture = true;\n        this.maxEdgeStart = 50;\n    }\n    typeChanged(type, oldType) {\n        const contentEl = this.contentEl;\n        if (contentEl) {\n            if (oldType !== undefined) {\n                contentEl.classList.remove(`menu-content-${oldType}`);\n            }\n            contentEl.classList.add(`menu-content-${type}`);\n            contentEl.removeAttribute('style');\n        }\n        if (this.menuInnerEl) {\n            // Remove effects of previous animations\n            this.menuInnerEl.removeAttribute('style');\n        }\n        this.animation = undefined;\n    }\n    disabledChanged() {\n        this.updateState();\n        this.ionMenuChange.emit({\n            disabled: this.disabled,\n            open: this._isOpen,\n        });\n    }\n    sideChanged() {\n        this.isEndSide = isEndSide(this.side);\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        this.animation = undefined;\n    }\n    swipeGestureChanged() {\n        this.updateState();\n    }\n    async connectedCallback() {\n        // TODO: connectedCallback is fired in CE build\n        // before WC is defined. This needs to be fixed in Stencil.\n        if (typeof customElements !== 'undefined' && customElements != null) {\n            await customElements.whenDefined('ion-menu');\n        }\n        if (this.type === undefined) {\n            this.type = config.get('menuType', 'overlay');\n        }\n        const content = this.contentId !== undefined ? document.getElementById(this.contentId) : null;\n        if (content === null) {\n            console.error('Menu: must have a \"content\" element to listen for drag events on.');\n            return;\n        }\n        if (this.el.contains(content)) {\n            console.error(`Menu: \"contentId\" should refer to the main view's ion-content, not the ion-content inside of the ion-menu.`);\n        }\n        this.contentEl = content;\n        // add menu's content classes\n        content.classList.add('menu-content');\n        this.typeChanged(this.type, undefined);\n        this.sideChanged();\n        // register this menu with the app's menu controller\n        menuController._register(this);\n        this.menuChanged();\n        this.gesture = (await import('./index-2cf77112.js')).createGesture({\n            el: document,\n            gestureName: 'menu-swipe',\n            gesturePriority: 30,\n            threshold: 10,\n            blurOnStart: true,\n            canStart: (ev) => this.canStart(ev),\n            onWillStart: () => this.onWillStart(),\n            onStart: () => this.onStart(),\n            onMove: (ev) => this.onMove(ev),\n            onEnd: (ev) => this.onEnd(ev),\n        });\n        this.updateState();\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    async componentDidLoad() {\n        this.didLoad = true;\n        this.menuChanged();\n        this.updateState();\n    }\n    menuChanged() {\n        /**\n         * Inform dependent components such as ion-menu-button\n         * that the menu is ready. Note that we only want to do this\n         * once the menu has been rendered which is why we check for didLoad.\n         */\n        if (this.didLoad) {\n            this.ionMenuChange.emit({ disabled: this.disabled, open: this._isOpen });\n        }\n    }\n    async disconnectedCallback() {\n        /**\n         * The menu should be closed when it is\n         * unmounted from the DOM.\n         * This is an async call, so we need to wait for\n         * this to finish otherwise contentEl\n         * will not have MENU_CONTENT_OPEN removed.\n         */\n        await this.close(false);\n        this.blocker.destroy();\n        menuController._unregister(this);\n        if (this.animation) {\n            this.animation.destroy();\n        }\n        if (this.gesture) {\n            this.gesture.destroy();\n            this.gesture = undefined;\n        }\n        this.animation = undefined;\n        this.contentEl = undefined;\n    }\n    onSplitPaneChanged(ev) {\n        const { target } = ev;\n        const closestSplitPane = this.el.closest('ion-split-pane');\n        /**\n         * Menu listens on the body for \"ionSplitPaneVisible\".\n         * However, this means the callback will run any time\n         * a SplitPane changes visibility. As a result, we only want\n         * Menu's visibility state to update if its parent SplitPane\n         * changes visibility.\n         */\n        if (target !== closestSplitPane) {\n            return;\n        }\n        this.isPaneVisible = ev.detail.isPane(this.el);\n        this.updateState();\n    }\n    onBackdropClick(ev) {\n        // TODO(FW-2832): type (CustomEvent triggers errors which should be sorted)\n        if (this._isOpen && this.lastOnEnd < ev.timeStamp - 100) {\n            const shouldClose = ev.composedPath ? !ev.composedPath().includes(this.menuInnerEl) : false;\n            if (shouldClose) {\n                ev.preventDefault();\n                ev.stopPropagation();\n                this.close();\n            }\n        }\n    }\n    onKeydown(ev) {\n        if (ev.key === 'Escape') {\n            this.close();\n        }\n    }\n    /**\n     * Returns `true` is the menu is open.\n     */\n    isOpen() {\n        return Promise.resolve(this._isOpen);\n    }\n    /**\n     * Returns `true` is the menu is active.\n     *\n     * A menu is active when it can be opened or closed, meaning it's enabled\n     * and it's not part of a `ion-split-pane`.\n     */\n    isActive() {\n        return Promise.resolve(this._isActive());\n    }\n    /**\n     * Opens the menu. If the menu is already open or it can't be opened,\n     * it returns `false`.\n     */\n    open(animated = true) {\n        return this.setOpen(true, animated);\n    }\n    /**\n     * Closes the menu. If the menu is already closed or it can't be closed,\n     * it returns `false`.\n     */\n    close(animated = true) {\n        return this.setOpen(false, animated);\n    }\n    /**\n     * Toggles the menu. If the menu is already open, it will try to close, otherwise it will try to open it.\n     * If the operation can't be completed successfully, it returns `false`.\n     */\n    toggle(animated = true) {\n        return this.setOpen(!this._isOpen, animated);\n    }\n    /**\n     * Opens or closes the button.\n     * If the operation can't be completed successfully, it returns `false`.\n     */\n    setOpen(shouldOpen, animated = true) {\n        return menuController._setOpen(this, shouldOpen, animated);\n    }\n    focusFirstDescendant() {\n        const { el } = this;\n        const firstInput = el.querySelector(focusableQueryString);\n        if (firstInput) {\n            firstInput.focus();\n        }\n        else {\n            el.focus();\n        }\n    }\n    focusLastDescendant() {\n        const { el } = this;\n        const inputs = Array.from(el.querySelectorAll(focusableQueryString));\n        const lastInput = inputs.length > 0 ? inputs[inputs.length - 1] : null;\n        if (lastInput) {\n            lastInput.focus();\n        }\n        else {\n            el.focus();\n        }\n    }\n    trapKeyboardFocus(ev, doc) {\n        const target = ev.target;\n        if (!target) {\n            return;\n        }\n        /**\n         * If the target is inside the menu contents, let the browser\n         * focus as normal and keep a log of the last focused element.\n         */\n        if (this.el.contains(target)) {\n            this.lastFocus = target;\n        }\n        else {\n            /**\n             * Otherwise, we are about to have focus go out of the menu.\n             * Wrap the focus to either the first or last element.\n             */\n            /**\n             * Once we call `focusFirstDescendant`, another focus event\n             * will fire, which will cause `lastFocus` to be updated\n             * before we can run the code after that. We cache the value\n             * here to avoid that.\n             */\n            this.focusFirstDescendant();\n            /**\n             * If the cached last focused element is the same as the now-\n             * active element, that means the user was on the first element\n             * already and pressed Shift + Tab, so we need to wrap to the\n             * last descendant.\n             */\n            if (this.lastFocus === doc.activeElement) {\n                this.focusLastDescendant();\n            }\n        }\n    }\n    async _setOpen(shouldOpen, animated = true) {\n        // If the menu is disabled or it is currently being animated, let's do nothing\n        if (!this._isActive() || this.isAnimating || shouldOpen === this._isOpen) {\n            return false;\n        }\n        this.beforeAnimation(shouldOpen);\n        await this.loadAnimation();\n        await this.startAnimation(shouldOpen, animated);\n        /**\n         * If the animation was cancelled then\n         * return false because the operation\n         * did not succeed.\n         */\n        if (this.operationCancelled) {\n            this.operationCancelled = false;\n            return false;\n        }\n        this.afterAnimation(shouldOpen);\n        return true;\n    }\n    async loadAnimation() {\n        // Menu swipe animation takes the menu's inner width as parameter,\n        // If `offsetWidth` changes, we need to create a new animation.\n        const width = this.menuInnerEl.offsetWidth;\n        /**\n         * Menu direction animation is calculated based on the document direction.\n         * If the document direction changes, we need to create a new animation.\n         */\n        const isEndSide$1 = isEndSide(this.side);\n        if (width === this.width && this.animation !== undefined && isEndSide$1 === this.isEndSide) {\n            return;\n        }\n        this.width = width;\n        this.isEndSide = isEndSide$1;\n        // Destroy existing animation\n        if (this.animation) {\n            this.animation.destroy();\n            this.animation = undefined;\n        }\n        // Create new animation\n        const animation = (this.animation = await menuController._createAnimation(this.type, this));\n        if (!config.getBoolean('animated', true)) {\n            animation.duration(0);\n        }\n        animation.fill('both');\n    }\n    async startAnimation(shouldOpen, animated) {\n        const isReversed = !shouldOpen;\n        const mode = getIonMode(this);\n        const easing = mode === 'ios' ? iosEasing : mdEasing;\n        const easingReverse = mode === 'ios' ? iosEasingReverse : mdEasingReverse;\n        const ani = this.animation\n            .direction(isReversed ? 'reverse' : 'normal')\n            .easing(isReversed ? easingReverse : easing);\n        if (animated) {\n            await ani.play();\n        }\n        else {\n            ani.play({ sync: true });\n        }\n        /**\n         * We run this after the play invocation\n         * instead of using ani.onFinish so that\n         * multiple onFinish callbacks do not get\n         * run if an animation is played, stopped,\n         * and then played again.\n         */\n        if (ani.getDirection() === 'reverse') {\n            ani.direction('normal');\n        }\n    }\n    _isActive() {\n        return !this.disabled && !this.isPaneVisible;\n    }\n    canSwipe() {\n        return this.swipeGesture && !this.isAnimating && this._isActive();\n    }\n    canStart(detail) {\n        // Do not allow swipe gesture if a modal is open\n        const isModalPresented = !!document.querySelector('ion-modal.show-modal');\n        if (isModalPresented || !this.canSwipe()) {\n            return false;\n        }\n        if (this._isOpen) {\n            return true;\n        }\n        else if (menuController._getOpenSync()) {\n            return false;\n        }\n        return checkEdgeSide(window, detail.currentX, this.isEndSide, this.maxEdgeStart);\n    }\n    onWillStart() {\n        this.beforeAnimation(!this._isOpen);\n        return this.loadAnimation();\n    }\n    onStart() {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        // the cloned animation should not use an easing curve during seek\n        this.animation.progressStart(true, this._isOpen ? 1 : 0);\n    }\n    onMove(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const delta = computeDelta(detail.deltaX, this._isOpen, this.isEndSide);\n        const stepValue = delta / this.width;\n        this.animation.progressStep(this._isOpen ? 1 - stepValue : stepValue);\n    }\n    onEnd(detail) {\n        if (!this.isAnimating || !this.animation) {\n            assert(false, 'isAnimating has to be true');\n            return;\n        }\n        const isOpen = this._isOpen;\n        const isEndSide = this.isEndSide;\n        const delta = computeDelta(detail.deltaX, isOpen, isEndSide);\n        const width = this.width;\n        const stepValue = delta / width;\n        const velocity = detail.velocityX;\n        const z = width / 2.0;\n        const shouldCompleteRight = velocity >= 0 && (velocity > 0.2 || detail.deltaX > z);\n        const shouldCompleteLeft = velocity <= 0 && (velocity < -0.2 || detail.deltaX < -z);\n        const shouldComplete = isOpen\n            ? isEndSide\n                ? shouldCompleteRight\n                : shouldCompleteLeft\n            : isEndSide\n                ? shouldCompleteLeft\n                : shouldCompleteRight;\n        let shouldOpen = !isOpen && shouldComplete;\n        if (isOpen && !shouldComplete) {\n            shouldOpen = true;\n        }\n        this.lastOnEnd = detail.currentTime;\n        // Account for rounding errors in JS\n        let newStepValue = shouldComplete ? 0.001 : -0.001;\n        /**\n         * stepValue can sometimes return a negative\n         * value, but you can't have a negative time value\n         * for the cubic bezier curve (at least with web animations)\n         */\n        const adjustedStepValue = stepValue < 0 ? 0.01 : stepValue;\n        /**\n         * Animation will be reversed here, so need to\n         * reverse the easing curve as well\n         *\n         * Additionally, we need to account for the time relative\n         * to the new easing curve, as `stepValue` is going to be given\n         * in terms of a linear curve.\n         */\n        newStepValue +=\n            getTimeGivenProgression([0, 0], [0.4, 0], [0.6, 1], [1, 1], clamp(0, adjustedStepValue, 0.9999))[0] || 0;\n        const playTo = this._isOpen ? !shouldComplete : shouldComplete;\n        this.animation\n            .easing('cubic-bezier(0.4, 0.0, 0.6, 1)')\n            .onFinish(() => this.afterAnimation(shouldOpen), { oneTimeCallback: true })\n            .progressEnd(playTo ? 1 : 0, this._isOpen ? 1 - newStepValue : newStepValue, 300);\n    }\n    beforeAnimation(shouldOpen) {\n        assert(!this.isAnimating, '_before() should not be called while animating');\n        // this places the menu into the correct location before it animates in\n        // this css class doesn't actually kick off any animations\n        this.el.classList.add(SHOW_MENU);\n        /**\n         * We add a tabindex here so that focus trapping\n         * still works even if the menu does not have\n         * any focusable elements slotted inside. The\n         * focus trapping utility will fallback to focusing\n         * the menu so focus does not leave when the menu\n         * is open.\n         */\n        this.el.setAttribute('tabindex', '0');\n        if (this.backdropEl) {\n            this.backdropEl.classList.add(SHOW_BACKDROP);\n        }\n        // add css class and hide content behind menu from screen readers\n        if (this.contentEl) {\n            this.contentEl.classList.add(MENU_CONTENT_OPEN);\n            /**\n             * When the menu is open and overlaying the main\n             * content, the main content should not be announced\n             * by the screenreader as the menu is the main\n             * focus. This is useful with screenreaders that have\n             * \"read from top\" gestures that read the entire\n             * page from top to bottom when activated.\n             * This should be done before the animation starts\n             * so that users cannot accidentally scroll\n             * the content while dragging a menu open.\n             */\n            this.contentEl.setAttribute('aria-hidden', 'true');\n        }\n        this.blocker.block();\n        this.isAnimating = true;\n        if (shouldOpen) {\n            this.ionWillOpen.emit();\n        }\n        else {\n            this.ionWillClose.emit();\n        }\n    }\n    afterAnimation(isOpen) {\n        var _a;\n        // keep opening/closing the menu disabled for a touch more yet\n        // only add listeners/css if it's enabled and isOpen\n        // and only remove listeners/css if it's not open\n        // emit opened/closed events\n        this._isOpen = isOpen;\n        this.isAnimating = false;\n        if (!this._isOpen) {\n            this.blocker.unblock();\n        }\n        if (isOpen) {\n            // emit open event\n            this.ionDidOpen.emit();\n            /**\n             * Move focus to the menu to prepare focus trapping, as long as\n             * it isn't already focused. Use the host element instead of the\n             * first descendant to avoid the scroll position jumping around.\n             */\n            const focusedMenu = (_a = document.activeElement) === null || _a === void 0 ? void 0 : _a.closest('ion-menu');\n            if (focusedMenu !== this.el) {\n                this.el.focus();\n            }\n            // start focus trapping\n            document.addEventListener('focus', this.handleFocus, true);\n        }\n        else {\n            // remove css classes and unhide content from screen readers\n            this.el.classList.remove(SHOW_MENU);\n            /**\n             * Remove tabindex from the menu component\n             * so that is cannot be tabbed to.\n             */\n            this.el.removeAttribute('tabindex');\n            if (this.contentEl) {\n                this.contentEl.classList.remove(MENU_CONTENT_OPEN);\n                /**\n                 * Remove aria-hidden so screen readers\n                 * can announce the main content again\n                 * now that the menu is not the main focus.\n                 */\n                this.contentEl.removeAttribute('aria-hidden');\n            }\n            if (this.backdropEl) {\n                this.backdropEl.classList.remove(SHOW_BACKDROP);\n            }\n            if (this.animation) {\n                this.animation.stop();\n            }\n            // emit close event\n            this.ionDidClose.emit();\n            // undo focus trapping so multiple menus don't collide\n            document.removeEventListener('focus', this.handleFocus, true);\n        }\n    }\n    updateState() {\n        const isActive = this._isActive();\n        if (this.gesture) {\n            this.gesture.enable(isActive && this.swipeGesture);\n        }\n        /**\n         * If the menu is disabled but it is still open\n         * then we should close the menu immediately.\n         * Additionally, if the menu is in the process\n         * of animating {open, close} and the menu is disabled\n         * then it should still be closed immediately.\n         */\n        if (!isActive) {\n            /**\n             * It is possible to disable the menu while\n             * it is mid-animation. When this happens, we\n             * need to set the operationCancelled flag\n             * so that this._setOpen knows to return false\n             * and not run the \"afterAnimation\" callback.\n             */\n            if (this.isAnimating) {\n                this.operationCancelled = true;\n            }\n            /**\n             * If the menu is disabled then we should\n             * forcibly close the menu even if it is open.\n             */\n            this.afterAnimation(false);\n        }\n    }\n    render() {\n        const { type, disabled, isPaneVisible, inheritedAttributes, side } = this;\n        const mode = getIonMode(this);\n        /**\n         * If the Close Watcher is enabled then\n         * the ionBackButton listener in the menu controller\n         * will handle closing the menu when Escape is pressed.\n         */\n        return (h(Host, { key: '7443f67fbe5122052025bab862136044fc942401', onKeyDown: shouldUseCloseWatcher() ? null : this.onKeydown, role: \"navigation\", \"aria-label\": inheritedAttributes['aria-label'] || 'menu', class: {\n                [mode]: true,\n                [`menu-type-${type}`]: true,\n                'menu-enabled': !disabled,\n                [`menu-side-${side}`]: true,\n                'menu-pane-visible': isPaneVisible,\n            } }, h(\"div\", { key: '45c7d37ace20f663a4bea89cb38bbc798f88dfbd', class: \"menu-inner\", part: \"container\", ref: (el) => (this.menuInnerEl = el) }, h(\"slot\", { key: '975437a5d4029cc200b6dbc2d47a16b4318c00aa' })), h(\"ion-backdrop\", { key: 'acc8a1f5dc1b1e2a34757bf797e794017f545bdc', ref: (el) => (this.backdropEl = el), class: \"menu-backdrop\", tappable: false, stopPropagation: false, part: \"backdrop\" })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"type\": [\"typeChanged\"],\n        \"disabled\": [\"disabledChanged\"],\n        \"side\": [\"sideChanged\"],\n        \"swipeGesture\": [\"swipeGestureChanged\"]\n    }; }\n};\nconst computeDelta = (deltaX, isOpen, isEndSide) => {\n    return Math.max(0, isOpen !== isEndSide ? -deltaX : deltaX);\n};\nconst checkEdgeSide = (win, posX, isEndSide, maxEdgeStart) => {\n    if (isEndSide) {\n        return posX >= win.innerWidth - maxEdgeStart;\n    }\n    else {\n        return posX <= maxEdgeStart;\n    }\n};\nconst SHOW_MENU = 'show-menu';\nconst SHOW_BACKDROP = 'show-backdrop';\nconst MENU_CONTENT_OPEN = 'menu-content-open';\nMenu.style = {\n    ios: IonMenuIosStyle0,\n    md: IonMenuMdStyle0\n};\n\n// Given a menu, return whether or not the menu toggle should be visible\nconst updateVisibility = async (menu) => {\n    const menuEl = await menuController.get(menu);\n    return !!(menuEl && (await menuEl.isActive()));\n};\n\nconst menuButtonIosCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.1;--border-radius:4px;--color:var(--ion-color-primary, #3880ff);--padding-start:5px;--padding-end:5px;min-height:32px;font-size:clamp(31px, 1.9375rem, 38.13px)}:host(.ion-activated){opacity:0.4}@media (any-hover: hover){:host(:hover){opacity:0.6}}\";\nconst IonMenuButtonIosStyle0 = menuButtonIosCss;\n\nconst menuButtonMdCss = \":host{--background:transparent;--color-focused:currentColor;--border-radius:initial;--padding-top:0;--padding-bottom:0;color:var(--color);text-align:center;text-decoration:none;text-overflow:ellipsis;text-transform:none;white-space:nowrap;-webkit-font-kerning:none;font-kerning:none}.button-native{border-radius:var(--border-radius);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;border:0;outline:none;background:var(--background);line-height:1;cursor:pointer;overflow:hidden;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:0;-webkit-appearance:none;-moz-appearance:none;appearance:none}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:row nowrap;flex-flow:row nowrap;-ms-flex-negative:0;flex-shrink:0;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:100%;height:100%;min-height:inherit;z-index:1}ion-icon{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;pointer-events:none}:host(.menu-button-hidden){display:none}:host(.menu-button-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:\\\"\\\";opacity:0}@media (any-hover: hover){:host(:hover) .button-native{color:var(--color-hover)}:host(:hover) .button-native::after{background:var(--background-hover);opacity:var(--background-hover-opacity, 0)}}:host(.ion-color) .button-native{color:var(--ion-color-base)}:host(.in-toolbar:not(.in-toolbar-color)){color:var(--ion-toolbar-color, var(--color))}:host{--background-focused:currentColor;--background-focused-opacity:.12;--background-hover:currentColor;--background-hover-opacity:.04;--border-radius:50%;--color:initial;--padding-start:8px;--padding-end:8px;width:3rem;height:3rem;font-size:1.5rem}:host(.ion-color.ion-focused)::after{background:var(--ion-color-base)}@media (any-hover: hover){:host(.ion-color:hover) .button-native::after{background:var(--ion-color-base)}}\";\nconst IonMenuButtonMdStyle0 = menuButtonMdCss;\n\nconst MenuButton = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.inheritedAttributes = {};\n        this.onClick = async () => {\n            return menuController.toggle(this.menu);\n        };\n        this.visible = false;\n        this.color = undefined;\n        this.disabled = false;\n        this.menu = undefined;\n        this.autoHide = true;\n        this.type = 'button';\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    componentDidLoad() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const { color, disabled, inheritedAttributes } = this;\n        const mode = getIonMode(this);\n        const menuIcon = config.get('menuIcon', mode === 'ios' ? menuOutline : menuSharp);\n        const hidden = this.autoHide && !this.visible;\n        const attrs = {\n            type: this.type,\n        };\n        const ariaLabel = inheritedAttributes['aria-label'] || 'menu';\n        return (h(Host, { key: '7a4543dfcbf559f0d3a473683f8e0bd1d4c3542a', onClick: this.onClick, \"aria-disabled\": disabled ? 'true' : null, \"aria-hidden\": hidden ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                button: true, // ion-buttons target .button\n                'menu-button-hidden': hidden,\n                'menu-button-disabled': disabled,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': true,\n                'ion-focusable': true,\n            }) }, h(\"button\", Object.assign({ key: '2b6944dc130fa765ac7559077254555583529ec3' }, attrs, { disabled: disabled, class: \"button-native\", part: \"native\", \"aria-label\": ariaLabel }), h(\"span\", { key: 'b4d1006bec8c9e761c64ae3e2fb64848dfc30307', class: \"button-inner\" }, h(\"slot\", { key: 'eaf1d57cd2e841c70095821576c52062dc76500b' }, h(\"ion-icon\", { key: '105ddb806aae2e6add6cb3989fd4a5cf5ee7d952', part: \"icon\", icon: menuIcon, mode: mode, lazy: false, \"aria-hidden\": \"true\" }))), mode === 'md' && h(\"ion-ripple-effect\", { key: '8a312aab747de2bdd6adee74fb0bfcbbde12c191', type: \"unbounded\" }))));\n    }\n    get el() { return getElement(this); }\n};\nMenuButton.style = {\n    ios: IonMenuButtonIosStyle0,\n    md: IonMenuButtonMdStyle0\n};\n\nconst menuToggleCss = \":host(.menu-toggle-hidden){display:none}\";\nconst IonMenuToggleStyle0 = menuToggleCss;\n\nconst MenuToggle = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.onClick = () => {\n            return menuController.toggle(this.menu);\n        };\n        this.visible = false;\n        this.menu = undefined;\n        this.autoHide = true;\n    }\n    connectedCallback() {\n        this.visibilityChanged();\n    }\n    async visibilityChanged() {\n        this.visible = await updateVisibility(this.menu);\n    }\n    render() {\n        const mode = getIonMode(this);\n        const hidden = this.autoHide && !this.visible;\n        return (h(Host, { key: '94a0815a634c6fb1991854bfbcf5b2b4b61d7710', onClick: this.onClick, \"aria-hidden\": hidden ? 'true' : null, class: {\n                [mode]: true,\n                'menu-toggle-hidden': hidden,\n            } }, h(\"slot\", { key: 'f3ac6d17d5421390ab05f3f31ad00ec4f2ca5c7c' })));\n    }\n};\nMenuToggle.style = IonMenuToggleStyle0;\n\nexport { Menu as ion_menu, MenuButton as ion_menu_button, MenuToggle as ion_menu_toggle };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,4BAA4B;AACzE,SAASC,CAAC,IAAIC,kBAAkB,QAAQ,kCAAkC;AAC1E,SAASC,qBAAqB,QAAQ,oCAAoC;AAC1E,SAASC,CAAC,IAAIC,SAAS,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,KAAK,QAAQ,uBAAuB;AAC3G,SAASC,CAAC,IAAIC,cAAc,QAAQ,qBAAqB;AACzD,SAASL,CAAC,IAAIM,mBAAmB,QAAQ,wBAAwB;AACjE,SAASC,CAAC,IAAIC,MAAM,EAAEC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AACzE,SAASH,CAAC,IAAII,kBAAkB,EAAEzB,CAAC,IAAI0B,WAAW,QAAQ,qBAAqB;AAC/E,SAASC,CAAC,IAAIC,WAAW,EAAEC,CAAC,IAAIC,SAAS,QAAQ,qBAAqB;AACtE,OAAO,qBAAqB;AAC5B,OAAO,qBAAqB;AAC5B,OAAO,yBAAyB;AAChC,OAAO,kCAAkC;AAEzC,MAAMC,UAAU,GAAG,2jGAA2jG;AAC9kG,MAAMC,gBAAgB,GAAGD,UAAU;AAEnC,MAAME,SAAS,GAAG,ymGAAymG;AAC3nG,MAAMC,eAAe,GAAGD,SAAS;AAEjC,MAAME,SAAS,GAAG,6BAA6B;AAC/C,MAAMC,QAAQ,GAAG,6BAA6B;AAC9C,MAAMC,gBAAgB,GAAG,gCAAgC;AACzD,MAAMC,eAAe,GAAG,8BAA8B;AACtD,MAAMC,oBAAoB,GAAG,8MAA8M;AAC3O,MAAMC,IAAI,GAAG,MAAM;EACfC,WAAWA,CAACC,OAAO,EAAE;IACjB7C,gBAAgB,CAAC,IAAI,EAAE6C,OAAO,CAAC;IAC/B,IAAI,CAACC,WAAW,GAAG5C,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAAC6C,YAAY,GAAG7C,WAAW,CAAC,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;IACxD,IAAI,CAAC8C,UAAU,GAAG9C,WAAW,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC+C,WAAW,GAAG/C,WAAW,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACtD,IAAI,CAACgD,aAAa,GAAGhD,WAAW,CAAC,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;IAC1D,IAAI,CAACiD,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,OAAO,GAAGzC,kBAAkB,CAAC0C,aAAa,CAAC;MAAEC,aAAa,EAAE;IAAK,CAAC,CAAC;IACxE,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,WAAW,GAAIC,EAAE,IAAK;MACvB;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,WAAW,GAAGvC,mBAAmB,CAACwC,QAAQ,CAAC;MACjD,IAAID,WAAW,IAAI,CAACA,WAAW,CAACE,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAC,EAAE;QAC/C;MACJ;MACA,IAAI,CAACC,iBAAiB,CAACL,EAAE,EAAEE,QAAQ,CAAC;IACxC,CAAC;IACD,IAAI,CAACI,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACrD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACsD,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACC,MAAM,GAAGD,SAAS;IACvB,IAAI,CAACE,IAAI,GAAGF,SAAS;IACrB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,IAAI,GAAG,OAAO;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACC,YAAY,GAAG,EAAE;EAC1B;EACAC,WAAWA,CAACL,IAAI,EAAEM,OAAO,EAAE;IACvB,MAAMC,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,IAAIA,SAAS,EAAE;MACX,IAAID,OAAO,KAAKR,SAAS,EAAE;QACvBS,SAAS,CAACC,SAAS,CAACC,MAAM,CAAC,gBAAgBH,OAAO,EAAE,CAAC;MACzD;MACAC,SAAS,CAACC,SAAS,CAACE,GAAG,CAAC,gBAAgBV,IAAI,EAAE,CAAC;MAC/CO,SAAS,CAACI,eAAe,CAAC,OAAO,CAAC;IACtC;IACA,IAAI,IAAI,CAACC,WAAW,EAAE;MAClB;MACA,IAAI,CAACA,WAAW,CAACD,eAAe,CAAC,OAAO,CAAC;IAC7C;IACA,IAAI,CAACE,SAAS,GAAGf,SAAS;EAC9B;EACAgB,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACpC,aAAa,CAACqC,IAAI,CAAC;MACpBf,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBgB,IAAI,EAAE,IAAI,CAAC9B;IACf,CAAC,CAAC;EACN;EACA+B,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC3E,SAAS,GAAGA,SAAS,CAAC,IAAI,CAAC2D,IAAI,CAAC;IACrC;AACR;AACA;AACA;IACQ,IAAI,CAACW,SAAS,GAAGf,SAAS;EAC9B;EACAqB,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACJ,WAAW,CAAC,CAAC;EACtB;EACMK,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACtB;MACA;MACA,IAAI,OAAOC,cAAc,KAAK,WAAW,IAAIA,cAAc,IAAI,IAAI,EAAE;QACjE,MAAMA,cAAc,CAACC,WAAW,CAAC,UAAU,CAAC;MAChD;MACA,IAAIH,KAAI,CAACrB,IAAI,KAAKF,SAAS,EAAE;QACzBuB,KAAI,CAACrB,IAAI,GAAG9C,MAAM,CAACuE,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;MACjD;MACA,MAAMC,OAAO,GAAGL,KAAI,CAACxB,SAAS,KAAKC,SAAS,GAAGN,QAAQ,CAACmC,cAAc,CAACN,KAAI,CAACxB,SAAS,CAAC,GAAG,IAAI;MAC7F,IAAI6B,OAAO,KAAK,IAAI,EAAE;QAClBE,OAAO,CAACC,KAAK,CAAC,mEAAmE,CAAC;QAClF;MACJ;MACA,IAAIR,KAAI,CAAC3B,EAAE,CAACD,QAAQ,CAACiC,OAAO,CAAC,EAAE;QAC3BE,OAAO,CAACC,KAAK,CAAC,4GAA4G,CAAC;MAC/H;MACAR,KAAI,CAACd,SAAS,GAAGmB,OAAO;MACxB;MACAA,OAAO,CAAClB,SAAS,CAACE,GAAG,CAAC,cAAc,CAAC;MACrCW,KAAI,CAAChB,WAAW,CAACgB,KAAI,CAACrB,IAAI,EAAEF,SAAS,CAAC;MACtCuB,KAAI,CAACH,WAAW,CAAC,CAAC;MAClB;MACAnE,cAAc,CAAC+E,SAAS,CAACT,KAAI,CAAC;MAC9BA,KAAI,CAACU,WAAW,CAAC,CAAC;MAClBV,KAAI,CAACW,OAAO,GAAG,OAAO,MAAM,CAAC,qBAAqB,CAAC,EAAEC,aAAa,CAAC;QAC/DvC,EAAE,EAAEF,QAAQ;QACZ0C,WAAW,EAAE,YAAY;QACzBC,eAAe,EAAE,EAAE;QACnBC,SAAS,EAAE,EAAE;QACbC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAGhD,EAAE,IAAK+B,KAAI,CAACiB,QAAQ,CAAChD,EAAE,CAAC;QACnCiD,WAAW,EAAEA,CAAA,KAAMlB,KAAI,CAACkB,WAAW,CAAC,CAAC;QACrCC,OAAO,EAAEA,CAAA,KAAMnB,KAAI,CAACmB,OAAO,CAAC,CAAC;QAC7BC,MAAM,EAAGnD,EAAE,IAAK+B,KAAI,CAACoB,MAAM,CAACnD,EAAE,CAAC;QAC/BoD,KAAK,EAAGpD,EAAE,IAAK+B,KAAI,CAACqB,KAAK,CAACpD,EAAE;MAChC,CAAC,CAAC;MACF+B,KAAI,CAACN,WAAW,CAAC,CAAC;IAAC;EACvB;EACA4B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvD,mBAAmB,GAAG3C,qBAAqB,CAAC,IAAI,CAACiD,EAAE,CAAC;EAC7D;EACMkD,gBAAgBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAvB,iBAAA;MACrBuB,MAAI,CAAC7D,OAAO,GAAG,IAAI;MACnB6D,MAAI,CAACd,WAAW,CAAC,CAAC;MAClBc,MAAI,CAAC9B,WAAW,CAAC,CAAC;IAAC;EACvB;EACAgB,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,IAAI,CAAC/C,OAAO,EAAE;MACd,IAAI,CAACL,aAAa,CAACqC,IAAI,CAAC;QAAEf,QAAQ,EAAE,IAAI,CAACA,QAAQ;QAAEgB,IAAI,EAAE,IAAI,CAAC9B;MAAQ,CAAC,CAAC;IAC5E;EACJ;EACM2D,oBAAoBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MACzB;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,MAAMyB,MAAI,CAACC,KAAK,CAAC,KAAK,CAAC;MACvBD,MAAI,CAAClE,OAAO,CAACoE,OAAO,CAAC,CAAC;MACtBlG,cAAc,CAACmG,WAAW,CAACH,MAAI,CAAC;MAChC,IAAIA,MAAI,CAAClC,SAAS,EAAE;QAChBkC,MAAI,CAAClC,SAAS,CAACoC,OAAO,CAAC,CAAC;MAC5B;MACA,IAAIF,MAAI,CAACf,OAAO,EAAE;QACde,MAAI,CAACf,OAAO,CAACiB,OAAO,CAAC,CAAC;QACtBF,MAAI,CAACf,OAAO,GAAGlC,SAAS;MAC5B;MACAiD,MAAI,CAAClC,SAAS,GAAGf,SAAS;MAC1BiD,MAAI,CAACxC,SAAS,GAAGT,SAAS;IAAC;EAC/B;EACAqD,kBAAkBA,CAAC7D,EAAE,EAAE;IACnB,MAAM;MAAE8D;IAAO,CAAC,GAAG9D,EAAE;IACrB,MAAM+D,gBAAgB,GAAG,IAAI,CAAC3D,EAAE,CAAC4D,OAAO,CAAC,gBAAgB,CAAC;IAC1D;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAIF,MAAM,KAAKC,gBAAgB,EAAE;MAC7B;IACJ;IACA,IAAI,CAACzD,aAAa,GAAGN,EAAE,CAACiE,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC9D,EAAE,CAAC;IAC9C,IAAI,CAACqB,WAAW,CAAC,CAAC;EACtB;EACA0C,eAAeA,CAACnE,EAAE,EAAE;IAChB;IACA,IAAI,IAAI,CAACH,OAAO,IAAI,IAAI,CAACP,SAAS,GAAGU,EAAE,CAACoE,SAAS,GAAG,GAAG,EAAE;MACrD,MAAMC,WAAW,GAAGrE,EAAE,CAACsE,YAAY,GAAG,CAACtE,EAAE,CAACsE,YAAY,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACjD,WAAW,CAAC,GAAG,KAAK;MAC3F,IAAI+C,WAAW,EAAE;QACbrE,EAAE,CAACwE,cAAc,CAAC,CAAC;QACnBxE,EAAE,CAACyE,eAAe,CAAC,CAAC;QACpB,IAAI,CAACf,KAAK,CAAC,CAAC;MAChB;IACJ;EACJ;EACAgB,SAASA,CAAC1E,EAAE,EAAE;IACV,IAAIA,EAAE,CAAC2E,GAAG,KAAK,QAAQ,EAAE;MACrB,IAAI,CAACjB,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;EACIkB,MAAMA,CAAA,EAAG;IACL,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAACjF,OAAO,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIkF,QAAQA,CAAA,EAAG;IACP,OAAOF,OAAO,CAACC,OAAO,CAAC,IAAI,CAACE,SAAS,CAAC,CAAC,CAAC;EAC5C;EACA;AACJ;AACA;AACA;EACIrD,IAAIA,CAACsD,QAAQ,GAAG,IAAI,EAAE;IAClB,OAAO,IAAI,CAACC,OAAO,CAAC,IAAI,EAAED,QAAQ,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACIvB,KAAKA,CAACuB,QAAQ,GAAG,IAAI,EAAE;IACnB,OAAO,IAAI,CAACC,OAAO,CAAC,KAAK,EAAED,QAAQ,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIE,MAAMA,CAACF,QAAQ,GAAG,IAAI,EAAE;IACpB,OAAO,IAAI,CAACC,OAAO,CAAC,CAAC,IAAI,CAACrF,OAAO,EAAEoF,QAAQ,CAAC;EAChD;EACA;AACJ;AACA;AACA;EACIC,OAAOA,CAACE,UAAU,EAAEH,QAAQ,GAAG,IAAI,EAAE;IACjC,OAAOxH,cAAc,CAAC4H,QAAQ,CAAC,IAAI,EAAED,UAAU,EAAEH,QAAQ,CAAC;EAC9D;EACAK,oBAAoBA,CAAA,EAAG;IACnB,MAAM;MAAElF;IAAG,CAAC,GAAG,IAAI;IACnB,MAAMmF,UAAU,GAAGnF,EAAE,CAACoF,aAAa,CAAC3G,oBAAoB,CAAC;IACzD,IAAI0G,UAAU,EAAE;MACZA,UAAU,CAACE,KAAK,CAAC,CAAC;IACtB,CAAC,MACI;MACDrF,EAAE,CAACqF,KAAK,CAAC,CAAC;IACd;EACJ;EACAC,mBAAmBA,CAAA,EAAG;IAClB,MAAM;MAAEtF;IAAG,CAAC,GAAG,IAAI;IACnB,MAAMuF,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACzF,EAAE,CAAC0F,gBAAgB,CAACjH,oBAAoB,CAAC,CAAC;IACpE,MAAMkH,SAAS,GAAGJ,MAAM,CAACK,MAAM,GAAG,CAAC,GAAGL,MAAM,CAACA,MAAM,CAACK,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;IACtE,IAAID,SAAS,EAAE;MACXA,SAAS,CAACN,KAAK,CAAC,CAAC;IACrB,CAAC,MACI;MACDrF,EAAE,CAACqF,KAAK,CAAC,CAAC;IACd;EACJ;EACApF,iBAAiBA,CAACL,EAAE,EAAEiG,GAAG,EAAE;IACvB,MAAMnC,MAAM,GAAG9D,EAAE,CAAC8D,MAAM;IACxB,IAAI,CAACA,MAAM,EAAE;MACT;IACJ;IACA;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAAC1D,EAAE,CAACD,QAAQ,CAAC2D,MAAM,CAAC,EAAE;MAC1B,IAAI,CAACoC,SAAS,GAAGpC,MAAM;IAC3B,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,CAACwB,oBAAoB,CAAC,CAAC;MAC3B;AACZ;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACY,SAAS,KAAKD,GAAG,CAACE,aAAa,EAAE;QACtC,IAAI,CAACT,mBAAmB,CAAC,CAAC;MAC9B;IACJ;EACJ;EACML,QAAQA,CAAAe,EAAA,EAA8B;IAAA,IAAAC,MAAA;IAAA,OAAArE,iBAAA,YAA7BoD,UAAU,EAAEH,QAAQ,GAAG,IAAI;MACtC;MACA,IAAI,CAACoB,MAAI,CAACrB,SAAS,CAAC,CAAC,IAAIqB,MAAI,CAACzG,WAAW,IAAIwF,UAAU,KAAKiB,MAAI,CAACxG,OAAO,EAAE;QACtE,OAAO,KAAK;MAChB;MACAwG,MAAI,CAACC,eAAe,CAAClB,UAAU,CAAC;MAChC,MAAMiB,MAAI,CAACE,aAAa,CAAC,CAAC;MAC1B,MAAMF,MAAI,CAACG,cAAc,CAACpB,UAAU,EAAEH,QAAQ,CAAC;MAC/C;AACR;AACA;AACA;AACA;MACQ,IAAIoB,MAAI,CAAC1G,kBAAkB,EAAE;QACzB0G,MAAI,CAAC1G,kBAAkB,GAAG,KAAK;QAC/B,OAAO,KAAK;MAChB;MACA0G,MAAI,CAACI,cAAc,CAACrB,UAAU,CAAC;MAC/B,OAAO,IAAI;IAAC,GAAAsB,KAAA,OAAAC,SAAA;EAChB;EACMJ,aAAaA,CAAA,EAAG;IAAA,IAAAK,MAAA;IAAA,OAAA5E,iBAAA;MAClB;MACA;MACA,MAAM6E,KAAK,GAAGD,MAAI,CAACtF,WAAW,CAACwF,WAAW;MAC1C;AACR;AACA;AACA;MACQ,MAAMC,WAAW,GAAG9J,SAAS,CAAC2J,MAAI,CAAChG,IAAI,CAAC;MACxC,IAAIiG,KAAK,KAAKD,MAAI,CAACC,KAAK,IAAID,MAAI,CAACrF,SAAS,KAAKf,SAAS,IAAIuG,WAAW,KAAKH,MAAI,CAAC3J,SAAS,EAAE;QACxF;MACJ;MACA2J,MAAI,CAACC,KAAK,GAAGA,KAAK;MAClBD,MAAI,CAAC3J,SAAS,GAAG8J,WAAW;MAC5B;MACA,IAAIH,MAAI,CAACrF,SAAS,EAAE;QAChBqF,MAAI,CAACrF,SAAS,CAACoC,OAAO,CAAC,CAAC;QACxBiD,MAAI,CAACrF,SAAS,GAAGf,SAAS;MAC9B;MACA;MACA,MAAMe,SAAS,GAAIqF,MAAI,CAACrF,SAAS,SAAS9D,cAAc,CAACuJ,gBAAgB,CAACJ,MAAI,CAAClG,IAAI,EAAEkG,MAAI,CAAE;MAC3F,IAAI,CAAChJ,MAAM,CAACqJ,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,EAAE;QACtC1F,SAAS,CAAC2F,QAAQ,CAAC,CAAC,CAAC;MACzB;MACA3F,SAAS,CAAC4F,IAAI,CAAC,MAAM,CAAC;IAAC;EAC3B;EACMX,cAAcA,CAACpB,UAAU,EAAEH,QAAQ,EAAE;IAAA,IAAAmC,MAAA;IAAA,OAAApF,iBAAA;MACvC,MAAMqF,UAAU,GAAG,CAACjC,UAAU;MAC9B,MAAMkC,IAAI,GAAGxJ,UAAU,CAACsJ,MAAI,CAAC;MAC7B,MAAMG,MAAM,GAAGD,IAAI,KAAK,KAAK,GAAG7I,SAAS,GAAGC,QAAQ;MACpD,MAAM8I,aAAa,GAAGF,IAAI,KAAK,KAAK,GAAG3I,gBAAgB,GAAGC,eAAe;MACzE,MAAM6I,GAAG,GAAGL,MAAI,CAAC7F,SAAS,CACrBmG,SAAS,CAACL,UAAU,GAAG,SAAS,GAAG,QAAQ,CAAC,CAC5CE,MAAM,CAACF,UAAU,GAAGG,aAAa,GAAGD,MAAM,CAAC;MAChD,IAAItC,QAAQ,EAAE;QACV,MAAMwC,GAAG,CAACE,IAAI,CAAC,CAAC;MACpB,CAAC,MACI;QACDF,GAAG,CAACE,IAAI,CAAC;UAAEC,IAAI,EAAE;QAAK,CAAC,CAAC;MAC5B;MACA;AACR;AACA;AACA;AACA;AACA;AACA;MACQ,IAAIH,GAAG,CAACI,YAAY,CAAC,CAAC,KAAK,SAAS,EAAE;QAClCJ,GAAG,CAACC,SAAS,CAAC,QAAQ,CAAC;MAC3B;IAAC;EACL;EACA1C,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACrE,QAAQ,IAAI,CAAC,IAAI,CAACL,aAAa;EAChD;EACAwH,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACjH,YAAY,IAAI,CAAC,IAAI,CAACjB,WAAW,IAAI,IAAI,CAACoF,SAAS,CAAC,CAAC;EACrE;EACAhC,QAAQA,CAACiB,MAAM,EAAE;IACb;IACA,MAAM8D,gBAAgB,GAAG,CAAC,CAAC7H,QAAQ,CAACsF,aAAa,CAAC,sBAAsB,CAAC;IACzE,IAAIuC,gBAAgB,IAAI,CAAC,IAAI,CAACD,QAAQ,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IAChB;IACA,IAAI,IAAI,CAACjI,OAAO,EAAE;MACd,OAAO,IAAI;IACf,CAAC,MACI,IAAIpC,cAAc,CAACuK,YAAY,CAAC,CAAC,EAAE;MACpC,OAAO,KAAK;IAChB;IACA,OAAOC,aAAa,CAACC,MAAM,EAAEjE,MAAM,CAACkE,QAAQ,EAAE,IAAI,CAAClL,SAAS,EAAE,IAAI,CAAC6D,YAAY,CAAC;EACpF;EACAmC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACqD,eAAe,CAAC,CAAC,IAAI,CAACzG,OAAO,CAAC;IACnC,OAAO,IAAI,CAAC0G,aAAa,CAAC,CAAC;EAC/B;EACArD,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACtD,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtClE,MAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA;IACA,IAAI,CAACkE,SAAS,CAAC6G,aAAa,CAAC,IAAI,EAAE,IAAI,CAACvI,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5D;EACAsD,MAAMA,CAACc,MAAM,EAAE;IACX,IAAI,CAAC,IAAI,CAACrE,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtClE,MAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAMgL,KAAK,GAAGC,YAAY,CAACrE,MAAM,CAACsE,MAAM,EAAE,IAAI,CAAC1I,OAAO,EAAE,IAAI,CAAC5C,SAAS,CAAC;IACvE,MAAMuL,SAAS,GAAGH,KAAK,GAAG,IAAI,CAACxB,KAAK;IACpC,IAAI,CAACtF,SAAS,CAACkH,YAAY,CAAC,IAAI,CAAC5I,OAAO,GAAG,CAAC,GAAG2I,SAAS,GAAGA,SAAS,CAAC;EACzE;EACApF,KAAKA,CAACa,MAAM,EAAE;IACV,IAAI,CAAC,IAAI,CAACrE,WAAW,IAAI,CAAC,IAAI,CAAC2B,SAAS,EAAE;MACtClE,MAAM,CAAC,KAAK,EAAE,4BAA4B,CAAC;MAC3C;IACJ;IACA,MAAMuH,MAAM,GAAG,IAAI,CAAC/E,OAAO;IAC3B,MAAM5C,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC,MAAMoL,KAAK,GAAGC,YAAY,CAACrE,MAAM,CAACsE,MAAM,EAAE3D,MAAM,EAAE3H,SAAS,CAAC;IAC5D,MAAM4J,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAM2B,SAAS,GAAGH,KAAK,GAAGxB,KAAK;IAC/B,MAAM6B,QAAQ,GAAGzE,MAAM,CAAC0E,SAAS;IACjC,MAAMC,CAAC,GAAG/B,KAAK,GAAG,GAAG;IACrB,MAAMgC,mBAAmB,GAAGH,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,GAAG,IAAIzE,MAAM,CAACsE,MAAM,GAAGK,CAAC,CAAC;IAClF,MAAME,kBAAkB,GAAGJ,QAAQ,IAAI,CAAC,KAAKA,QAAQ,GAAG,CAAC,GAAG,IAAIzE,MAAM,CAACsE,MAAM,GAAG,CAACK,CAAC,CAAC;IACnF,MAAMG,cAAc,GAAGnE,MAAM,GACvB3H,SAAS,GACL4L,mBAAmB,GACnBC,kBAAkB,GACtB7L,SAAS,GACL6L,kBAAkB,GAClBD,mBAAmB;IAC7B,IAAIzD,UAAU,GAAG,CAACR,MAAM,IAAImE,cAAc;IAC1C,IAAInE,MAAM,IAAI,CAACmE,cAAc,EAAE;MAC3B3D,UAAU,GAAG,IAAI;IACrB;IACA,IAAI,CAAC9F,SAAS,GAAG2E,MAAM,CAAC+E,WAAW;IACnC;IACA,IAAIC,YAAY,GAAGF,cAAc,GAAG,KAAK,GAAG,CAAC,KAAK;IAClD;AACR;AACA;AACA;AACA;IACQ,MAAMG,iBAAiB,GAAGV,SAAS,GAAG,CAAC,GAAG,IAAI,GAAGA,SAAS;IAC1D;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQS,YAAY,IACRrM,uBAAuB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAEW,KAAK,CAAC,CAAC,EAAE2L,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5G,MAAMC,MAAM,GAAG,IAAI,CAACtJ,OAAO,GAAG,CAACkJ,cAAc,GAAGA,cAAc;IAC9D,IAAI,CAACxH,SAAS,CACTgG,MAAM,CAAC,gCAAgC,CAAC,CACxC6B,QAAQ,CAAC,MAAM,IAAI,CAAC3C,cAAc,CAACrB,UAAU,CAAC,EAAE;MAAEiE,eAAe,EAAE;IAAK,CAAC,CAAC,CAC1EC,WAAW,CAACH,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,CAACtJ,OAAO,GAAG,CAAC,GAAGoJ,YAAY,GAAGA,YAAY,EAAE,GAAG,CAAC;EACzF;EACA3C,eAAeA,CAAClB,UAAU,EAAE;IACxB/H,MAAM,CAAC,CAAC,IAAI,CAACuC,WAAW,EAAE,gDAAgD,CAAC;IAC3E;IACA;IACA,IAAI,CAACQ,EAAE,CAACc,SAAS,CAACE,GAAG,CAACmI,SAAS,CAAC;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACnJ,EAAE,CAACoJ,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC;IACrC,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACvI,SAAS,CAACE,GAAG,CAACsI,aAAa,CAAC;IAChD;IACA;IACA,IAAI,IAAI,CAACzI,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACC,SAAS,CAACE,GAAG,CAACuI,iBAAiB,CAAC;MAC/C;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,CAAC1I,SAAS,CAACuI,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACtD;IACA,IAAI,CAACjK,OAAO,CAACqK,KAAK,CAAC,CAAC;IACpB,IAAI,CAAChK,WAAW,GAAG,IAAI;IACvB,IAAIwF,UAAU,EAAE;MACZ,IAAI,CAACnG,WAAW,CAACyC,IAAI,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,IAAI,CAACxC,YAAY,CAACwC,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA+E,cAAcA,CAAC7B,MAAM,EAAE;IACnB,IAAIiF,EAAE;IACN;IACA;IACA;IACA;IACA,IAAI,CAAChK,OAAO,GAAG+E,MAAM;IACrB,IAAI,CAAChF,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACf,IAAI,CAACN,OAAO,CAACuK,OAAO,CAAC,CAAC;IAC1B;IACA,IAAIlF,MAAM,EAAE;MACR;MACA,IAAI,CAACzF,UAAU,CAACuC,IAAI,CAAC,CAAC;MACtB;AACZ;AACA;AACA;AACA;MACY,MAAMqI,WAAW,GAAG,CAACF,EAAE,GAAG3J,QAAQ,CAACiG,aAAa,MAAM,IAAI,IAAI0D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7F,OAAO,CAAC,UAAU,CAAC;MAC7G,IAAI+F,WAAW,KAAK,IAAI,CAAC3J,EAAE,EAAE;QACzB,IAAI,CAACA,EAAE,CAACqF,KAAK,CAAC,CAAC;MACnB;MACA;MACAvF,QAAQ,CAAC8J,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACjK,WAAW,EAAE,IAAI,CAAC;IAC9D,CAAC,MACI;MACD;MACA,IAAI,CAACK,EAAE,CAACc,SAAS,CAACC,MAAM,CAACoI,SAAS,CAAC;MACnC;AACZ;AACA;AACA;MACY,IAAI,CAACnJ,EAAE,CAACiB,eAAe,CAAC,UAAU,CAAC;MACnC,IAAI,IAAI,CAACJ,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAACC,SAAS,CAACC,MAAM,CAACwI,iBAAiB,CAAC;QAClD;AAChB;AACA;AACA;AACA;QACgB,IAAI,CAAC1I,SAAS,CAACI,eAAe,CAAC,aAAa,CAAC;MACjD;MACA,IAAI,IAAI,CAACoI,UAAU,EAAE;QACjB,IAAI,CAACA,UAAU,CAACvI,SAAS,CAACC,MAAM,CAACuI,aAAa,CAAC;MACnD;MACA,IAAI,IAAI,CAACnI,SAAS,EAAE;QAChB,IAAI,CAACA,SAAS,CAAC0I,IAAI,CAAC,CAAC;MACzB;MACA;MACA,IAAI,CAAC7K,WAAW,CAACsC,IAAI,CAAC,CAAC;MACvB;MACAxB,QAAQ,CAACgK,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACnK,WAAW,EAAE,IAAI,CAAC;IACjE;EACJ;EACA0B,WAAWA,CAAA,EAAG;IACV,MAAMsD,QAAQ,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IACjC,IAAI,IAAI,CAACtC,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACyH,MAAM,CAACpF,QAAQ,IAAI,IAAI,CAAClE,YAAY,CAAC;IACtD;IACA;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACkE,QAAQ,EAAE;MACX;AACZ;AACA;AACA;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACnF,WAAW,EAAE;QAClB,IAAI,CAACD,kBAAkB,GAAG,IAAI;MAClC;MACA;AACZ;AACA;AACA;MACY,IAAI,CAAC8G,cAAc,CAAC,KAAK,CAAC;IAC9B;EACJ;EACA2D,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE1J,IAAI;MAAEC,QAAQ;MAAEL,aAAa;MAAER,mBAAmB;MAAEc;IAAK,CAAC,GAAG,IAAI;IACzE,MAAM0G,IAAI,GAAGxJ,UAAU,CAAC,IAAI,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,OAAQxB,CAAC,CAACE,IAAI,EAAE;MAAEmI,GAAG,EAAE,0CAA0C;MAAE0F,SAAS,EAAEtN,qBAAqB,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC2H,SAAS;MAAE4F,IAAI,EAAE,YAAY;MAAE,YAAY,EAAExK,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;MAAEyK,KAAK,EAAE;QAC7M,CAACjD,IAAI,GAAG,IAAI;QACZ,CAAC,aAAa5G,IAAI,EAAE,GAAG,IAAI;QAC3B,cAAc,EAAE,CAACC,QAAQ;QACzB,CAAC,aAAaC,IAAI,EAAE,GAAG,IAAI;QAC3B,mBAAmB,EAAEN;MACzB;IAAE,CAAC,EAAEhE,CAAC,CAAC,KAAK,EAAE;MAAEqI,GAAG,EAAE,0CAA0C;MAAE4F,KAAK,EAAE,YAAY;MAAEC,IAAI,EAAE,WAAW;MAAEC,GAAG,EAAGrK,EAAE,IAAM,IAAI,CAACkB,WAAW,GAAGlB;IAAI,CAAC,EAAE9D,CAAC,CAAC,MAAM,EAAE;MAAEqI,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC,EAAErI,CAAC,CAAC,cAAc,EAAE;MAAEqI,GAAG,EAAE,0CAA0C;MAAE8F,GAAG,EAAGrK,EAAE,IAAM,IAAI,CAACqJ,UAAU,GAAGrJ,EAAG;MAAEmK,KAAK,EAAE,eAAe;MAAEG,QAAQ,EAAE,KAAK;MAAEjG,eAAe,EAAE,KAAK;MAAE+F,IAAI,EAAE;IAAW,CAAC,CAAC,CAAC;EACzZ;EACA,IAAIpK,EAAEA,CAAA,EAAG;IAAE,OAAO1D,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWiO,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,MAAM,EAAE,CAAC,aAAa,CAAC;MACvB,cAAc,EAAE,CAAC,qBAAqB;IAC1C,CAAC;EAAE;AACP,CAAC;AACD,MAAMrC,YAAY,GAAGA,CAACC,MAAM,EAAE3D,MAAM,EAAE3H,SAAS,KAAK;EAChD,OAAO2N,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjG,MAAM,KAAK3H,SAAS,GAAG,CAACsL,MAAM,GAAGA,MAAM,CAAC;AAC/D,CAAC;AACD,MAAMN,aAAa,GAAGA,CAAC6C,GAAG,EAAEC,IAAI,EAAE9N,SAAS,EAAE6D,YAAY,KAAK;EAC1D,IAAI7D,SAAS,EAAE;IACX,OAAO8N,IAAI,IAAID,GAAG,CAACE,UAAU,GAAGlK,YAAY;EAChD,CAAC,MACI;IACD,OAAOiK,IAAI,IAAIjK,YAAY;EAC/B;AACJ,CAAC;AACD,MAAMyI,SAAS,GAAG,WAAW;AAC7B,MAAMG,aAAa,GAAG,eAAe;AACrC,MAAMC,iBAAiB,GAAG,mBAAmB;AAC7C7K,IAAI,CAACmM,KAAK,GAAG;EACTC,GAAG,EAAE5M,gBAAgB;EACrB6M,EAAE,EAAE3M;AACR,CAAC;;AAED;AACA,MAAM4M,gBAAgB;EAAA,IAAAC,IAAA,GAAArJ,iBAAA,CAAG,WAAOsJ,IAAI,EAAK;IACrC,MAAMC,MAAM,SAAS9N,cAAc,CAAC0E,GAAG,CAACmJ,IAAI,CAAC;IAC7C,OAAO,CAAC,EAAEC,MAAM,WAAWA,MAAM,CAACxG,QAAQ,CAAC,CAAC,CAAC,CAAC;EAClD,CAAC;EAAA,gBAHKqG,gBAAgBA,CAAAI,GAAA;IAAA,OAAAH,IAAA,CAAA3E,KAAA,OAAAC,SAAA;EAAA;AAAA,GAGrB;AAED,MAAM8E,gBAAgB,GAAG,+2FAA+2F;AACx4F,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,eAAe,GAAG,49FAA49F;AACp/F,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,UAAU,GAAG,MAAM;EACrB9M,WAAWA,CAACC,OAAO,EAAE;IAAA,IAAA8M,MAAA;IACjB3P,gBAAgB,CAAC,IAAI,EAAE6C,OAAO,CAAC;IAC/B,IAAI,CAACc,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACiM,OAAO,gBAAA/J,iBAAA,CAAG,aAAY;MACvB,OAAOvE,cAAc,CAAC0H,MAAM,CAAC2G,MAAI,CAACR,IAAI,CAAC;IAC3C,CAAC;IACD,IAAI,CAACU,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,KAAK,GAAGzL,SAAS;IACtB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC2K,IAAI,GAAG9K,SAAS;IACrB,IAAI,CAAC0L,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACxL,IAAI,GAAG,QAAQ;EACxB;EACA2C,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACvD,mBAAmB,GAAG3C,qBAAqB,CAAC,IAAI,CAACiD,EAAE,CAAC;EAC7D;EACAkD,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC6I,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAApK,iBAAA;MACtBoK,MAAI,CAACJ,OAAO,SAASZ,gBAAgB,CAACgB,MAAI,CAACd,IAAI,CAAC;IAAC;EACrD;EACAlB,MAAMA,CAAA,EAAG;IACL,MAAM;MAAE6B,KAAK;MAAEtL,QAAQ;MAAEb;IAAoB,CAAC,GAAG,IAAI;IACrD,MAAMwH,IAAI,GAAGxJ,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuO,QAAQ,GAAGzO,MAAM,CAACuE,GAAG,CAAC,UAAU,EAAEmF,IAAI,KAAK,KAAK,GAAGpJ,WAAW,GAAGE,SAAS,CAAC;IACjF,MAAMkO,MAAM,GAAG,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACF,OAAO;IAC7C,MAAMO,KAAK,GAAG;MACV7L,IAAI,EAAE,IAAI,CAACA;IACf,CAAC;IACD,MAAM8L,SAAS,GAAG1M,mBAAmB,CAAC,YAAY,CAAC,IAAI,MAAM;IAC7D,OAAQxD,CAAC,CAACE,IAAI,EAAE;MAAEmI,GAAG,EAAE,0CAA0C;MAAEoH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,eAAe,EAAEpL,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAE,aAAa,EAAE2L,MAAM,GAAG,MAAM,GAAG,IAAI;MAAE/B,KAAK,EAAExM,kBAAkB,CAACkO,KAAK,EAAE;QACrM,CAAC3E,IAAI,GAAG,IAAI;QACZmF,MAAM,EAAE,IAAI;QAAE;QACd,oBAAoB,EAAEH,MAAM;QAC5B,sBAAsB,EAAE3L,QAAQ;QAChC,YAAY,EAAE3C,WAAW,CAAC,aAAa,EAAE,IAAI,CAACoC,EAAE,CAAC;QACjD,kBAAkB,EAAEpC,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAACoC,EAAE,CAAC;QAC9D,iBAAiB,EAAE,IAAI;QACvB,eAAe,EAAE;MACrB,CAAC;IAAE,CAAC,EAAE9D,CAAC,CAAC,QAAQ,EAAEoQ,MAAM,CAACC,MAAM,CAAC;MAAEhI,GAAG,EAAE;IAA2C,CAAC,EAAE4H,KAAK,EAAE;MAAE5L,QAAQ,EAAEA,QAAQ;MAAE4J,KAAK,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAE,YAAY,EAAEgC;IAAU,CAAC,CAAC,EAAElQ,CAAC,CAAC,MAAM,EAAE;MAAEqI,GAAG,EAAE,0CAA0C;MAAE4F,KAAK,EAAE;IAAe,CAAC,EAAEjO,CAAC,CAAC,MAAM,EAAE;MAAEqI,GAAG,EAAE;IAA2C,CAAC,EAAErI,CAAC,CAAC,UAAU,EAAE;MAAEqI,GAAG,EAAE,0CAA0C;MAAE6F,IAAI,EAAE,MAAM;MAAEoC,IAAI,EAAEP,QAAQ;MAAE/E,IAAI,EAAEA,IAAI;MAAEuF,IAAI,EAAE,KAAK;MAAE,aAAa,EAAE;IAAO,CAAC,CAAC,CAAC,CAAC,EAAEvF,IAAI,KAAK,IAAI,IAAIhL,CAAC,CAAC,mBAAmB,EAAE;MAAEqI,GAAG,EAAE,0CAA0C;MAAEjE,IAAI,EAAE;IAAY,CAAC,CAAC,CAAC,CAAC;EACxlB;EACA,IAAIN,EAAEA,CAAA,EAAG;IAAE,OAAO1D,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDmP,UAAU,CAACZ,KAAK,GAAG;EACfC,GAAG,EAAEQ,sBAAsB;EAC3BP,EAAE,EAAES;AACR,CAAC;AAED,MAAMkB,aAAa,GAAG,0CAA0C;AAChE,MAAMC,mBAAmB,GAAGD,aAAa;AAEzC,MAAME,UAAU,GAAG,MAAM;EACrBjO,WAAWA,CAACC,OAAO,EAAE;IACjB7C,gBAAgB,CAAC,IAAI,EAAE6C,OAAO,CAAC;IAC/B,IAAI,CAAC+M,OAAO,GAAG,MAAM;MACjB,OAAOtO,cAAc,CAAC0H,MAAM,CAAC,IAAI,CAACmG,IAAI,CAAC;IAC3C,CAAC;IACD,IAAI,CAACU,OAAO,GAAG,KAAK;IACpB,IAAI,CAACV,IAAI,GAAG9K,SAAS;IACrB,IAAI,CAAC0L,QAAQ,GAAG,IAAI;EACxB;EACApK,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACqK,iBAAiB,CAAC,CAAC;EAC5B;EACMA,iBAAiBA,CAAA,EAAG;IAAA,IAAAc,MAAA;IAAA,OAAAjL,iBAAA;MACtBiL,MAAI,CAACjB,OAAO,SAASZ,gBAAgB,CAAC6B,MAAI,CAAC3B,IAAI,CAAC;IAAC;EACrD;EACAlB,MAAMA,CAAA,EAAG;IACL,MAAM9C,IAAI,GAAGxJ,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMwO,MAAM,GAAG,IAAI,CAACJ,QAAQ,IAAI,CAAC,IAAI,CAACF,OAAO;IAC7C,OAAQ1P,CAAC,CAACE,IAAI,EAAE;MAAEmI,GAAG,EAAE,0CAA0C;MAAEoH,OAAO,EAAE,IAAI,CAACA,OAAO;MAAE,aAAa,EAAEO,MAAM,GAAG,MAAM,GAAG,IAAI;MAAE/B,KAAK,EAAE;QAChI,CAACjD,IAAI,GAAG,IAAI;QACZ,oBAAoB,EAAEgF;MAC1B;IAAE,CAAC,EAAEhQ,CAAC,CAAC,MAAM,EAAE;MAAEqI,GAAG,EAAE;IAA2C,CAAC,CAAC,CAAC;EAC5E;AACJ,CAAC;AACDqI,UAAU,CAAC/B,KAAK,GAAG8B,mBAAmB;AAEtC,SAASjO,IAAI,IAAIoO,QAAQ,EAAErB,UAAU,IAAIsB,eAAe,EAAEH,UAAU,IAAII,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}