{"ast": null, "code": "import { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, forwardRef, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, ContentChild, ViewChild, Input, Output, QueryList, numberAttribute, ContentChildren, NgModule } from '@angular/core';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\nconst _c0 = [\"*\"];\nfunction CdkStep_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nclass CdkStepHeader {\n  constructor(_elementRef) {\n    this._elementRef = _elementRef;\n  }\n  /** Focuses the step header. */\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  static {\n    this.ɵfac = function CdkStepHeader_Factory(t) {\n      return new (t || CdkStepHeader)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepHeader,\n      selectors: [[\"\", \"cdkStepHeader\", \"\"]],\n      hostAttrs: [\"role\", \"tab\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepHeader, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkStepHeader]',\n      host: {\n        'role': 'tab'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], null);\n})();\nclass CdkStepLabel {\n  constructor(/** @docs-private */template) {\n    this.template = template;\n  }\n  static {\n    this.ɵfac = function CdkStepLabel_Factory(t) {\n      return new (t || CdkStepLabel)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepLabel,\n      selectors: [[\"\", \"cdkStepLabel\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepLabel, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkStepLabel]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/** Used to generate unique ID for each stepper component. */\nlet nextId = 0;\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n  NUMBER: 'number',\n  EDIT: 'edit',\n  DONE: 'done',\n  ERROR: 'error'\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nclass CdkStep {\n  /** Whether step is marked as completed. */\n  get completed() {\n    return this._completedOverride == null ? this._getDefaultCompleted() : this._completedOverride;\n  }\n  set completed(value) {\n    this._completedOverride = value;\n  }\n  _getDefaultCompleted() {\n    return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n  }\n  /** Whether step has an error. */\n  get hasError() {\n    return this._customError == null ? this._getDefaultError() : this._customError;\n  }\n  set hasError(value) {\n    this._customError = value;\n  }\n  _getDefaultError() {\n    return this.stepControl && this.stepControl.invalid && this.interacted;\n  }\n  constructor(_stepper, stepperOptions) {\n    this._stepper = _stepper;\n    /** Whether user has attempted to move away from the step. */\n    this.interacted = false;\n    /** Emits when the user has attempted to move away from the step. */\n    this.interactedStream = new EventEmitter();\n    /** Whether the user can return to this step once it has been marked as completed. */\n    this.editable = true;\n    /** Whether the completion of step is optional. */\n    this.optional = false;\n    this._completedOverride = null;\n    this._customError = null;\n    this._stepperOptions = stepperOptions ? stepperOptions : {};\n    this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n  }\n  /** Selects this step component. */\n  select() {\n    this._stepper.selected = this;\n  }\n  /** Resets the step to its initial state. Note that this includes resetting form data. */\n  reset() {\n    this.interacted = false;\n    if (this._completedOverride != null) {\n      this._completedOverride = false;\n    }\n    if (this._customError != null) {\n      this._customError = false;\n    }\n    if (this.stepControl) {\n      this.stepControl.reset();\n    }\n  }\n  ngOnChanges() {\n    // Since basically all inputs of the MatStep get proxied through the view down to the\n    // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n    this._stepper._stateChanged();\n  }\n  _markAsInteracted() {\n    if (!this.interacted) {\n      this.interacted = true;\n      this.interactedStream.emit(this);\n    }\n  }\n  /** Determines whether the error state can be shown. */\n  _showError() {\n    // We want to show the error state either if the user opted into/out of it using the\n    // global options, or if they've explicitly set it through the `hasError` input.\n    return this._stepperOptions.showError ?? this._customError != null;\n  }\n  static {\n    this.ɵfac = function CdkStep_Factory(t) {\n      return new (t || CdkStep)(i0.ɵɵdirectiveInject(forwardRef(() => CdkStepper)), i0.ɵɵdirectiveInject(STEPPER_GLOBAL_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkStep,\n      selectors: [[\"cdk-step\"]],\n      contentQueries: function CdkStep_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStepLabel, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.stepLabel = _t.first);\n        }\n      },\n      viewQuery: function CdkStep_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(TemplateRef, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.content = _t.first);\n        }\n      },\n      inputs: {\n        stepControl: \"stepControl\",\n        label: \"label\",\n        errorMessage: \"errorMessage\",\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        state: \"state\",\n        editable: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"editable\", \"editable\", booleanAttribute],\n        optional: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"optional\", \"optional\", booleanAttribute],\n        completed: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"completed\", \"completed\", booleanAttribute],\n        hasError: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hasError\", \"hasError\", booleanAttribute]\n      },\n      outputs: {\n        interactedStream: \"interacted\"\n      },\n      exportAs: [\"cdkStep\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function CdkStep_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, CdkStep_ng_template_0_Template, 1, 0, \"ng-template\");\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStep, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-step',\n      exportAs: 'cdkStep',\n      template: '<ng-template><ng-content></ng-content></ng-template>',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkStepper,\n    decorators: [{\n      type: Inject,\n      args: [forwardRef(() => CdkStepper)]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [STEPPER_GLOBAL_OPTIONS]\n    }]\n  }], {\n    stepLabel: [{\n      type: ContentChild,\n      args: [CdkStepLabel]\n    }],\n    content: [{\n      type: ViewChild,\n      args: [TemplateRef, {\n        static: true\n      }]\n    }],\n    stepControl: [{\n      type: Input\n    }],\n    interactedStream: [{\n      type: Output,\n      args: ['interacted']\n    }],\n    label: [{\n      type: Input\n    }],\n    errorMessage: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    state: [{\n      type: Input\n    }],\n    editable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    optional: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    completed: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hasError: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nclass CdkStepper {\n  /** The index of the selected step. */\n  get selectedIndex() {\n    return this._selectedIndex;\n  }\n  set selectedIndex(index) {\n    if (this.steps && this._steps) {\n      // Ensure that the index can't be out of bounds.\n      if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n      }\n      this.selected?._markAsInteracted();\n      if (this._selectedIndex !== index && !this._anyControlsInvalidOrPending(index) && (index >= this._selectedIndex || this.steps.toArray()[index].editable)) {\n        this._updateSelectedItemIndex(index);\n      }\n    } else {\n      this._selectedIndex = index;\n    }\n  }\n  /** The step that is selected. */\n  get selected() {\n    return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n  }\n  set selected(step) {\n    this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n  }\n  /** Orientation of the stepper. */\n  get orientation() {\n    return this._orientation;\n  }\n  set orientation(value) {\n    // This is a protected method so that `MatStepper` can hook into it.\n    this._orientation = value;\n    if (this._keyManager) {\n      this._keyManager.withVerticalOrientation(value === 'vertical');\n    }\n  }\n  constructor(_dir, _changeDetectorRef, _elementRef) {\n    this._dir = _dir;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._elementRef = _elementRef;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n    this.steps = new QueryList();\n    /** List of step headers sorted based on their DOM order. */\n    this._sortedHeaders = new QueryList();\n    /** Whether the validity of previous steps should be checked or not. */\n    this.linear = false;\n    this._selectedIndex = 0;\n    /** Event emitted when the selected step has changed. */\n    this.selectionChange = new EventEmitter();\n    /** Output to support two-way binding on `[(selectedIndex)]` */\n    this.selectedIndexChange = new EventEmitter();\n    this._orientation = 'horizontal';\n    this._groupId = nextId++;\n  }\n  ngAfterContentInit() {\n    this._steps.changes.pipe(startWith(this._steps), takeUntil(this._destroyed)).subscribe(steps => {\n      this.steps.reset(steps.filter(step => step._stepper === this));\n      this.steps.notifyOnChanges();\n    });\n  }\n  ngAfterViewInit() {\n    // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n    // Material stepper, they won't appear in the `QueryList` in the same order as they're\n    // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n    // them manually to ensure that they're correct. Alternatively, we can change the Material\n    // template to inline the headers in the `ngFor`, but that'll result in a lot of\n    // code duplication. See #23539.\n    this._stepHeader.changes.pipe(startWith(this._stepHeader), takeUntil(this._destroyed)).subscribe(headers => {\n      this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n        const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n        // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        // tslint:disable-next-line:no-bitwise\n        return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n      }));\n      this._sortedHeaders.notifyOnChanges();\n    });\n    // Note that while the step headers are content children by default, any components that\n    // extend this one might have them as view children. We initialize the keyboard handling in\n    // AfterViewInit so we're guaranteed for both view and content children to be defined.\n    this._keyManager = new FocusKeyManager(this._sortedHeaders).withWrap().withHomeAndEnd().withVerticalOrientation(this._orientation === 'vertical');\n    (this._dir ? this._dir.change : of()).pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed)).subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n    this._keyManager.updateActiveItem(this._selectedIndex);\n    // No need to `takeUntil` here, because we're the ones destroying `steps`.\n    this.steps.changes.subscribe(() => {\n      if (!this.selected) {\n        this._selectedIndex = Math.max(this._selectedIndex - 1, 0);\n      }\n    });\n    // The logic which asserts that the selected index is within bounds doesn't run before the\n    // steps are initialized, because we don't how many steps there are yet so we may have an\n    // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n    if (!this._isValidIndex(this._selectedIndex)) {\n      this._selectedIndex = 0;\n    }\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this.steps.destroy();\n    this._sortedHeaders.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Selects and focuses the next step in list. */\n  next() {\n    this.selectedIndex = Math.min(this._selectedIndex + 1, this.steps.length - 1);\n  }\n  /** Selects and focuses the previous step in list. */\n  previous() {\n    this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n  }\n  /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n  reset() {\n    this._updateSelectedItemIndex(0);\n    this.steps.forEach(step => step.reset());\n    this._stateChanged();\n  }\n  /** Returns a unique id for each step label element. */\n  _getStepLabelId(i) {\n    return `cdk-step-label-${this._groupId}-${i}`;\n  }\n  /** Returns unique id for each step content element. */\n  _getStepContentId(i) {\n    return `cdk-step-content-${this._groupId}-${i}`;\n  }\n  /** Marks the component to be change detected. */\n  _stateChanged() {\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Returns position state of the step with the given index. */\n  _getAnimationDirection(index) {\n    const position = index - this._selectedIndex;\n    if (position < 0) {\n      return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n    } else if (position > 0) {\n      return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n    }\n    return 'current';\n  }\n  /** Returns the type of icon to be displayed. */\n  _getIndicatorType(index, state = STEP_STATE.NUMBER) {\n    const step = this.steps.toArray()[index];\n    const isCurrentStep = this._isCurrentStep(index);\n    return step._displayDefaultIndicatorType ? this._getDefaultIndicatorLogic(step, isCurrentStep) : this._getGuidelineLogic(step, isCurrentStep, state);\n  }\n  _getDefaultIndicatorLogic(step, isCurrentStep) {\n    if (step._showError() && step.hasError && !isCurrentStep) {\n      return STEP_STATE.ERROR;\n    } else if (!step.completed || isCurrentStep) {\n      return STEP_STATE.NUMBER;\n    } else {\n      return step.editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n    }\n  }\n  _getGuidelineLogic(step, isCurrentStep, state = STEP_STATE.NUMBER) {\n    if (step._showError() && step.hasError && !isCurrentStep) {\n      return STEP_STATE.ERROR;\n    } else if (step.completed && !isCurrentStep) {\n      return STEP_STATE.DONE;\n    } else if (step.completed && isCurrentStep) {\n      return state;\n    } else if (step.editable && isCurrentStep) {\n      return STEP_STATE.EDIT;\n    } else {\n      return state;\n    }\n  }\n  _isCurrentStep(index) {\n    return this._selectedIndex === index;\n  }\n  /** Returns the index of the currently-focused step header. */\n  _getFocusIndex() {\n    return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex;\n  }\n  _updateSelectedItemIndex(newIndex) {\n    const stepsArray = this.steps.toArray();\n    this.selectionChange.emit({\n      selectedIndex: newIndex,\n      previouslySelectedIndex: this._selectedIndex,\n      selectedStep: stepsArray[newIndex],\n      previouslySelectedStep: stepsArray[this._selectedIndex]\n    });\n    // If focus is inside the stepper, move it to the next header, otherwise it may become\n    // lost when the active step content is hidden. We can't be more granular with the check\n    // (e.g. checking whether focus is inside the active step), because we don't have a\n    // reference to the elements that are rendering out the content.\n    this._containsFocus() ? this._keyManager.setActiveItem(newIndex) : this._keyManager.updateActiveItem(newIndex);\n    this._selectedIndex = newIndex;\n    this.selectedIndexChange.emit(this._selectedIndex);\n    this._stateChanged();\n  }\n  _onKeydown(event) {\n    const hasModifier = hasModifierKey(event);\n    const keyCode = event.keyCode;\n    const manager = this._keyManager;\n    if (manager.activeItemIndex != null && !hasModifier && (keyCode === SPACE || keyCode === ENTER)) {\n      this.selectedIndex = manager.activeItemIndex;\n      event.preventDefault();\n    } else {\n      manager.setFocusOrigin('keyboard').onKeydown(event);\n    }\n  }\n  _anyControlsInvalidOrPending(index) {\n    if (this.linear && index >= 0) {\n      return this.steps.toArray().slice(0, index).some(step => {\n        const control = step.stepControl;\n        const isIncomplete = control ? control.invalid || control.pending || !step.interacted : !step.completed;\n        return isIncomplete && !step.optional && !step._completedOverride;\n      });\n    }\n    return false;\n  }\n  _layoutDirection() {\n    return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n  }\n  /** Checks whether the stepper contains the focused element. */\n  _containsFocus() {\n    const stepperElement = this._elementRef.nativeElement;\n    const focusedElement = _getFocusedElementPierceShadowDom();\n    return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n  }\n  /** Checks whether the passed-in index is a valid step index. */\n  _isValidIndex(index) {\n    return index > -1 && (!this.steps || index < this.steps.length);\n  }\n  static {\n    this.ɵfac = function CdkStepper_Factory(t) {\n      return new (t || CdkStepper)(i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepper,\n      selectors: [[\"\", \"cdkStepper\", \"\"]],\n      contentQueries: function CdkStepper_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, CdkStep, 5);\n          i0.ɵɵcontentQuery(dirIndex, CdkStepHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._steps = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._stepHeader = _t);\n        }\n      },\n      inputs: {\n        linear: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"linear\", \"linear\", booleanAttribute],\n        selectedIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"selectedIndex\", \"selectedIndex\", numberAttribute],\n        selected: \"selected\",\n        orientation: \"orientation\"\n      },\n      outputs: {\n        selectionChange: \"selectionChange\",\n        selectedIndexChange: \"selectedIndexChange\"\n      },\n      exportAs: [\"cdkStepper\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepper, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkStepper]',\n      exportAs: 'cdkStepper',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }], {\n    _steps: [{\n      type: ContentChildren,\n      args: [CdkStep, {\n        descendants: true\n      }]\n    }],\n    _stepHeader: [{\n      type: ContentChildren,\n      args: [CdkStepHeader, {\n        descendants: true\n      }]\n    }],\n    linear: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectedIndex: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    selected: [{\n      type: Input\n    }],\n    selectionChange: [{\n      type: Output\n    }],\n    selectedIndexChange: [{\n      type: Output\n    }],\n    orientation: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Button that moves to the next step in a stepper workflow. */\nclass CdkStepperNext {\n  constructor(_stepper) {\n    this._stepper = _stepper;\n    /** Type of the next button. Defaults to \"submit\" if not specified. */\n    this.type = 'submit';\n  }\n  static {\n    this.ɵfac = function CdkStepperNext_Factory(t) {\n      return new (t || CdkStepperNext)(i0.ɵɵdirectiveInject(CdkStepper));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperNext,\n      selectors: [[\"button\", \"cdkStepperNext\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperNext_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperNext_click_HostBindingHandler() {\n            return ctx._stepper.next();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepperNext, [{\n    type: Directive,\n    args: [{\n      selector: 'button[cdkStepperNext]',\n      host: {\n        '[type]': 'type',\n        '(click)': '_stepper.next()'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkStepper\n  }], {\n    type: [{\n      type: Input\n    }]\n  });\n})();\n/** Button that moves to the previous step in a stepper workflow. */\nclass CdkStepperPrevious {\n  constructor(_stepper) {\n    this._stepper = _stepper;\n    /** Type of the previous button. Defaults to \"button\" if not specified. */\n    this.type = 'button';\n  }\n  static {\n    this.ɵfac = function CdkStepperPrevious_Factory(t) {\n      return new (t || CdkStepperPrevious)(i0.ɵɵdirectiveInject(CdkStepper));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: CdkStepperPrevious,\n      selectors: [[\"button\", \"cdkStepperPrevious\", \"\"]],\n      hostVars: 1,\n      hostBindings: function CdkStepperPrevious_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function CdkStepperPrevious_click_HostBindingHandler() {\n            return ctx._stepper.previous();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", ctx.type);\n        }\n      },\n      inputs: {\n        type: \"type\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepperPrevious, [{\n    type: Directive,\n    args: [{\n      selector: 'button[cdkStepperPrevious]',\n      host: {\n        '[type]': 'type',\n        '(click)': '_stepper.previous()'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: CdkStepper\n  }], {\n    type: [{\n      type: Input\n    }]\n  });\n})();\nclass CdkStepperModule {\n  static {\n    this.ɵfac = function CdkStepperModule_Factory(t) {\n      return new (t || CdkStepperModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: CdkStepperModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [BidiModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkStepperModule, [{\n    type: NgModule,\n    args: [{\n      imports: [BidiModule, CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n      exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };", "map": {"version": 3, "names": ["FocusKeyManager", "i1", "BidiModule", "hasModifierKey", "SPACE", "ENTER", "i0", "Directive", "InjectionToken", "EventEmitter", "forwardRef", "booleanAttribute", "TemplateRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "Optional", "ContentChild", "ViewChild", "Input", "Output", "QueryList", "numberAttribute", "ContentChildren", "NgModule", "_getFocusedElementPierceShadowDom", "Subject", "of", "startWith", "takeUntil", "_c0", "CdkStep_ng_template_0_Template", "rf", "ctx", "ɵɵprojection", "CdkStepHeader", "constructor", "_elementRef", "focus", "nativeElement", "ɵfac", "CdkStepHeader_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "CdkStepLabel", "template", "CdkStepLabel_Factory", "nextId", "StepperSelectionEvent", "STEP_STATE", "NUMBER", "EDIT", "DONE", "ERROR", "STEPPER_GLOBAL_OPTIONS", "CdkStep", "completed", "_completedOverride", "_getDefaultCompleted", "value", "stepControl", "valid", "interacted", "<PERSON><PERSON><PERSON><PERSON>", "_customError", "_getDefaultError", "invalid", "_stepper", "stepperOptions", "interactedStream", "editable", "optional", "_stepperOptions", "_displayDefaultIndicatorType", "displayDefaultIndicatorType", "select", "selected", "reset", "ngOnChanges", "_stateChanged", "_markAsInteracted", "emit", "_showError", "showError", "CdkStep_Factory", "CdkStepper", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "CdkStep_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "<PERSON><PERSON><PERSON><PERSON>", "first", "viewQuery", "CdkStep_Query", "ɵɵviewQuery", "content", "inputs", "label", "errorMessage", "aria<PERSON><PERSON><PERSON>", "ɵɵInputFlags", "None", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "state", "HasDecoratorInputTransform", "outputs", "exportAs", "features", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "CdkStep_Template", "ɵɵprojectionDef", "ɵɵtemplate", "encapsulation", "changeDetection", "OnPush", "decorators", "undefined", "static", "transform", "selectedIndex", "_selectedIndex", "index", "steps", "_steps", "_isValidIndex", "Error", "_anyControlsInvalidOrPending", "toArray", "_updateSelectedItemIndex", "step", "indexOf", "orientation", "_orientation", "_keyManager", "withVerticalOrientation", "_dir", "_changeDetectorRef", "_destroyed", "_sortedHeaders", "linear", "selectionChange", "selectedIndexChange", "_groupId", "ngAfterContentInit", "changes", "pipe", "subscribe", "filter", "notifyOn<PERSON><PERSON>es", "ngAfterViewInit", "_step<PERSON><PERSON>er", "headers", "sort", "a", "b", "documentPosition", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "withWrap", "withHomeAndEnd", "change", "_layoutDirection", "direction", "withHorizontalOrientation", "updateActiveItem", "Math", "max", "ngOnDestroy", "destroy", "next", "complete", "min", "length", "previous", "for<PERSON>ach", "_getStepLabelId", "i", "_getStepContentId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAnimationDirection", "position", "_getIndicatorType", "isCurrentStep", "_isCurrentStep", "_getDefaultIndicatorLogic", "_get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getFocusIndex", "activeItemIndex", "newIndex", "stepsArray", "previouslySelectedIndex", "selectedStep", "previouslySelectedStep", "_containsFocus", "setActiveItem", "_onKeydown", "event", "hasModifier", "keyCode", "manager", "preventDefault", "setFocusOrigin", "onKeydown", "slice", "some", "control", "isIncomplete", "pending", "stepper<PERSON><PERSON>", "focusedElement", "contains", "CdkStepper_Factory", "Directionality", "ChangeDetectorRef", "CdkStepper_ContentQueries", "descendants", "CdkStepperNext", "CdkStepperNext_Factory", "hostVars", "hostBindings", "CdkStepperNext_HostBindings", "ɵɵlistener", "CdkStepperNext_click_HostBindingHandler", "ɵɵhostProperty", "CdkStepperPrevious", "CdkStepperPrevious_Factory", "CdkStepperPrevious_HostBindings", "CdkStepperPrevious_click_HostBindingHandler", "CdkStepperModule", "CdkStepperModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@angular/cdk/fesm2022/stepper.mjs"], "sourcesContent": ["import { FocusKeyManager } from '@angular/cdk/a11y';\nimport * as i1 from '@angular/cdk/bidi';\nimport { BidiModule } from '@angular/cdk/bidi';\nimport { hasModifierKey, SPACE, ENTER } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { Directive, InjectionToken, EventEmitter, forwardRef, booleanAttribute, TemplateRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, ContentChild, ViewChild, Input, Output, QueryList, numberAttribute, ContentChildren, NgModule } from '@angular/core';\nimport { _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { Subject, of } from 'rxjs';\nimport { startWith, takeUntil } from 'rxjs/operators';\n\nclass CdkStepHeader {\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n    }\n    /** Focuses the step header. */\n    focus() {\n        this._elementRef.nativeElement.focus();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepHeader, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkStepHeader, isStandalone: true, selector: \"[cdkStepHeader]\", host: { attributes: { \"role\": \"tab\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepHeader, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepHeader]',\n                    host: {\n                        'role': 'tab',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }] });\n\nclass CdkStepLabel {\n    constructor(/** @docs-private */ template) {\n        this.template = template;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepLabel, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkStepLabel, isStandalone: true, selector: \"[cdkStepLabel]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepLabel]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\n/** Used to generate unique ID for each stepper component. */\nlet nextId = 0;\n/** Change event emitted on selection changes. */\nclass StepperSelectionEvent {\n}\n/** Enum to represent the different states of the steps. */\nconst STEP_STATE = {\n    NUMBER: 'number',\n    EDIT: 'edit',\n    DONE: 'done',\n    ERROR: 'error',\n};\n/** InjectionToken that can be used to specify the global stepper options. */\nconst STEPPER_GLOBAL_OPTIONS = new InjectionToken('STEPPER_GLOBAL_OPTIONS');\nclass CdkStep {\n    /** Whether step is marked as completed. */\n    get completed() {\n        return this._completedOverride == null ? this._getDefaultCompleted() : this._completedOverride;\n    }\n    set completed(value) {\n        this._completedOverride = value;\n    }\n    _getDefaultCompleted() {\n        return this.stepControl ? this.stepControl.valid && this.interacted : this.interacted;\n    }\n    /** Whether step has an error. */\n    get hasError() {\n        return this._customError == null ? this._getDefaultError() : this._customError;\n    }\n    set hasError(value) {\n        this._customError = value;\n    }\n    _getDefaultError() {\n        return this.stepControl && this.stepControl.invalid && this.interacted;\n    }\n    constructor(_stepper, stepperOptions) {\n        this._stepper = _stepper;\n        /** Whether user has attempted to move away from the step. */\n        this.interacted = false;\n        /** Emits when the user has attempted to move away from the step. */\n        this.interactedStream = new EventEmitter();\n        /** Whether the user can return to this step once it has been marked as completed. */\n        this.editable = true;\n        /** Whether the completion of step is optional. */\n        this.optional = false;\n        this._completedOverride = null;\n        this._customError = null;\n        this._stepperOptions = stepperOptions ? stepperOptions : {};\n        this._displayDefaultIndicatorType = this._stepperOptions.displayDefaultIndicatorType !== false;\n    }\n    /** Selects this step component. */\n    select() {\n        this._stepper.selected = this;\n    }\n    /** Resets the step to its initial state. Note that this includes resetting form data. */\n    reset() {\n        this.interacted = false;\n        if (this._completedOverride != null) {\n            this._completedOverride = false;\n        }\n        if (this._customError != null) {\n            this._customError = false;\n        }\n        if (this.stepControl) {\n            this.stepControl.reset();\n        }\n    }\n    ngOnChanges() {\n        // Since basically all inputs of the MatStep get proxied through the view down to the\n        // underlying MatStepHeader, we have to make sure that change detection runs correctly.\n        this._stepper._stateChanged();\n    }\n    _markAsInteracted() {\n        if (!this.interacted) {\n            this.interacted = true;\n            this.interactedStream.emit(this);\n        }\n    }\n    /** Determines whether the error state can be shown. */\n    _showError() {\n        // We want to show the error state either if the user opted into/out of it using the\n        // global options, or if they've explicitly set it through the `hasError` input.\n        return this._stepperOptions.showError ?? this._customError != null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStep, deps: [{ token: forwardRef(() => CdkStepper) }, { token: STEPPER_GLOBAL_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkStep, isStandalone: true, selector: \"cdk-step\", inputs: { stepControl: \"stepControl\", label: \"label\", errorMessage: \"errorMessage\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], state: \"state\", editable: [\"editable\", \"editable\", booleanAttribute], optional: [\"optional\", \"optional\", booleanAttribute], completed: [\"completed\", \"completed\", booleanAttribute], hasError: [\"hasError\", \"hasError\", booleanAttribute] }, outputs: { interactedStream: \"interacted\" }, queries: [{ propertyName: \"stepLabel\", first: true, predicate: CdkStepLabel, descendants: true }], viewQueries: [{ propertyName: \"content\", first: true, predicate: TemplateRef, descendants: true, static: true }], exportAs: [\"cdkStep\"], usesOnChanges: true, ngImport: i0, template: '<ng-template><ng-content></ng-content></ng-template>', isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStep, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'cdk-step',\n                    exportAs: 'cdkStep',\n                    template: '<ng-template><ng-content></ng-content></ng-template>',\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkStepper, decorators: [{\n                    type: Inject,\n                    args: [forwardRef(() => CdkStepper)]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [STEPPER_GLOBAL_OPTIONS]\n                }] }], propDecorators: { stepLabel: [{\n                type: ContentChild,\n                args: [CdkStepLabel]\n            }], content: [{\n                type: ViewChild,\n                args: [TemplateRef, { static: true }]\n            }], stepControl: [{\n                type: Input\n            }], interactedStream: [{\n                type: Output,\n                args: ['interacted']\n            }], label: [{\n                type: Input\n            }], errorMessage: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], state: [{\n                type: Input\n            }], editable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], optional: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], completed: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hasError: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\nclass CdkStepper {\n    /** The index of the selected step. */\n    get selectedIndex() {\n        return this._selectedIndex;\n    }\n    set selectedIndex(index) {\n        if (this.steps && this._steps) {\n            // Ensure that the index can't be out of bounds.\n            if (!this._isValidIndex(index) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw Error('cdkStepper: Cannot assign out-of-bounds value to `selectedIndex`.');\n            }\n            this.selected?._markAsInteracted();\n            if (this._selectedIndex !== index &&\n                !this._anyControlsInvalidOrPending(index) &&\n                (index >= this._selectedIndex || this.steps.toArray()[index].editable)) {\n                this._updateSelectedItemIndex(index);\n            }\n        }\n        else {\n            this._selectedIndex = index;\n        }\n    }\n    /** The step that is selected. */\n    get selected() {\n        return this.steps ? this.steps.toArray()[this.selectedIndex] : undefined;\n    }\n    set selected(step) {\n        this.selectedIndex = step && this.steps ? this.steps.toArray().indexOf(step) : -1;\n    }\n    /** Orientation of the stepper. */\n    get orientation() {\n        return this._orientation;\n    }\n    set orientation(value) {\n        // This is a protected method so that `MatStepper` can hook into it.\n        this._orientation = value;\n        if (this._keyManager) {\n            this._keyManager.withVerticalOrientation(value === 'vertical');\n        }\n    }\n    constructor(_dir, _changeDetectorRef, _elementRef) {\n        this._dir = _dir;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /** Steps that belong to the current stepper, excluding ones from nested steppers. */\n        this.steps = new QueryList();\n        /** List of step headers sorted based on their DOM order. */\n        this._sortedHeaders = new QueryList();\n        /** Whether the validity of previous steps should be checked or not. */\n        this.linear = false;\n        this._selectedIndex = 0;\n        /** Event emitted when the selected step has changed. */\n        this.selectionChange = new EventEmitter();\n        /** Output to support two-way binding on `[(selectedIndex)]` */\n        this.selectedIndexChange = new EventEmitter();\n        this._orientation = 'horizontal';\n        this._groupId = nextId++;\n    }\n    ngAfterContentInit() {\n        this._steps.changes\n            .pipe(startWith(this._steps), takeUntil(this._destroyed))\n            .subscribe((steps) => {\n            this.steps.reset(steps.filter(step => step._stepper === this));\n            this.steps.notifyOnChanges();\n        });\n    }\n    ngAfterViewInit() {\n        // If the step headers are defined outside of the `ngFor` that renders the steps, like in the\n        // Material stepper, they won't appear in the `QueryList` in the same order as they're\n        // rendered in the DOM which will lead to incorrect keyboard navigation. We need to sort\n        // them manually to ensure that they're correct. Alternatively, we can change the Material\n        // template to inline the headers in the `ngFor`, but that'll result in a lot of\n        // code duplication. See #23539.\n        this._stepHeader.changes\n            .pipe(startWith(this._stepHeader), takeUntil(this._destroyed))\n            .subscribe((headers) => {\n            this._sortedHeaders.reset(headers.toArray().sort((a, b) => {\n                const documentPosition = a._elementRef.nativeElement.compareDocumentPosition(b._elementRef.nativeElement);\n                // `compareDocumentPosition` returns a bitmask so we have to use a bitwise operator.\n                // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n                // tslint:disable-next-line:no-bitwise\n                return documentPosition & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;\n            }));\n            this._sortedHeaders.notifyOnChanges();\n        });\n        // Note that while the step headers are content children by default, any components that\n        // extend this one might have them as view children. We initialize the keyboard handling in\n        // AfterViewInit so we're guaranteed for both view and content children to be defined.\n        this._keyManager = new FocusKeyManager(this._sortedHeaders)\n            .withWrap()\n            .withHomeAndEnd()\n            .withVerticalOrientation(this._orientation === 'vertical');\n        (this._dir ? this._dir.change : of())\n            .pipe(startWith(this._layoutDirection()), takeUntil(this._destroyed))\n            .subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n        this._keyManager.updateActiveItem(this._selectedIndex);\n        // No need to `takeUntil` here, because we're the ones destroying `steps`.\n        this.steps.changes.subscribe(() => {\n            if (!this.selected) {\n                this._selectedIndex = Math.max(this._selectedIndex - 1, 0);\n            }\n        });\n        // The logic which asserts that the selected index is within bounds doesn't run before the\n        // steps are initialized, because we don't how many steps there are yet so we may have an\n        // invalid index on init. If that's the case, auto-correct to the default so we don't throw.\n        if (!this._isValidIndex(this._selectedIndex)) {\n            this._selectedIndex = 0;\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this.steps.destroy();\n        this._sortedHeaders.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Selects and focuses the next step in list. */\n    next() {\n        this.selectedIndex = Math.min(this._selectedIndex + 1, this.steps.length - 1);\n    }\n    /** Selects and focuses the previous step in list. */\n    previous() {\n        this.selectedIndex = Math.max(this._selectedIndex - 1, 0);\n    }\n    /** Resets the stepper to its initial state. Note that this includes clearing form data. */\n    reset() {\n        this._updateSelectedItemIndex(0);\n        this.steps.forEach(step => step.reset());\n        this._stateChanged();\n    }\n    /** Returns a unique id for each step label element. */\n    _getStepLabelId(i) {\n        return `cdk-step-label-${this._groupId}-${i}`;\n    }\n    /** Returns unique id for each step content element. */\n    _getStepContentId(i) {\n        return `cdk-step-content-${this._groupId}-${i}`;\n    }\n    /** Marks the component to be change detected. */\n    _stateChanged() {\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Returns position state of the step with the given index. */\n    _getAnimationDirection(index) {\n        const position = index - this._selectedIndex;\n        if (position < 0) {\n            return this._layoutDirection() === 'rtl' ? 'next' : 'previous';\n        }\n        else if (position > 0) {\n            return this._layoutDirection() === 'rtl' ? 'previous' : 'next';\n        }\n        return 'current';\n    }\n    /** Returns the type of icon to be displayed. */\n    _getIndicatorType(index, state = STEP_STATE.NUMBER) {\n        const step = this.steps.toArray()[index];\n        const isCurrentStep = this._isCurrentStep(index);\n        return step._displayDefaultIndicatorType\n            ? this._getDefaultIndicatorLogic(step, isCurrentStep)\n            : this._getGuidelineLogic(step, isCurrentStep, state);\n    }\n    _getDefaultIndicatorLogic(step, isCurrentStep) {\n        if (step._showError() && step.hasError && !isCurrentStep) {\n            return STEP_STATE.ERROR;\n        }\n        else if (!step.completed || isCurrentStep) {\n            return STEP_STATE.NUMBER;\n        }\n        else {\n            return step.editable ? STEP_STATE.EDIT : STEP_STATE.DONE;\n        }\n    }\n    _getGuidelineLogic(step, isCurrentStep, state = STEP_STATE.NUMBER) {\n        if (step._showError() && step.hasError && !isCurrentStep) {\n            return STEP_STATE.ERROR;\n        }\n        else if (step.completed && !isCurrentStep) {\n            return STEP_STATE.DONE;\n        }\n        else if (step.completed && isCurrentStep) {\n            return state;\n        }\n        else if (step.editable && isCurrentStep) {\n            return STEP_STATE.EDIT;\n        }\n        else {\n            return state;\n        }\n    }\n    _isCurrentStep(index) {\n        return this._selectedIndex === index;\n    }\n    /** Returns the index of the currently-focused step header. */\n    _getFocusIndex() {\n        return this._keyManager ? this._keyManager.activeItemIndex : this._selectedIndex;\n    }\n    _updateSelectedItemIndex(newIndex) {\n        const stepsArray = this.steps.toArray();\n        this.selectionChange.emit({\n            selectedIndex: newIndex,\n            previouslySelectedIndex: this._selectedIndex,\n            selectedStep: stepsArray[newIndex],\n            previouslySelectedStep: stepsArray[this._selectedIndex],\n        });\n        // If focus is inside the stepper, move it to the next header, otherwise it may become\n        // lost when the active step content is hidden. We can't be more granular with the check\n        // (e.g. checking whether focus is inside the active step), because we don't have a\n        // reference to the elements that are rendering out the content.\n        this._containsFocus()\n            ? this._keyManager.setActiveItem(newIndex)\n            : this._keyManager.updateActiveItem(newIndex);\n        this._selectedIndex = newIndex;\n        this.selectedIndexChange.emit(this._selectedIndex);\n        this._stateChanged();\n    }\n    _onKeydown(event) {\n        const hasModifier = hasModifierKey(event);\n        const keyCode = event.keyCode;\n        const manager = this._keyManager;\n        if (manager.activeItemIndex != null &&\n            !hasModifier &&\n            (keyCode === SPACE || keyCode === ENTER)) {\n            this.selectedIndex = manager.activeItemIndex;\n            event.preventDefault();\n        }\n        else {\n            manager.setFocusOrigin('keyboard').onKeydown(event);\n        }\n    }\n    _anyControlsInvalidOrPending(index) {\n        if (this.linear && index >= 0) {\n            return this.steps\n                .toArray()\n                .slice(0, index)\n                .some(step => {\n                const control = step.stepControl;\n                const isIncomplete = control\n                    ? control.invalid || control.pending || !step.interacted\n                    : !step.completed;\n                return isIncomplete && !step.optional && !step._completedOverride;\n            });\n        }\n        return false;\n    }\n    _layoutDirection() {\n        return this._dir && this._dir.value === 'rtl' ? 'rtl' : 'ltr';\n    }\n    /** Checks whether the stepper contains the focused element. */\n    _containsFocus() {\n        const stepperElement = this._elementRef.nativeElement;\n        const focusedElement = _getFocusedElementPierceShadowDom();\n        return stepperElement === focusedElement || stepperElement.contains(focusedElement);\n    }\n    /** Checks whether the passed-in index is a valid step index. */\n    _isValidIndex(index) {\n        return index > -1 && (!this.steps || index < this.steps.length);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepper, deps: [{ token: i1.Directionality, optional: true }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkStepper, isStandalone: true, selector: \"[cdkStepper]\", inputs: { linear: [\"linear\", \"linear\", booleanAttribute], selectedIndex: [\"selectedIndex\", \"selectedIndex\", numberAttribute], selected: \"selected\", orientation: \"orientation\" }, outputs: { selectionChange: \"selectionChange\", selectedIndexChange: \"selectedIndexChange\" }, queries: [{ propertyName: \"_steps\", predicate: CdkStep, descendants: true }, { propertyName: \"_stepHeader\", predicate: CdkStepHeader, descendants: true }], exportAs: [\"cdkStepper\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepper, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkStepper]',\n                    exportAs: 'cdkStepper',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }], propDecorators: { _steps: [{\n                type: ContentChildren,\n                args: [CdkStep, { descendants: true }]\n            }], _stepHeader: [{\n                type: ContentChildren,\n                args: [CdkStepHeader, { descendants: true }]\n            }], linear: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectedIndex: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], selected: [{\n                type: Input\n            }], selectionChange: [{\n                type: Output\n            }], selectedIndexChange: [{\n                type: Output\n            }], orientation: [{\n                type: Input\n            }] } });\n\n/** Button that moves to the next step in a stepper workflow. */\nclass CdkStepperNext {\n    constructor(_stepper) {\n        this._stepper = _stepper;\n        /** Type of the next button. Defaults to \"submit\" if not specified. */\n        this.type = 'submit';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperNext, deps: [{ token: CdkStepper }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkStepperNext, isStandalone: true, selector: \"button[cdkStepperNext]\", inputs: { type: \"type\" }, host: { listeners: { \"click\": \"_stepper.next()\" }, properties: { \"type\": \"type\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperNext, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[cdkStepperNext]',\n                    host: {\n                        '[type]': 'type',\n                        '(click)': '_stepper.next()',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkStepper }], propDecorators: { type: [{\n                type: Input\n            }] } });\n/** Button that moves to the previous step in a stepper workflow. */\nclass CdkStepperPrevious {\n    constructor(_stepper) {\n        this._stepper = _stepper;\n        /** Type of the previous button. Defaults to \"button\" if not specified. */\n        this.type = 'button';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperPrevious, deps: [{ token: CdkStepper }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkStepperPrevious, isStandalone: true, selector: \"button[cdkStepperPrevious]\", inputs: { type: \"type\" }, host: { listeners: { \"click\": \"_stepper.previous()\" }, properties: { \"type\": \"type\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperPrevious, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[cdkStepperPrevious]',\n                    host: {\n                        '[type]': 'type',\n                        '(click)': '_stepper.previous()',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: CdkStepper }], propDecorators: { type: [{\n                type: Input\n            }] } });\n\nclass CdkStepperModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperModule, imports: [BidiModule,\n            CdkStep,\n            CdkStepper,\n            CdkStepHeader,\n            CdkStepLabel,\n            CdkStepperNext,\n            CdkStepperPrevious], exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperModule, imports: [BidiModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkStepperModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        BidiModule,\n                        CdkStep,\n                        CdkStepper,\n                        CdkStepHeader,\n                        CdkStepLabel,\n                        CdkStepperNext,\n                        CdkStepperPrevious,\n                    ],\n                    exports: [CdkStep, CdkStepper, CdkStepHeader, CdkStepLabel, CdkStepperNext, CdkStepperPrevious],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkStep, CdkStepHeader, CdkStepLabel, CdkStepper, CdkStepperModule, CdkStepperNext, CdkStepperPrevious, STEPPER_GLOBAL_OPTIONS, STEP_STATE, StepperSelectionEvent };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mBAAmB;AACnD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,cAAc,EAAEC,KAAK,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,cAAc,EAAEC,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,WAAW,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,eAAe,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AAC1R,SAASC,iCAAiC,QAAQ,uBAAuB;AACzE,SAASC,OAAO,EAAEC,EAAE,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAAC,MAAAC,GAAA;AAAA,SAAAC,+BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAU8C3B,EAAE,CAAA6B,YAAA,EAkH2zB,CAAC;EAAA;AAAA;AA1Hl6B,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACA;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACD,WAAW,CAACE,aAAa,CAACD,KAAK,CAAC,CAAC;EAC1C;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,sBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFP,aAAa,EAAvB9B,EAAE,CAAAsC,iBAAA,CAAuCtC,EAAE,CAACuC,UAAU;IAAA,CAA4C;EAAE;EACpM;IAAS,IAAI,CAACC,IAAI,kBAD8ExC,EAAE,CAAAyC,iBAAA;MAAAC,IAAA,EACJZ,aAAa;MAAAa,SAAA;MAAAC,SAAA,WAAiF,KAAK;MAAAC,UAAA;IAAA,EAAqB;EAAE;AAC5N;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG9C,EAAE,CAAA+C,iBAAA,CAGXjB,aAAa,EAAc,CAAC;IAC3GY,IAAI,EAAEzC,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QACF,MAAM,EAAE;MACZ,CAAC;MACDL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAE1C,EAAE,CAACuC;EAAW,CAAC,CAAC;AAAA;AAE3D,MAAMY,YAAY,CAAC;EACfpB,WAAWA,CAAC,oBAAqBqB,QAAQ,EAAE;IACvC,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACA;IAAS,IAAI,CAACjB,IAAI,YAAAkB,qBAAAhB,CAAA;MAAA,YAAAA,CAAA,IAAwFc,YAAY,EAlBtBnD,EAAE,CAAAsC,iBAAA,CAkBsCtC,EAAE,CAACM,WAAW;IAAA,CAA4C;EAAE;EACpM;IAAS,IAAI,CAACkC,IAAI,kBAnB8ExC,EAAE,CAAAyC,iBAAA;MAAAC,IAAA,EAmBJS,YAAY;MAAAR,SAAA;MAAAE,UAAA;IAAA,EAAiE;EAAE;AACjL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KArBoG9C,EAAE,CAAA+C,iBAAA,CAqBXI,YAAY,EAAc,CAAC;IAC1GT,IAAI,EAAEzC,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1BJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAE1C,EAAE,CAACM;EAAY,CAAC,CAAC;AAAA;;AAE5D;AACA,IAAIgD,MAAM,GAAG,CAAC;AACd;AACA,MAAMC,qBAAqB,CAAC;AAE5B;AACA,MAAMC,UAAU,GAAG;EACfC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,sBAAsB,GAAG,IAAI3D,cAAc,CAAC,wBAAwB,CAAC;AAC3E,MAAM4D,OAAO,CAAC;EACV;EACA,IAAIC,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,kBAAkB,IAAI,IAAI,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC,GAAG,IAAI,CAACD,kBAAkB;EAClG;EACA,IAAID,SAASA,CAACG,KAAK,EAAE;IACjB,IAAI,CAACF,kBAAkB,GAAGE,KAAK;EACnC;EACAD,oBAAoBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACE,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,KAAK,IAAI,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU;EACzF;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,YAAY,IAAI,IAAI,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACD,YAAY;EAClF;EACA,IAAID,QAAQA,CAACJ,KAAK,EAAE;IAChB,IAAI,CAACK,YAAY,GAAGL,KAAK;EAC7B;EACAM,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACL,WAAW,IAAI,IAAI,CAACA,WAAW,CAACM,OAAO,IAAI,IAAI,CAACJ,UAAU;EAC1E;EACAtC,WAAWA,CAAC2C,QAAQ,EAAEC,cAAc,EAAE;IAClC,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAACL,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACO,gBAAgB,GAAG,IAAIzE,YAAY,CAAC,CAAC;IAC1C;IACA,IAAI,CAAC0E,QAAQ,GAAG,IAAI;IACpB;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACd,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACO,YAAY,GAAG,IAAI;IACxB,IAAI,CAACQ,eAAe,GAAGJ,cAAc,GAAGA,cAAc,GAAG,CAAC,CAAC;IAC3D,IAAI,CAACK,4BAA4B,GAAG,IAAI,CAACD,eAAe,CAACE,2BAA2B,KAAK,KAAK;EAClG;EACA;EACAC,MAAMA,CAAA,EAAG;IACL,IAAI,CAACR,QAAQ,CAACS,QAAQ,GAAG,IAAI;EACjC;EACA;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACf,UAAU,GAAG,KAAK;IACvB,IAAI,IAAI,CAACL,kBAAkB,IAAI,IAAI,EAAE;MACjC,IAAI,CAACA,kBAAkB,GAAG,KAAK;IACnC;IACA,IAAI,IAAI,CAACO,YAAY,IAAI,IAAI,EAAE;MAC3B,IAAI,CAACA,YAAY,GAAG,KAAK;IAC7B;IACA,IAAI,IAAI,CAACJ,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACiB,KAAK,CAAC,CAAC;IAC5B;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,CAACX,QAAQ,CAACY,aAAa,CAAC,CAAC;EACjC;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAClB,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAACO,gBAAgB,CAACY,IAAI,CAAC,IAAI,CAAC;IACpC;EACJ;EACA;EACAC,UAAUA,CAAA,EAAG;IACT;IACA;IACA,OAAO,IAAI,CAACV,eAAe,CAACW,SAAS,IAAI,IAAI,CAACnB,YAAY,IAAI,IAAI;EACtE;EACA;IAAS,IAAI,CAACpC,IAAI,YAAAwD,gBAAAtD,CAAA;MAAA,YAAAA,CAAA,IAAwFyB,OAAO,EAjHjB9D,EAAE,CAAAsC,iBAAA,CAiHiClC,UAAU,CAAC,MAAMwF,UAAU,CAAC,GAjH/D5F,EAAE,CAAAsC,iBAAA,CAiH0EuB,sBAAsB;IAAA,CAA4D;EAAE;EAChQ;IAAS,IAAI,CAACgC,IAAI,kBAlH8E7F,EAAE,CAAA8F,iBAAA;MAAApD,IAAA,EAkHJoB,OAAO;MAAAnB,SAAA;MAAAoD,cAAA,WAAAC,uBAAArE,EAAA,EAAAC,GAAA,EAAAqE,QAAA;QAAA,IAAAtE,EAAA;UAlHL3B,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EAkH2jB9C,YAAY;QAAA;QAAA,IAAAxB,EAAA;UAAA,IAAAwE,EAAA;UAlHzkBnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAzE,GAAA,CAAA0E,SAAA,GAAAH,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAC,cAAA9E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3B,EAAE,CAAA0G,WAAA,CAkHgqBpG,WAAW;QAAA;QAAA,IAAAqB,EAAA;UAAA,IAAAwE,EAAA;UAlH7qBnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAzE,GAAA,CAAA+E,OAAA,GAAAR,EAAA,CAAAI,KAAA;QAAA;MAAA;MAAAK,MAAA;QAAAzC,WAAA;QAAA0C,KAAA;QAAAC,YAAA;QAAAC,SAAA,GAAF/G,EAAE,CAAAgH,YAAA,CAAAC,IAAA;QAAAC,cAAA,GAAFlH,EAAE,CAAAgH,YAAA,CAAAC,IAAA;QAAAE,KAAA;QAAAtC,QAAA,GAAF7E,EAAE,CAAAgH,YAAA,CAAAI,0BAAA,0BAkHqR/G,gBAAgB;QAAAyE,QAAA,GAlHvS9E,EAAE,CAAAgH,YAAA,CAAAI,0BAAA,0BAkH2U/G,gBAAgB;QAAA0D,SAAA,GAlH7V/D,EAAE,CAAAgH,YAAA,CAAAI,0BAAA,4BAkHoY/G,gBAAgB;QAAAiE,QAAA,GAlHtZtE,EAAE,CAAAgH,YAAA,CAAAI,0BAAA,0BAkH0b/G,gBAAgB;MAAA;MAAAgH,OAAA;QAAAzC,gBAAA;MAAA;MAAA0C,QAAA;MAAAzE,UAAA;MAAA0E,QAAA,GAlH5cvH,EAAE,CAAAwH,wBAAA,EAAFxH,EAAE,CAAAyH,oBAAA,EAAFzH,EAAE,CAAA0H,mBAAA;MAAAC,kBAAA,EAAAlG,GAAA;MAAAmG,KAAA;MAAAC,IAAA;MAAAzE,QAAA,WAAA0E,iBAAAnG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3B,EAAE,CAAA+H,eAAA;UAAF/H,EAAE,CAAAgI,UAAA,IAAAtG,8BAAA,qBAkHkyB,CAAC;QAAA;MAAA;MAAAuG,aAAA;MAAAC,eAAA;IAAA,EAAyJ;EAAE;AACpiC;AACA;EAAA,QAAApF,SAAA,oBAAAA,SAAA,KApHoG9C,EAAE,CAAA+C,iBAAA,CAoHXe,OAAO,EAAc,CAAC;IACrGpB,IAAI,EAAEnC,SAAS;IACfyC,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBqE,QAAQ,EAAE,SAAS;MACnBlE,QAAQ,EAAE,sDAAsD;MAChE6E,aAAa,EAAEzH,iBAAiB,CAACyG,IAAI;MACrCiB,eAAe,EAAEzH,uBAAuB,CAAC0H,MAAM;MAC/CtF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEkD,UAAU;IAAEwC,UAAU,EAAE,CAAC;MAChD1F,IAAI,EAAEhC,MAAM;MACZsC,IAAI,EAAE,CAAC5C,UAAU,CAAC,MAAMwF,UAAU,CAAC;IACvC,CAAC;EAAE,CAAC,EAAE;IAAElD,IAAI,EAAE2F,SAAS;IAAED,UAAU,EAAE,CAAC;MAClC1F,IAAI,EAAE/B;IACV,CAAC,EAAE;MACC+B,IAAI,EAAEhC,MAAM;MACZsC,IAAI,EAAE,CAACa,sBAAsB;IACjC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEyC,SAAS,EAAE,CAAC;MACrC5D,IAAI,EAAE9B,YAAY;MAClBoC,IAAI,EAAE,CAACG,YAAY;IACvB,CAAC,CAAC;IAAEwD,OAAO,EAAE,CAAC;MACVjE,IAAI,EAAE7B,SAAS;MACfmC,IAAI,EAAE,CAAC1C,WAAW,EAAE;QAAEgI,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC,CAAC;IAAEnE,WAAW,EAAE,CAAC;MACdzB,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAE8D,gBAAgB,EAAE,CAAC;MACnBlC,IAAI,EAAE3B,MAAM;MACZiC,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE6D,KAAK,EAAE,CAAC;MACRnE,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAEgG,YAAY,EAAE,CAAC;MACfpE,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAEiG,SAAS,EAAE,CAAC;MACZrE,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEkE,cAAc,EAAE,CAAC;MACjBxE,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEmE,KAAK,EAAE,CAAC;MACRzE,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAE+D,QAAQ,EAAE,CAAC;MACXnC,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAEuF,SAAS,EAAElI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyE,QAAQ,EAAE,CAAC;MACXpC,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAEuF,SAAS,EAAElI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0D,SAAS,EAAE,CAAC;MACZrB,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAEuF,SAAS,EAAElI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiE,QAAQ,EAAE,CAAC;MACX5B,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAEuF,SAAS,EAAElI;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMuF,UAAU,CAAC;EACb;EACA,IAAI4C,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,cAAc;EAC9B;EACA,IAAID,aAAaA,CAACE,KAAK,EAAE;IACrB,IAAI,IAAI,CAACC,KAAK,IAAI,IAAI,CAACC,MAAM,EAAE;MAC3B;MACA,IAAI,CAAC,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,KAAK,OAAO5F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC/E,MAAMgG,KAAK,CAAC,mEAAmE,CAAC;MACpF;MACA,IAAI,CAAC3D,QAAQ,EAAEI,iBAAiB,CAAC,CAAC;MAClC,IAAI,IAAI,CAACkD,cAAc,KAAKC,KAAK,IAC7B,CAAC,IAAI,CAACK,4BAA4B,CAACL,KAAK,CAAC,KACxCA,KAAK,IAAI,IAAI,CAACD,cAAc,IAAI,IAAI,CAACE,KAAK,CAACK,OAAO,CAAC,CAAC,CAACN,KAAK,CAAC,CAAC7D,QAAQ,CAAC,EAAE;QACxE,IAAI,CAACoE,wBAAwB,CAACP,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAI,CAACD,cAAc,GAAGC,KAAK;IAC/B;EACJ;EACA;EACA,IAAIvD,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACwD,KAAK,GAAG,IAAI,CAACA,KAAK,CAACK,OAAO,CAAC,CAAC,CAAC,IAAI,CAACR,aAAa,CAAC,GAAGH,SAAS;EAC5E;EACA,IAAIlD,QAAQA,CAAC+D,IAAI,EAAE;IACf,IAAI,CAACV,aAAa,GAAGU,IAAI,IAAI,IAAI,CAACP,KAAK,GAAG,IAAI,CAACA,KAAK,CAACK,OAAO,CAAC,CAAC,CAACG,OAAO,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;EACrF;EACA;EACA,IAAIE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAAClF,KAAK,EAAE;IACnB;IACA,IAAI,CAACmF,YAAY,GAAGnF,KAAK;IACzB,IAAI,IAAI,CAACoF,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAACC,uBAAuB,CAACrF,KAAK,KAAK,UAAU,CAAC;IAClE;EACJ;EACAnC,WAAWA,CAACyH,IAAI,EAAEC,kBAAkB,EAAEzH,WAAW,EAAE;IAC/C,IAAI,CAACwH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACzH,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAAC0H,UAAU,GAAG,IAAIrI,OAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACsH,KAAK,GAAG,IAAI3H,SAAS,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC2I,cAAc,GAAG,IAAI3I,SAAS,CAAC,CAAC;IACrC;IACA,IAAI,CAAC4I,MAAM,GAAG,KAAK;IACnB,IAAI,CAACnB,cAAc,GAAG,CAAC;IACvB;IACA,IAAI,CAACoB,eAAe,GAAG,IAAI1J,YAAY,CAAC,CAAC;IACzC;IACA,IAAI,CAAC2J,mBAAmB,GAAG,IAAI3J,YAAY,CAAC,CAAC;IAC7C,IAAI,CAACkJ,YAAY,GAAG,YAAY;IAChC,IAAI,CAACU,QAAQ,GAAGzG,MAAM,EAAE;EAC5B;EACA0G,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpB,MAAM,CAACqB,OAAO,CACdC,IAAI,CAAC3I,SAAS,CAAC,IAAI,CAACqH,MAAM,CAAC,EAAEpH,SAAS,CAAC,IAAI,CAACkI,UAAU,CAAC,CAAC,CACxDS,SAAS,CAAExB,KAAK,IAAK;MACtB,IAAI,CAACA,KAAK,CAACvD,KAAK,CAACuD,KAAK,CAACyB,MAAM,CAAClB,IAAI,IAAIA,IAAI,CAACxE,QAAQ,KAAK,IAAI,CAAC,CAAC;MAC9D,IAAI,CAACiE,KAAK,CAAC0B,eAAe,CAAC,CAAC;IAChC,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,WAAW,CAACN,OAAO,CACnBC,IAAI,CAAC3I,SAAS,CAAC,IAAI,CAACgJ,WAAW,CAAC,EAAE/I,SAAS,CAAC,IAAI,CAACkI,UAAU,CAAC,CAAC,CAC7DS,SAAS,CAAEK,OAAO,IAAK;MACxB,IAAI,CAACb,cAAc,CAACvE,KAAK,CAACoF,OAAO,CAACxB,OAAO,CAAC,CAAC,CAACyB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACvD,MAAMC,gBAAgB,GAAGF,CAAC,CAAC1I,WAAW,CAACE,aAAa,CAAC2I,uBAAuB,CAACF,CAAC,CAAC3I,WAAW,CAACE,aAAa,CAAC;QACzG;QACA;QACA;QACA,OAAO0I,gBAAgB,GAAGE,IAAI,CAACC,2BAA2B,GAAG,CAAC,CAAC,GAAG,CAAC;MACvE,CAAC,CAAC,CAAC;MACH,IAAI,CAACpB,cAAc,CAACU,eAAe,CAAC,CAAC;IACzC,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAACf,WAAW,GAAG,IAAI5J,eAAe,CAAC,IAAI,CAACiK,cAAc,CAAC,CACtDqB,QAAQ,CAAC,CAAC,CACVC,cAAc,CAAC,CAAC,CAChB1B,uBAAuB,CAAC,IAAI,CAACF,YAAY,KAAK,UAAU,CAAC;IAC9D,CAAC,IAAI,CAACG,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC0B,MAAM,GAAG5J,EAAE,CAAC,CAAC,EAC/B4I,IAAI,CAAC3I,SAAS,CAAC,IAAI,CAAC4J,gBAAgB,CAAC,CAAC,CAAC,EAAE3J,SAAS,CAAC,IAAI,CAACkI,UAAU,CAAC,CAAC,CACpES,SAAS,CAACiB,SAAS,IAAI,IAAI,CAAC9B,WAAW,CAAC+B,yBAAyB,CAACD,SAAS,CAAC,CAAC;IAClF,IAAI,CAAC9B,WAAW,CAACgC,gBAAgB,CAAC,IAAI,CAAC7C,cAAc,CAAC;IACtD;IACA,IAAI,CAACE,KAAK,CAACsB,OAAO,CAACE,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAAChF,QAAQ,EAAE;QAChB,IAAI,CAACsD,cAAc,GAAG8C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC;MAC9D;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACI,aAAa,CAAC,IAAI,CAACJ,cAAc,CAAC,EAAE;MAC1C,IAAI,CAACA,cAAc,GAAG,CAAC;IAC3B;EACJ;EACAgD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnC,WAAW,EAAEoC,OAAO,CAAC,CAAC;IAC3B,IAAI,CAAC/C,KAAK,CAAC+C,OAAO,CAAC,CAAC;IACpB,IAAI,CAAC/B,cAAc,CAAC+B,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAChC,UAAU,CAACiC,IAAI,CAAC,CAAC;IACtB,IAAI,CAACjC,UAAU,CAACkC,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAD,IAAIA,CAAA,EAAG;IACH,IAAI,CAACnD,aAAa,GAAG+C,IAAI,CAACM,GAAG,CAAC,IAAI,CAACpD,cAAc,GAAG,CAAC,EAAE,IAAI,CAACE,KAAK,CAACmD,MAAM,GAAG,CAAC,CAAC;EACjF;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACvD,aAAa,GAAG+C,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC/C,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC;EAC7D;EACA;EACArD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC6D,wBAAwB,CAAC,CAAC,CAAC;IAChC,IAAI,CAACN,KAAK,CAACqD,OAAO,CAAC9C,IAAI,IAAIA,IAAI,CAAC9D,KAAK,CAAC,CAAC,CAAC;IACxC,IAAI,CAACE,aAAa,CAAC,CAAC;EACxB;EACA;EACA2G,eAAeA,CAACC,CAAC,EAAE;IACf,OAAO,kBAAkB,IAAI,CAACnC,QAAQ,IAAImC,CAAC,EAAE;EACjD;EACA;EACAC,iBAAiBA,CAACD,CAAC,EAAE;IACjB,OAAO,oBAAoB,IAAI,CAACnC,QAAQ,IAAImC,CAAC,EAAE;EACnD;EACA;EACA5G,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACmE,kBAAkB,CAAC2C,YAAY,CAAC,CAAC;EAC1C;EACA;EACAC,sBAAsBA,CAAC3D,KAAK,EAAE;IAC1B,MAAM4D,QAAQ,GAAG5D,KAAK,GAAG,IAAI,CAACD,cAAc;IAC5C,IAAI6D,QAAQ,GAAG,CAAC,EAAE;MACd,OAAO,IAAI,CAACnB,gBAAgB,CAAC,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,UAAU;IAClE,CAAC,MACI,IAAImB,QAAQ,GAAG,CAAC,EAAE;MACnB,OAAO,IAAI,CAACnB,gBAAgB,CAAC,CAAC,KAAK,KAAK,GAAG,UAAU,GAAG,MAAM;IAClE;IACA,OAAO,SAAS;EACpB;EACA;EACAoB,iBAAiBA,CAAC7D,KAAK,EAAEvB,KAAK,GAAG3D,UAAU,CAACC,MAAM,EAAE;IAChD,MAAMyF,IAAI,GAAG,IAAI,CAACP,KAAK,CAACK,OAAO,CAAC,CAAC,CAACN,KAAK,CAAC;IACxC,MAAM8D,aAAa,GAAG,IAAI,CAACC,cAAc,CAAC/D,KAAK,CAAC;IAChD,OAAOQ,IAAI,CAAClE,4BAA4B,GAClC,IAAI,CAAC0H,yBAAyB,CAACxD,IAAI,EAAEsD,aAAa,CAAC,GACnD,IAAI,CAACG,kBAAkB,CAACzD,IAAI,EAAEsD,aAAa,EAAErF,KAAK,CAAC;EAC7D;EACAuF,yBAAyBA,CAACxD,IAAI,EAAEsD,aAAa,EAAE;IAC3C,IAAItD,IAAI,CAACzD,UAAU,CAAC,CAAC,IAAIyD,IAAI,CAAC5E,QAAQ,IAAI,CAACkI,aAAa,EAAE;MACtD,OAAOhJ,UAAU,CAACI,KAAK;IAC3B,CAAC,MACI,IAAI,CAACsF,IAAI,CAACnF,SAAS,IAAIyI,aAAa,EAAE;MACvC,OAAOhJ,UAAU,CAACC,MAAM;IAC5B,CAAC,MACI;MACD,OAAOyF,IAAI,CAACrE,QAAQ,GAAGrB,UAAU,CAACE,IAAI,GAAGF,UAAU,CAACG,IAAI;IAC5D;EACJ;EACAgJ,kBAAkBA,CAACzD,IAAI,EAAEsD,aAAa,EAAErF,KAAK,GAAG3D,UAAU,CAACC,MAAM,EAAE;IAC/D,IAAIyF,IAAI,CAACzD,UAAU,CAAC,CAAC,IAAIyD,IAAI,CAAC5E,QAAQ,IAAI,CAACkI,aAAa,EAAE;MACtD,OAAOhJ,UAAU,CAACI,KAAK;IAC3B,CAAC,MACI,IAAIsF,IAAI,CAACnF,SAAS,IAAI,CAACyI,aAAa,EAAE;MACvC,OAAOhJ,UAAU,CAACG,IAAI;IAC1B,CAAC,MACI,IAAIuF,IAAI,CAACnF,SAAS,IAAIyI,aAAa,EAAE;MACtC,OAAOrF,KAAK;IAChB,CAAC,MACI,IAAI+B,IAAI,CAACrE,QAAQ,IAAI2H,aAAa,EAAE;MACrC,OAAOhJ,UAAU,CAACE,IAAI;IAC1B,CAAC,MACI;MACD,OAAOyD,KAAK;IAChB;EACJ;EACAsF,cAAcA,CAAC/D,KAAK,EAAE;IAClB,OAAO,IAAI,CAACD,cAAc,KAAKC,KAAK;EACxC;EACA;EACAkE,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACtD,WAAW,GAAG,IAAI,CAACA,WAAW,CAACuD,eAAe,GAAG,IAAI,CAACpE,cAAc;EACpF;EACAQ,wBAAwBA,CAAC6D,QAAQ,EAAE;IAC/B,MAAMC,UAAU,GAAG,IAAI,CAACpE,KAAK,CAACK,OAAO,CAAC,CAAC;IACvC,IAAI,CAACa,eAAe,CAACrE,IAAI,CAAC;MACtBgD,aAAa,EAAEsE,QAAQ;MACvBE,uBAAuB,EAAE,IAAI,CAACvE,cAAc;MAC5CwE,YAAY,EAAEF,UAAU,CAACD,QAAQ,CAAC;MAClCI,sBAAsB,EAAEH,UAAU,CAAC,IAAI,CAACtE,cAAc;IAC1D,CAAC,CAAC;IACF;IACA;IACA;IACA;IACA,IAAI,CAAC0E,cAAc,CAAC,CAAC,GACf,IAAI,CAAC7D,WAAW,CAAC8D,aAAa,CAACN,QAAQ,CAAC,GACxC,IAAI,CAACxD,WAAW,CAACgC,gBAAgB,CAACwB,QAAQ,CAAC;IACjD,IAAI,CAACrE,cAAc,GAAGqE,QAAQ;IAC9B,IAAI,CAAChD,mBAAmB,CAACtE,IAAI,CAAC,IAAI,CAACiD,cAAc,CAAC;IAClD,IAAI,CAACnD,aAAa,CAAC,CAAC;EACxB;EACA+H,UAAUA,CAACC,KAAK,EAAE;IACd,MAAMC,WAAW,GAAG1N,cAAc,CAACyN,KAAK,CAAC;IACzC,MAAME,OAAO,GAAGF,KAAK,CAACE,OAAO;IAC7B,MAAMC,OAAO,GAAG,IAAI,CAACnE,WAAW;IAChC,IAAImE,OAAO,CAACZ,eAAe,IAAI,IAAI,IAC/B,CAACU,WAAW,KACXC,OAAO,KAAK1N,KAAK,IAAI0N,OAAO,KAAKzN,KAAK,CAAC,EAAE;MAC1C,IAAI,CAACyI,aAAa,GAAGiF,OAAO,CAACZ,eAAe;MAC5CS,KAAK,CAACI,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI;MACDD,OAAO,CAACE,cAAc,CAAC,UAAU,CAAC,CAACC,SAAS,CAACN,KAAK,CAAC;IACvD;EACJ;EACAvE,4BAA4BA,CAACL,KAAK,EAAE;IAChC,IAAI,IAAI,CAACkB,MAAM,IAAIlB,KAAK,IAAI,CAAC,EAAE;MAC3B,OAAO,IAAI,CAACC,KAAK,CACZK,OAAO,CAAC,CAAC,CACT6E,KAAK,CAAC,CAAC,EAAEnF,KAAK,CAAC,CACfoF,IAAI,CAAC5E,IAAI,IAAI;QACd,MAAM6E,OAAO,GAAG7E,IAAI,CAAC/E,WAAW;QAChC,MAAM6J,YAAY,GAAGD,OAAO,GACtBA,OAAO,CAACtJ,OAAO,IAAIsJ,OAAO,CAACE,OAAO,IAAI,CAAC/E,IAAI,CAAC7E,UAAU,GACtD,CAAC6E,IAAI,CAACnF,SAAS;QACrB,OAAOiK,YAAY,IAAI,CAAC9E,IAAI,CAACpE,QAAQ,IAAI,CAACoE,IAAI,CAAClF,kBAAkB;MACrE,CAAC,CAAC;IACN;IACA,OAAO,KAAK;EAChB;EACAmH,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC3B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACtF,KAAK,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EACjE;EACA;EACAiJ,cAAcA,CAAA,EAAG;IACb,MAAMe,cAAc,GAAG,IAAI,CAAClM,WAAW,CAACE,aAAa;IACrD,MAAMiM,cAAc,GAAG/M,iCAAiC,CAAC,CAAC;IAC1D,OAAO8M,cAAc,KAAKC,cAAc,IAAID,cAAc,CAACE,QAAQ,CAACD,cAAc,CAAC;EACvF;EACA;EACAtF,aAAaA,CAACH,KAAK,EAAE;IACjB,OAAOA,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAACC,KAAK,IAAID,KAAK,GAAG,IAAI,CAACC,KAAK,CAACmD,MAAM,CAAC;EACnE;EACA;IAAS,IAAI,CAAC3J,IAAI,YAAAkM,mBAAAhM,CAAA;MAAA,YAAAA,CAAA,IAAwFuD,UAAU,EA7apB5F,EAAE,CAAAsC,iBAAA,CA6aoC3C,EAAE,CAAC2O,cAAc,MA7avDtO,EAAE,CAAAsC,iBAAA,CA6akFtC,EAAE,CAACuO,iBAAiB,GA7axGvO,EAAE,CAAAsC,iBAAA,CA6amHtC,EAAE,CAACuC,UAAU;IAAA,CAA4C;EAAE;EAChR;IAAS,IAAI,CAACC,IAAI,kBA9a8ExC,EAAE,CAAAyC,iBAAA;MAAAC,IAAA,EA8aJkD,UAAU;MAAAjD,SAAA;MAAAoD,cAAA,WAAAyI,0BAAA7M,EAAA,EAAAC,GAAA,EAAAqE,QAAA;QAAA,IAAAtE,EAAA;UA9aR3B,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EA8aoXnC,OAAO;UA9a7X9D,EAAE,CAAAkG,cAAA,CAAAD,QAAA,EA8a4bnE,aAAa;QAAA;QAAA,IAAAH,EAAA;UAAA,IAAAwE,EAAA;UA9a3cnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAzE,GAAA,CAAAgH,MAAA,GAAAzC,EAAA;UAAFnG,EAAE,CAAAoG,cAAA,CAAAD,EAAA,GAAFnG,EAAE,CAAAqG,WAAA,QAAAzE,GAAA,CAAA2I,WAAA,GAAApE,EAAA;QAAA;MAAA;MAAAS,MAAA;QAAAgD,MAAA,GAAF5J,EAAE,CAAAgH,YAAA,CAAAI,0BAAA,sBA8a6F/G,gBAAgB;QAAAmI,aAAA,GA9a/GxI,EAAE,CAAAgH,YAAA,CAAAI,0BAAA,oCA8akKnG,eAAe;QAAAkE,QAAA;QAAAiE,WAAA;MAAA;MAAA/B,OAAA;QAAAwC,eAAA;QAAAC,mBAAA;MAAA;MAAAxC,QAAA;MAAAzE,UAAA;MAAA0E,QAAA,GA9anLvH,EAAE,CAAAwH,wBAAA;IAAA,EA8a0gB;EAAE;AAClnB;AACA;EAAA,QAAA1E,SAAA,oBAAAA,SAAA,KAhboG9C,EAAE,CAAA+C,iBAAA,CAgbX6C,UAAU,EAAc,CAAC;IACxGlD,IAAI,EAAEzC,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxBqE,QAAQ,EAAE,YAAY;MACtBzE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAE/C,EAAE,CAAC2O,cAAc;IAAElG,UAAU,EAAE,CAAC;MACvD1F,IAAI,EAAE/B;IACV,CAAC;EAAE,CAAC,EAAE;IAAE+B,IAAI,EAAE1C,EAAE,CAACuO;EAAkB,CAAC,EAAE;IAAE7L,IAAI,EAAE1C,EAAE,CAACuC;EAAW,CAAC,CAAC,EAAkB;IAAEqG,MAAM,EAAE,CAAC;MAC3FlG,IAAI,EAAExB,eAAe;MACrB8B,IAAI,EAAE,CAACc,OAAO,EAAE;QAAE2K,WAAW,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAElE,WAAW,EAAE,CAAC;MACd7H,IAAI,EAAExB,eAAe;MACrB8B,IAAI,EAAE,CAAClB,aAAa,EAAE;QAAE2M,WAAW,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAE7E,MAAM,EAAE,CAAC;MACTlH,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAEuF,SAAS,EAAElI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEmI,aAAa,EAAE,CAAC;MAChB9F,IAAI,EAAE5B,KAAK;MACXkC,IAAI,EAAE,CAAC;QAAEuF,SAAS,EAAEtH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEkE,QAAQ,EAAE,CAAC;MACXzC,IAAI,EAAE5B;IACV,CAAC,CAAC;IAAE+I,eAAe,EAAE,CAAC;MAClBnH,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAE+I,mBAAmB,EAAE,CAAC;MACtBpH,IAAI,EAAE3B;IACV,CAAC,CAAC;IAAEqI,WAAW,EAAE,CAAC;MACd1G,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM4N,cAAc,CAAC;EACjB3M,WAAWA,CAAC2C,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAAChC,IAAI,GAAG,QAAQ;EACxB;EACA;IAAS,IAAI,CAACP,IAAI,YAAAwM,uBAAAtM,CAAA;MAAA,YAAAA,CAAA,IAAwFqM,cAAc,EAtdxB1O,EAAE,CAAAsC,iBAAA,CAsdwCsD,UAAU;IAAA,CAA4C;EAAE;EAClM;IAAS,IAAI,CAACpD,IAAI,kBAvd8ExC,EAAE,CAAAyC,iBAAA;MAAAC,IAAA,EAudJgM,cAAc;MAAA/L,SAAA;MAAAiM,QAAA;MAAAC,YAAA,WAAAC,4BAAAnN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvdZ3B,EAAE,CAAA+O,UAAA,mBAAAC,wCAAA;YAAA,OAudJpN,GAAA,CAAA8C,QAAA,CAAAiH,IAAA,CAAc,CAAC;UAAA,CAAF,CAAC;QAAA;QAAA,IAAAhK,EAAA;UAvdZ3B,EAAE,CAAAiP,cAAA,SAAArN,GAAA,CAAAc,IAudS,CAAC;QAAA;MAAA;MAAAkE,MAAA;QAAAlE,IAAA;MAAA;MAAAG,UAAA;IAAA,EAAwL;EAAE;AAC1S;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAzdoG9C,EAAE,CAAA+C,iBAAA,CAydX2L,cAAc,EAAc,CAAC;IAC5GhM,IAAI,EAAEzC,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCC,IAAI,EAAE;QACF,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE;MACf,CAAC;MACDL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEkD;EAAW,CAAC,CAAC,EAAkB;IAAElD,IAAI,EAAE,CAAC;MACnEA,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,MAAMoO,kBAAkB,CAAC;EACrBnN,WAAWA,CAAC2C,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAAChC,IAAI,GAAG,QAAQ;EACxB;EACA;IAAS,IAAI,CAACP,IAAI,YAAAgN,2BAAA9M,CAAA;MAAA,YAAAA,CAAA,IAAwF6M,kBAAkB,EA7e5BlP,EAAE,CAAAsC,iBAAA,CA6e4CsD,UAAU;IAAA,CAA4C;EAAE;EACtM;IAAS,IAAI,CAACpD,IAAI,kBA9e8ExC,EAAE,CAAAyC,iBAAA;MAAAC,IAAA,EA8eJwM,kBAAkB;MAAAvM,SAAA;MAAAiM,QAAA;MAAAC,YAAA,WAAAO,gCAAAzN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9ehB3B,EAAE,CAAA+O,UAAA,mBAAAM,4CAAA;YAAA,OA8eJzN,GAAA,CAAA8C,QAAA,CAAAqH,QAAA,CAAkB,CAAC;UAAA,CAAF,CAAC;QAAA;QAAA,IAAApK,EAAA;UA9ehB3B,EAAE,CAAAiP,cAAA,SAAArN,GAAA,CAAAc,IA8ea,CAAC;QAAA;MAAA;MAAAkE,MAAA;QAAAlE,IAAA;MAAA;MAAAG,UAAA;IAAA,EAAgM;EAAE;AACtT;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhfoG9C,EAAE,CAAA+C,iBAAA,CAgfXmM,kBAAkB,EAAc,CAAC;IAChHxM,IAAI,EAAEzC,SAAS;IACf+C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,4BAA4B;MACtCC,IAAI,EAAE;QACF,QAAQ,EAAE,MAAM;QAChB,SAAS,EAAE;MACf,CAAC;MACDL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEH,IAAI,EAAEkD;EAAW,CAAC,CAAC,EAAkB;IAAElD,IAAI,EAAE,CAAC;MACnEA,IAAI,EAAE5B;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwO,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACnN,IAAI,YAAAoN,yBAAAlN,CAAA;MAAA,YAAAA,CAAA,IAAwFiN,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBAhgB8ExP,EAAE,CAAAyP,gBAAA;MAAA/M,IAAA,EAggBS4M;IAAgB,EAMI;EAAE;EACjI;IAAS,IAAI,CAACI,IAAI,kBAvgB8E1P,EAAE,CAAA2P,gBAAA;MAAAC,OAAA,GAugBqChQ,UAAU;IAAA,EAAI;EAAE;AAC3J;AACA;EAAA,QAAAkD,SAAA,oBAAAA,SAAA,KAzgBoG9C,EAAE,CAAA+C,iBAAA,CAygBXuM,gBAAgB,EAAc,CAAC;IAC9G5M,IAAI,EAAEvB,QAAQ;IACd6B,IAAI,EAAE,CAAC;MACC4M,OAAO,EAAE,CACLhQ,UAAU,EACVkE,OAAO,EACP8B,UAAU,EACV9D,aAAa,EACbqB,YAAY,EACZuL,cAAc,EACdQ,kBAAkB,CACrB;MACDW,OAAO,EAAE,CAAC/L,OAAO,EAAE8B,UAAU,EAAE9D,aAAa,EAAEqB,YAAY,EAAEuL,cAAc,EAAEQ,kBAAkB;IAClG,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASpL,OAAO,EAAEhC,aAAa,EAAEqB,YAAY,EAAEyC,UAAU,EAAE0J,gBAAgB,EAAEZ,cAAc,EAAEQ,kBAAkB,EAAErL,sBAAsB,EAAEL,UAAU,EAAED,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}