{"ast": null, "code": "import * as i1 from '@angular/cdk/bidi';\nimport { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ViewChild, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Optional, ViewChildren, ContentChild, ContentChildren, forwardRef, EventEmitter, signal, Directive, Output, NgModule } from '@angular/core';\nimport { RippleState, MatRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\n\n/**\n * Thumb types: range slider has two thumbs (START, END) whereas single point\n * slider only has one thumb (END).\n */\nconst _c0 = [\"knob\"];\nconst _c1 = [\"valueIndicatorContainer\"];\nfunction MatSliderVisualThumb_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2, 1)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r0.valueIndicatorText);\n  }\n}\nconst _c2 = [\"trackActive\"];\nconst _c3 = [\"*\"];\nfunction MatSlider_Conditional_6_Conditional_2_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\");\n  }\n  if (rf & 2) {\n    const tickMark_r1 = ctx.$implicit;\n    const i_r2 = ctx.$index;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassMap(tickMark_r1 === 0 ? \"mdc-slider__tick-mark--active\" : \"mdc-slider__tick-mark--inactive\");\n    i0.ɵɵstyleProp(\"transform\", ctx_r2._calcTickMarkTransform(i_r2));\n  }\n}\nfunction MatSlider_Conditional_6_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵrepeaterCreate(0, MatSlider_Conditional_6_Conditional_2_For_1_Template, 1, 4, \"div\", 8, i0.ɵɵrepeaterTrackByIndex);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵrepeater(ctx_r2._tickMarks);\n  }\n}\nfunction MatSlider_Conditional_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6, 1);\n    i0.ɵɵtemplate(2, MatSlider_Conditional_6_Conditional_2_Template, 2, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, ctx_r2._cachedWidth ? 2 : -1);\n  }\n}\nfunction MatSlider_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-slider-visual-thumb\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"discrete\", ctx_r2.discrete)(\"thumbPosition\", 1)(\"valueIndicatorText\", ctx_r2.startValueIndicatorText);\n  }\n}\nvar _MatThumb;\n(function (_MatThumb) {\n  _MatThumb[_MatThumb[\"START\"] = 1] = \"START\";\n  _MatThumb[_MatThumb[\"END\"] = 2] = \"END\";\n})(_MatThumb || (_MatThumb = {}));\n/** Tick mark enum, for discrete sliders. */\nvar _MatTickMark;\n(function (_MatTickMark) {\n  _MatTickMark[_MatTickMark[\"ACTIVE\"] = 0] = \"ACTIVE\";\n  _MatTickMark[_MatTickMark[\"INACTIVE\"] = 1] = \"INACTIVE\";\n})(_MatTickMark || (_MatTickMark = {}));\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER = new InjectionToken('_MatSlider');\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB = new InjectionToken('_MatSliderThumb');\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB = new InjectionToken('_MatSliderRangeThumb');\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_VISUAL_THUMB = new InjectionToken('_MatSliderVisualThumb');\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nclass MatSliderChange {}\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\nclass MatSliderVisualThumb {\n  constructor(_cdr, _ngZone, _elementRef, _slider) {\n    this._cdr = _cdr;\n    this._ngZone = _ngZone;\n    this._slider = _slider;\n    /** Whether the slider thumb is currently being hovered. */\n    this._isHovered = false;\n    /** Whether the slider thumb is currently being pressed. */\n    this._isActive = false;\n    /** Whether the value indicator tooltip is visible. */\n    this._isValueIndicatorVisible = false;\n    this._platform = inject(Platform);\n    this._onPointerMove = event => {\n      if (this._sliderInput._isFocused) {\n        return;\n      }\n      const rect = this._hostElement.getBoundingClientRect();\n      const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n      this._isHovered = isHovered;\n      if (isHovered) {\n        this._showHoverRipple();\n      } else {\n        this._hideRipple(this._hoverRippleRef);\n      }\n    };\n    this._onMouseLeave = () => {\n      this._isHovered = false;\n      this._hideRipple(this._hoverRippleRef);\n    };\n    this._onFocus = () => {\n      // We don't want to show the hover ripple on top of the focus ripple.\n      // Happen when the users cursor is over a thumb and then the user tabs to it.\n      this._hideRipple(this._hoverRippleRef);\n      this._showFocusRipple();\n      this._hostElement.classList.add('mdc-slider__thumb--focused');\n    };\n    this._onBlur = () => {\n      // Happens when the user tabs away while still dragging a thumb.\n      if (!this._isActive) {\n        this._hideRipple(this._focusRippleRef);\n      }\n      // Happens when the user tabs away from a thumb but their cursor is still over it.\n      if (this._isHovered) {\n        this._showHoverRipple();\n      }\n      this._hostElement.classList.remove('mdc-slider__thumb--focused');\n    };\n    this._onDragStart = event => {\n      if (event.button !== 0) {\n        return;\n      }\n      this._isActive = true;\n      this._showActiveRipple();\n    };\n    this._onDragEnd = () => {\n      this._isActive = false;\n      this._hideRipple(this._activeRippleRef);\n      // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n      if (!this._sliderInput._isFocused) {\n        this._hideRipple(this._focusRippleRef);\n      }\n      // On Safari we need to immediately re-show the hover ripple because\n      // sliders do not retain focus from pointer events on that platform.\n      if (this._platform.SAFARI) {\n        this._showHoverRipple();\n      }\n    };\n    this._hostElement = _elementRef.nativeElement;\n  }\n  ngAfterViewInit() {\n    this._ripple.radius = 24;\n    this._sliderInput = this._slider._getInput(this.thumbPosition);\n    this._sliderInputEl = this._sliderInput._hostElement;\n    const input = this._sliderInputEl;\n    // These listeners don't update any data bindings so we bind them outside\n    // of the NgZone to prevent Angular from needlessly running change detection.\n    this._ngZone.runOutsideAngular(() => {\n      input.addEventListener('pointermove', this._onPointerMove);\n      input.addEventListener('pointerdown', this._onDragStart);\n      input.addEventListener('pointerup', this._onDragEnd);\n      input.addEventListener('pointerleave', this._onMouseLeave);\n      input.addEventListener('focus', this._onFocus);\n      input.addEventListener('blur', this._onBlur);\n    });\n  }\n  ngOnDestroy() {\n    const input = this._sliderInputEl;\n    if (input) {\n      input.removeEventListener('pointermove', this._onPointerMove);\n      input.removeEventListener('pointerdown', this._onDragStart);\n      input.removeEventListener('pointerup', this._onDragEnd);\n      input.removeEventListener('pointerleave', this._onMouseLeave);\n      input.removeEventListener('focus', this._onFocus);\n      input.removeEventListener('blur', this._onBlur);\n    }\n  }\n  /** Handles displaying the hover ripple. */\n  _showHoverRipple() {\n    if (!this._isShowingRipple(this._hoverRippleRef)) {\n      this._hoverRippleRef = this._showRipple({\n        enterDuration: 0,\n        exitDuration: 0\n      });\n      this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n    }\n  }\n  /** Handles displaying the focus ripple. */\n  _showFocusRipple() {\n    // Show the focus ripple event if noop animations are enabled.\n    if (!this._isShowingRipple(this._focusRippleRef)) {\n      this._focusRippleRef = this._showRipple({\n        enterDuration: 0,\n        exitDuration: 0\n      }, true);\n      this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n    }\n  }\n  /** Handles displaying the active ripple. */\n  _showActiveRipple() {\n    if (!this._isShowingRipple(this._activeRippleRef)) {\n      this._activeRippleRef = this._showRipple({\n        enterDuration: 225,\n        exitDuration: 400\n      });\n      this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n    }\n  }\n  /** Whether the given rippleRef is currently fading in or visible. */\n  _isShowingRipple(rippleRef) {\n    return rippleRef?.state === RippleState.FADING_IN || rippleRef?.state === RippleState.VISIBLE;\n  }\n  /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n  _showRipple(animation, ignoreGlobalRippleConfig) {\n    if (this._slider.disabled) {\n      return;\n    }\n    this._showValueIndicator();\n    if (this._slider._isRange) {\n      const sibling = this._slider._getThumb(this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START);\n      sibling._showValueIndicator();\n    }\n    if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n      return;\n    }\n    return this._ripple.launch({\n      animation: this._slider._noopAnimations ? {\n        enterDuration: 0,\n        exitDuration: 0\n      } : animation,\n      centered: true,\n      persistent: true\n    });\n  }\n  /**\n   * Fades out the given ripple.\n   * Also hides the value indicator if no ripple is showing.\n   */\n  _hideRipple(rippleRef) {\n    rippleRef?.fadeOut();\n    if (this._isShowingAnyRipple()) {\n      return;\n    }\n    if (!this._slider._isRange) {\n      this._hideValueIndicator();\n    }\n    const sibling = this._getSibling();\n    if (!sibling._isShowingAnyRipple()) {\n      this._hideValueIndicator();\n      sibling._hideValueIndicator();\n    }\n  }\n  /** Shows the value indicator ui. */\n  _showValueIndicator() {\n    this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n  }\n  /** Hides the value indicator ui. */\n  _hideValueIndicator() {\n    this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n  }\n  _getSibling() {\n    return this._slider._getThumb(this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START);\n  }\n  /** Gets the value indicator container's native HTML element. */\n  _getValueIndicatorContainer() {\n    return this._valueIndicatorContainer?.nativeElement;\n  }\n  /** Gets the native HTML element of the slider thumb knob. */\n  _getKnob() {\n    return this._knob.nativeElement;\n  }\n  _isShowingAnyRipple() {\n    return this._isShowingRipple(this._hoverRippleRef) || this._isShowingRipple(this._focusRippleRef) || this._isShowingRipple(this._activeRippleRef);\n  }\n  static {\n    this.ɵfac = function MatSliderVisualThumb_Factory(t) {\n      return new (t || MatSliderVisualThumb)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_SLIDER));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSliderVisualThumb,\n      selectors: [[\"mat-slider-visual-thumb\"]],\n      viewQuery: function MatSliderVisualThumb_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(MatRipple, 5);\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._ripple = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._knob = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._valueIndicatorContainer = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-slider__thumb\", \"mat-mdc-slider-visual-thumb\"],\n      inputs: {\n        discrete: \"discrete\",\n        thumbPosition: \"thumbPosition\",\n        valueIndicatorText: \"valueIndicatorText\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SLIDER_VISUAL_THUMB,\n        useExisting: MatSliderVisualThumb\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 4,\n      vars: 2,\n      consts: [[\"knob\", \"\"], [\"valueIndicatorContainer\", \"\"], [1, \"mdc-slider__value-indicator-container\"], [1, \"mdc-slider__thumb-knob\"], [\"matRipple\", \"\", 1, \"mat-mdc-focus-indicator\", 3, \"matRippleDisabled\"], [1, \"mdc-slider__value-indicator\"], [1, \"mdc-slider__value-indicator-text\"]],\n      template: function MatSliderVisualThumb_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, MatSliderVisualThumb_Conditional_0_Template, 5, 1, \"div\", 2);\n          i0.ɵɵelement(1, \"div\", 3, 0)(3, \"div\", 4);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.discrete ? 0 : -1);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"matRippleDisabled\", true);\n        }\n      },\n      dependencies: [MatRipple],\n      styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderVisualThumb, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slider-visual-thumb',\n      host: {\n        'class': 'mdc-slider__thumb mat-mdc-slider-visual-thumb'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_SLIDER_VISUAL_THUMB,\n        useExisting: MatSliderVisualThumb\n      }],\n      standalone: true,\n      imports: [MatRipple],\n      template: \"@if (discrete) {\\n  <div class=\\\"mdc-slider__value-indicator-container\\\" #valueIndicatorContainer>\\n    <div class=\\\"mdc-slider__value-indicator\\\">\\n      <span class=\\\"mdc-slider__value-indicator-text\\\">{{valueIndicatorText}}</span>\\n    </div>\\n  </div>\\n}\\n<div class=\\\"mdc-slider__thumb-knob\\\" #knob></div>\\n<div matRipple class=\\\"mat-mdc-focus-indicator\\\" [matRippleDisabled]=\\\"true\\\"></div>\\n\",\n      styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"]\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SLIDER]\n    }]\n  }], {\n    discrete: [{\n      type: Input\n    }],\n    thumbPosition: [{\n      type: Input\n    }],\n    valueIndicatorText: [{\n      type: Input\n    }],\n    _ripple: [{\n      type: ViewChild,\n      args: [MatRipple]\n    }],\n    _knob: [{\n      type: ViewChild,\n      args: ['knob']\n    }],\n    _valueIndicatorContainer: [{\n      type: ViewChild,\n      args: ['valueIndicatorContainer']\n    }]\n  });\n})();\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\nclass MatSlider {\n  /** Whether the slider is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(v) {\n    this._disabled = v;\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    if (endInput) {\n      endInput.disabled = this._disabled;\n    }\n    if (startInput) {\n      startInput.disabled = this._disabled;\n    }\n  }\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  get discrete() {\n    return this._discrete;\n  }\n  set discrete(v) {\n    this._discrete = v;\n    this._updateValueIndicatorUIs();\n  }\n  /** The minimum value that the slider can have. */\n  get min() {\n    return this._min;\n  }\n  set min(v) {\n    const min = isNaN(v) ? this._min : v;\n    if (this._min !== min) {\n      this._updateMin(min);\n    }\n  }\n  _updateMin(min) {\n    const prevMin = this._min;\n    this._min = min;\n    this._isRange ? this._updateMinRange({\n      old: prevMin,\n      new: min\n    }) : this._updateMinNonRange(min);\n    this._onMinMaxOrStepChange();\n  }\n  _updateMinRange(min) {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    startInput.min = min.new;\n    endInput.min = Math.max(min.new, startInput.value);\n    startInput.max = Math.min(endInput.max, endInput.value);\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n    min.new < min.old ? this._onTranslateXChangeBySideEffect(endInput, startInput) : this._onTranslateXChangeBySideEffect(startInput, endInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateMinNonRange(min) {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n      input.min = min;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  /** The maximum value that the slider can have. */\n  get max() {\n    return this._max;\n  }\n  set max(v) {\n    const max = isNaN(v) ? this._max : v;\n    if (this._max !== max) {\n      this._updateMax(max);\n    }\n  }\n  _updateMax(max) {\n    const prevMax = this._max;\n    this._max = max;\n    this._isRange ? this._updateMaxRange({\n      old: prevMax,\n      new: max\n    }) : this._updateMaxNonRange(max);\n    this._onMinMaxOrStepChange();\n  }\n  _updateMaxRange(max) {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    endInput.max = max.new;\n    startInput.max = Math.min(max.new, endInput.value);\n    endInput.min = startInput.value;\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n    max.new > max.old ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateMaxNonRange(max) {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n      input.max = max;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  /** The values at which the thumb will snap. */\n  get step() {\n    return this._step;\n  }\n  set step(v) {\n    const step = isNaN(v) ? this._step : v;\n    if (this._step !== step) {\n      this._updateStep(step);\n    }\n  }\n  _updateStep(step) {\n    this._step = step;\n    this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n    this._onMinMaxOrStepChange();\n  }\n  _updateStepRange() {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n    const prevStartValue = startInput.value;\n    endInput.min = this._min;\n    startInput.max = this._max;\n    endInput.step = this._step;\n    startInput.step = this._step;\n    if (this._platform.SAFARI) {\n      endInput.value = endInput.value;\n      startInput.value = startInput.value;\n    }\n    endInput.min = Math.max(this._min, startInput.value);\n    startInput.max = Math.min(this._max, endInput.value);\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n    endInput.value < prevStartValue ? this._onTranslateXChangeBySideEffect(startInput, endInput) : this._onTranslateXChangeBySideEffect(endInput, startInput);\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n  _updateStepNonRange() {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n      input.step = this._step;\n      if (this._platform.SAFARI) {\n        input.value = input.value;\n      }\n      input._updateThumbUIByValue();\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n  constructor(_ngZone, _cdr, _elementRef, _dir, _globalRippleOptions, animationMode) {\n    this._ngZone = _ngZone;\n    this._cdr = _cdr;\n    this._elementRef = _elementRef;\n    this._dir = _dir;\n    this._globalRippleOptions = _globalRippleOptions;\n    this._disabled = false;\n    this._discrete = false;\n    /** Whether the slider displays tick marks along the slider track. */\n    this.showTickMarks = false;\n    this._min = 0;\n    /** Whether ripples are disabled in the slider. */\n    this.disableRipple = false;\n    this._max = 100;\n    this._step = 1;\n    /**\n     * Function that will be used to format the value before it is displayed\n     * in the thumb label. Can be used to format very large number in order\n     * for them to fit into the slider thumb.\n     */\n    this.displayWith = value => `${value}`;\n    this._rippleRadius = 24;\n    // The value indicator tooltip text for the visual slider thumb(s).\n    /** @docs-private */\n    this.startValueIndicatorText = '';\n    /** @docs-private */\n    this.endValueIndicatorText = '';\n    this._isRange = false;\n    /** Whether the slider is rtl. */\n    this._isRtl = false;\n    this._hasViewInitialized = false;\n    /**\n     * The width of the tick mark track.\n     * The tick mark track width is different from full track width\n     */\n    this._tickMarkTrackWidth = 0;\n    this._hasAnimation = false;\n    this._resizeTimer = null;\n    this._platform = inject(Platform);\n    /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n    this._knobRadius = 8;\n    /** Whether or not the slider thumbs overlap. */\n    this._thumbsOverlap = false;\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n    this._isRtl = this._dir.value === 'rtl';\n  }\n  ngAfterViewInit() {\n    if (this._platform.isBrowser) {\n      this._updateDimensions();\n    }\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n    this._isRange = !!eInput && !!sInput;\n    this._cdr.detectChanges();\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      _validateInputs(this._isRange, this._getInput(_MatThumb.END), this._getInput(_MatThumb.START));\n    }\n    const thumb = this._getThumb(_MatThumb.END);\n    this._rippleRadius = thumb._ripple.radius;\n    this._inputPadding = this._rippleRadius - this._knobRadius;\n    this._isRange ? this._initUIRange(eInput, sInput) : this._initUINonRange(eInput);\n    this._updateTrackUI(eInput);\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._observeHostResize();\n    this._cdr.detectChanges();\n  }\n  _initUINonRange(eInput) {\n    eInput.initProps();\n    eInput.initUI();\n    this._updateValueIndicatorUI(eInput);\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n  }\n  _initUIRange(eInput, sInput) {\n    eInput.initProps();\n    eInput.initUI();\n    sInput.initProps();\n    sInput.initUI();\n    eInput._updateMinMax();\n    sInput._updateMinMax();\n    eInput._updateStaticStyles();\n    sInput._updateStaticStyles();\n    this._updateValueIndicatorUIs();\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n    sInput._updateThumbUIByValue();\n  }\n  ngOnDestroy() {\n    this._dirChangeSubscription.unsubscribe();\n    this._resizeObserver?.disconnect();\n    this._resizeObserver = null;\n  }\n  /** Handles updating the slider ui after a dir change. */\n  _onDirChange() {\n    this._isRtl = this._dir.value === 'rtl';\n    this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n    this._updateTickMarkUI();\n  }\n  _onDirChangeRange() {\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n    endInput._setIsLeftThumb();\n    startInput._setIsLeftThumb();\n    endInput.translateX = endInput._calcTranslateXByValue();\n    startInput.translateX = startInput._calcTranslateXByValue();\n    endInput._updateStaticStyles();\n    startInput._updateStaticStyles();\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n    endInput._updateThumbUIByValue();\n    startInput._updateThumbUIByValue();\n  }\n  _onDirChangeNonRange() {\n    const input = this._getInput(_MatThumb.END);\n    input._updateThumbUIByValue();\n  }\n  /** Starts observing and updating the slider if the host changes its size. */\n  _observeHostResize() {\n    if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n      return;\n    }\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeObserver = new ResizeObserver(() => {\n        if (this._isActive()) {\n          return;\n        }\n        if (this._resizeTimer) {\n          clearTimeout(this._resizeTimer);\n        }\n        this._onResize();\n      });\n      this._resizeObserver.observe(this._elementRef.nativeElement);\n    });\n  }\n  /** Whether any of the thumbs are currently active. */\n  _isActive() {\n    return this._getThumb(_MatThumb.START)._isActive || this._getThumb(_MatThumb.END)._isActive;\n  }\n  _getValue(thumbPosition = _MatThumb.END) {\n    const input = this._getInput(thumbPosition);\n    if (!input) {\n      return this.min;\n    }\n    return input.value;\n  }\n  _skipUpdate() {\n    return !!(this._getInput(_MatThumb.START)?._skipUIUpdate || this._getInput(_MatThumb.END)?._skipUIUpdate);\n  }\n  /** Stores the slider dimensions. */\n  _updateDimensions() {\n    this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n    this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n  }\n  /** Sets the styles for the active portion of the track. */\n  _setTrackActiveStyles(styles) {\n    const trackStyle = this._trackActive.nativeElement.style;\n    trackStyle.left = styles.left;\n    trackStyle.right = styles.right;\n    trackStyle.transformOrigin = styles.transformOrigin;\n    trackStyle.transform = styles.transform;\n  }\n  /** Returns the translateX positioning for a tick mark based on it's index. */\n  _calcTickMarkTransform(index) {\n    // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n    const translateX = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n    return `translateX(${translateX}px`;\n  }\n  // Handlers for updating the slider ui.\n  _onTranslateXChange(source) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateThumbUI(source);\n    this._updateTrackUI(source);\n    this._updateOverlappingThumbUI(source);\n  }\n  _onTranslateXChangeBySideEffect(input1, input2) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    input1._updateThumbUIByValue();\n    input2._updateThumbUIByValue();\n  }\n  _onValueChange(source) {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateValueIndicatorUI(source);\n    this._updateTickMarkUI();\n    this._cdr.detectChanges();\n  }\n  _onMinMaxOrStepChange() {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.markForCheck();\n  }\n  _onResize() {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n    this._updateDimensions();\n    if (this._isRange) {\n      const eInput = this._getInput(_MatThumb.END);\n      const sInput = this._getInput(_MatThumb.START);\n      eInput._updateThumbUIByValue();\n      sInput._updateThumbUIByValue();\n      eInput._updateStaticStyles();\n      sInput._updateStaticStyles();\n      eInput._updateMinMax();\n      sInput._updateMinMax();\n      eInput._updateWidthInactive();\n      sInput._updateWidthInactive();\n    } else {\n      const eInput = this._getInput(_MatThumb.END);\n      if (eInput) {\n        eInput._updateThumbUIByValue();\n      }\n    }\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.detectChanges();\n  }\n  /** Returns true if the slider knobs are overlapping one another. */\n  _areThumbsOverlapping() {\n    const startInput = this._getInput(_MatThumb.START);\n    const endInput = this._getInput(_MatThumb.END);\n    if (!startInput || !endInput) {\n      return false;\n    }\n    return endInput.translateX - startInput.translateX < 20;\n  }\n  /**\n   * Updates the class names of overlapping slider thumbs so\n   * that the current active thumb is styled to be on \"top\".\n   */\n  _updateOverlappingThumbClassNames(source) {\n    const sibling = source.getSibling();\n    const sourceThumb = this._getThumb(source.thumbPosition);\n    const siblingThumb = this._getThumb(sibling.thumbPosition);\n    siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n    sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n  }\n  /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n  _updateOverlappingThumbUI(source) {\n    if (!this._isRange || this._skipUpdate()) {\n      return;\n    }\n    if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n      this._thumbsOverlap = !this._thumbsOverlap;\n      this._updateOverlappingThumbClassNames(source);\n    }\n  }\n  // _MatThumb styles update conditions\n  //\n  // 1. TranslateX, resize, or dir change\n  //    - Reason: The thumb styles need to be updated according to the new translateX.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n  /** Updates the translateX of the given thumb. */\n  _updateThumbUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const thumb = this._getThumb(source.thumbPosition === _MatThumb.END ? _MatThumb.END : _MatThumb.START);\n    thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n  }\n  // Value indicator text update conditions\n  //\n  // 1. Value\n  //    - Reason: The value displayed needs to be updated.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n  /** Updates the value indicator tooltip ui for the given thumb. */\n  _updateValueIndicatorUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const valuetext = this.displayWith(source.value);\n    this._hasViewInitialized ? source._valuetext.set(valuetext) : source._hostElement.setAttribute('aria-valuetext', valuetext);\n    if (this.discrete) {\n      source.thumbPosition === _MatThumb.START ? this.startValueIndicatorText = valuetext : this.endValueIndicatorText = valuetext;\n      const visualThumb = this._getThumb(source.thumbPosition);\n      valuetext.length < 3 ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value') : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n    }\n  }\n  /** Updates all value indicator UIs in the slider. */\n  _updateValueIndicatorUIs() {\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n    if (eInput) {\n      this._updateValueIndicatorUI(eInput);\n    }\n    if (sInput) {\n      this._updateValueIndicatorUI(sInput);\n    }\n  }\n  // Update Tick Mark Track Width\n  //\n  // 1. Min, max, or step\n  //    - Reason: The maximum reachable value may have changed.\n  //    - Side note: The maximum reachable value is different from the maximum value set by the\n  //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n  //      reachable value of 95.\n  // 2. Resize\n  //    - Reason: The position for the maximum reachable value needs to be recalculated.\n  /** Updates the width of the tick mark track. */\n  _updateTickMarkTrackUI() {\n    if (!this.showTickMarks || this._skipUpdate()) {\n      return;\n    }\n    const step = this._step && this._step > 0 ? this._step : 1;\n    const maxValue = Math.floor(this.max / step) * step;\n    const percentage = (maxValue - this.min) / (this.max - this.min);\n    this._tickMarkTrackWidth = this._cachedWidth * percentage - 6;\n  }\n  // Track active update conditions\n  //\n  // 1. TranslateX\n  //    - Reason: The track active should line up with the new thumb position.\n  // 2. Min or max\n  //    - Reason #1: The 'active' percentage needs to be recalculated.\n  //    - Reason #2: The value may have silently changed.\n  // 3. Step\n  //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n  // 4. Dir change\n  //    - Reason: The track active will need to be updated according to the new thumb position(s).\n  // 5. Resize\n  //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n  /** Updates the scale on the active portion of the track. */\n  _updateTrackUI(source) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    this._isRange ? this._updateTrackUIRange(source) : this._updateTrackUINonRange(source);\n  }\n  _updateTrackUIRange(source) {\n    const sibling = source.getSibling();\n    if (!sibling || !this._cachedWidth) {\n      return;\n    }\n    const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n    if (source._isLeftThumb && this._cachedWidth) {\n      this._setTrackActiveStyles({\n        left: 'auto',\n        right: `${this._cachedWidth - sibling.translateX}px`,\n        transformOrigin: 'right',\n        transform: `scaleX(${activePercentage})`\n      });\n    } else {\n      this._setTrackActiveStyles({\n        left: `${sibling.translateX}px`,\n        right: 'auto',\n        transformOrigin: 'left',\n        transform: `scaleX(${activePercentage})`\n      });\n    }\n  }\n  _updateTrackUINonRange(source) {\n    this._isRtl ? this._setTrackActiveStyles({\n      left: 'auto',\n      right: '0px',\n      transformOrigin: 'right',\n      transform: `scaleX(${1 - source.fillPercentage})`\n    }) : this._setTrackActiveStyles({\n      left: '0px',\n      right: 'auto',\n      transformOrigin: 'left',\n      transform: `scaleX(${source.fillPercentage})`\n    });\n  }\n  // Tick mark update conditions\n  //\n  // 1. Value\n  //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n  // 2. Min, max, or step\n  //    - Reason #1: the number of tick marks may have changed.\n  //    - Reason #2: The value may have silently changed.\n  /** Updates the dots along the slider track. */\n  _updateTickMarkUI() {\n    if (!this.showTickMarks || this.step === undefined || this.min === undefined || this.max === undefined) {\n      return;\n    }\n    const step = this.step > 0 ? this.step : 1;\n    this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n    if (this._isRtl) {\n      this._tickMarks.reverse();\n    }\n  }\n  _updateTickMarkUINonRange(step) {\n    const value = this._getValue();\n    let numActive = Math.max(Math.floor((value - this.min) / step), 0);\n    let numInactive = Math.max(Math.floor((this.max - value) / step), 0);\n    this._isRtl ? numActive++ : numInactive++;\n    this._tickMarks = Array(numActive).fill(_MatTickMark.ACTIVE).concat(Array(numInactive).fill(_MatTickMark.INACTIVE));\n  }\n  _updateTickMarkUIRange(step) {\n    const endValue = this._getValue();\n    const startValue = this._getValue(_MatThumb.START);\n    const numInactiveBeforeStartThumb = Math.max(Math.floor((startValue - this.min) / step), 0);\n    const numActive = Math.max(Math.floor((endValue - startValue) / step) + 1, 0);\n    const numInactiveAfterEndThumb = Math.max(Math.floor((this.max - endValue) / step), 0);\n    this._tickMarks = Array(numInactiveBeforeStartThumb).fill(_MatTickMark.INACTIVE).concat(Array(numActive).fill(_MatTickMark.ACTIVE), Array(numInactiveAfterEndThumb).fill(_MatTickMark.INACTIVE));\n  }\n  /** Gets the slider thumb input of the given thumb position. */\n  _getInput(thumbPosition) {\n    if (thumbPosition === _MatThumb.END && this._input) {\n      return this._input;\n    }\n    if (this._inputs?.length) {\n      return thumbPosition === _MatThumb.START ? this._inputs.first : this._inputs.last;\n    }\n    return;\n  }\n  /** Gets the slider thumb HTML input element of the given thumb position. */\n  _getThumb(thumbPosition) {\n    return thumbPosition === _MatThumb.END ? this._thumbs?.last : this._thumbs?.first;\n  }\n  _setTransition(withAnimation) {\n    this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n    this._elementRef.nativeElement.classList.toggle('mat-mdc-slider-with-animation', this._hasAnimation);\n  }\n  /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n  _isCursorOnSliderThumb(event, rect) {\n    const radius = rect.width / 2;\n    const centerX = rect.x + radius;\n    const centerY = rect.y + radius;\n    const dx = event.clientX - centerX;\n    const dy = event.clientY - centerY;\n    return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n  }\n  static {\n    this.ɵfac = function MatSlider_Factory(t) {\n      return new (t || MatSlider)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8), i0.ɵɵdirectiveInject(MAT_RIPPLE_GLOBAL_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSlider,\n      selectors: [[\"mat-slider\"]],\n      contentQueries: function MatSlider_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_THUMB, 5);\n          i0.ɵɵcontentQuery(dirIndex, MAT_SLIDER_RANGE_THUMB, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._input = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._inputs = _t);\n        }\n      },\n      viewQuery: function MatSlider_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c2, 5);\n          i0.ɵɵviewQuery(MAT_SLIDER_VISUAL_THUMB, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._trackActive = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._thumbs = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-slider\", \"mdc-slider\"],\n      hostVars: 12,\n      hostBindings: function MatSlider_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n          i0.ɵɵclassProp(\"mdc-slider--range\", ctx._isRange)(\"mdc-slider--disabled\", ctx.disabled)(\"mdc-slider--discrete\", ctx.discrete)(\"mdc-slider--tick-marks\", ctx.showTickMarks)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        discrete: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"discrete\", \"discrete\", booleanAttribute],\n        showTickMarks: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"showTickMarks\", \"showTickMarks\", booleanAttribute],\n        min: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"min\", \"min\", numberAttribute],\n        color: \"color\",\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        max: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"max\", \"max\", numberAttribute],\n        step: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"step\", \"step\", numberAttribute],\n        displayWith: \"displayWith\"\n      },\n      exportAs: [\"matSlider\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_SLIDER,\n        useExisting: MatSlider\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c3,\n      decls: 9,\n      vars: 5,\n      consts: [[\"trackActive\", \"\"], [\"tickMarkContainer\", \"\"], [1, \"mdc-slider__track\"], [1, \"mdc-slider__track--inactive\"], [1, \"mdc-slider__track--active\"], [1, \"mdc-slider__track--active_fill\"], [1, \"mdc-slider__tick-marks\"], [3, \"discrete\", \"thumbPosition\", \"valueIndicatorText\"], [3, \"class\", \"transform\"]],\n      template: function MatSlider_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 2);\n          i0.ɵɵelement(2, \"div\", 3);\n          i0.ɵɵelementStart(3, \"div\", 4);\n          i0.ɵɵelement(4, \"div\", 5, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(6, MatSlider_Conditional_6_Template, 3, 1, \"div\", 6);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(7, MatSlider_Conditional_7_Template, 1, 3, \"mat-slider-visual-thumb\", 7);\n          i0.ɵɵelement(8, \"mat-slider-visual-thumb\", 7);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵconditional(6, ctx.showTickMarks ? 6 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(7, ctx._isRange ? 7 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"discrete\", ctx.discrete)(\"thumbPosition\", 2)(\"valueIndicatorText\", ctx.endValueIndicatorText);\n        }\n      },\n      dependencies: [MatSliderVisualThumb],\n      styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:50%;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:translateX(-50%);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:50%;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:translateX(-50%);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color);border-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color);border-color:var(--mdc-slider-disabled-handle-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color)}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color);opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color);opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color)}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height);top:calc((var(--mdc-slider-inactive-track-height) - var(--mdc-slider-active-track-height)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size);width:var(--mdc-slider-with-tick-marks-container-size)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking);font-size:var(--mdc-slider-label-label-text-size);font-family:var(--mdc-slider-label-label-text-font);font-weight:var(--mdc-slider-label-label-text-weight);line-height:var(--mdc-slider-label-label-text-line-height)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape);width:var(--mdc-slider-handle-width);height:var(--mdc-slider-handle-height);border-style:solid;border-width:calc(var(--mdc-slider-handle-height) / 2) calc(var(--mdc-slider-handle-width) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color);border-color:var(--mdc-slider-hover-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color);border-color:var(--mdc-slider-focus-handle-color)}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color);border-width:var(--mdc-slider-with-overlap-handle-outline-width)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator-container{transform:var(--mat-slider-value-indicator-container-transform)}.mat-mdc-slider .mdc-slider__value-indicator{width:var(--mat-slider-value-indicator-width);height:var(--mat-slider-value-indicator-height);padding:var(--mat-slider-value-indicator-padding);opacity:var(--mat-slider-value-indicator-opacity);border-radius:var(--mat-slider-value-indicator-border-radius)}.mat-mdc-slider .mdc-slider__value-indicator::before{display:var(--mat-slider-value-indicator-caret-display)}.mat-mdc-slider .mdc-slider__value-indicator-text{width:var(--mat-slider-value-indicator-width);transform:var(--mat-slider-value-indicator-text-transform)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-slider-ripple-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-slider-hover-state-layer-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-slider-focus-state-layer-color)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slider .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__value-indicator-text{text-align:center}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlider, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slider',\n      host: {\n        'class': 'mat-mdc-slider mdc-slider',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mdc-slider--range]': '_isRange',\n        '[class.mdc-slider--disabled]': 'disabled',\n        '[class.mdc-slider--discrete]': 'discrete',\n        '[class.mdc-slider--tick-marks]': 'showTickMarks',\n        '[class._mat-animation-noopable]': '_noopAnimations'\n      },\n      exportAs: 'matSlider',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      providers: [{\n        provide: MAT_SLIDER,\n        useExisting: MatSlider\n      }],\n      standalone: true,\n      imports: [MatSliderVisualThumb],\n      template: \"<!-- Inputs -->\\n<ng-content></ng-content>\\n\\n<!-- Track -->\\n<div class=\\\"mdc-slider__track\\\">\\n  <div class=\\\"mdc-slider__track--inactive\\\"></div>\\n  <div class=\\\"mdc-slider__track--active\\\">\\n    <div #trackActive class=\\\"mdc-slider__track--active_fill\\\"></div>\\n  </div>\\n  @if (showTickMarks) {\\n    <div class=\\\"mdc-slider__tick-marks\\\" #tickMarkContainer>\\n      @if (_cachedWidth) {\\n        @for (tickMark of _tickMarks; track i; let i = $index) {\\n          <div\\n            [class]=\\\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\\\"\\n            [style.transform]=\\\"_calcTickMarkTransform(i)\\\"></div>\\n        }\\n      }\\n    </div>\\n  }\\n</div>\\n\\n<!-- Thumbs -->\\n@if (_isRange) {\\n  <mat-slider-visual-thumb\\n    [discrete]=\\\"discrete\\\"\\n    [thumbPosition]=\\\"1\\\"\\n    [valueIndicatorText]=\\\"startValueIndicatorText\\\">\\n  </mat-slider-visual-thumb>\\n}\\n\\n<mat-slider-visual-thumb\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"2\\\"\\n  [valueIndicatorText]=\\\"endValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\",\n      styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:50%;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:translateX(-50%);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:50%;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:translateX(-50%);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color);border-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color);border-color:var(--mdc-slider-disabled-handle-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color)}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color);opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color);opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color)}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height);top:calc((var(--mdc-slider-inactive-track-height) - var(--mdc-slider-active-track-height)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size);width:var(--mdc-slider-with-tick-marks-container-size)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking);font-size:var(--mdc-slider-label-label-text-size);font-family:var(--mdc-slider-label-label-text-font);font-weight:var(--mdc-slider-label-label-text-weight);line-height:var(--mdc-slider-label-label-text-line-height)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape);width:var(--mdc-slider-handle-width);height:var(--mdc-slider-handle-height);border-style:solid;border-width:calc(var(--mdc-slider-handle-height) / 2) calc(var(--mdc-slider-handle-width) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color);border-color:var(--mdc-slider-hover-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color);border-color:var(--mdc-slider-focus-handle-color)}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color);border-width:var(--mdc-slider-with-overlap-handle-outline-width)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator-container{transform:var(--mat-slider-value-indicator-container-transform)}.mat-mdc-slider .mdc-slider__value-indicator{width:var(--mat-slider-value-indicator-width);height:var(--mat-slider-value-indicator-height);padding:var(--mat-slider-value-indicator-padding);opacity:var(--mat-slider-value-indicator-opacity);border-radius:var(--mat-slider-value-indicator-border-radius)}.mat-mdc-slider .mdc-slider__value-indicator::before{display:var(--mat-slider-value-indicator-caret-display)}.mat-mdc-slider .mdc-slider__value-indicator-text{width:var(--mat-slider-value-indicator-width);transform:var(--mat-slider-value-indicator-text-transform)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-slider-ripple-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-slider-hover-state-layer-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-slider-focus-state-layer-color)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slider .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__value-indicator-text{text-align:center}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    _trackActive: [{\n      type: ViewChild,\n      args: ['trackActive']\n    }],\n    _thumbs: [{\n      type: ViewChildren,\n      args: [MAT_SLIDER_VISUAL_THUMB]\n    }],\n    _input: [{\n      type: ContentChild,\n      args: [MAT_SLIDER_THUMB]\n    }],\n    _inputs: [{\n      type: ContentChildren,\n      args: [MAT_SLIDER_RANGE_THUMB, {\n        descendants: false\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    discrete: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    showTickMarks: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    min: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    max: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    step: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    displayWith: [{\n      type: Input\n    }]\n  });\n})();\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(isRange, endInputElement, startInputElement) {\n  const startValid = !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n  const endValid = endInputElement._hostElement.hasAttribute(isRange ? 'matSliderEndThumb' : 'matSliderThumb');\n  if (!startValid || !endValid) {\n    _throwInvalidInputConfigurationError();\n  }\n}\nfunction _throwInvalidInputConfigurationError() {\n  throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderThumb),\n  multi: true\n};\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderRangeThumb),\n  multi: true\n};\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\nclass MatSliderThumb {\n  get value() {\n    return numberAttribute(this._hostElement.value, 0);\n  }\n  set value(value) {\n    value = isNaN(value) ? 0 : value;\n    const stringValue = value + '';\n    if (!this._hasSetInitialValue) {\n      this._initialValue = stringValue;\n      return;\n    }\n    if (this._isActive) {\n      return;\n    }\n    this._setValue(stringValue);\n  }\n  /**\n   * Handles programmatic value setting. This has been split out to\n   * allow the range thumb to override it and add additional necessary logic.\n   */\n  _setValue(value) {\n    this._hostElement.value = value;\n    this._updateThumbUIByValue();\n    this._slider._onValueChange(this);\n    this._cdr.detectChanges();\n    this._slider._cdr.markForCheck();\n  }\n  /**\n   * The current translateX in px of the slider visual thumb.\n   * @docs-private\n   */\n  get translateX() {\n    if (this._slider.min >= this._slider.max) {\n      this._translateX = this._tickMarkOffset;\n      return this._translateX;\n    }\n    if (this._translateX === undefined) {\n      this._translateX = this._calcTranslateXByValue();\n    }\n    return this._translateX;\n  }\n  set translateX(v) {\n    this._translateX = v;\n  }\n  /** @docs-private */\n  get min() {\n    return numberAttribute(this._hostElement.min, 0);\n  }\n  set min(v) {\n    this._hostElement.min = v + '';\n    this._cdr.detectChanges();\n  }\n  /** @docs-private */\n  get max() {\n    return numberAttribute(this._hostElement.max, 0);\n  }\n  set max(v) {\n    this._hostElement.max = v + '';\n    this._cdr.detectChanges();\n  }\n  get step() {\n    return numberAttribute(this._hostElement.step, 0);\n  }\n  set step(v) {\n    this._hostElement.step = v + '';\n    this._cdr.detectChanges();\n  }\n  /** @docs-private */\n  get disabled() {\n    return booleanAttribute(this._hostElement.disabled);\n  }\n  set disabled(v) {\n    this._hostElement.disabled = v;\n    this._cdr.detectChanges();\n    if (this._slider.disabled !== this.disabled) {\n      this._slider.disabled = this.disabled;\n    }\n  }\n  /** The percentage of the slider that coincides with the value. */\n  get percentage() {\n    if (this._slider.min >= this._slider.max) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n  }\n  /** @docs-private */\n  get fillPercentage() {\n    if (!this._slider._cachedWidth) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    if (this._translateX === 0) {\n      return 0;\n    }\n    return this.translateX / this._slider._cachedWidth;\n  }\n  /** Used to relay updates to _isFocused to the slider visual thumbs. */\n  _setIsFocused(v) {\n    this._isFocused = v;\n  }\n  constructor(_ngZone, _elementRef, _cdr, _slider) {\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._cdr = _cdr;\n    this._slider = _slider;\n    /** Event emitted when the `value` is changed. */\n    this.valueChange = new EventEmitter();\n    /** Event emitted when the slider thumb starts being dragged. */\n    this.dragStart = new EventEmitter();\n    /** Event emitted when the slider thumb stops being dragged. */\n    this.dragEnd = new EventEmitter();\n    /**\n     * Indicates whether this thumb is the start or end thumb.\n     * @docs-private\n     */\n    this.thumbPosition = _MatThumb.END;\n    /** The aria-valuetext string representation of the input's value. */\n    this._valuetext = signal('');\n    /** The radius of a native html slider's knob. */\n    this._knobRadius = 8;\n    /** The distance in px from the start of the slider track to the first tick mark. */\n    this._tickMarkOffset = 3;\n    /** Whether user's cursor is currently in a mouse down state on the input. */\n    this._isActive = false;\n    /** Whether the input is currently focused (either by tab or after clicking). */\n    this._isFocused = false;\n    /**\n     * Whether the initial value has been set.\n     * This exists because the initial value cannot be immediately set because the min and max\n     * must first be relayed from the parent MatSlider component, which can only happen later\n     * in the component lifecycle.\n     */\n    this._hasSetInitialValue = false;\n    /** Emits when the component is destroyed. */\n    this._destroyed = new Subject();\n    /**\n     * Indicates whether UI updates should be skipped.\n     *\n     * This flag is used to avoid flickering\n     * when correcting values on pointer up/down.\n     */\n    this._skipUIUpdate = false;\n    /** Callback called when the slider input has been touched. */\n    this._onTouchedFn = () => {};\n    /**\n     * Whether the NgModel has been initialized.\n     *\n     * This flag is used to ignore ghost null calls to\n     * writeValue which can break slider initialization.\n     *\n     * See https://github.com/angular/angular/issues/14988.\n     */\n    this._isControlInitialized = false;\n    this._platform = inject(Platform);\n    this._hostElement = _elementRef.nativeElement;\n    this._ngZone.runOutsideAngular(() => {\n      this._hostElement.addEventListener('pointerdown', this._onPointerDown.bind(this));\n      this._hostElement.addEventListener('pointermove', this._onPointerMove.bind(this));\n      this._hostElement.addEventListener('pointerup', this._onPointerUp.bind(this));\n    });\n  }\n  ngOnDestroy() {\n    this._hostElement.removeEventListener('pointerdown', this._onPointerDown);\n    this._hostElement.removeEventListener('pointermove', this._onPointerMove);\n    this._hostElement.removeEventListener('pointerup', this._onPointerUp);\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.dragStart.complete();\n    this.dragEnd.complete();\n  }\n  /** @docs-private */\n  initProps() {\n    this._updateWidthInactive();\n    // If this or the parent slider is disabled, just make everything disabled.\n    if (this.disabled !== this._slider.disabled) {\n      // The MatSlider setter for disabled will relay this and disable both inputs.\n      this._slider.disabled = true;\n    }\n    this.step = this._slider.step;\n    this.min = this._slider.min;\n    this.max = this._slider.max;\n    this._initValue();\n  }\n  /** @docs-private */\n  initUI() {\n    this._updateThumbUIByValue();\n  }\n  _initValue() {\n    this._hasSetInitialValue = true;\n    if (this._initialValue === undefined) {\n      this.value = this._getDefaultValue();\n    } else {\n      this._hostElement.value = this._initialValue;\n      this._updateThumbUIByValue();\n      this._slider._onValueChange(this);\n      this._cdr.detectChanges();\n    }\n  }\n  _getDefaultValue() {\n    return this.min;\n  }\n  _onBlur() {\n    this._setIsFocused(false);\n    this._onTouchedFn();\n  }\n  _onFocus() {\n    this._slider._setTransition(false);\n    this._slider._updateTrackUI(this);\n    this._setIsFocused(true);\n  }\n  _onChange() {\n    this.valueChange.emit(this.value);\n    // only used to handle the edge case where user\n    // mousedown on the slider then uses arrow keys.\n    if (this._isActive) {\n      this._updateThumbUIByValue({\n        withAnimation: true\n      });\n    }\n  }\n  _onInput() {\n    this._onChangeFn?.(this.value);\n    // handles arrowing and updating the value when\n    // a step is defined.\n    if (this._slider.step || !this._isActive) {\n      this._updateThumbUIByValue({\n        withAnimation: true\n      });\n    }\n    this._slider._onValueChange(this);\n  }\n  _onNgControlValueChange() {\n    // only used to handle when the value change\n    // originates outside of the slider.\n    if (!this._isActive || !this._isFocused) {\n      this._slider._onValueChange(this);\n      this._updateThumbUIByValue();\n    }\n    this._slider.disabled = this._formControl.disabled;\n  }\n  _onPointerDown(event) {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    // On IOS, dragging only works if the pointer down happens on the\n    // slider thumb and the slider does not receive focus from pointer events.\n    if (this._platform.IOS) {\n      const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(event, this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect());\n      this._isActive = isCursorOnSliderThumb;\n      this._updateWidthActive();\n      this._slider._updateDimensions();\n      return;\n    }\n    this._isActive = true;\n    this._setIsFocused(true);\n    this._updateWidthActive();\n    this._slider._updateDimensions();\n    // Does nothing if a step is defined because we\n    // want the value to snap to the values on input.\n    if (!this._slider.step) {\n      this._updateThumbUIByPointerEvent(event, {\n        withAnimation: true\n      });\n    }\n    if (!this.disabled) {\n      this._handleValueCorrection(event);\n      this.dragStart.emit({\n        source: this,\n        parent: this._slider,\n        value: this.value\n      });\n    }\n  }\n  /**\n   * Corrects the value of the slider on pointer up/down.\n   *\n   * Called on pointer down and up because the value is set based\n   * on the inactive width instead of the active width.\n   */\n  _handleValueCorrection(event) {\n    // Don't update the UI with the current value! The value on pointerdown\n    // and pointerup is calculated in the split second before the input(s)\n    // resize. See _updateWidthInactive() and _updateWidthActive() for more\n    // details.\n    this._skipUIUpdate = true;\n    // Note that this function gets triggered before the actual value of the\n    // slider is updated. This means if we were to set the value here, it\n    // would immediately be overwritten. Using setTimeout ensures the setting\n    // of the value happens after the value has been updated by the\n    // pointerdown event.\n    setTimeout(() => {\n      this._skipUIUpdate = false;\n      this._fixValue(event);\n    }, 0);\n  }\n  /** Corrects the value of the slider based on the pointer event's position. */\n  _fixValue(event) {\n    const xPos = event.clientX - this._slider._cachedLeft;\n    const width = this._slider._cachedWidth;\n    const step = this._slider.step === 0 ? 1 : this._slider.step;\n    const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n    const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n    // To ensure the percentage is rounded to the necessary number of decimals.\n    const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n    const impreciseValue = fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n    const value = Math.round(impreciseValue / step) * step;\n    const prevValue = this.value;\n    if (value === prevValue) {\n      // Because we prevented UI updates, if it turns out that the race\n      // condition didn't happen and the value is already correct, we\n      // have to apply the ui updates now.\n      this._slider._onValueChange(this);\n      this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n        withAnimation: this._slider._hasAnimation\n      });\n      return;\n    }\n    this.value = value;\n    this.valueChange.emit(this.value);\n    this._onChangeFn?.(this.value);\n    this._slider._onValueChange(this);\n    this._slider.step > 0 ? this._updateThumbUIByValue() : this._updateThumbUIByPointerEvent(event, {\n      withAnimation: this._slider._hasAnimation\n    });\n  }\n  _onPointerMove(event) {\n    // Again, does nothing if a step is defined because\n    // we want the value to snap to the values on input.\n    if (!this._slider.step && this._isActive) {\n      this._updateThumbUIByPointerEvent(event);\n    }\n  }\n  _onPointerUp() {\n    if (this._isActive) {\n      this._isActive = false;\n      if (this._platform.SAFARI) {\n        this._setIsFocused(false);\n      }\n      this.dragEnd.emit({\n        source: this,\n        parent: this._slider,\n        value: this.value\n      });\n      // This setTimeout is to prevent the pointerup from triggering a value\n      // change on the input based on the inactive width. It's not clear why\n      // but for some reason on IOS this race condition is even more common so\n      // the timeout needs to be increased.\n      setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n    }\n  }\n  _clamp(v) {\n    const min = this._tickMarkOffset;\n    const max = this._slider._cachedWidth - this._tickMarkOffset;\n    return Math.max(Math.min(v, max), min);\n  }\n  _calcTranslateXByValue() {\n    if (this._slider._isRtl) {\n      return (1 - this.percentage) * (this._slider._cachedWidth - this._tickMarkOffset * 2) + this._tickMarkOffset;\n    }\n    return this.percentage * (this._slider._cachedWidth - this._tickMarkOffset * 2) + this._tickMarkOffset;\n  }\n  _calcTranslateXByPointerEvent(event) {\n    return event.clientX - this._slider._cachedLeft;\n  }\n  /**\n   * Used to set the slider width to the correct\n   * dimensions while the user is dragging.\n   */\n  _updateWidthActive() {}\n  /**\n   * Sets the slider input to disproportionate dimensions to allow for touch\n   * events to be captured on touch devices.\n   */\n  _updateWidthInactive() {\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    this._hostElement.style.width = `calc(100% + ${this._slider._inputPadding - this._tickMarkOffset * 2}px)`;\n    this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n  }\n  _updateThumbUIByValue(options) {\n    this.translateX = this._clamp(this._calcTranslateXByValue());\n    this._updateThumbUI(options);\n  }\n  _updateThumbUIByPointerEvent(event, options) {\n    this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n    this._updateThumbUI(options);\n  }\n  _updateThumbUI(options) {\n    this._slider._setTransition(!!options?.withAnimation);\n    this._slider._onTranslateXChange(this);\n  }\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value) {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n    }\n  }\n  /**\n   * Registers a callback to be invoked when the input's value changes from user input.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChangeFn = fn;\n    this._isControlInitialized = true;\n  }\n  /**\n   * Registers a callback to be invoked when the input is blurred by the user.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouchedFn = fn;\n  }\n  /**\n   * Sets the disabled state of the slider.\n   * @param isDisabled The new disabled state\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  focus() {\n    this._hostElement.focus();\n  }\n  blur() {\n    this._hostElement.blur();\n  }\n  static {\n    this.ɵfac = function MatSliderThumb_Factory(t) {\n      return new (t || MatSliderThumb)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_SLIDER));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSliderThumb,\n      selectors: [[\"input\", \"matSliderThumb\", \"\"]],\n      hostAttrs: [\"type\", \"range\", 1, \"mdc-slider__input\"],\n      hostVars: 1,\n      hostBindings: function MatSliderThumb_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"change\", function MatSliderThumb_change_HostBindingHandler() {\n            return ctx._onChange();\n          })(\"input\", function MatSliderThumb_input_HostBindingHandler() {\n            return ctx._onInput();\n          })(\"blur\", function MatSliderThumb_blur_HostBindingHandler() {\n            return ctx._onBlur();\n          })(\"focus\", function MatSliderThumb_focus_HostBindingHandler() {\n            return ctx._onFocus();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-valuetext\", ctx._valuetext());\n        }\n      },\n      inputs: {\n        value: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"value\", \"value\", numberAttribute]\n      },\n      outputs: {\n        valueChange: \"valueChange\",\n        dragStart: \"dragStart\",\n        dragEnd: \"dragEnd\"\n      },\n      exportAs: [\"matSliderThumb\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDER_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_THUMB,\n        useExisting: MatSliderThumb\n      }]), i0.ɵɵInputTransformsFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderThumb, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matSliderThumb]',\n      exportAs: 'matSliderThumb',\n      host: {\n        'class': 'mdc-slider__input',\n        'type': 'range',\n        '[attr.aria-valuetext]': '_valuetext()',\n        '(change)': '_onChange()',\n        '(input)': '_onInput()',\n        // TODO(wagnermaciel): Consider using a global event listener instead.\n        // Reason: I have found a semi-consistent way to mouse up without triggering this event.\n        '(blur)': '_onBlur()',\n        '(focus)': '_onFocus()'\n      },\n      providers: [MAT_SLIDER_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_THUMB,\n        useExisting: MatSliderThumb\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SLIDER]\n    }]\n  }], {\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    dragStart: [{\n      type: Output\n    }],\n    dragEnd: [{\n      type: Output\n    }]\n  });\n})();\nclass MatSliderRangeThumb extends MatSliderThumb {\n  /** @docs-private */\n  getSibling() {\n    if (!this._sibling) {\n      this._sibling = this._slider._getInput(this._isEndThumb ? _MatThumb.START : _MatThumb.END);\n    }\n    return this._sibling;\n  }\n  /**\n   * Returns the minimum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMinPos() {\n    const sibling = this.getSibling();\n    if (!this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._tickMarkOffset;\n  }\n  /**\n   * Returns the maximum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMaxPos() {\n    const sibling = this.getSibling();\n    if (this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._slider._cachedWidth - this._tickMarkOffset;\n  }\n  _setIsLeftThumb() {\n    this._isLeftThumb = this._isEndThumb && this._slider._isRtl || !this._isEndThumb && !this._slider._isRtl;\n  }\n  constructor(_ngZone, _slider, _elementRef, _cdr) {\n    super(_ngZone, _elementRef, _cdr, _slider);\n    this._cdr = _cdr;\n    this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n    this._setIsLeftThumb();\n    this.thumbPosition = this._isEndThumb ? _MatThumb.END : _MatThumb.START;\n  }\n  _getDefaultValue() {\n    return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n  }\n  _onInput() {\n    super._onInput();\n    this._updateSibling();\n    if (!this._isActive) {\n      this._updateWidthInactive();\n    }\n  }\n  _onNgControlValueChange() {\n    super._onNgControlValueChange();\n    this.getSibling()?._updateMinMax();\n  }\n  _onPointerDown(event) {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    if (this._sibling) {\n      this._sibling._updateWidthActive();\n      this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n    }\n    super._onPointerDown(event);\n  }\n  _onPointerUp() {\n    super._onPointerUp();\n    if (this._sibling) {\n      setTimeout(() => {\n        this._sibling._updateWidthInactive();\n        this._sibling._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n      });\n    }\n  }\n  _onPointerMove(event) {\n    super._onPointerMove(event);\n    if (!this._slider.step && this._isActive) {\n      this._updateSibling();\n    }\n  }\n  _fixValue(event) {\n    super._fixValue(event);\n    this._sibling?._updateMinMax();\n  }\n  _clamp(v) {\n    return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n  }\n  _updateMinMax() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    if (this._isEndThumb) {\n      this.min = Math.max(this._slider.min, sibling.value);\n      this.max = this._slider.max;\n    } else {\n      this.min = this._slider.min;\n      this.max = Math.min(this._slider.max, sibling.value);\n    }\n  }\n  _updateWidthActive() {\n    const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n    const maxWidth = this._slider._cachedWidth + this._slider._inputPadding - minWidth - this._tickMarkOffset * 2;\n    const percentage = this._slider.min < this._slider.max ? (this.max - this.min) / (this._slider.max - this._slider.min) : 1;\n    const width = maxWidth * percentage + minWidth;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n  }\n  _updateWidthInactive() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    const maxWidth = this._slider._cachedWidth - this._tickMarkOffset * 2;\n    const midValue = this._isEndThumb ? this.value - (this.value - sibling.value) / 2 : this.value + (sibling.value - this.value) / 2;\n    const _percentage = this._isEndThumb ? (this.max - midValue) / (this._slider.max - this._slider.min) : (midValue - this.min) / (this._slider.max - this._slider.min);\n    const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n    // Extend the native input width by the radius of the ripple\n    let ripplePadding = this._slider._rippleRadius;\n    // If one of the inputs is maximally sized (the value of both thumbs is\n    // equal to the min or max), make that input take up all of the width and\n    // make the other unselectable.\n    if (percentage === 1) {\n      ripplePadding = 48;\n    } else if (percentage === 0) {\n      ripplePadding = 0;\n    }\n    const width = maxWidth * percentage + ripplePadding;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = '0px';\n    if (this._isLeftThumb) {\n      this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n      this._hostElement.style.right = 'auto';\n    } else {\n      this._hostElement.style.left = 'auto';\n      this._hostElement.style.right = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n    }\n  }\n  _updateStaticStyles() {\n    this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n  }\n  _updateSibling() {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    sibling._updateMinMax();\n    if (this._isActive) {\n      sibling._updateWidthActive();\n    } else {\n      sibling._updateWidthInactive();\n    }\n  }\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value) {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n      this._updateWidthInactive();\n      this._updateSibling();\n    }\n  }\n  _setValue(value) {\n    super._setValue(value);\n    this._updateWidthInactive();\n    this._updateSibling();\n  }\n  static {\n    this.ɵfac = function MatSliderRangeThumb_Factory(t) {\n      return new (t || MatSliderRangeThumb)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(MAT_SLIDER), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSliderRangeThumb,\n      selectors: [[\"input\", \"matSliderStartThumb\", \"\"], [\"input\", \"matSliderEndThumb\", \"\"]],\n      exportAs: [\"matSliderRangeThumb\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_RANGE_THUMB,\n        useExisting: MatSliderRangeThumb\n      }]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderRangeThumb, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matSliderStartThumb], input[matSliderEndThumb]',\n      exportAs: 'matSliderRangeThumb',\n      providers: [MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR, {\n        provide: MAT_SLIDER_RANGE_THUMB,\n        useExisting: MatSliderRangeThumb\n      }],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SLIDER]\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }], null);\n})();\nclass MatSliderModule {\n  static {\n    this.ɵfac = function MatSliderModule_Factory(t) {\n      return new (t || MatSliderModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSliderModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatRippleModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSliderModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatSlider, MatSliderThumb, MatSliderRangeThumb, MatSliderVisualThumb],\n      exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatSlider, MatSliderChange, MatSliderModule, MatSliderRangeThumb, MatSliderThumb, MatSliderVisualThumb };", "map": {"version": 3, "names": ["i1", "Platform", "i0", "InjectionToken", "inject", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Inject", "Input", "ViewChild", "ANIMATION_MODULE_TYPE", "booleanAttribute", "numberAttribute", "Optional", "ViewChildren", "ContentChild", "ContentChildren", "forwardRef", "EventEmitter", "signal", "Directive", "Output", "NgModule", "RippleState", "<PERSON><PERSON><PERSON><PERSON>", "MAT_RIPPLE_GLOBAL_OPTIONS", "MatCommonModule", "MatRippleModule", "NG_VALUE_ACCESSOR", "Subject", "_c0", "_c1", "MatSliderVisualThumb_Conditional_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "valueIndicatorText", "_c2", "_c3", "MatSlider_Conditional_6_Conditional_2_For_1_Template", "ɵɵelement", "tickMark_r1", "$implicit", "i_r2", "$index", "ctx_r2", "ɵɵclassMap", "ɵɵstyleProp", "_calcTickMarkTransform", "MatSlider_Conditional_6_Conditional_2_Template", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIndex", "ɵɵrepeater", "_tickMarks", "MatSlider_Conditional_6_Template", "ɵɵtemplate", "ɵɵconditional", "_cachedWidth", "MatSlider_Conditional_7_Template", "ɵɵproperty", "discrete", "startValueIndicatorText", "_MatThumb", "_MatTickMark", "MAT_SLIDER", "MAT_SLIDER_THUMB", "MAT_SLIDER_RANGE_THUMB", "MAT_SLIDER_VISUAL_THUMB", "MatSliderChange", "MatSliderVisualThumb", "constructor", "_cdr", "_ngZone", "_elementRef", "_slider", "_isHovered", "_isActive", "_isValueIndicatorVisible", "_platform", "_onPointer<PERSON>ove", "event", "_sliderInput", "_isFocused", "rect", "_hostElement", "getBoundingClientRect", "isHovered", "_isCursorOnSliderThumb", "_showHoverRipple", "_hideRipple", "_hoverRippleRef", "_onMouseLeave", "_onFocus", "_showFocusRipple", "classList", "add", "_onBlur", "_focusRippleRef", "remove", "_onDragStart", "button", "_showActiveRipple", "_onDragEnd", "_activeRippleRef", "SAFARI", "nativeElement", "ngAfterViewInit", "_ripple", "radius", "_getInput", "thumbPosition", "_sliderInputEl", "input", "runOutsideAngular", "addEventListener", "ngOnDestroy", "removeEventListener", "_isShowingRipple", "_showRipple", "enterDuration", "exitDuration", "element", "rippleRef", "state", "FADING_IN", "VISIBLE", "animation", "ignoreGlobalRippleConfig", "disabled", "_showValueIndicator", "_isRange", "sibling", "_getThumb", "START", "END", "_globalRippleOptions", "launch", "_noopAnimations", "centered", "persistent", "fadeOut", "_isShowingAnyRipple", "_hideValueIndicator", "_getSibling", "_getValueIndicatorContainer", "_valueIndicatorContainer", "_getKnob", "_knob", "ɵfac", "MatSliderVisualThumb_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "NgZone", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "MatSliderVisualThumb_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "inputs", "standalone", "features", "ɵɵProvidersFeature", "provide", "useExisting", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "MatSliderVisualThumb_Template", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "providers", "imports", "undefined", "decorators", "<PERSON><PERSON><PERSON><PERSON>", "_disabled", "v", "endInput", "startInput", "_discrete", "_updateValueIndicatorUIs", "min", "_min", "isNaN", "_updateMin", "prevMin", "_updateMinRange", "old", "new", "_updateMinNonRange", "_onMinMaxOrStepChange", "oldEndValue", "value", "oldStartValue", "Math", "max", "_updateWidthInactive", "_onTranslateXChangeBySideEffect", "_onValueChange", "oldValue", "_updateThumbUIByValue", "_updateTrackUI", "_max", "_updateMax", "prevMax", "_updateMaxRange", "_updateMaxNonRange", "step", "_step", "_updateStep", "_updateStepRange", "_updateStepNonRange", "prevStartValue", "_dir", "animationMode", "showTickMarks", "disable<PERSON><PERSON><PERSON>", "displayWith", "_rippleRadius", "endValueIndicatorText", "_isRtl", "_hasViewInitialized", "_tickMarkTrackWidth", "_hasAnimation", "_resizeTimer", "_knobRadius", "_thumbsOverlap", "_dirChangeSubscription", "change", "subscribe", "_onDir<PERSON>hange", "<PERSON><PERSON><PERSON><PERSON>", "_updateDimensions", "eInput", "sInput", "detectChanges", "_validateInputs", "thumb", "_inputPadding", "_initUIRange", "_initUINonRange", "_updateTickMarkUI", "_updateTickMarkTrackUI", "_observeHostResize", "initProps", "initUI", "_updateValueIndicatorUI", "_updateMinMax", "_updateStaticStyles", "unsubscribe", "_resizeObserver", "disconnect", "_onDirChangeRange", "_onDirChangeNonRange", "_setIsLeftThumb", "translateX", "_calcTranslateXByValue", "ResizeObserver", "clearTimeout", "_onResize", "observe", "_getValue", "_skipUpdate", "_skipUIUpdate", "offsetWidth", "_cachedLeft", "left", "_setTrackActiveStyles", "trackStyle", "_trackActive", "style", "right", "transform<PERSON><PERSON>in", "transform", "index", "length", "_onTranslateXChange", "source", "_updateThumbUI", "_updateOverlappingThumbUI", "input1", "input2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_areThumbsOverlapping", "_updateOverlappingThumbClassNames", "getSibling", "sourceThumb", "siblingThumb", "toggle", "valuetext", "_valuetext", "set", "setAttribute", "visualThumb", "maxValue", "floor", "percentage", "_updateTrackUIRange", "_updateTrackUINonRange", "activePercentage", "abs", "_isLeftThumb", "fillPercentage", "_updateTickMarkUIRange", "_updateTickMarkUINonRange", "reverse", "numActive", "numInactive", "Array", "fill", "ACTIVE", "concat", "INACTIVE", "endValue", "startValue", "numInactiveBeforeStartThumb", "numInactiveAfterEndThumb", "_input", "_inputs", "last", "_thumbs", "_setTransition", "withAnimation", "IOS", "width", "centerX", "x", "centerY", "y", "dx", "clientX", "dy", "clientY", "pow", "MatSlider_Factory", "Directionality", "contentQueries", "MatSlider_ContentQueries", "dirIndex", "ɵɵcontentQuery", "MatSlider_Query", "hostVars", "hostBindings", "MatSlider_HostBindings", "color", "ɵɵclassProp", "ɵɵInputFlags", "HasDecoratorInputTransform", "exportAs", "ɵɵInputTransformsFeature", "ngContentSelectors", "MatSlider_Template", "ɵɵprojectionDef", "ɵɵprojection", "descendants", "isRange", "endInputElement", "startInputElement", "startValid", "hasAttribute", "endValid", "_throwInvalidInputConfigurationError", "Error", "MAT_SLIDER_THUMB_VALUE_ACCESSOR", "MatSlider<PERSON><PERSON>b", "multi", "MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR", "MatSliderRangeThumb", "stringValue", "_hasSetInitialValue", "_initialValue", "_setValue", "_translateX", "_tickMarkOffset", "_setIsFocused", "valueChange", "dragStart", "dragEnd", "_destroyed", "_onTouchedFn", "_isControlInitialized", "_onPointerDown", "bind", "_onPointerUp", "next", "complete", "_initValue", "_getDefaultValue", "_onChange", "emit", "_onInput", "_onChangeFn", "_onNgControlValueChange", "_formControl", "isCursorOnSliderThumb", "_updateWidthActive", "_updateThumbUIByPointerEvent", "_handleValueCorrection", "parent", "setTimeout", "_fixValue", "xPos", "numSteps", "fixedPercentage", "round", "impreciseValue", "prevValue", "_clamp", "_calcTranslateXByPointerEvent", "padding", "options", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "focus", "blur", "MatSliderThumb_Factory", "ɵdir", "ɵɵdefineDirective", "MatSliderThumb_HostBindings", "ɵɵlistener", "MatSliderThumb_change_HostBindingHandler", "MatSliderThumb_input_HostBindingHandler", "MatSliderThumb_blur_HostBindingHandler", "MatSliderThumb_focus_HostBindingHandler", "ɵɵattribute", "outputs", "_sibling", "_isEndThumb", "getMinPos", "getMaxPos", "_updateSibling", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "midValue", "_percentage", "ripplePadding", "MatSliderRangeThumb_Factory", "ɵɵInheritDefinitionFeature", "MatSliderModule", "MatSliderModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@angular/material/fesm2022/slider.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/bidi';\nimport { Platform } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, Component, ChangeDetectionStrategy, ViewEncapsulation, Inject, Input, ViewChild, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Optional, ViewChildren, ContentChild, ContentChildren, forwardRef, EventEmitter, signal, Directive, Output, NgModule } from '@angular/core';\nimport { RippleState, MatRipple, MAT_RIPPLE_GLOBAL_OPTIONS, MatCommonModule, MatRippleModule } from '@angular/material/core';\nimport { NG_VALUE_ACCESSOR } from '@angular/forms';\nimport { Subject } from 'rxjs';\n\n/**\n * Thumb types: range slider has two thumbs (START, END) whereas single point\n * slider only has one thumb (END).\n */\nvar _MatThumb;\n(function (_MatThumb) {\n    _MatThumb[_MatThumb[\"START\"] = 1] = \"START\";\n    _MatThumb[_MatThumb[\"END\"] = 2] = \"END\";\n})(_MatThumb || (_MatThumb = {}));\n/** Tick mark enum, for discrete sliders. */\nvar _MatTickMark;\n(function (_MatTickMark) {\n    _MatTickMark[_MatTickMark[\"ACTIVE\"] = 0] = \"ACTIVE\";\n    _MatTickMark[_MatTickMark[\"INACTIVE\"] = 1] = \"INACTIVE\";\n})(_MatTickMark || (_MatTickMark = {}));\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER = new InjectionToken('_MatSlider');\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB = new InjectionToken('_MatSliderThumb');\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB = new InjectionToken('_MatSliderRangeThumb');\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nconst MAT_SLIDER_VISUAL_THUMB = new InjectionToken('_MatSliderVisualThumb');\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://material.angular.io/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nclass MatSliderChange {\n}\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\nclass MatSliderVisualThumb {\n    constructor(_cdr, _ngZone, _elementRef, _slider) {\n        this._cdr = _cdr;\n        this._ngZone = _ngZone;\n        this._slider = _slider;\n        /** Whether the slider thumb is currently being hovered. */\n        this._isHovered = false;\n        /** Whether the slider thumb is currently being pressed. */\n        this._isActive = false;\n        /** Whether the value indicator tooltip is visible. */\n        this._isValueIndicatorVisible = false;\n        this._platform = inject(Platform);\n        this._onPointerMove = (event) => {\n            if (this._sliderInput._isFocused) {\n                return;\n            }\n            const rect = this._hostElement.getBoundingClientRect();\n            const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n            this._isHovered = isHovered;\n            if (isHovered) {\n                this._showHoverRipple();\n            }\n            else {\n                this._hideRipple(this._hoverRippleRef);\n            }\n        };\n        this._onMouseLeave = () => {\n            this._isHovered = false;\n            this._hideRipple(this._hoverRippleRef);\n        };\n        this._onFocus = () => {\n            // We don't want to show the hover ripple on top of the focus ripple.\n            // Happen when the users cursor is over a thumb and then the user tabs to it.\n            this._hideRipple(this._hoverRippleRef);\n            this._showFocusRipple();\n            this._hostElement.classList.add('mdc-slider__thumb--focused');\n        };\n        this._onBlur = () => {\n            // Happens when the user tabs away while still dragging a thumb.\n            if (!this._isActive) {\n                this._hideRipple(this._focusRippleRef);\n            }\n            // Happens when the user tabs away from a thumb but their cursor is still over it.\n            if (this._isHovered) {\n                this._showHoverRipple();\n            }\n            this._hostElement.classList.remove('mdc-slider__thumb--focused');\n        };\n        this._onDragStart = (event) => {\n            if (event.button !== 0) {\n                return;\n            }\n            this._isActive = true;\n            this._showActiveRipple();\n        };\n        this._onDragEnd = () => {\n            this._isActive = false;\n            this._hideRipple(this._activeRippleRef);\n            // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n            if (!this._sliderInput._isFocused) {\n                this._hideRipple(this._focusRippleRef);\n            }\n            // On Safari we need to immediately re-show the hover ripple because\n            // sliders do not retain focus from pointer events on that platform.\n            if (this._platform.SAFARI) {\n                this._showHoverRipple();\n            }\n        };\n        this._hostElement = _elementRef.nativeElement;\n    }\n    ngAfterViewInit() {\n        this._ripple.radius = 24;\n        this._sliderInput = this._slider._getInput(this.thumbPosition);\n        this._sliderInputEl = this._sliderInput._hostElement;\n        const input = this._sliderInputEl;\n        // These listeners don't update any data bindings so we bind them outside\n        // of the NgZone to prevent Angular from needlessly running change detection.\n        this._ngZone.runOutsideAngular(() => {\n            input.addEventListener('pointermove', this._onPointerMove);\n            input.addEventListener('pointerdown', this._onDragStart);\n            input.addEventListener('pointerup', this._onDragEnd);\n            input.addEventListener('pointerleave', this._onMouseLeave);\n            input.addEventListener('focus', this._onFocus);\n            input.addEventListener('blur', this._onBlur);\n        });\n    }\n    ngOnDestroy() {\n        const input = this._sliderInputEl;\n        if (input) {\n            input.removeEventListener('pointermove', this._onPointerMove);\n            input.removeEventListener('pointerdown', this._onDragStart);\n            input.removeEventListener('pointerup', this._onDragEnd);\n            input.removeEventListener('pointerleave', this._onMouseLeave);\n            input.removeEventListener('focus', this._onFocus);\n            input.removeEventListener('blur', this._onBlur);\n        }\n    }\n    /** Handles displaying the hover ripple. */\n    _showHoverRipple() {\n        if (!this._isShowingRipple(this._hoverRippleRef)) {\n            this._hoverRippleRef = this._showRipple({ enterDuration: 0, exitDuration: 0 });\n            this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n        }\n    }\n    /** Handles displaying the focus ripple. */\n    _showFocusRipple() {\n        // Show the focus ripple event if noop animations are enabled.\n        if (!this._isShowingRipple(this._focusRippleRef)) {\n            this._focusRippleRef = this._showRipple({ enterDuration: 0, exitDuration: 0 }, true);\n            this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n        }\n    }\n    /** Handles displaying the active ripple. */\n    _showActiveRipple() {\n        if (!this._isShowingRipple(this._activeRippleRef)) {\n            this._activeRippleRef = this._showRipple({ enterDuration: 225, exitDuration: 400 });\n            this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n        }\n    }\n    /** Whether the given rippleRef is currently fading in or visible. */\n    _isShowingRipple(rippleRef) {\n        return rippleRef?.state === RippleState.FADING_IN || rippleRef?.state === RippleState.VISIBLE;\n    }\n    /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n    _showRipple(animation, ignoreGlobalRippleConfig) {\n        if (this._slider.disabled) {\n            return;\n        }\n        this._showValueIndicator();\n        if (this._slider._isRange) {\n            const sibling = this._slider._getThumb(this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START);\n            sibling._showValueIndicator();\n        }\n        if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n            return;\n        }\n        return this._ripple.launch({\n            animation: this._slider._noopAnimations ? { enterDuration: 0, exitDuration: 0 } : animation,\n            centered: true,\n            persistent: true,\n        });\n    }\n    /**\n     * Fades out the given ripple.\n     * Also hides the value indicator if no ripple is showing.\n     */\n    _hideRipple(rippleRef) {\n        rippleRef?.fadeOut();\n        if (this._isShowingAnyRipple()) {\n            return;\n        }\n        if (!this._slider._isRange) {\n            this._hideValueIndicator();\n        }\n        const sibling = this._getSibling();\n        if (!sibling._isShowingAnyRipple()) {\n            this._hideValueIndicator();\n            sibling._hideValueIndicator();\n        }\n    }\n    /** Shows the value indicator ui. */\n    _showValueIndicator() {\n        this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n    }\n    /** Hides the value indicator ui. */\n    _hideValueIndicator() {\n        this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n    }\n    _getSibling() {\n        return this._slider._getThumb(this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START);\n    }\n    /** Gets the value indicator container's native HTML element. */\n    _getValueIndicatorContainer() {\n        return this._valueIndicatorContainer?.nativeElement;\n    }\n    /** Gets the native HTML element of the slider thumb knob. */\n    _getKnob() {\n        return this._knob.nativeElement;\n    }\n    _isShowingAnyRipple() {\n        return (this._isShowingRipple(this._hoverRippleRef) ||\n            this._isShowingRipple(this._focusRippleRef) ||\n            this._isShowingRipple(this._activeRippleRef));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderVisualThumb, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i0.ElementRef }, { token: MAT_SLIDER }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatSliderVisualThumb, isStandalone: true, selector: \"mat-slider-visual-thumb\", inputs: { discrete: \"discrete\", thumbPosition: \"thumbPosition\", valueIndicatorText: \"valueIndicatorText\" }, host: { classAttribute: \"mdc-slider__thumb mat-mdc-slider-visual-thumb\" }, providers: [{ provide: MAT_SLIDER_VISUAL_THUMB, useExisting: MatSliderVisualThumb }], viewQueries: [{ propertyName: \"_ripple\", first: true, predicate: MatRipple, descendants: true }, { propertyName: \"_knob\", first: true, predicate: [\"knob\"], descendants: true }, { propertyName: \"_valueIndicatorContainer\", first: true, predicate: [\"valueIndicatorContainer\"], descendants: true }], ngImport: i0, template: \"@if (discrete) {\\n  <div class=\\\"mdc-slider__value-indicator-container\\\" #valueIndicatorContainer>\\n    <div class=\\\"mdc-slider__value-indicator\\\">\\n      <span class=\\\"mdc-slider__value-indicator-text\\\">{{valueIndicatorText}}</span>\\n    </div>\\n  </div>\\n}\\n<div class=\\\"mdc-slider__thumb-knob\\\" #knob></div>\\n<div matRipple class=\\\"mat-mdc-focus-indicator\\\" [matRippleDisabled]=\\\"true\\\"></div>\\n\", styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderVisualThumb, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-slider-visual-thumb', host: {\n                        'class': 'mdc-slider__thumb mat-mdc-slider-visual-thumb',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [{ provide: MAT_SLIDER_VISUAL_THUMB, useExisting: MatSliderVisualThumb }], standalone: true, imports: [MatRipple], template: \"@if (discrete) {\\n  <div class=\\\"mdc-slider__value-indicator-container\\\" #valueIndicatorContainer>\\n    <div class=\\\"mdc-slider__value-indicator\\\">\\n      <span class=\\\"mdc-slider__value-indicator-text\\\">{{valueIndicatorText}}</span>\\n    </div>\\n  </div>\\n}\\n<div class=\\\"mdc-slider__thumb-knob\\\" #knob></div>\\n<div matRipple class=\\\"mat-mdc-focus-indicator\\\" [matRippleDisabled]=\\\"true\\\"></div>\\n\", styles: [\".mat-mdc-slider-visual-thumb .mat-ripple{height:100%;width:100%}.mat-mdc-slider .mdc-slider__tick-marks{justify-content:start}.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-marks .mdc-slider__tick-mark--inactive{position:absolute;left:2px}\"] }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDER]\n                }] }], propDecorators: { discrete: [{\n                type: Input\n            }], thumbPosition: [{\n                type: Input\n            }], valueIndicatorText: [{\n                type: Input\n            }], _ripple: [{\n                type: ViewChild,\n                args: [MatRipple]\n            }], _knob: [{\n                type: ViewChild,\n                args: ['knob']\n            }], _valueIndicatorContainer: [{\n                type: ViewChild,\n                args: ['valueIndicatorContainer']\n            }] } });\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\nclass MatSlider {\n    /** Whether the slider is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(v) {\n        this._disabled = v;\n        const endInput = this._getInput(_MatThumb.END);\n        const startInput = this._getInput(_MatThumb.START);\n        if (endInput) {\n            endInput.disabled = this._disabled;\n        }\n        if (startInput) {\n            startInput.disabled = this._disabled;\n        }\n    }\n    /** Whether the slider displays a numeric value label upon pressing the thumb. */\n    get discrete() {\n        return this._discrete;\n    }\n    set discrete(v) {\n        this._discrete = v;\n        this._updateValueIndicatorUIs();\n    }\n    /** The minimum value that the slider can have. */\n    get min() {\n        return this._min;\n    }\n    set min(v) {\n        const min = isNaN(v) ? this._min : v;\n        if (this._min !== min) {\n            this._updateMin(min);\n        }\n    }\n    _updateMin(min) {\n        const prevMin = this._min;\n        this._min = min;\n        this._isRange ? this._updateMinRange({ old: prevMin, new: min }) : this._updateMinNonRange(min);\n        this._onMinMaxOrStepChange();\n    }\n    _updateMinRange(min) {\n        const endInput = this._getInput(_MatThumb.END);\n        const startInput = this._getInput(_MatThumb.START);\n        const oldEndValue = endInput.value;\n        const oldStartValue = startInput.value;\n        startInput.min = min.new;\n        endInput.min = Math.max(min.new, startInput.value);\n        startInput.max = Math.min(endInput.max, endInput.value);\n        startInput._updateWidthInactive();\n        endInput._updateWidthInactive();\n        min.new < min.old\n            ? this._onTranslateXChangeBySideEffect(endInput, startInput)\n            : this._onTranslateXChangeBySideEffect(startInput, endInput);\n        if (oldEndValue !== endInput.value) {\n            this._onValueChange(endInput);\n        }\n        if (oldStartValue !== startInput.value) {\n            this._onValueChange(startInput);\n        }\n    }\n    _updateMinNonRange(min) {\n        const input = this._getInput(_MatThumb.END);\n        if (input) {\n            const oldValue = input.value;\n            input.min = min;\n            input._updateThumbUIByValue();\n            this._updateTrackUI(input);\n            if (oldValue !== input.value) {\n                this._onValueChange(input);\n            }\n        }\n    }\n    /** The maximum value that the slider can have. */\n    get max() {\n        return this._max;\n    }\n    set max(v) {\n        const max = isNaN(v) ? this._max : v;\n        if (this._max !== max) {\n            this._updateMax(max);\n        }\n    }\n    _updateMax(max) {\n        const prevMax = this._max;\n        this._max = max;\n        this._isRange ? this._updateMaxRange({ old: prevMax, new: max }) : this._updateMaxNonRange(max);\n        this._onMinMaxOrStepChange();\n    }\n    _updateMaxRange(max) {\n        const endInput = this._getInput(_MatThumb.END);\n        const startInput = this._getInput(_MatThumb.START);\n        const oldEndValue = endInput.value;\n        const oldStartValue = startInput.value;\n        endInput.max = max.new;\n        startInput.max = Math.min(max.new, endInput.value);\n        endInput.min = startInput.value;\n        endInput._updateWidthInactive();\n        startInput._updateWidthInactive();\n        max.new > max.old\n            ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n            : this._onTranslateXChangeBySideEffect(endInput, startInput);\n        if (oldEndValue !== endInput.value) {\n            this._onValueChange(endInput);\n        }\n        if (oldStartValue !== startInput.value) {\n            this._onValueChange(startInput);\n        }\n    }\n    _updateMaxNonRange(max) {\n        const input = this._getInput(_MatThumb.END);\n        if (input) {\n            const oldValue = input.value;\n            input.max = max;\n            input._updateThumbUIByValue();\n            this._updateTrackUI(input);\n            if (oldValue !== input.value) {\n                this._onValueChange(input);\n            }\n        }\n    }\n    /** The values at which the thumb will snap. */\n    get step() {\n        return this._step;\n    }\n    set step(v) {\n        const step = isNaN(v) ? this._step : v;\n        if (this._step !== step) {\n            this._updateStep(step);\n        }\n    }\n    _updateStep(step) {\n        this._step = step;\n        this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n        this._onMinMaxOrStepChange();\n    }\n    _updateStepRange() {\n        const endInput = this._getInput(_MatThumb.END);\n        const startInput = this._getInput(_MatThumb.START);\n        const oldEndValue = endInput.value;\n        const oldStartValue = startInput.value;\n        const prevStartValue = startInput.value;\n        endInput.min = this._min;\n        startInput.max = this._max;\n        endInput.step = this._step;\n        startInput.step = this._step;\n        if (this._platform.SAFARI) {\n            endInput.value = endInput.value;\n            startInput.value = startInput.value;\n        }\n        endInput.min = Math.max(this._min, startInput.value);\n        startInput.max = Math.min(this._max, endInput.value);\n        startInput._updateWidthInactive();\n        endInput._updateWidthInactive();\n        endInput.value < prevStartValue\n            ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n            : this._onTranslateXChangeBySideEffect(endInput, startInput);\n        if (oldEndValue !== endInput.value) {\n            this._onValueChange(endInput);\n        }\n        if (oldStartValue !== startInput.value) {\n            this._onValueChange(startInput);\n        }\n    }\n    _updateStepNonRange() {\n        const input = this._getInput(_MatThumb.END);\n        if (input) {\n            const oldValue = input.value;\n            input.step = this._step;\n            if (this._platform.SAFARI) {\n                input.value = input.value;\n            }\n            input._updateThumbUIByValue();\n            if (oldValue !== input.value) {\n                this._onValueChange(input);\n            }\n        }\n    }\n    constructor(_ngZone, _cdr, _elementRef, _dir, _globalRippleOptions, animationMode) {\n        this._ngZone = _ngZone;\n        this._cdr = _cdr;\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        this._globalRippleOptions = _globalRippleOptions;\n        this._disabled = false;\n        this._discrete = false;\n        /** Whether the slider displays tick marks along the slider track. */\n        this.showTickMarks = false;\n        this._min = 0;\n        /** Whether ripples are disabled in the slider. */\n        this.disableRipple = false;\n        this._max = 100;\n        this._step = 1;\n        /**\n         * Function that will be used to format the value before it is displayed\n         * in the thumb label. Can be used to format very large number in order\n         * for them to fit into the slider thumb.\n         */\n        this.displayWith = (value) => `${value}`;\n        this._rippleRadius = 24;\n        // The value indicator tooltip text for the visual slider thumb(s).\n        /** @docs-private */\n        this.startValueIndicatorText = '';\n        /** @docs-private */\n        this.endValueIndicatorText = '';\n        this._isRange = false;\n        /** Whether the slider is rtl. */\n        this._isRtl = false;\n        this._hasViewInitialized = false;\n        /**\n         * The width of the tick mark track.\n         * The tick mark track width is different from full track width\n         */\n        this._tickMarkTrackWidth = 0;\n        this._hasAnimation = false;\n        this._resizeTimer = null;\n        this._platform = inject(Platform);\n        /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n        this._knobRadius = 8;\n        /** Whether or not the slider thumbs overlap. */\n        this._thumbsOverlap = false;\n        this._noopAnimations = animationMode === 'NoopAnimations';\n        this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n        this._isRtl = this._dir.value === 'rtl';\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._updateDimensions();\n        }\n        const eInput = this._getInput(_MatThumb.END);\n        const sInput = this._getInput(_MatThumb.START);\n        this._isRange = !!eInput && !!sInput;\n        this._cdr.detectChanges();\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            _validateInputs(this._isRange, this._getInput(_MatThumb.END), this._getInput(_MatThumb.START));\n        }\n        const thumb = this._getThumb(_MatThumb.END);\n        this._rippleRadius = thumb._ripple.radius;\n        this._inputPadding = this._rippleRadius - this._knobRadius;\n        this._isRange\n            ? this._initUIRange(eInput, sInput)\n            : this._initUINonRange(eInput);\n        this._updateTrackUI(eInput);\n        this._updateTickMarkUI();\n        this._updateTickMarkTrackUI();\n        this._observeHostResize();\n        this._cdr.detectChanges();\n    }\n    _initUINonRange(eInput) {\n        eInput.initProps();\n        eInput.initUI();\n        this._updateValueIndicatorUI(eInput);\n        this._hasViewInitialized = true;\n        eInput._updateThumbUIByValue();\n    }\n    _initUIRange(eInput, sInput) {\n        eInput.initProps();\n        eInput.initUI();\n        sInput.initProps();\n        sInput.initUI();\n        eInput._updateMinMax();\n        sInput._updateMinMax();\n        eInput._updateStaticStyles();\n        sInput._updateStaticStyles();\n        this._updateValueIndicatorUIs();\n        this._hasViewInitialized = true;\n        eInput._updateThumbUIByValue();\n        sInput._updateThumbUIByValue();\n    }\n    ngOnDestroy() {\n        this._dirChangeSubscription.unsubscribe();\n        this._resizeObserver?.disconnect();\n        this._resizeObserver = null;\n    }\n    /** Handles updating the slider ui after a dir change. */\n    _onDirChange() {\n        this._isRtl = this._dir.value === 'rtl';\n        this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n        this._updateTickMarkUI();\n    }\n    _onDirChangeRange() {\n        const endInput = this._getInput(_MatThumb.END);\n        const startInput = this._getInput(_MatThumb.START);\n        endInput._setIsLeftThumb();\n        startInput._setIsLeftThumb();\n        endInput.translateX = endInput._calcTranslateXByValue();\n        startInput.translateX = startInput._calcTranslateXByValue();\n        endInput._updateStaticStyles();\n        startInput._updateStaticStyles();\n        endInput._updateWidthInactive();\n        startInput._updateWidthInactive();\n        endInput._updateThumbUIByValue();\n        startInput._updateThumbUIByValue();\n    }\n    _onDirChangeNonRange() {\n        const input = this._getInput(_MatThumb.END);\n        input._updateThumbUIByValue();\n    }\n    /** Starts observing and updating the slider if the host changes its size. */\n    _observeHostResize() {\n        if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n            return;\n        }\n        this._ngZone.runOutsideAngular(() => {\n            this._resizeObserver = new ResizeObserver(() => {\n                if (this._isActive()) {\n                    return;\n                }\n                if (this._resizeTimer) {\n                    clearTimeout(this._resizeTimer);\n                }\n                this._onResize();\n            });\n            this._resizeObserver.observe(this._elementRef.nativeElement);\n        });\n    }\n    /** Whether any of the thumbs are currently active. */\n    _isActive() {\n        return this._getThumb(_MatThumb.START)._isActive || this._getThumb(_MatThumb.END)._isActive;\n    }\n    _getValue(thumbPosition = _MatThumb.END) {\n        const input = this._getInput(thumbPosition);\n        if (!input) {\n            return this.min;\n        }\n        return input.value;\n    }\n    _skipUpdate() {\n        return !!(this._getInput(_MatThumb.START)?._skipUIUpdate || this._getInput(_MatThumb.END)?._skipUIUpdate);\n    }\n    /** Stores the slider dimensions. */\n    _updateDimensions() {\n        this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n        this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n    }\n    /** Sets the styles for the active portion of the track. */\n    _setTrackActiveStyles(styles) {\n        const trackStyle = this._trackActive.nativeElement.style;\n        trackStyle.left = styles.left;\n        trackStyle.right = styles.right;\n        trackStyle.transformOrigin = styles.transformOrigin;\n        trackStyle.transform = styles.transform;\n    }\n    /** Returns the translateX positioning for a tick mark based on it's index. */\n    _calcTickMarkTransform(index) {\n        // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n        const translateX = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n        return `translateX(${translateX}px`;\n    }\n    // Handlers for updating the slider ui.\n    _onTranslateXChange(source) {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateThumbUI(source);\n        this._updateTrackUI(source);\n        this._updateOverlappingThumbUI(source);\n    }\n    _onTranslateXChangeBySideEffect(input1, input2) {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        input1._updateThumbUIByValue();\n        input2._updateThumbUIByValue();\n    }\n    _onValueChange(source) {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateValueIndicatorUI(source);\n        this._updateTickMarkUI();\n        this._cdr.detectChanges();\n    }\n    _onMinMaxOrStepChange() {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateTickMarkUI();\n        this._updateTickMarkTrackUI();\n        this._cdr.markForCheck();\n    }\n    _onResize() {\n        if (!this._hasViewInitialized) {\n            return;\n        }\n        this._updateDimensions();\n        if (this._isRange) {\n            const eInput = this._getInput(_MatThumb.END);\n            const sInput = this._getInput(_MatThumb.START);\n            eInput._updateThumbUIByValue();\n            sInput._updateThumbUIByValue();\n            eInput._updateStaticStyles();\n            sInput._updateStaticStyles();\n            eInput._updateMinMax();\n            sInput._updateMinMax();\n            eInput._updateWidthInactive();\n            sInput._updateWidthInactive();\n        }\n        else {\n            const eInput = this._getInput(_MatThumb.END);\n            if (eInput) {\n                eInput._updateThumbUIByValue();\n            }\n        }\n        this._updateTickMarkUI();\n        this._updateTickMarkTrackUI();\n        this._cdr.detectChanges();\n    }\n    /** Returns true if the slider knobs are overlapping one another. */\n    _areThumbsOverlapping() {\n        const startInput = this._getInput(_MatThumb.START);\n        const endInput = this._getInput(_MatThumb.END);\n        if (!startInput || !endInput) {\n            return false;\n        }\n        return endInput.translateX - startInput.translateX < 20;\n    }\n    /**\n     * Updates the class names of overlapping slider thumbs so\n     * that the current active thumb is styled to be on \"top\".\n     */\n    _updateOverlappingThumbClassNames(source) {\n        const sibling = source.getSibling();\n        const sourceThumb = this._getThumb(source.thumbPosition);\n        const siblingThumb = this._getThumb(sibling.thumbPosition);\n        siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n        sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n    }\n    /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n    _updateOverlappingThumbUI(source) {\n        if (!this._isRange || this._skipUpdate()) {\n            return;\n        }\n        if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n            this._thumbsOverlap = !this._thumbsOverlap;\n            this._updateOverlappingThumbClassNames(source);\n        }\n    }\n    // _MatThumb styles update conditions\n    //\n    // 1. TranslateX, resize, or dir change\n    //    - Reason: The thumb styles need to be updated according to the new translateX.\n    // 2. Min, max, or step\n    //    - Reason: The value may have silently changed.\n    /** Updates the translateX of the given thumb. */\n    _updateThumbUI(source) {\n        if (this._skipUpdate()) {\n            return;\n        }\n        const thumb = this._getThumb(source.thumbPosition === _MatThumb.END ? _MatThumb.END : _MatThumb.START);\n        thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n    }\n    // Value indicator text update conditions\n    //\n    // 1. Value\n    //    - Reason: The value displayed needs to be updated.\n    // 2. Min, max, or step\n    //    - Reason: The value may have silently changed.\n    /** Updates the value indicator tooltip ui for the given thumb. */\n    _updateValueIndicatorUI(source) {\n        if (this._skipUpdate()) {\n            return;\n        }\n        const valuetext = this.displayWith(source.value);\n        this._hasViewInitialized\n            ? source._valuetext.set(valuetext)\n            : source._hostElement.setAttribute('aria-valuetext', valuetext);\n        if (this.discrete) {\n            source.thumbPosition === _MatThumb.START\n                ? (this.startValueIndicatorText = valuetext)\n                : (this.endValueIndicatorText = valuetext);\n            const visualThumb = this._getThumb(source.thumbPosition);\n            valuetext.length < 3\n                ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value')\n                : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n        }\n    }\n    /** Updates all value indicator UIs in the slider. */\n    _updateValueIndicatorUIs() {\n        const eInput = this._getInput(_MatThumb.END);\n        const sInput = this._getInput(_MatThumb.START);\n        if (eInput) {\n            this._updateValueIndicatorUI(eInput);\n        }\n        if (sInput) {\n            this._updateValueIndicatorUI(sInput);\n        }\n    }\n    // Update Tick Mark Track Width\n    //\n    // 1. Min, max, or step\n    //    - Reason: The maximum reachable value may have changed.\n    //    - Side note: The maximum reachable value is different from the maximum value set by the\n    //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n    //      reachable value of 95.\n    // 2. Resize\n    //    - Reason: The position for the maximum reachable value needs to be recalculated.\n    /** Updates the width of the tick mark track. */\n    _updateTickMarkTrackUI() {\n        if (!this.showTickMarks || this._skipUpdate()) {\n            return;\n        }\n        const step = this._step && this._step > 0 ? this._step : 1;\n        const maxValue = Math.floor(this.max / step) * step;\n        const percentage = (maxValue - this.min) / (this.max - this.min);\n        this._tickMarkTrackWidth = this._cachedWidth * percentage - 6;\n    }\n    // Track active update conditions\n    //\n    // 1. TranslateX\n    //    - Reason: The track active should line up with the new thumb position.\n    // 2. Min or max\n    //    - Reason #1: The 'active' percentage needs to be recalculated.\n    //    - Reason #2: The value may have silently changed.\n    // 3. Step\n    //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n    // 4. Dir change\n    //    - Reason: The track active will need to be updated according to the new thumb position(s).\n    // 5. Resize\n    //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n    /** Updates the scale on the active portion of the track. */\n    _updateTrackUI(source) {\n        if (this._skipUpdate()) {\n            return;\n        }\n        this._isRange\n            ? this._updateTrackUIRange(source)\n            : this._updateTrackUINonRange(source);\n    }\n    _updateTrackUIRange(source) {\n        const sibling = source.getSibling();\n        if (!sibling || !this._cachedWidth) {\n            return;\n        }\n        const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n        if (source._isLeftThumb && this._cachedWidth) {\n            this._setTrackActiveStyles({\n                left: 'auto',\n                right: `${this._cachedWidth - sibling.translateX}px`,\n                transformOrigin: 'right',\n                transform: `scaleX(${activePercentage})`,\n            });\n        }\n        else {\n            this._setTrackActiveStyles({\n                left: `${sibling.translateX}px`,\n                right: 'auto',\n                transformOrigin: 'left',\n                transform: `scaleX(${activePercentage})`,\n            });\n        }\n    }\n    _updateTrackUINonRange(source) {\n        this._isRtl\n            ? this._setTrackActiveStyles({\n                left: 'auto',\n                right: '0px',\n                transformOrigin: 'right',\n                transform: `scaleX(${1 - source.fillPercentage})`,\n            })\n            : this._setTrackActiveStyles({\n                left: '0px',\n                right: 'auto',\n                transformOrigin: 'left',\n                transform: `scaleX(${source.fillPercentage})`,\n            });\n    }\n    // Tick mark update conditions\n    //\n    // 1. Value\n    //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n    // 2. Min, max, or step\n    //    - Reason #1: the number of tick marks may have changed.\n    //    - Reason #2: The value may have silently changed.\n    /** Updates the dots along the slider track. */\n    _updateTickMarkUI() {\n        if (!this.showTickMarks ||\n            this.step === undefined ||\n            this.min === undefined ||\n            this.max === undefined) {\n            return;\n        }\n        const step = this.step > 0 ? this.step : 1;\n        this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n        if (this._isRtl) {\n            this._tickMarks.reverse();\n        }\n    }\n    _updateTickMarkUINonRange(step) {\n        const value = this._getValue();\n        let numActive = Math.max(Math.floor((value - this.min) / step), 0);\n        let numInactive = Math.max(Math.floor((this.max - value) / step), 0);\n        this._isRtl ? numActive++ : numInactive++;\n        this._tickMarks = Array(numActive)\n            .fill(_MatTickMark.ACTIVE)\n            .concat(Array(numInactive).fill(_MatTickMark.INACTIVE));\n    }\n    _updateTickMarkUIRange(step) {\n        const endValue = this._getValue();\n        const startValue = this._getValue(_MatThumb.START);\n        const numInactiveBeforeStartThumb = Math.max(Math.floor((startValue - this.min) / step), 0);\n        const numActive = Math.max(Math.floor((endValue - startValue) / step) + 1, 0);\n        const numInactiveAfterEndThumb = Math.max(Math.floor((this.max - endValue) / step), 0);\n        this._tickMarks = Array(numInactiveBeforeStartThumb)\n            .fill(_MatTickMark.INACTIVE)\n            .concat(Array(numActive).fill(_MatTickMark.ACTIVE), Array(numInactiveAfterEndThumb).fill(_MatTickMark.INACTIVE));\n    }\n    /** Gets the slider thumb input of the given thumb position. */\n    _getInput(thumbPosition) {\n        if (thumbPosition === _MatThumb.END && this._input) {\n            return this._input;\n        }\n        if (this._inputs?.length) {\n            return thumbPosition === _MatThumb.START ? this._inputs.first : this._inputs.last;\n        }\n        return;\n    }\n    /** Gets the slider thumb HTML input element of the given thumb position. */\n    _getThumb(thumbPosition) {\n        return thumbPosition === _MatThumb.END ? this._thumbs?.last : this._thumbs?.first;\n    }\n    _setTransition(withAnimation) {\n        this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n        this._elementRef.nativeElement.classList.toggle('mat-mdc-slider-with-animation', this._hasAnimation);\n    }\n    /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n    _isCursorOnSliderThumb(event, rect) {\n        const radius = rect.width / 2;\n        const centerX = rect.x + radius;\n        const centerY = rect.y + radius;\n        const dx = event.clientX - centerX;\n        const dy = event.clientY - centerY;\n        return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlider, deps: [{ token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: i1.Directionality, optional: true }, { token: MAT_RIPPLE_GLOBAL_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatSlider, isStandalone: true, selector: \"mat-slider\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], discrete: [\"discrete\", \"discrete\", booleanAttribute], showTickMarks: [\"showTickMarks\", \"showTickMarks\", booleanAttribute], min: [\"min\", \"min\", numberAttribute], color: \"color\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], max: [\"max\", \"max\", numberAttribute], step: [\"step\", \"step\", numberAttribute], displayWith: \"displayWith\" }, host: { properties: { \"class\": \"\\\"mat-\\\" + (color || \\\"primary\\\")\", \"class.mdc-slider--range\": \"_isRange\", \"class.mdc-slider--disabled\": \"disabled\", \"class.mdc-slider--discrete\": \"discrete\", \"class.mdc-slider--tick-marks\": \"showTickMarks\", \"class._mat-animation-noopable\": \"_noopAnimations\" }, classAttribute: \"mat-mdc-slider mdc-slider\" }, providers: [{ provide: MAT_SLIDER, useExisting: MatSlider }], queries: [{ propertyName: \"_input\", first: true, predicate: MAT_SLIDER_THUMB, descendants: true }, { propertyName: \"_inputs\", predicate: MAT_SLIDER_RANGE_THUMB }], viewQueries: [{ propertyName: \"_trackActive\", first: true, predicate: [\"trackActive\"], descendants: true }, { propertyName: \"_thumbs\", predicate: MAT_SLIDER_VISUAL_THUMB, descendants: true }], exportAs: [\"matSlider\"], ngImport: i0, template: \"<!-- Inputs -->\\n<ng-content></ng-content>\\n\\n<!-- Track -->\\n<div class=\\\"mdc-slider__track\\\">\\n  <div class=\\\"mdc-slider__track--inactive\\\"></div>\\n  <div class=\\\"mdc-slider__track--active\\\">\\n    <div #trackActive class=\\\"mdc-slider__track--active_fill\\\"></div>\\n  </div>\\n  @if (showTickMarks) {\\n    <div class=\\\"mdc-slider__tick-marks\\\" #tickMarkContainer>\\n      @if (_cachedWidth) {\\n        @for (tickMark of _tickMarks; track i; let i = $index) {\\n          <div\\n            [class]=\\\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\\\"\\n            [style.transform]=\\\"_calcTickMarkTransform(i)\\\"></div>\\n        }\\n      }\\n    </div>\\n  }\\n</div>\\n\\n<!-- Thumbs -->\\n@if (_isRange) {\\n  <mat-slider-visual-thumb\\n    [discrete]=\\\"discrete\\\"\\n    [thumbPosition]=\\\"1\\\"\\n    [valueIndicatorText]=\\\"startValueIndicatorText\\\">\\n  </mat-slider-visual-thumb>\\n}\\n\\n<mat-slider-visual-thumb\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"2\\\"\\n  [valueIndicatorText]=\\\"endValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\", styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:50%;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:translateX(-50%);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:50%;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:translateX(-50%);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color);border-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color);border-color:var(--mdc-slider-disabled-handle-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color)}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color);opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color);opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color)}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height);top:calc((var(--mdc-slider-inactive-track-height) - var(--mdc-slider-active-track-height)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size);width:var(--mdc-slider-with-tick-marks-container-size)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking);font-size:var(--mdc-slider-label-label-text-size);font-family:var(--mdc-slider-label-label-text-font);font-weight:var(--mdc-slider-label-label-text-weight);line-height:var(--mdc-slider-label-label-text-line-height)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape);width:var(--mdc-slider-handle-width);height:var(--mdc-slider-handle-height);border-style:solid;border-width:calc(var(--mdc-slider-handle-height) / 2) calc(var(--mdc-slider-handle-width) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color);border-color:var(--mdc-slider-hover-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color);border-color:var(--mdc-slider-focus-handle-color)}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color);border-width:var(--mdc-slider-with-overlap-handle-outline-width)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator-container{transform:var(--mat-slider-value-indicator-container-transform)}.mat-mdc-slider .mdc-slider__value-indicator{width:var(--mat-slider-value-indicator-width);height:var(--mat-slider-value-indicator-height);padding:var(--mat-slider-value-indicator-padding);opacity:var(--mat-slider-value-indicator-opacity);border-radius:var(--mat-slider-value-indicator-border-radius)}.mat-mdc-slider .mdc-slider__value-indicator::before{display:var(--mat-slider-value-indicator-caret-display)}.mat-mdc-slider .mdc-slider__value-indicator-text{width:var(--mat-slider-value-indicator-width);transform:var(--mat-slider-value-indicator-text-transform)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-slider-ripple-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-slider-hover-state-layer-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-slider-focus-state-layer-color)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slider .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__value-indicator-text{text-align:center}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"component\", type: MatSliderVisualThumb, selector: \"mat-slider-visual-thumb\", inputs: [\"discrete\", \"thumbPosition\", \"valueIndicatorText\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlider, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-slider', host: {\n                        'class': 'mat-mdc-slider mdc-slider',\n                        '[class]': '\"mat-\" + (color || \"primary\")',\n                        '[class.mdc-slider--range]': '_isRange',\n                        '[class.mdc-slider--disabled]': 'disabled',\n                        '[class.mdc-slider--discrete]': 'discrete',\n                        '[class.mdc-slider--tick-marks]': 'showTickMarks',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                    }, exportAs: 'matSlider', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, providers: [{ provide: MAT_SLIDER, useExisting: MatSlider }], standalone: true, imports: [MatSliderVisualThumb], template: \"<!-- Inputs -->\\n<ng-content></ng-content>\\n\\n<!-- Track -->\\n<div class=\\\"mdc-slider__track\\\">\\n  <div class=\\\"mdc-slider__track--inactive\\\"></div>\\n  <div class=\\\"mdc-slider__track--active\\\">\\n    <div #trackActive class=\\\"mdc-slider__track--active_fill\\\"></div>\\n  </div>\\n  @if (showTickMarks) {\\n    <div class=\\\"mdc-slider__tick-marks\\\" #tickMarkContainer>\\n      @if (_cachedWidth) {\\n        @for (tickMark of _tickMarks; track i; let i = $index) {\\n          <div\\n            [class]=\\\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\\\"\\n            [style.transform]=\\\"_calcTickMarkTransform(i)\\\"></div>\\n        }\\n      }\\n    </div>\\n  }\\n</div>\\n\\n<!-- Thumbs -->\\n@if (_isRange) {\\n  <mat-slider-visual-thumb\\n    [discrete]=\\\"discrete\\\"\\n    [thumbPosition]=\\\"1\\\"\\n    [valueIndicatorText]=\\\"startValueIndicatorText\\\">\\n  </mat-slider-visual-thumb>\\n}\\n\\n<mat-slider-visual-thumb\\n  [discrete]=\\\"discrete\\\"\\n  [thumbPosition]=\\\"2\\\"\\n  [valueIndicatorText]=\\\"endValueIndicatorText\\\">\\n</mat-slider-visual-thumb>\\n\", styles: [\".mdc-slider{cursor:pointer;height:48px;margin:0 24px;position:relative;touch-action:pan-y}.mdc-slider .mdc-slider__track{position:absolute;top:50%;transform:translateY(-50%);width:100%}.mdc-slider .mdc-slider__track--active,.mdc-slider .mdc-slider__track--inactive{display:flex;height:100%;position:absolute;width:100%}.mdc-slider .mdc-slider__track--active{overflow:hidden}.mdc-slider .mdc-slider__track--active_fill{border-top-style:solid;box-sizing:border-box;height:100%;width:100%;position:relative;-webkit-transform-origin:left;transform-origin:left}[dir=rtl] .mdc-slider .mdc-slider__track--active_fill,.mdc-slider .mdc-slider__track--active_fill[dir=rtl]{-webkit-transform-origin:right;transform-origin:right}.mdc-slider .mdc-slider__track--inactive{left:0;top:0}.mdc-slider .mdc-slider__track--inactive::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__track--inactive::before{border-color:CanvasText}}.mdc-slider .mdc-slider__value-indicator-container{bottom:44px;left:50%;left:var(--slider-value-indicator-container-left, 50%);pointer-events:none;position:absolute;right:var(--slider-value-indicator-container-right);transform:translateX(-50%);transform:var(--slider-value-indicator-container-transform, translateX(-50%))}.mdc-slider .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0.4, 0, 1, 1);align-items:center;border-radius:4px;display:flex;height:32px;padding:0 12px;transform:scale(0);transform-origin:bottom}.mdc-slider .mdc-slider__value-indicator::before{border-left:6px solid rgba(0,0,0,0);border-right:6px solid rgba(0,0,0,0);border-top:6px solid;bottom:-5px;content:\\\"\\\";height:0;left:50%;left:var(--slider-value-indicator-caret-left, 50%);position:absolute;right:var(--slider-value-indicator-caret-right);transform:translateX(-50%);transform:var(--slider-value-indicator-caret-transform, translateX(-50%));width:0}.mdc-slider .mdc-slider__value-indicator::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-slider .mdc-slider__value-indicator::after{border-color:CanvasText}}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator-container{pointer-events:auto}.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:transform 100ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:scale(1)}@media(prefers-reduced-motion){.mdc-slider .mdc-slider__value-indicator,.mdc-slider .mdc-slider__thumb--with-indicator .mdc-slider__value-indicator{transition:none}}.mdc-slider .mdc-slider__thumb{display:flex;left:-24px;outline:none;position:absolute;user-select:none;height:48px;width:48px}.mdc-slider .mdc-slider__thumb--top{z-index:1}.mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-style:solid;border-width:1px;box-sizing:content-box}.mdc-slider .mdc-slider__thumb-knob{box-sizing:border-box;left:50%;position:absolute;top:50%;transform:translate(-50%, -50%)}.mdc-slider .mdc-slider__tick-marks{align-items:center;box-sizing:border-box;display:flex;height:100%;justify-content:space-between;padding:0 1px;position:absolute;width:100%}.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:transform 80ms ease}@media(prefers-reduced-motion){.mdc-slider--discrete .mdc-slider__thumb,.mdc-slider--discrete .mdc-slider__track--active_fill{transition:none}}.mdc-slider--disabled{cursor:auto}.mdc-slider--disabled .mdc-slider__thumb{pointer-events:none}.mdc-slider__input{cursor:pointer;left:2px;margin:0;height:44px;opacity:0;pointer-events:none;position:absolute;top:2px;width:44px}.mat-mdc-slider{display:inline-block;box-sizing:border-box;outline:none;vertical-align:middle;margin-left:8px;margin-right:8px;width:auto;min-width:112px;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-slider .mdc-slider__thumb-knob{background-color:var(--mdc-slider-handle-color);border-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb-knob{background-color:var(--mdc-slider-disabled-handle-color);border-color:var(--mdc-slider-disabled-handle-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider.mdc-slider--disabled .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb::before,.mat-mdc-slider .mdc-slider__thumb::after{background-color:var(--mdc-slider-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover::before,.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-surface--hover::before{opacity:var(--mdc-ripple-hover-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded--background-focused::before,.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):focus::before{transition-duration:75ms;opacity:var(--mdc-ripple-focus-opacity)}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded)::after{transition:opacity 150ms linear}.mat-mdc-slider .mdc-slider__thumb:not(.mdc-ripple-upgraded):active::after{transition-duration:75ms;opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__thumb.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-ripple-press-opacity)}.mat-mdc-slider .mdc-slider__track--active_fill{border-color:var(--mdc-slider-active-track-color)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--active_fill{border-color:var(--mdc-slider-disabled-active-track-color)}.mat-mdc-slider .mdc-slider__track--inactive{background-color:var(--mdc-slider-inactive-track-color);opacity:.24}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__track--inactive{background-color:var(--mdc-slider-disabled-inactive-track-color);opacity:.24}.mat-mdc-slider .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--active{background-color:var(--mdc-slider-with-tick-marks-active-container-color);opacity:var(--mdc-slider-with-tick-marks-active-container-opacity)}.mat-mdc-slider .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-inactive-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider.mdc-slider--disabled .mdc-slider__tick-mark--inactive{background-color:var(--mdc-slider-with-tick-marks-disabled-container-color);opacity:var(--mdc-slider-with-tick-marks-inactive-container-opacity)}.mat-mdc-slider .mdc-slider__value-indicator{background-color:var(--mdc-slider-label-container-color);opacity:1}.mat-mdc-slider .mdc-slider__value-indicator::before{border-top-color:var(--mdc-slider-label-container-color)}.mat-mdc-slider .mdc-slider__value-indicator{color:var(--mdc-slider-label-label-text-color)}.mat-mdc-slider .mdc-slider__track{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__track--active{height:var(--mdc-slider-active-track-height);top:calc((var(--mdc-slider-inactive-track-height) - var(--mdc-slider-active-track-height)) / 2)}.mat-mdc-slider .mdc-slider__track--active_fill{border-top-width:var(--mdc-slider-active-track-height)}.mat-mdc-slider .mdc-slider__track--inactive{height:var(--mdc-slider-inactive-track-height)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{height:var(--mdc-slider-with-tick-marks-container-size);width:var(--mdc-slider-with-tick-marks-container-size)}.mat-mdc-slider.mdc-slider--disabled{opacity:0.38}.mat-mdc-slider .mdc-slider__value-indicator-text{letter-spacing:var(--mdc-slider-label-label-text-tracking);font-size:var(--mdc-slider-label-label-text-size);font-family:var(--mdc-slider-label-label-text-font);font-weight:var(--mdc-slider-label-label-text-weight);line-height:var(--mdc-slider-label-label-text-line-height)}.mat-mdc-slider .mdc-slider__track--active{border-radius:var(--mdc-slider-active-track-shape)}.mat-mdc-slider .mdc-slider__track--inactive{border-radius:var(--mdc-slider-inactive-track-shape)}.mat-mdc-slider .mdc-slider__thumb-knob{border-radius:var(--mdc-slider-handle-shape);width:var(--mdc-slider-handle-width);height:var(--mdc-slider-handle-height);border-style:solid;border-width:calc(var(--mdc-slider-handle-height) / 2) calc(var(--mdc-slider-handle-width) / 2)}.mat-mdc-slider .mdc-slider__tick-mark--active,.mat-mdc-slider .mdc-slider__tick-mark--inactive{border-radius:var(--mdc-slider-with-tick-marks-container-shape)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb-knob{background-color:var(--mdc-slider-hover-handle-color);border-color:var(--mdc-slider-hover-handle-color)}.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:hover .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb-knob{background-color:var(--mdc-slider-focus-handle-color);border-color:var(--mdc-slider-focus-handle-color)}.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--focused .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb:not(:disabled):active .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:#fff}.mat-mdc-slider .mdc-slider__thumb--top .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb:hover .mdc-slider__thumb-knob,.mat-mdc-slider .mdc-slider__thumb--top.mdc-slider__thumb--focused .mdc-slider__thumb-knob{border-color:var(--mdc-slider-with-overlap-handle-outline-color);border-width:var(--mdc-slider-with-overlap-handle-outline-width)}.mat-mdc-slider .mdc-slider__thumb-knob{box-shadow:var(--mdc-slider-handle-elevation)}.mat-mdc-slider .mdc-slider__input{box-sizing:content-box;pointer-events:auto}.mat-mdc-slider .mdc-slider__input.mat-mdc-slider-input-no-pointer-events{pointer-events:none}.mat-mdc-slider .mdc-slider__input.mat-slider__right-input{left:auto;right:0}.mat-mdc-slider .mdc-slider__thumb,.mat-mdc-slider .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider.mdc-slider--discrete .mdc-slider__track--active_fill{transition-duration:0ms}.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__thumb,.mat-mdc-slider.mat-mdc-slider-with-animation .mdc-slider__track--active_fill{transition-duration:80ms}.mat-mdc-slider .mdc-slider__track,.mat-mdc-slider .mdc-slider__thumb{pointer-events:none}.mat-mdc-slider .mdc-slider__value-indicator-container{transform:var(--mat-slider-value-indicator-container-transform)}.mat-mdc-slider .mdc-slider__value-indicator{width:var(--mat-slider-value-indicator-width);height:var(--mat-slider-value-indicator-height);padding:var(--mat-slider-value-indicator-padding);opacity:var(--mat-slider-value-indicator-opacity);border-radius:var(--mat-slider-value-indicator-border-radius)}.mat-mdc-slider .mdc-slider__value-indicator::before{display:var(--mat-slider-value-indicator-caret-display)}.mat-mdc-slider .mdc-slider__value-indicator-text{width:var(--mat-slider-value-indicator-width);transform:var(--mat-slider-value-indicator-text-transform)}.mat-mdc-slider .mat-ripple .mat-ripple-element{background-color:var(--mat-slider-ripple-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-hover-ripple{background-color:var(--mat-slider-hover-state-layer-color)}.mat-mdc-slider .mat-ripple .mat-mdc-slider-focus-ripple,.mat-mdc-slider .mat-ripple .mat-mdc-slider-active-ripple{background-color:var(--mat-slider-focus-state-layer-color)}.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__thumb,.mat-mdc-slider._mat-animation-noopable.mdc-slider--discrete .mdc-slider__track--active_fill,.mat-mdc-slider._mat-animation-noopable .mdc-slider__value-indicator{transition:none}.mat-mdc-slider .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slider .mdc-slider__value-indicator{word-break:normal}.mat-mdc-slider .mdc-slider__value-indicator-text{text-align:center}.mdc-slider__thumb--focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_RIPPLE_GLOBAL_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _trackActive: [{\n                type: ViewChild,\n                args: ['trackActive']\n            }], _thumbs: [{\n                type: ViewChildren,\n                args: [MAT_SLIDER_VISUAL_THUMB]\n            }], _input: [{\n                type: ContentChild,\n                args: [MAT_SLIDER_THUMB]\n            }], _inputs: [{\n                type: ContentChildren,\n                args: [MAT_SLIDER_RANGE_THUMB, { descendants: false }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], discrete: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], showTickMarks: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], min: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], color: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], max: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], step: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], displayWith: [{\n                type: Input\n            }] } });\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(isRange, endInputElement, startInputElement) {\n    const startValid = !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n    const endValid = endInputElement._hostElement.hasAttribute(isRange ? 'matSliderEndThumb' : 'matSliderThumb');\n    if (!startValid || !endValid) {\n        _throwInvalidInputConfigurationError();\n    }\n}\nfunction _throwInvalidInputConfigurationError() {\n    throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_THUMB_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSliderThumb),\n    multi: true,\n};\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nconst MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSliderRangeThumb),\n    multi: true,\n};\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\nclass MatSliderThumb {\n    get value() {\n        return numberAttribute(this._hostElement.value, 0);\n    }\n    set value(value) {\n        value = isNaN(value) ? 0 : value;\n        const stringValue = value + '';\n        if (!this._hasSetInitialValue) {\n            this._initialValue = stringValue;\n            return;\n        }\n        if (this._isActive) {\n            return;\n        }\n        this._setValue(stringValue);\n    }\n    /**\n     * Handles programmatic value setting. This has been split out to\n     * allow the range thumb to override it and add additional necessary logic.\n     */\n    _setValue(value) {\n        this._hostElement.value = value;\n        this._updateThumbUIByValue();\n        this._slider._onValueChange(this);\n        this._cdr.detectChanges();\n        this._slider._cdr.markForCheck();\n    }\n    /**\n     * The current translateX in px of the slider visual thumb.\n     * @docs-private\n     */\n    get translateX() {\n        if (this._slider.min >= this._slider.max) {\n            this._translateX = this._tickMarkOffset;\n            return this._translateX;\n        }\n        if (this._translateX === undefined) {\n            this._translateX = this._calcTranslateXByValue();\n        }\n        return this._translateX;\n    }\n    set translateX(v) {\n        this._translateX = v;\n    }\n    /** @docs-private */\n    get min() {\n        return numberAttribute(this._hostElement.min, 0);\n    }\n    set min(v) {\n        this._hostElement.min = v + '';\n        this._cdr.detectChanges();\n    }\n    /** @docs-private */\n    get max() {\n        return numberAttribute(this._hostElement.max, 0);\n    }\n    set max(v) {\n        this._hostElement.max = v + '';\n        this._cdr.detectChanges();\n    }\n    get step() {\n        return numberAttribute(this._hostElement.step, 0);\n    }\n    set step(v) {\n        this._hostElement.step = v + '';\n        this._cdr.detectChanges();\n    }\n    /** @docs-private */\n    get disabled() {\n        return booleanAttribute(this._hostElement.disabled);\n    }\n    set disabled(v) {\n        this._hostElement.disabled = v;\n        this._cdr.detectChanges();\n        if (this._slider.disabled !== this.disabled) {\n            this._slider.disabled = this.disabled;\n        }\n    }\n    /** The percentage of the slider that coincides with the value. */\n    get percentage() {\n        if (this._slider.min >= this._slider.max) {\n            return this._slider._isRtl ? 1 : 0;\n        }\n        return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n    }\n    /** @docs-private */\n    get fillPercentage() {\n        if (!this._slider._cachedWidth) {\n            return this._slider._isRtl ? 1 : 0;\n        }\n        if (this._translateX === 0) {\n            return 0;\n        }\n        return this.translateX / this._slider._cachedWidth;\n    }\n    /** Used to relay updates to _isFocused to the slider visual thumbs. */\n    _setIsFocused(v) {\n        this._isFocused = v;\n    }\n    constructor(_ngZone, _elementRef, _cdr, _slider) {\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._cdr = _cdr;\n        this._slider = _slider;\n        /** Event emitted when the `value` is changed. */\n        this.valueChange = new EventEmitter();\n        /** Event emitted when the slider thumb starts being dragged. */\n        this.dragStart = new EventEmitter();\n        /** Event emitted when the slider thumb stops being dragged. */\n        this.dragEnd = new EventEmitter();\n        /**\n         * Indicates whether this thumb is the start or end thumb.\n         * @docs-private\n         */\n        this.thumbPosition = _MatThumb.END;\n        /** The aria-valuetext string representation of the input's value. */\n        this._valuetext = signal('');\n        /** The radius of a native html slider's knob. */\n        this._knobRadius = 8;\n        /** The distance in px from the start of the slider track to the first tick mark. */\n        this._tickMarkOffset = 3;\n        /** Whether user's cursor is currently in a mouse down state on the input. */\n        this._isActive = false;\n        /** Whether the input is currently focused (either by tab or after clicking). */\n        this._isFocused = false;\n        /**\n         * Whether the initial value has been set.\n         * This exists because the initial value cannot be immediately set because the min and max\n         * must first be relayed from the parent MatSlider component, which can only happen later\n         * in the component lifecycle.\n         */\n        this._hasSetInitialValue = false;\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        /**\n         * Indicates whether UI updates should be skipped.\n         *\n         * This flag is used to avoid flickering\n         * when correcting values on pointer up/down.\n         */\n        this._skipUIUpdate = false;\n        /** Callback called when the slider input has been touched. */\n        this._onTouchedFn = () => { };\n        /**\n         * Whether the NgModel has been initialized.\n         *\n         * This flag is used to ignore ghost null calls to\n         * writeValue which can break slider initialization.\n         *\n         * See https://github.com/angular/angular/issues/14988.\n         */\n        this._isControlInitialized = false;\n        this._platform = inject(Platform);\n        this._hostElement = _elementRef.nativeElement;\n        this._ngZone.runOutsideAngular(() => {\n            this._hostElement.addEventListener('pointerdown', this._onPointerDown.bind(this));\n            this._hostElement.addEventListener('pointermove', this._onPointerMove.bind(this));\n            this._hostElement.addEventListener('pointerup', this._onPointerUp.bind(this));\n        });\n    }\n    ngOnDestroy() {\n        this._hostElement.removeEventListener('pointerdown', this._onPointerDown);\n        this._hostElement.removeEventListener('pointermove', this._onPointerMove);\n        this._hostElement.removeEventListener('pointerup', this._onPointerUp);\n        this._destroyed.next();\n        this._destroyed.complete();\n        this.dragStart.complete();\n        this.dragEnd.complete();\n    }\n    /** @docs-private */\n    initProps() {\n        this._updateWidthInactive();\n        // If this or the parent slider is disabled, just make everything disabled.\n        if (this.disabled !== this._slider.disabled) {\n            // The MatSlider setter for disabled will relay this and disable both inputs.\n            this._slider.disabled = true;\n        }\n        this.step = this._slider.step;\n        this.min = this._slider.min;\n        this.max = this._slider.max;\n        this._initValue();\n    }\n    /** @docs-private */\n    initUI() {\n        this._updateThumbUIByValue();\n    }\n    _initValue() {\n        this._hasSetInitialValue = true;\n        if (this._initialValue === undefined) {\n            this.value = this._getDefaultValue();\n        }\n        else {\n            this._hostElement.value = this._initialValue;\n            this._updateThumbUIByValue();\n            this._slider._onValueChange(this);\n            this._cdr.detectChanges();\n        }\n    }\n    _getDefaultValue() {\n        return this.min;\n    }\n    _onBlur() {\n        this._setIsFocused(false);\n        this._onTouchedFn();\n    }\n    _onFocus() {\n        this._slider._setTransition(false);\n        this._slider._updateTrackUI(this);\n        this._setIsFocused(true);\n    }\n    _onChange() {\n        this.valueChange.emit(this.value);\n        // only used to handle the edge case where user\n        // mousedown on the slider then uses arrow keys.\n        if (this._isActive) {\n            this._updateThumbUIByValue({ withAnimation: true });\n        }\n    }\n    _onInput() {\n        this._onChangeFn?.(this.value);\n        // handles arrowing and updating the value when\n        // a step is defined.\n        if (this._slider.step || !this._isActive) {\n            this._updateThumbUIByValue({ withAnimation: true });\n        }\n        this._slider._onValueChange(this);\n    }\n    _onNgControlValueChange() {\n        // only used to handle when the value change\n        // originates outside of the slider.\n        if (!this._isActive || !this._isFocused) {\n            this._slider._onValueChange(this);\n            this._updateThumbUIByValue();\n        }\n        this._slider.disabled = this._formControl.disabled;\n    }\n    _onPointerDown(event) {\n        if (this.disabled || event.button !== 0) {\n            return;\n        }\n        // On IOS, dragging only works if the pointer down happens on the\n        // slider thumb and the slider does not receive focus from pointer events.\n        if (this._platform.IOS) {\n            const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(event, this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect());\n            this._isActive = isCursorOnSliderThumb;\n            this._updateWidthActive();\n            this._slider._updateDimensions();\n            return;\n        }\n        this._isActive = true;\n        this._setIsFocused(true);\n        this._updateWidthActive();\n        this._slider._updateDimensions();\n        // Does nothing if a step is defined because we\n        // want the value to snap to the values on input.\n        if (!this._slider.step) {\n            this._updateThumbUIByPointerEvent(event, { withAnimation: true });\n        }\n        if (!this.disabled) {\n            this._handleValueCorrection(event);\n            this.dragStart.emit({ source: this, parent: this._slider, value: this.value });\n        }\n    }\n    /**\n     * Corrects the value of the slider on pointer up/down.\n     *\n     * Called on pointer down and up because the value is set based\n     * on the inactive width instead of the active width.\n     */\n    _handleValueCorrection(event) {\n        // Don't update the UI with the current value! The value on pointerdown\n        // and pointerup is calculated in the split second before the input(s)\n        // resize. See _updateWidthInactive() and _updateWidthActive() for more\n        // details.\n        this._skipUIUpdate = true;\n        // Note that this function gets triggered before the actual value of the\n        // slider is updated. This means if we were to set the value here, it\n        // would immediately be overwritten. Using setTimeout ensures the setting\n        // of the value happens after the value has been updated by the\n        // pointerdown event.\n        setTimeout(() => {\n            this._skipUIUpdate = false;\n            this._fixValue(event);\n        }, 0);\n    }\n    /** Corrects the value of the slider based on the pointer event's position. */\n    _fixValue(event) {\n        const xPos = event.clientX - this._slider._cachedLeft;\n        const width = this._slider._cachedWidth;\n        const step = this._slider.step === 0 ? 1 : this._slider.step;\n        const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n        const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n        // To ensure the percentage is rounded to the necessary number of decimals.\n        const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n        const impreciseValue = fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n        const value = Math.round(impreciseValue / step) * step;\n        const prevValue = this.value;\n        if (value === prevValue) {\n            // Because we prevented UI updates, if it turns out that the race\n            // condition didn't happen and the value is already correct, we\n            // have to apply the ui updates now.\n            this._slider._onValueChange(this);\n            this._slider.step > 0\n                ? this._updateThumbUIByValue()\n                : this._updateThumbUIByPointerEvent(event, { withAnimation: this._slider._hasAnimation });\n            return;\n        }\n        this.value = value;\n        this.valueChange.emit(this.value);\n        this._onChangeFn?.(this.value);\n        this._slider._onValueChange(this);\n        this._slider.step > 0\n            ? this._updateThumbUIByValue()\n            : this._updateThumbUIByPointerEvent(event, { withAnimation: this._slider._hasAnimation });\n    }\n    _onPointerMove(event) {\n        // Again, does nothing if a step is defined because\n        // we want the value to snap to the values on input.\n        if (!this._slider.step && this._isActive) {\n            this._updateThumbUIByPointerEvent(event);\n        }\n    }\n    _onPointerUp() {\n        if (this._isActive) {\n            this._isActive = false;\n            if (this._platform.SAFARI) {\n                this._setIsFocused(false);\n            }\n            this.dragEnd.emit({ source: this, parent: this._slider, value: this.value });\n            // This setTimeout is to prevent the pointerup from triggering a value\n            // change on the input based on the inactive width. It's not clear why\n            // but for some reason on IOS this race condition is even more common so\n            // the timeout needs to be increased.\n            setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n        }\n    }\n    _clamp(v) {\n        const min = this._tickMarkOffset;\n        const max = this._slider._cachedWidth - this._tickMarkOffset;\n        return Math.max(Math.min(v, max), min);\n    }\n    _calcTranslateXByValue() {\n        if (this._slider._isRtl) {\n            return ((1 - this.percentage) * (this._slider._cachedWidth - this._tickMarkOffset * 2) +\n                this._tickMarkOffset);\n        }\n        return (this.percentage * (this._slider._cachedWidth - this._tickMarkOffset * 2) +\n            this._tickMarkOffset);\n    }\n    _calcTranslateXByPointerEvent(event) {\n        return event.clientX - this._slider._cachedLeft;\n    }\n    /**\n     * Used to set the slider width to the correct\n     * dimensions while the user is dragging.\n     */\n    _updateWidthActive() { }\n    /**\n     * Sets the slider input to disproportionate dimensions to allow for touch\n     * events to be captured on touch devices.\n     */\n    _updateWidthInactive() {\n        this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n        this._hostElement.style.width = `calc(100% + ${this._slider._inputPadding - this._tickMarkOffset * 2}px)`;\n        this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n    }\n    _updateThumbUIByValue(options) {\n        this.translateX = this._clamp(this._calcTranslateXByValue());\n        this._updateThumbUI(options);\n    }\n    _updateThumbUIByPointerEvent(event, options) {\n        this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n        this._updateThumbUI(options);\n    }\n    _updateThumbUI(options) {\n        this._slider._setTransition(!!options?.withAnimation);\n        this._slider._onTranslateXChange(this);\n    }\n    /**\n     * Sets the input's value.\n     * @param value The new value of the input\n     * @docs-private\n     */\n    writeValue(value) {\n        if (this._isControlInitialized || value !== null) {\n            this.value = value;\n        }\n    }\n    /**\n     * Registers a callback to be invoked when the input's value changes from user input.\n     * @param fn The callback to register\n     * @docs-private\n     */\n    registerOnChange(fn) {\n        this._onChangeFn = fn;\n        this._isControlInitialized = true;\n    }\n    /**\n     * Registers a callback to be invoked when the input is blurred by the user.\n     * @param fn The callback to register\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n        this._onTouchedFn = fn;\n    }\n    /**\n     * Sets the disabled state of the slider.\n     * @param isDisabled The new disabled state\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    focus() {\n        this._hostElement.focus();\n    }\n    blur() {\n        this._hostElement.blur();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderThumb, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: MAT_SLIDER }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatSliderThumb, isStandalone: true, selector: \"input[matSliderThumb]\", inputs: { value: [\"value\", \"value\", numberAttribute] }, outputs: { valueChange: \"valueChange\", dragStart: \"dragStart\", dragEnd: \"dragEnd\" }, host: { attributes: { \"type\": \"range\" }, listeners: { \"change\": \"_onChange()\", \"input\": \"_onInput()\", \"blur\": \"_onBlur()\", \"focus\": \"_onFocus()\" }, properties: { \"attr.aria-valuetext\": \"_valuetext()\" }, classAttribute: \"mdc-slider__input\" }, providers: [\n            MAT_SLIDER_THUMB_VALUE_ACCESSOR,\n            { provide: MAT_SLIDER_THUMB, useExisting: MatSliderThumb },\n        ], exportAs: [\"matSliderThumb\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderThumb, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matSliderThumb]',\n                    exportAs: 'matSliderThumb',\n                    host: {\n                        'class': 'mdc-slider__input',\n                        'type': 'range',\n                        '[attr.aria-valuetext]': '_valuetext()',\n                        '(change)': '_onChange()',\n                        '(input)': '_onInput()',\n                        // TODO(wagnermaciel): Consider using a global event listener instead.\n                        // Reason: I have found a semi-consistent way to mouse up without triggering this event.\n                        '(blur)': '_onBlur()',\n                        '(focus)': '_onFocus()',\n                    },\n                    providers: [\n                        MAT_SLIDER_THUMB_VALUE_ACCESSOR,\n                        { provide: MAT_SLIDER_THUMB, useExisting: MatSliderThumb },\n                    ],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDER]\n                }] }], propDecorators: { value: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], valueChange: [{\n                type: Output\n            }], dragStart: [{\n                type: Output\n            }], dragEnd: [{\n                type: Output\n            }] } });\nclass MatSliderRangeThumb extends MatSliderThumb {\n    /** @docs-private */\n    getSibling() {\n        if (!this._sibling) {\n            this._sibling = this._slider._getInput(this._isEndThumb ? _MatThumb.START : _MatThumb.END);\n        }\n        return this._sibling;\n    }\n    /**\n     * Returns the minimum translateX position allowed for this slider input's visual thumb.\n     * @docs-private\n     */\n    getMinPos() {\n        const sibling = this.getSibling();\n        if (!this._isLeftThumb && sibling) {\n            return sibling.translateX;\n        }\n        return this._tickMarkOffset;\n    }\n    /**\n     * Returns the maximum translateX position allowed for this slider input's visual thumb.\n     * @docs-private\n     */\n    getMaxPos() {\n        const sibling = this.getSibling();\n        if (this._isLeftThumb && sibling) {\n            return sibling.translateX;\n        }\n        return this._slider._cachedWidth - this._tickMarkOffset;\n    }\n    _setIsLeftThumb() {\n        this._isLeftThumb =\n            (this._isEndThumb && this._slider._isRtl) || (!this._isEndThumb && !this._slider._isRtl);\n    }\n    constructor(_ngZone, _slider, _elementRef, _cdr) {\n        super(_ngZone, _elementRef, _cdr, _slider);\n        this._cdr = _cdr;\n        this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n        this._setIsLeftThumb();\n        this.thumbPosition = this._isEndThumb ? _MatThumb.END : _MatThumb.START;\n    }\n    _getDefaultValue() {\n        return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n    }\n    _onInput() {\n        super._onInput();\n        this._updateSibling();\n        if (!this._isActive) {\n            this._updateWidthInactive();\n        }\n    }\n    _onNgControlValueChange() {\n        super._onNgControlValueChange();\n        this.getSibling()?._updateMinMax();\n    }\n    _onPointerDown(event) {\n        if (this.disabled || event.button !== 0) {\n            return;\n        }\n        if (this._sibling) {\n            this._sibling._updateWidthActive();\n            this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n        }\n        super._onPointerDown(event);\n    }\n    _onPointerUp() {\n        super._onPointerUp();\n        if (this._sibling) {\n            setTimeout(() => {\n                this._sibling._updateWidthInactive();\n                this._sibling._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n            });\n        }\n    }\n    _onPointerMove(event) {\n        super._onPointerMove(event);\n        if (!this._slider.step && this._isActive) {\n            this._updateSibling();\n        }\n    }\n    _fixValue(event) {\n        super._fixValue(event);\n        this._sibling?._updateMinMax();\n    }\n    _clamp(v) {\n        return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n    }\n    _updateMinMax() {\n        const sibling = this.getSibling();\n        if (!sibling) {\n            return;\n        }\n        if (this._isEndThumb) {\n            this.min = Math.max(this._slider.min, sibling.value);\n            this.max = this._slider.max;\n        }\n        else {\n            this.min = this._slider.min;\n            this.max = Math.min(this._slider.max, sibling.value);\n        }\n    }\n    _updateWidthActive() {\n        const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n        const maxWidth = this._slider._cachedWidth + this._slider._inputPadding - minWidth - this._tickMarkOffset * 2;\n        const percentage = this._slider.min < this._slider.max\n            ? (this.max - this.min) / (this._slider.max - this._slider.min)\n            : 1;\n        const width = maxWidth * percentage + minWidth;\n        this._hostElement.style.width = `${width}px`;\n        this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    }\n    _updateWidthInactive() {\n        const sibling = this.getSibling();\n        if (!sibling) {\n            return;\n        }\n        const maxWidth = this._slider._cachedWidth - this._tickMarkOffset * 2;\n        const midValue = this._isEndThumb\n            ? this.value - (this.value - sibling.value) / 2\n            : this.value + (sibling.value - this.value) / 2;\n        const _percentage = this._isEndThumb\n            ? (this.max - midValue) / (this._slider.max - this._slider.min)\n            : (midValue - this.min) / (this._slider.max - this._slider.min);\n        const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n        // Extend the native input width by the radius of the ripple\n        let ripplePadding = this._slider._rippleRadius;\n        // If one of the inputs is maximally sized (the value of both thumbs is\n        // equal to the min or max), make that input take up all of the width and\n        // make the other unselectable.\n        if (percentage === 1) {\n            ripplePadding = 48;\n        }\n        else if (percentage === 0) {\n            ripplePadding = 0;\n        }\n        const width = maxWidth * percentage + ripplePadding;\n        this._hostElement.style.width = `${width}px`;\n        this._hostElement.style.padding = '0px';\n        if (this._isLeftThumb) {\n            this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n            this._hostElement.style.right = 'auto';\n        }\n        else {\n            this._hostElement.style.left = 'auto';\n            this._hostElement.style.right = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n        }\n    }\n    _updateStaticStyles() {\n        this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n    }\n    _updateSibling() {\n        const sibling = this.getSibling();\n        if (!sibling) {\n            return;\n        }\n        sibling._updateMinMax();\n        if (this._isActive) {\n            sibling._updateWidthActive();\n        }\n        else {\n            sibling._updateWidthInactive();\n        }\n    }\n    /**\n     * Sets the input's value.\n     * @param value The new value of the input\n     * @docs-private\n     */\n    writeValue(value) {\n        if (this._isControlInitialized || value !== null) {\n            this.value = value;\n            this._updateWidthInactive();\n            this._updateSibling();\n        }\n    }\n    _setValue(value) {\n        super._setValue(value);\n        this._updateWidthInactive();\n        this._updateSibling();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderRangeThumb, deps: [{ token: i0.NgZone }, { token: MAT_SLIDER }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSliderRangeThumb, isStandalone: true, selector: \"input[matSliderStartThumb], input[matSliderEndThumb]\", providers: [\n            MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR,\n            { provide: MAT_SLIDER_RANGE_THUMB, useExisting: MatSliderRangeThumb },\n        ], exportAs: [\"matSliderRangeThumb\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderRangeThumb, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matSliderStartThumb], input[matSliderEndThumb]',\n                    exportAs: 'matSliderRangeThumb',\n                    providers: [\n                        MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR,\n                        { provide: MAT_SLIDER_RANGE_THUMB, useExisting: MatSliderRangeThumb },\n                    ],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDER]\n                }] }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }] });\n\nclass MatSliderModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderModule, imports: [MatCommonModule,\n            MatRippleModule,\n            MatSlider,\n            MatSliderThumb,\n            MatSliderRangeThumb,\n            MatSliderVisualThumb], exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderModule, imports: [MatCommonModule,\n            MatRippleModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSliderModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        MatRippleModule,\n                        MatSlider,\n                        MatSliderThumb,\n                        MatSliderRangeThumb,\n                        MatSliderVisualThumb,\n                    ],\n                    exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatSlider, MatSliderChange, MatSliderModule, MatSliderRangeThumb, MatSliderThumb, MatSliderVisualThumb };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,QAAQ,QAAQ,uBAAuB;AAChD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,UAAU,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACvT,SAASC,WAAW,EAAEC,SAAS,EAAEC,yBAAyB,EAAEC,eAAe,EAAEC,eAAe,QAAQ,wBAAwB;AAC5H,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,MAAM;;AAE9B;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,4CAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA+OoGhC,EAAE,CAAAkC,cAAA,eAC0vB,CAAC,YAAgD,CAAC,aAAwD,CAAC;IADv2BlC,EAAE,CAAAmC,MAAA,EAC03B,CAAC;IAD73BnC,EAAE,CAAAoC,YAAA,CACi4B,CAAC,CAAW,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,MAAA,GAD15BrC,EAAE,CAAAsC,aAAA;IAAFtC,EAAE,CAAAuC,SAAA,EAC03B,CAAC;IAD73BvC,EAAE,CAAAwC,iBAAA,CAAAH,MAAA,CAAAI,kBAC03B,CAAC;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,qDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAD73BhC,EAAE,CAAA6C,SAAA,SA8pBy4D,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAc,WAAA,GAAAb,GAAA,CAAAc,SAAA;IAAA,MAAAC,IAAA,GAAAf,GAAA,CAAAgB,MAAA;IAAA,MAAAC,MAAA,GA9pB54DlD,EAAE,CAAAsC,aAAA;IAAFtC,EAAE,CAAAmD,UAAA,CAAAL,WAAA,4EA8pBq0D,CAAC;IA9pBx0D9C,EAAE,CAAAoD,WAAA,cAAAF,MAAA,CAAAG,sBAAA,CAAAL,IAAA,CA8pBk4D,CAAC;EAAA;AAAA;AAAA,SAAAM,+CAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9pBr4DhC,EAAE,CAAAuD,gBAAA,IAAAX,oDAAA,kBAAF5C,EAAE,CAAAwD,sBA8pBo5D,CAAC;EAAA;EAAA,IAAAxB,EAAA;IAAA,MAAAkB,MAAA,GA9pBv5DlD,EAAE,CAAAsC,aAAA;IAAFtC,EAAE,CAAAyD,UAAA,CAAAP,MAAA,CAAAQ,UA8pBo5D,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9pBv5DhC,EAAE,CAAAkC,cAAA,eA8pBymD,CAAC;IA9pB5mDlC,EAAE,CAAA4D,UAAA,IAAAN,8CAAA,MA8pBqoD,CAAC;IA9pBxoDtD,EAAE,CAAAoC,YAAA,CA8pBy6D,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAkB,MAAA,GA9pB56DlD,EAAE,CAAAsC,aAAA;IAAFtC,EAAE,CAAAuC,SAAA,EA8pB65D,CAAC;IA9pBh6DvC,EAAE,CAAA6D,aAAA,IAAAX,MAAA,CAAAY,YAAA,SA8pB65D,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9pBh6DhC,EAAE,CAAA6C,SAAA,gCA8pBooE,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAkB,MAAA,GA9pBvoElD,EAAE,CAAAsC,aAAA;IAAFtC,EAAE,CAAAgE,UAAA,aAAAd,MAAA,CAAAe,QA8pBohE,CAAC,mBAA0B,CAAC,uBAAAf,MAAA,CAAAgB,uBAAqD,CAAC;EAAA;AAAA;AAz4B5sE,IAAIC,SAAS;AACb,CAAC,UAAUA,SAAS,EAAE;EAClBA,SAAS,CAACA,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO;EAC3CA,SAAS,CAACA,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;AAC3C,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC;AACA,IAAIC,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrBA,YAAY,CAACA,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;EACnDA,YAAY,CAACA,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,GAAG,UAAU;AAC3D,CAAC,EAAEA,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,IAAIpE,cAAc,CAAC,YAAY,CAAC;AACnD;AACA;AACA;AACA;AACA;AACA,MAAMqE,gBAAgB,GAAG,IAAIrE,cAAc,CAAC,iBAAiB,CAAC;AAC9D;AACA;AACA;AACA;AACA;AACA,MAAMsE,sBAAsB,GAAG,IAAItE,cAAc,CAAC,sBAAsB,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA,MAAMuE,uBAAuB,GAAG,IAAIvE,cAAc,CAAC,uBAAuB,CAAC;AAC3E;AACA;AACA;AACA;AACA;AACA,MAAMwE,eAAe,CAAC;;AAGtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CAACC,IAAI,EAAEC,OAAO,EAAEC,WAAW,EAAEC,OAAO,EAAE;IAC7C,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACC,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACC,SAAS,GAAGjF,MAAM,CAACH,QAAQ,CAAC;IACjC,IAAI,CAACqF,cAAc,GAAIC,KAAK,IAAK;MAC7B,IAAI,IAAI,CAACC,YAAY,CAACC,UAAU,EAAE;QAC9B;MACJ;MACA,MAAMC,IAAI,GAAG,IAAI,CAACC,YAAY,CAACC,qBAAqB,CAAC,CAAC;MACtD,MAAMC,SAAS,GAAG,IAAI,CAACZ,OAAO,CAACa,sBAAsB,CAACP,KAAK,EAAEG,IAAI,CAAC;MAClE,IAAI,CAACR,UAAU,GAAGW,SAAS;MAC3B,IAAIA,SAAS,EAAE;QACX,IAAI,CAACE,gBAAgB,CAAC,CAAC;MAC3B,CAAC,MACI;QACD,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,eAAe,CAAC;MAC1C;IACJ,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,MAAM;MACvB,IAAI,CAAChB,UAAU,GAAG,KAAK;MACvB,IAAI,CAACc,WAAW,CAAC,IAAI,CAACC,eAAe,CAAC;IAC1C,CAAC;IACD,IAAI,CAACE,QAAQ,GAAG,MAAM;MAClB;MACA;MACA,IAAI,CAACH,WAAW,CAAC,IAAI,CAACC,eAAe,CAAC;MACtC,IAAI,CAACG,gBAAgB,CAAC,CAAC;MACvB,IAAI,CAACT,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACjE,CAAC;IACD,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB;MACA,IAAI,CAAC,IAAI,CAACpB,SAAS,EAAE;QACjB,IAAI,CAACa,WAAW,CAAC,IAAI,CAACQ,eAAe,CAAC;MAC1C;MACA;MACA,IAAI,IAAI,CAACtB,UAAU,EAAE;QACjB,IAAI,CAACa,gBAAgB,CAAC,CAAC;MAC3B;MACA,IAAI,CAACJ,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,4BAA4B,CAAC;IACpE,CAAC;IACD,IAAI,CAACC,YAAY,GAAInB,KAAK,IAAK;MAC3B,IAAIA,KAAK,CAACoB,MAAM,KAAK,CAAC,EAAE;QACpB;MACJ;MACA,IAAI,CAACxB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACyB,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAACC,UAAU,GAAG,MAAM;MACpB,IAAI,CAAC1B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACa,WAAW,CAAC,IAAI,CAACc,gBAAgB,CAAC;MACvC;MACA,IAAI,CAAC,IAAI,CAACtB,YAAY,CAACC,UAAU,EAAE;QAC/B,IAAI,CAACO,WAAW,CAAC,IAAI,CAACQ,eAAe,CAAC;MAC1C;MACA;MACA;MACA,IAAI,IAAI,CAACnB,SAAS,CAAC0B,MAAM,EAAE;QACvB,IAAI,CAAChB,gBAAgB,CAAC,CAAC;MAC3B;IACJ,CAAC;IACD,IAAI,CAACJ,YAAY,GAAGX,WAAW,CAACgC,aAAa;EACjD;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,OAAO,CAACC,MAAM,GAAG,EAAE;IACxB,IAAI,CAAC3B,YAAY,GAAG,IAAI,CAACP,OAAO,CAACmC,SAAS,CAAC,IAAI,CAACC,aAAa,CAAC;IAC9D,IAAI,CAACC,cAAc,GAAG,IAAI,CAAC9B,YAAY,CAACG,YAAY;IACpD,MAAM4B,KAAK,GAAG,IAAI,CAACD,cAAc;IACjC;IACA;IACA,IAAI,CAACvC,OAAO,CAACyC,iBAAiB,CAAC,MAAM;MACjCD,KAAK,CAACE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACnC,cAAc,CAAC;MAC1DiC,KAAK,CAACE,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACf,YAAY,CAAC;MACxDa,KAAK,CAACE,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACZ,UAAU,CAAC;MACpDU,KAAK,CAACE,gBAAgB,CAAC,cAAc,EAAE,IAAI,CAACvB,aAAa,CAAC;MAC1DqB,KAAK,CAACE,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACtB,QAAQ,CAAC;MAC9CoB,KAAK,CAACE,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAClB,OAAO,CAAC;IAChD,CAAC,CAAC;EACN;EACAmB,WAAWA,CAAA,EAAG;IACV,MAAMH,KAAK,GAAG,IAAI,CAACD,cAAc;IACjC,IAAIC,KAAK,EAAE;MACPA,KAAK,CAACI,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACrC,cAAc,CAAC;MAC7DiC,KAAK,CAACI,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACjB,YAAY,CAAC;MAC3Da,KAAK,CAACI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACd,UAAU,CAAC;MACvDU,KAAK,CAACI,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAACzB,aAAa,CAAC;MAC7DqB,KAAK,CAACI,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACxB,QAAQ,CAAC;MACjDoB,KAAK,CAACI,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAACpB,OAAO,CAAC;IACnD;EACJ;EACA;EACAR,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAC6B,gBAAgB,CAAC,IAAI,CAAC3B,eAAe,CAAC,EAAE;MAC9C,IAAI,CAACA,eAAe,GAAG,IAAI,CAAC4B,WAAW,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC,CAAC;MAC9E,IAAI,CAAC9B,eAAe,EAAE+B,OAAO,CAAC3B,SAAS,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC9E;EACJ;EACA;EACAF,gBAAgBA,CAAA,EAAG;IACf;IACA,IAAI,CAAC,IAAI,CAACwB,gBAAgB,CAAC,IAAI,CAACpB,eAAe,CAAC,EAAE;MAC9C,IAAI,CAACA,eAAe,GAAG,IAAI,CAACqB,WAAW,CAAC;QAAEC,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC,EAAE,IAAI,CAAC;MACpF,IAAI,CAACvB,eAAe,EAAEwB,OAAO,CAAC3B,SAAS,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC9E;EACJ;EACA;EACAM,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACgB,gBAAgB,CAAC,IAAI,CAACd,gBAAgB,CAAC,EAAE;MAC/C,IAAI,CAACA,gBAAgB,GAAG,IAAI,CAACe,WAAW,CAAC;QAAEC,aAAa,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAI,CAAC,CAAC;MACnF,IAAI,CAACjB,gBAAgB,EAAEkB,OAAO,CAAC3B,SAAS,CAACC,GAAG,CAAC,8BAA8B,CAAC;IAChF;EACJ;EACA;EACAsB,gBAAgBA,CAACK,SAAS,EAAE;IACxB,OAAOA,SAAS,EAAEC,KAAK,KAAK1G,WAAW,CAAC2G,SAAS,IAAIF,SAAS,EAAEC,KAAK,KAAK1G,WAAW,CAAC4G,OAAO;EACjG;EACA;EACAP,WAAWA,CAACQ,SAAS,EAAEC,wBAAwB,EAAE;IAC7C,IAAI,IAAI,CAACrD,OAAO,CAACsD,QAAQ,EAAE;MACvB;IACJ;IACA,IAAI,CAACC,mBAAmB,CAAC,CAAC;IAC1B,IAAI,IAAI,CAACvD,OAAO,CAACwD,QAAQ,EAAE;MACvB,MAAMC,OAAO,GAAG,IAAI,CAACzD,OAAO,CAAC0D,SAAS,CAAC,IAAI,CAACtB,aAAa,KAAKhD,SAAS,CAACuE,KAAK,GAAGvE,SAAS,CAACwE,GAAG,GAAGxE,SAAS,CAACuE,KAAK,CAAC;MAChHF,OAAO,CAACF,mBAAmB,CAAC,CAAC;IACjC;IACA,IAAI,IAAI,CAACvD,OAAO,CAAC6D,oBAAoB,EAAEP,QAAQ,IAAI,CAACD,wBAAwB,EAAE;MAC1E;IACJ;IACA,OAAO,IAAI,CAACpB,OAAO,CAAC6B,MAAM,CAAC;MACvBV,SAAS,EAAE,IAAI,CAACpD,OAAO,CAAC+D,eAAe,GAAG;QAAElB,aAAa,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAE,CAAC,GAAGM,SAAS;MAC3FY,QAAQ,EAAE,IAAI;MACdC,UAAU,EAAE;IAChB,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIlD,WAAWA,CAACiC,SAAS,EAAE;IACnBA,SAAS,EAAEkB,OAAO,CAAC,CAAC;IACpB,IAAI,IAAI,CAACC,mBAAmB,CAAC,CAAC,EAAE;MAC5B;IACJ;IACA,IAAI,CAAC,IAAI,CAACnE,OAAO,CAACwD,QAAQ,EAAE;MACxB,IAAI,CAACY,mBAAmB,CAAC,CAAC;IAC9B;IACA,MAAMX,OAAO,GAAG,IAAI,CAACY,WAAW,CAAC,CAAC;IAClC,IAAI,CAACZ,OAAO,CAACU,mBAAmB,CAAC,CAAC,EAAE;MAChC,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1BX,OAAO,CAACW,mBAAmB,CAAC,CAAC;IACjC;EACJ;EACA;EACAb,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC7C,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,mCAAmC,CAAC;EACxE;EACA;EACA+C,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC1D,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,mCAAmC,CAAC;EAC3E;EACA6C,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACrE,OAAO,CAAC0D,SAAS,CAAC,IAAI,CAACtB,aAAa,KAAKhD,SAAS,CAACuE,KAAK,GAAGvE,SAAS,CAACwE,GAAG,GAAGxE,SAAS,CAACuE,KAAK,CAAC;EAC3G;EACA;EACAW,2BAA2BA,CAAA,EAAG;IAC1B,OAAO,IAAI,CAACC,wBAAwB,EAAExC,aAAa;EACvD;EACA;EACAyC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK,CAAC1C,aAAa;EACnC;EACAoC,mBAAmBA,CAAA,EAAG;IAClB,OAAQ,IAAI,CAACxB,gBAAgB,CAAC,IAAI,CAAC3B,eAAe,CAAC,IAC/C,IAAI,CAAC2B,gBAAgB,CAAC,IAAI,CAACpB,eAAe,CAAC,IAC3C,IAAI,CAACoB,gBAAgB,CAAC,IAAI,CAACd,gBAAgB,CAAC;EACpD;EACA;IAAS,IAAI,CAAC6C,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFjF,oBAAoB,EAA9B1E,EAAE,CAAA4J,iBAAA,CAA8C5J,EAAE,CAAC6J,iBAAiB,GAApE7J,EAAE,CAAA4J,iBAAA,CAA+E5J,EAAE,CAAC8J,MAAM,GAA1F9J,EAAE,CAAA4J,iBAAA,CAAqG5J,EAAE,CAAC+J,UAAU,GAApH/J,EAAE,CAAA4J,iBAAA,CAA+HvF,UAAU;IAAA,CAA4C;EAAE;EACzR;IAAS,IAAI,CAAC2F,IAAI,kBAD8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EACJxF,oBAAoB;MAAAyF,SAAA;MAAAC,SAAA,WAAAC,2BAAArI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADlBhC,EAAE,CAAAsK,WAAA,CACyZ/I,SAAS;UADpavB,EAAE,CAAAsK,WAAA,CAAAzI,GAAA;UAAF7B,EAAE,CAAAsK,WAAA,CAAAxI,GAAA;QAAA;QAAA,IAAAE,EAAA;UAAA,IAAAuI,EAAA;UAAFvK,EAAE,CAAAwK,cAAA,CAAAD,EAAA,GAAFvK,EAAE,CAAAyK,WAAA,QAAAxI,GAAA,CAAA+E,OAAA,GAAAuD,EAAA,CAAAG,KAAA;UAAF1K,EAAE,CAAAwK,cAAA,CAAAD,EAAA,GAAFvK,EAAE,CAAAyK,WAAA,QAAAxI,GAAA,CAAAuH,KAAA,GAAAe,EAAA,CAAAG,KAAA;UAAF1K,EAAE,CAAAwK,cAAA,CAAAD,EAAA,GAAFvK,EAAE,CAAAyK,WAAA,QAAAxI,GAAA,CAAAqH,wBAAA,GAAAiB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAC,MAAA;QAAA3G,QAAA;QAAAkD,aAAA;QAAA1E,kBAAA;MAAA;MAAAoI,UAAA;MAAAC,QAAA,GAAF9K,EAAE,CAAA+K,kBAAA,CAC6Q,CAAC;QAAEC,OAAO,EAAExG,uBAAuB;QAAEyG,WAAW,EAAEvG;MAAqB,CAAC,CAAC,GADxV1E,EAAE,CAAAkL,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAvJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAA4D,UAAA,IAAA7B,2CAAA,gBACwqB,CAAC;UAD3qB/B,EAAE,CAAA6C,SAAA,eAC88B,CAAC,YAAqF,CAAC;QAAA;QAAA,IAAAb,EAAA;UADviChC,EAAE,CAAA6D,aAAA,IAAA5B,GAAA,CAAAgC,QAAA,SAC05B,CAAC;UAD75BjE,EAAE,CAAAuC,SAAA,EAC6hC,CAAC;UADhiCvC,EAAE,CAAAgE,UAAA,0BAC6hC,CAAC;QAAA;MAAA;MAAAwH,YAAA,GAA6WjK,SAAS;MAAAkK,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA6T;EAAE;AACzzD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG5L,EAAE,CAAA6L,iBAAA,CAGXnH,oBAAoB,EAAc,CAAC;IAClHwF,IAAI,EAAE/J,SAAS;IACf2L,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAEC,IAAI,EAAE;QACxC,OAAO,EAAE;MACb,CAAC;MAAEL,eAAe,EAAEvL,uBAAuB,CAAC6L,MAAM;MAAEP,aAAa,EAAErL,iBAAiB,CAAC6L,IAAI;MAAEC,SAAS,EAAE,CAAC;QAAEnB,OAAO,EAAExG,uBAAuB;QAAEyG,WAAW,EAAEvG;MAAqB,CAAC,CAAC;MAAEmG,UAAU,EAAE,IAAI;MAAEuB,OAAO,EAAE,CAAC7K,SAAS,CAAC;MAAE+J,QAAQ,EAAE,gZAAgZ;MAAEG,MAAM,EAAE,CAAC,2SAA2S;IAAE,CAAC;EACx7B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvB,IAAI,EAAElK,EAAE,CAAC6J;EAAkB,CAAC,EAAE;IAAEK,IAAI,EAAElK,EAAE,CAAC8J;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAElK,EAAE,CAAC+J;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7HpC,IAAI,EAAE5J,MAAM;MACZwL,IAAI,EAAE,CAACzH,UAAU;IACrB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEJ,QAAQ,EAAE,CAAC;MACpCiG,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAE4G,aAAa,EAAE,CAAC;MAChB+C,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAEkC,kBAAkB,EAAE,CAAC;MACrByH,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAEyG,OAAO,EAAE,CAAC;MACVkD,IAAI,EAAE1J,SAAS;MACfsL,IAAI,EAAE,CAACvK,SAAS;IACpB,CAAC,CAAC;IAAEiI,KAAK,EAAE,CAAC;MACRU,IAAI,EAAE1J,SAAS;MACfsL,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC,CAAC;IAAExC,wBAAwB,EAAE,CAAC;MAC3BY,IAAI,EAAE1J,SAAS;MACfsL,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,SAAS,CAAC;EACZ;EACA,IAAIlE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACmE,SAAS;EACzB;EACA,IAAInE,QAAQA,CAACoE,CAAC,EAAE;IACZ,IAAI,CAACD,SAAS,GAAGC,CAAC;IAClB,MAAMC,QAAQ,GAAG,IAAI,CAACxF,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC9C,MAAMgE,UAAU,GAAG,IAAI,CAACzF,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAClD,IAAIgE,QAAQ,EAAE;MACVA,QAAQ,CAACrE,QAAQ,GAAG,IAAI,CAACmE,SAAS;IACtC;IACA,IAAIG,UAAU,EAAE;MACZA,UAAU,CAACtE,QAAQ,GAAG,IAAI,CAACmE,SAAS;IACxC;EACJ;EACA;EACA,IAAIvI,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC2I,SAAS;EACzB;EACA,IAAI3I,QAAQA,CAACwI,CAAC,EAAE;IACZ,IAAI,CAACG,SAAS,GAAGH,CAAC;IAClB,IAAI,CAACI,wBAAwB,CAAC,CAAC;EACnC;EACA;EACA,IAAIC,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,IAAI;EACpB;EACA,IAAID,GAAGA,CAACL,CAAC,EAAE;IACP,MAAMK,GAAG,GAAGE,KAAK,CAACP,CAAC,CAAC,GAAG,IAAI,CAACM,IAAI,GAAGN,CAAC;IACpC,IAAI,IAAI,CAACM,IAAI,KAAKD,GAAG,EAAE;MACnB,IAAI,CAACG,UAAU,CAACH,GAAG,CAAC;IACxB;EACJ;EACAG,UAAUA,CAACH,GAAG,EAAE;IACZ,MAAMI,OAAO,GAAG,IAAI,CAACH,IAAI;IACzB,IAAI,CAACA,IAAI,GAAGD,GAAG;IACf,IAAI,CAACvE,QAAQ,GAAG,IAAI,CAAC4E,eAAe,CAAC;MAAEC,GAAG,EAAEF,OAAO;MAAEG,GAAG,EAAEP;IAAI,CAAC,CAAC,GAAG,IAAI,CAACQ,kBAAkB,CAACR,GAAG,CAAC;IAC/F,IAAI,CAACS,qBAAqB,CAAC,CAAC;EAChC;EACAJ,eAAeA,CAACL,GAAG,EAAE;IACjB,MAAMJ,QAAQ,GAAG,IAAI,CAACxF,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC9C,MAAMgE,UAAU,GAAG,IAAI,CAACzF,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAClD,MAAM8E,WAAW,GAAGd,QAAQ,CAACe,KAAK;IAClC,MAAMC,aAAa,GAAGf,UAAU,CAACc,KAAK;IACtCd,UAAU,CAACG,GAAG,GAAGA,GAAG,CAACO,GAAG;IACxBX,QAAQ,CAACI,GAAG,GAAGa,IAAI,CAACC,GAAG,CAACd,GAAG,CAACO,GAAG,EAAEV,UAAU,CAACc,KAAK,CAAC;IAClDd,UAAU,CAACiB,GAAG,GAAGD,IAAI,CAACb,GAAG,CAACJ,QAAQ,CAACkB,GAAG,EAAElB,QAAQ,CAACe,KAAK,CAAC;IACvDd,UAAU,CAACkB,oBAAoB,CAAC,CAAC;IACjCnB,QAAQ,CAACmB,oBAAoB,CAAC,CAAC;IAC/Bf,GAAG,CAACO,GAAG,GAAGP,GAAG,CAACM,GAAG,GACX,IAAI,CAACU,+BAA+B,CAACpB,QAAQ,EAAEC,UAAU,CAAC,GAC1D,IAAI,CAACmB,+BAA+B,CAACnB,UAAU,EAAED,QAAQ,CAAC;IAChE,IAAIc,WAAW,KAAKd,QAAQ,CAACe,KAAK,EAAE;MAChC,IAAI,CAACM,cAAc,CAACrB,QAAQ,CAAC;IACjC;IACA,IAAIgB,aAAa,KAAKf,UAAU,CAACc,KAAK,EAAE;MACpC,IAAI,CAACM,cAAc,CAACpB,UAAU,CAAC;IACnC;EACJ;EACAW,kBAAkBA,CAACR,GAAG,EAAE;IACpB,MAAMzF,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC3C,IAAItB,KAAK,EAAE;MACP,MAAM2G,QAAQ,GAAG3G,KAAK,CAACoG,KAAK;MAC5BpG,KAAK,CAACyF,GAAG,GAAGA,GAAG;MACfzF,KAAK,CAAC4G,qBAAqB,CAAC,CAAC;MAC7B,IAAI,CAACC,cAAc,CAAC7G,KAAK,CAAC;MAC1B,IAAI2G,QAAQ,KAAK3G,KAAK,CAACoG,KAAK,EAAE;QAC1B,IAAI,CAACM,cAAc,CAAC1G,KAAK,CAAC;MAC9B;IACJ;EACJ;EACA;EACA,IAAIuG,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAACO,IAAI;EACpB;EACA,IAAIP,GAAGA,CAACnB,CAAC,EAAE;IACP,MAAMmB,GAAG,GAAGZ,KAAK,CAACP,CAAC,CAAC,GAAG,IAAI,CAAC0B,IAAI,GAAG1B,CAAC;IACpC,IAAI,IAAI,CAAC0B,IAAI,KAAKP,GAAG,EAAE;MACnB,IAAI,CAACQ,UAAU,CAACR,GAAG,CAAC;IACxB;EACJ;EACAQ,UAAUA,CAACR,GAAG,EAAE;IACZ,MAAMS,OAAO,GAAG,IAAI,CAACF,IAAI;IACzB,IAAI,CAACA,IAAI,GAAGP,GAAG;IACf,IAAI,CAACrF,QAAQ,GAAG,IAAI,CAAC+F,eAAe,CAAC;MAAElB,GAAG,EAAEiB,OAAO;MAAEhB,GAAG,EAAEO;IAAI,CAAC,CAAC,GAAG,IAAI,CAACW,kBAAkB,CAACX,GAAG,CAAC;IAC/F,IAAI,CAACL,qBAAqB,CAAC,CAAC;EAChC;EACAe,eAAeA,CAACV,GAAG,EAAE;IACjB,MAAMlB,QAAQ,GAAG,IAAI,CAACxF,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC9C,MAAMgE,UAAU,GAAG,IAAI,CAACzF,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAClD,MAAM8E,WAAW,GAAGd,QAAQ,CAACe,KAAK;IAClC,MAAMC,aAAa,GAAGf,UAAU,CAACc,KAAK;IACtCf,QAAQ,CAACkB,GAAG,GAAGA,GAAG,CAACP,GAAG;IACtBV,UAAU,CAACiB,GAAG,GAAGD,IAAI,CAACb,GAAG,CAACc,GAAG,CAACP,GAAG,EAAEX,QAAQ,CAACe,KAAK,CAAC;IAClDf,QAAQ,CAACI,GAAG,GAAGH,UAAU,CAACc,KAAK;IAC/Bf,QAAQ,CAACmB,oBAAoB,CAAC,CAAC;IAC/BlB,UAAU,CAACkB,oBAAoB,CAAC,CAAC;IACjCD,GAAG,CAACP,GAAG,GAAGO,GAAG,CAACR,GAAG,GACX,IAAI,CAACU,+BAA+B,CAACnB,UAAU,EAAED,QAAQ,CAAC,GAC1D,IAAI,CAACoB,+BAA+B,CAACpB,QAAQ,EAAEC,UAAU,CAAC;IAChE,IAAIa,WAAW,KAAKd,QAAQ,CAACe,KAAK,EAAE;MAChC,IAAI,CAACM,cAAc,CAACrB,QAAQ,CAAC;IACjC;IACA,IAAIgB,aAAa,KAAKf,UAAU,CAACc,KAAK,EAAE;MACpC,IAAI,CAACM,cAAc,CAACpB,UAAU,CAAC;IACnC;EACJ;EACA4B,kBAAkBA,CAACX,GAAG,EAAE;IACpB,MAAMvG,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC3C,IAAItB,KAAK,EAAE;MACP,MAAM2G,QAAQ,GAAG3G,KAAK,CAACoG,KAAK;MAC5BpG,KAAK,CAACuG,GAAG,GAAGA,GAAG;MACfvG,KAAK,CAAC4G,qBAAqB,CAAC,CAAC;MAC7B,IAAI,CAACC,cAAc,CAAC7G,KAAK,CAAC;MAC1B,IAAI2G,QAAQ,KAAK3G,KAAK,CAACoG,KAAK,EAAE;QAC1B,IAAI,CAACM,cAAc,CAAC1G,KAAK,CAAC;MAC9B;IACJ;EACJ;EACA;EACA,IAAImH,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACC,KAAK;EACrB;EACA,IAAID,IAAIA,CAAC/B,CAAC,EAAE;IACR,MAAM+B,IAAI,GAAGxB,KAAK,CAACP,CAAC,CAAC,GAAG,IAAI,CAACgC,KAAK,GAAGhC,CAAC;IACtC,IAAI,IAAI,CAACgC,KAAK,KAAKD,IAAI,EAAE;MACrB,IAAI,CAACE,WAAW,CAACF,IAAI,CAAC;IAC1B;EACJ;EACAE,WAAWA,CAACF,IAAI,EAAE;IACd,IAAI,CAACC,KAAK,GAAGD,IAAI;IACjB,IAAI,CAACjG,QAAQ,GAAG,IAAI,CAACoG,gBAAgB,CAAC,CAAC,GAAG,IAAI,CAACC,mBAAmB,CAAC,CAAC;IACpE,IAAI,CAACrB,qBAAqB,CAAC,CAAC;EAChC;EACAoB,gBAAgBA,CAAA,EAAG;IACf,MAAMjC,QAAQ,GAAG,IAAI,CAACxF,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC9C,MAAMgE,UAAU,GAAG,IAAI,CAACzF,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAClD,MAAM8E,WAAW,GAAGd,QAAQ,CAACe,KAAK;IAClC,MAAMC,aAAa,GAAGf,UAAU,CAACc,KAAK;IACtC,MAAMoB,cAAc,GAAGlC,UAAU,CAACc,KAAK;IACvCf,QAAQ,CAACI,GAAG,GAAG,IAAI,CAACC,IAAI;IACxBJ,UAAU,CAACiB,GAAG,GAAG,IAAI,CAACO,IAAI;IAC1BzB,QAAQ,CAAC8B,IAAI,GAAG,IAAI,CAACC,KAAK;IAC1B9B,UAAU,CAAC6B,IAAI,GAAG,IAAI,CAACC,KAAK;IAC5B,IAAI,IAAI,CAACtJ,SAAS,CAAC0B,MAAM,EAAE;MACvB6F,QAAQ,CAACe,KAAK,GAAGf,QAAQ,CAACe,KAAK;MAC/Bd,UAAU,CAACc,KAAK,GAAGd,UAAU,CAACc,KAAK;IACvC;IACAf,QAAQ,CAACI,GAAG,GAAGa,IAAI,CAACC,GAAG,CAAC,IAAI,CAACb,IAAI,EAAEJ,UAAU,CAACc,KAAK,CAAC;IACpDd,UAAU,CAACiB,GAAG,GAAGD,IAAI,CAACb,GAAG,CAAC,IAAI,CAACqB,IAAI,EAAEzB,QAAQ,CAACe,KAAK,CAAC;IACpDd,UAAU,CAACkB,oBAAoB,CAAC,CAAC;IACjCnB,QAAQ,CAACmB,oBAAoB,CAAC,CAAC;IAC/BnB,QAAQ,CAACe,KAAK,GAAGoB,cAAc,GACzB,IAAI,CAACf,+BAA+B,CAACnB,UAAU,EAAED,QAAQ,CAAC,GAC1D,IAAI,CAACoB,+BAA+B,CAACpB,QAAQ,EAAEC,UAAU,CAAC;IAChE,IAAIa,WAAW,KAAKd,QAAQ,CAACe,KAAK,EAAE;MAChC,IAAI,CAACM,cAAc,CAACrB,QAAQ,CAAC;IACjC;IACA,IAAIgB,aAAa,KAAKf,UAAU,CAACc,KAAK,EAAE;MACpC,IAAI,CAACM,cAAc,CAACpB,UAAU,CAAC;IACnC;EACJ;EACAiC,mBAAmBA,CAAA,EAAG;IAClB,MAAMvH,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC3C,IAAItB,KAAK,EAAE;MACP,MAAM2G,QAAQ,GAAG3G,KAAK,CAACoG,KAAK;MAC5BpG,KAAK,CAACmH,IAAI,GAAG,IAAI,CAACC,KAAK;MACvB,IAAI,IAAI,CAACtJ,SAAS,CAAC0B,MAAM,EAAE;QACvBQ,KAAK,CAACoG,KAAK,GAAGpG,KAAK,CAACoG,KAAK;MAC7B;MACApG,KAAK,CAAC4G,qBAAqB,CAAC,CAAC;MAC7B,IAAID,QAAQ,KAAK3G,KAAK,CAACoG,KAAK,EAAE;QAC1B,IAAI,CAACM,cAAc,CAAC1G,KAAK,CAAC;MAC9B;IACJ;EACJ;EACA1C,WAAWA,CAACE,OAAO,EAAED,IAAI,EAAEE,WAAW,EAAEgK,IAAI,EAAElG,oBAAoB,EAAEmG,aAAa,EAAE;IAC/E,IAAI,CAAClK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACgK,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAClG,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAAC4D,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACoC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACjC,IAAI,GAAG,CAAC;IACb;IACA,IAAI,CAACkC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACd,IAAI,GAAG,GAAG;IACf,IAAI,CAACM,KAAK,GAAG,CAAC;IACd;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACS,WAAW,GAAIzB,KAAK,IAAK,GAAGA,KAAK,EAAE;IACxC,IAAI,CAAC0B,aAAa,GAAG,EAAE;IACvB;IACA;IACA,IAAI,CAACjL,uBAAuB,GAAG,EAAE;IACjC;IACA,IAAI,CAACkL,qBAAqB,GAAG,EAAE;IAC/B,IAAI,CAAC7G,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAAC8G,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAACtK,SAAS,GAAGjF,MAAM,CAACH,QAAQ,CAAC;IACjC;IACA,IAAI,CAAC2P,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC7G,eAAe,GAAGiG,aAAa,KAAK,gBAAgB;IACzD,IAAI,CAACa,sBAAsB,GAAG,IAAI,CAACd,IAAI,CAACe,MAAM,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,YAAY,CAAC,CAAC,CAAC;IACnF,IAAI,CAACV,MAAM,GAAG,IAAI,CAACP,IAAI,CAACrB,KAAK,KAAK,KAAK;EAC3C;EACA1G,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAAC5B,SAAS,CAAC6K,SAAS,EAAE;MAC1B,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAC5B;IACA,MAAMC,MAAM,GAAG,IAAI,CAAChJ,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC5C,MAAMwH,MAAM,GAAG,IAAI,CAACjJ,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAC9C,IAAI,CAACH,QAAQ,GAAG,CAAC,CAAC2H,MAAM,IAAI,CAAC,CAACC,MAAM;IACpC,IAAI,CAACvL,IAAI,CAACwL,aAAa,CAAC,CAAC;IACzB,IAAI,OAAOxE,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/CyE,eAAe,CAAC,IAAI,CAAC9H,QAAQ,EAAE,IAAI,CAACrB,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC,EAAE,IAAI,CAACzB,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC,CAAC;IAClG;IACA,MAAM4H,KAAK,GAAG,IAAI,CAAC7H,SAAS,CAACtE,SAAS,CAACwE,GAAG,CAAC;IAC3C,IAAI,CAACwG,aAAa,GAAGmB,KAAK,CAACtJ,OAAO,CAACC,MAAM;IACzC,IAAI,CAACsJ,aAAa,GAAG,IAAI,CAACpB,aAAa,GAAG,IAAI,CAACO,WAAW;IAC1D,IAAI,CAACnH,QAAQ,GACP,IAAI,CAACiI,YAAY,CAACN,MAAM,EAAEC,MAAM,CAAC,GACjC,IAAI,CAACM,eAAe,CAACP,MAAM,CAAC;IAClC,IAAI,CAAChC,cAAc,CAACgC,MAAM,CAAC;IAC3B,IAAI,CAACQ,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAAChM,IAAI,CAACwL,aAAa,CAAC,CAAC;EAC7B;EACAK,eAAeA,CAACP,MAAM,EAAE;IACpBA,MAAM,CAACW,SAAS,CAAC,CAAC;IAClBX,MAAM,CAACY,MAAM,CAAC,CAAC;IACf,IAAI,CAACC,uBAAuB,CAACb,MAAM,CAAC;IACpC,IAAI,CAACZ,mBAAmB,GAAG,IAAI;IAC/BY,MAAM,CAACjC,qBAAqB,CAAC,CAAC;EAClC;EACAuC,YAAYA,CAACN,MAAM,EAAEC,MAAM,EAAE;IACzBD,MAAM,CAACW,SAAS,CAAC,CAAC;IAClBX,MAAM,CAACY,MAAM,CAAC,CAAC;IACfX,MAAM,CAACU,SAAS,CAAC,CAAC;IAClBV,MAAM,CAACW,MAAM,CAAC,CAAC;IACfZ,MAAM,CAACc,aAAa,CAAC,CAAC;IACtBb,MAAM,CAACa,aAAa,CAAC,CAAC;IACtBd,MAAM,CAACe,mBAAmB,CAAC,CAAC;IAC5Bd,MAAM,CAACc,mBAAmB,CAAC,CAAC;IAC5B,IAAI,CAACpE,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACyC,mBAAmB,GAAG,IAAI;IAC/BY,MAAM,CAACjC,qBAAqB,CAAC,CAAC;IAC9BkC,MAAM,CAAClC,qBAAqB,CAAC,CAAC;EAClC;EACAzG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoI,sBAAsB,CAACsB,WAAW,CAAC,CAAC;IACzC,IAAI,CAACC,eAAe,EAAEC,UAAU,CAAC,CAAC;IAClC,IAAI,CAACD,eAAe,GAAG,IAAI;EAC/B;EACA;EACApB,YAAYA,CAAA,EAAG;IACX,IAAI,CAACV,MAAM,GAAG,IAAI,CAACP,IAAI,CAACrB,KAAK,KAAK,KAAK;IACvC,IAAI,CAAClF,QAAQ,GAAG,IAAI,CAAC8I,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACC,oBAAoB,CAAC,CAAC;IACtE,IAAI,CAACZ,iBAAiB,CAAC,CAAC;EAC5B;EACAW,iBAAiBA,CAAA,EAAG;IAChB,MAAM3E,QAAQ,GAAG,IAAI,CAACxF,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC9C,MAAMgE,UAAU,GAAG,IAAI,CAACzF,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAClDgE,QAAQ,CAAC6E,eAAe,CAAC,CAAC;IAC1B5E,UAAU,CAAC4E,eAAe,CAAC,CAAC;IAC5B7E,QAAQ,CAAC8E,UAAU,GAAG9E,QAAQ,CAAC+E,sBAAsB,CAAC,CAAC;IACvD9E,UAAU,CAAC6E,UAAU,GAAG7E,UAAU,CAAC8E,sBAAsB,CAAC,CAAC;IAC3D/E,QAAQ,CAACuE,mBAAmB,CAAC,CAAC;IAC9BtE,UAAU,CAACsE,mBAAmB,CAAC,CAAC;IAChCvE,QAAQ,CAACmB,oBAAoB,CAAC,CAAC;IAC/BlB,UAAU,CAACkB,oBAAoB,CAAC,CAAC;IACjCnB,QAAQ,CAACuB,qBAAqB,CAAC,CAAC;IAChCtB,UAAU,CAACsB,qBAAqB,CAAC,CAAC;EACtC;EACAqD,oBAAoBA,CAAA,EAAG;IACnB,MAAMjK,KAAK,GAAG,IAAI,CAACH,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC3CtB,KAAK,CAAC4G,qBAAqB,CAAC,CAAC;EACjC;EACA;EACA2C,kBAAkBA,CAAA,EAAG;IACjB,IAAI,OAAOc,cAAc,KAAK,WAAW,IAAI,CAACA,cAAc,EAAE;MAC1D;IACJ;IACA,IAAI,CAAC7M,OAAO,CAACyC,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC6J,eAAe,GAAG,IAAIO,cAAc,CAAC,MAAM;QAC5C,IAAI,IAAI,CAACzM,SAAS,CAAC,CAAC,EAAE;UAClB;QACJ;QACA,IAAI,IAAI,CAACwK,YAAY,EAAE;UACnBkC,YAAY,CAAC,IAAI,CAAClC,YAAY,CAAC;QACnC;QACA,IAAI,CAACmC,SAAS,CAAC,CAAC;MACpB,CAAC,CAAC;MACF,IAAI,CAACT,eAAe,CAACU,OAAO,CAAC,IAAI,CAAC/M,WAAW,CAACgC,aAAa,CAAC;IAChE,CAAC,CAAC;EACN;EACA;EACA7B,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACwD,SAAS,CAACtE,SAAS,CAACuE,KAAK,CAAC,CAACzD,SAAS,IAAI,IAAI,CAACwD,SAAS,CAACtE,SAAS,CAACwE,GAAG,CAAC,CAAC1D,SAAS;EAC/F;EACA6M,SAASA,CAAC3K,aAAa,GAAGhD,SAAS,CAACwE,GAAG,EAAE;IACrC,MAAMtB,KAAK,GAAG,IAAI,CAACH,SAAS,CAACC,aAAa,CAAC;IAC3C,IAAI,CAACE,KAAK,EAAE;MACR,OAAO,IAAI,CAACyF,GAAG;IACnB;IACA,OAAOzF,KAAK,CAACoG,KAAK;EACtB;EACAsE,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,EAAE,IAAI,CAAC7K,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC,EAAEsJ,aAAa,IAAI,IAAI,CAAC9K,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC,EAAEqJ,aAAa,CAAC;EAC7G;EACA;EACA/B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACnM,YAAY,GAAG,IAAI,CAACgB,WAAW,CAACgC,aAAa,CAACmL,WAAW;IAC9D,IAAI,CAACC,WAAW,GAAG,IAAI,CAACpN,WAAW,CAACgC,aAAa,CAACpB,qBAAqB,CAAC,CAAC,CAACyM,IAAI;EAClF;EACA;EACAC,qBAAqBA,CAAC3G,MAAM,EAAE;IAC1B,MAAM4G,UAAU,GAAG,IAAI,CAACC,YAAY,CAACxL,aAAa,CAACyL,KAAK;IACxDF,UAAU,CAACF,IAAI,GAAG1G,MAAM,CAAC0G,IAAI;IAC7BE,UAAU,CAACG,KAAK,GAAG/G,MAAM,CAAC+G,KAAK;IAC/BH,UAAU,CAACI,eAAe,GAAGhH,MAAM,CAACgH,eAAe;IACnDJ,UAAU,CAACK,SAAS,GAAGjH,MAAM,CAACiH,SAAS;EAC3C;EACA;EACArP,sBAAsBA,CAACsP,KAAK,EAAE;IAC1B;IACA,MAAMnB,UAAU,GAAGmB,KAAK,IAAI,IAAI,CAACpD,mBAAmB,IAAI,IAAI,CAAC7L,UAAU,CAACkP,MAAM,GAAG,CAAC,CAAC,CAAC;IACpF,OAAO,cAAcpB,UAAU,IAAI;EACvC;EACA;EACAqB,mBAAmBA,CAACC,MAAM,EAAE;IACxB,IAAI,CAAC,IAAI,CAACxD,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACyD,cAAc,CAACD,MAAM,CAAC;IAC3B,IAAI,CAAC5E,cAAc,CAAC4E,MAAM,CAAC;IAC3B,IAAI,CAACE,yBAAyB,CAACF,MAAM,CAAC;EAC1C;EACAhF,+BAA+BA,CAACmF,MAAM,EAAEC,MAAM,EAAE;IAC5C,IAAI,CAAC,IAAI,CAAC5D,mBAAmB,EAAE;MAC3B;IACJ;IACA2D,MAAM,CAAChF,qBAAqB,CAAC,CAAC;IAC9BiF,MAAM,CAACjF,qBAAqB,CAAC,CAAC;EAClC;EACAF,cAAcA,CAAC+E,MAAM,EAAE;IACnB,IAAI,CAAC,IAAI,CAACxD,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACyB,uBAAuB,CAAC+B,MAAM,CAAC;IACpC,IAAI,CAACpC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC9L,IAAI,CAACwL,aAAa,CAAC,CAAC;EAC7B;EACA7C,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAAC+B,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACoB,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC/L,IAAI,CAACuO,YAAY,CAAC,CAAC;EAC5B;EACAvB,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAACtC,mBAAmB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACW,iBAAiB,CAAC,CAAC;IACxB,IAAI,IAAI,CAAC1H,QAAQ,EAAE;MACf,MAAM2H,MAAM,GAAG,IAAI,CAAChJ,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;MAC5C,MAAMwH,MAAM,GAAG,IAAI,CAACjJ,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;MAC9CwH,MAAM,CAACjC,qBAAqB,CAAC,CAAC;MAC9BkC,MAAM,CAAClC,qBAAqB,CAAC,CAAC;MAC9BiC,MAAM,CAACe,mBAAmB,CAAC,CAAC;MAC5Bd,MAAM,CAACc,mBAAmB,CAAC,CAAC;MAC5Bf,MAAM,CAACc,aAAa,CAAC,CAAC;MACtBb,MAAM,CAACa,aAAa,CAAC,CAAC;MACtBd,MAAM,CAACrC,oBAAoB,CAAC,CAAC;MAC7BsC,MAAM,CAACtC,oBAAoB,CAAC,CAAC;IACjC,CAAC,MACI;MACD,MAAMqC,MAAM,GAAG,IAAI,CAAChJ,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;MAC5C,IAAIuH,MAAM,EAAE;QACRA,MAAM,CAACjC,qBAAqB,CAAC,CAAC;MAClC;IACJ;IACA,IAAI,CAACyC,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAACC,sBAAsB,CAAC,CAAC;IAC7B,IAAI,CAAC/L,IAAI,CAACwL,aAAa,CAAC,CAAC;EAC7B;EACA;EACAgD,qBAAqBA,CAAA,EAAG;IACpB,MAAMzG,UAAU,GAAG,IAAI,CAACzF,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAClD,MAAMgE,QAAQ,GAAG,IAAI,CAACxF,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC9C,IAAI,CAACgE,UAAU,IAAI,CAACD,QAAQ,EAAE;MAC1B,OAAO,KAAK;IAChB;IACA,OAAOA,QAAQ,CAAC8E,UAAU,GAAG7E,UAAU,CAAC6E,UAAU,GAAG,EAAE;EAC3D;EACA;AACJ;AACA;AACA;EACI6B,iCAAiCA,CAACP,MAAM,EAAE;IACtC,MAAMtK,OAAO,GAAGsK,MAAM,CAACQ,UAAU,CAAC,CAAC;IACnC,MAAMC,WAAW,GAAG,IAAI,CAAC9K,SAAS,CAACqK,MAAM,CAAC3L,aAAa,CAAC;IACxD,MAAMqM,YAAY,GAAG,IAAI,CAAC/K,SAAS,CAACD,OAAO,CAACrB,aAAa,CAAC;IAC1DqM,YAAY,CAAC/N,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,wBAAwB,CAAC;IACpEgN,WAAW,CAAC9N,YAAY,CAACU,SAAS,CAACsN,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC9D,cAAc,CAAC;EAC5F;EACA;EACAqD,yBAAyBA,CAACF,MAAM,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACvK,QAAQ,IAAI,IAAI,CAACwJ,WAAW,CAAC,CAAC,EAAE;MACtC;IACJ;IACA,IAAI,IAAI,CAACpC,cAAc,KAAK,IAAI,CAACyD,qBAAqB,CAAC,CAAC,EAAE;MACtD,IAAI,CAACzD,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;MAC1C,IAAI,CAAC0D,iCAAiC,CAACP,MAAM,CAAC;IAClD;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAC,cAAcA,CAACD,MAAM,EAAE;IACnB,IAAI,IAAI,CAACf,WAAW,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,MAAMzB,KAAK,GAAG,IAAI,CAAC7H,SAAS,CAACqK,MAAM,CAAC3L,aAAa,KAAKhD,SAAS,CAACwE,GAAG,GAAGxE,SAAS,CAACwE,GAAG,GAAGxE,SAAS,CAACuE,KAAK,CAAC;IACtG4H,KAAK,CAAC7K,YAAY,CAAC8M,KAAK,CAACG,SAAS,GAAG,cAAcI,MAAM,CAACtB,UAAU,KAAK;EAC7E;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAT,uBAAuBA,CAAC+B,MAAM,EAAE;IAC5B,IAAI,IAAI,CAACf,WAAW,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,MAAM2B,SAAS,GAAG,IAAI,CAACxE,WAAW,CAAC4D,MAAM,CAACrF,KAAK,CAAC;IAChD,IAAI,CAAC6B,mBAAmB,GAClBwD,MAAM,CAACa,UAAU,CAACC,GAAG,CAACF,SAAS,CAAC,GAChCZ,MAAM,CAACrN,YAAY,CAACoO,YAAY,CAAC,gBAAgB,EAAEH,SAAS,CAAC;IACnE,IAAI,IAAI,CAACzP,QAAQ,EAAE;MACf6O,MAAM,CAAC3L,aAAa,KAAKhD,SAAS,CAACuE,KAAK,GACjC,IAAI,CAACxE,uBAAuB,GAAGwP,SAAS,GACxC,IAAI,CAACtE,qBAAqB,GAAGsE,SAAU;MAC9C,MAAMI,WAAW,GAAG,IAAI,CAACrL,SAAS,CAACqK,MAAM,CAAC3L,aAAa,CAAC;MACxDuM,SAAS,CAACd,MAAM,GAAG,CAAC,GACdkB,WAAW,CAACrO,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,gCAAgC,CAAC,GACxE0N,WAAW,CAACrO,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,gCAAgC,CAAC;IACrF;EACJ;EACA;EACAsG,wBAAwBA,CAAA,EAAG;IACvB,MAAMqD,MAAM,GAAG,IAAI,CAAChJ,SAAS,CAAC/C,SAAS,CAACwE,GAAG,CAAC;IAC5C,MAAMwH,MAAM,GAAG,IAAI,CAACjJ,SAAS,CAAC/C,SAAS,CAACuE,KAAK,CAAC;IAC9C,IAAIwH,MAAM,EAAE;MACR,IAAI,CAACa,uBAAuB,CAACb,MAAM,CAAC;IACxC;IACA,IAAIC,MAAM,EAAE;MACR,IAAI,CAACY,uBAAuB,CAACZ,MAAM,CAAC;IACxC;EACJ;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAQ,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAAC3B,aAAa,IAAI,IAAI,CAAC+C,WAAW,CAAC,CAAC,EAAE;MAC3C;IACJ;IACA,MAAMvD,IAAI,GAAG,IAAI,CAACC,KAAK,IAAI,IAAI,CAACA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC;IAC1D,MAAMsF,QAAQ,GAAGpG,IAAI,CAACqG,KAAK,CAAC,IAAI,CAACpG,GAAG,GAAGY,IAAI,CAAC,GAAGA,IAAI;IACnD,MAAMyF,UAAU,GAAG,CAACF,QAAQ,GAAG,IAAI,CAACjH,GAAG,KAAK,IAAI,CAACc,GAAG,GAAG,IAAI,CAACd,GAAG,CAAC;IAChE,IAAI,CAACyC,mBAAmB,GAAG,IAAI,CAACzL,YAAY,GAAGmQ,UAAU,GAAG,CAAC;EACjE;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA/F,cAAcA,CAAC4E,MAAM,EAAE;IACnB,IAAI,IAAI,CAACf,WAAW,CAAC,CAAC,EAAE;MACpB;IACJ;IACA,IAAI,CAACxJ,QAAQ,GACP,IAAI,CAAC2L,mBAAmB,CAACpB,MAAM,CAAC,GAChC,IAAI,CAACqB,sBAAsB,CAACrB,MAAM,CAAC;EAC7C;EACAoB,mBAAmBA,CAACpB,MAAM,EAAE;IACxB,MAAMtK,OAAO,GAAGsK,MAAM,CAACQ,UAAU,CAAC,CAAC;IACnC,IAAI,CAAC9K,OAAO,IAAI,CAAC,IAAI,CAAC1E,YAAY,EAAE;MAChC;IACJ;IACA,MAAMsQ,gBAAgB,GAAGzG,IAAI,CAAC0G,GAAG,CAAC7L,OAAO,CAACgJ,UAAU,GAAGsB,MAAM,CAACtB,UAAU,CAAC,GAAG,IAAI,CAAC1N,YAAY;IAC7F,IAAIgP,MAAM,CAACwB,YAAY,IAAI,IAAI,CAACxQ,YAAY,EAAE;MAC1C,IAAI,CAACsO,qBAAqB,CAAC;QACvBD,IAAI,EAAE,MAAM;QACZK,KAAK,EAAE,GAAG,IAAI,CAAC1O,YAAY,GAAG0E,OAAO,CAACgJ,UAAU,IAAI;QACpDiB,eAAe,EAAE,OAAO;QACxBC,SAAS,EAAE,UAAU0B,gBAAgB;MACzC,CAAC,CAAC;IACN,CAAC,MACI;MACD,IAAI,CAAChC,qBAAqB,CAAC;QACvBD,IAAI,EAAE,GAAG3J,OAAO,CAACgJ,UAAU,IAAI;QAC/BgB,KAAK,EAAE,MAAM;QACbC,eAAe,EAAE,MAAM;QACvBC,SAAS,EAAE,UAAU0B,gBAAgB;MACzC,CAAC,CAAC;IACN;EACJ;EACAD,sBAAsBA,CAACrB,MAAM,EAAE;IAC3B,IAAI,CAACzD,MAAM,GACL,IAAI,CAAC+C,qBAAqB,CAAC;MACzBD,IAAI,EAAE,MAAM;MACZK,KAAK,EAAE,KAAK;MACZC,eAAe,EAAE,OAAO;MACxBC,SAAS,EAAE,UAAU,CAAC,GAAGI,MAAM,CAACyB,cAAc;IAClD,CAAC,CAAC,GACA,IAAI,CAACnC,qBAAqB,CAAC;MACzBD,IAAI,EAAE,KAAK;MACXK,KAAK,EAAE,MAAM;MACbC,eAAe,EAAE,MAAM;MACvBC,SAAS,EAAE,UAAUI,MAAM,CAACyB,cAAc;IAC9C,CAAC,CAAC;EACV;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA7D,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC1B,aAAa,IACnB,IAAI,CAACR,IAAI,KAAKnC,SAAS,IACvB,IAAI,CAACS,GAAG,KAAKT,SAAS,IACtB,IAAI,CAACuB,GAAG,KAAKvB,SAAS,EAAE;MACxB;IACJ;IACA,MAAMmC,IAAI,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC,GAAG,IAAI,CAACA,IAAI,GAAG,CAAC;IAC1C,IAAI,CAACjG,QAAQ,GAAG,IAAI,CAACiM,sBAAsB,CAAChG,IAAI,CAAC,GAAG,IAAI,CAACiG,yBAAyB,CAACjG,IAAI,CAAC;IACxF,IAAI,IAAI,CAACa,MAAM,EAAE;MACb,IAAI,CAAC3L,UAAU,CAACgR,OAAO,CAAC,CAAC;IAC7B;EACJ;EACAD,yBAAyBA,CAACjG,IAAI,EAAE;IAC5B,MAAMf,KAAK,GAAG,IAAI,CAACqE,SAAS,CAAC,CAAC;IAC9B,IAAI6C,SAAS,GAAGhH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAACvG,KAAK,GAAG,IAAI,CAACX,GAAG,IAAI0B,IAAI,CAAC,EAAE,CAAC,CAAC;IAClE,IAAIoG,WAAW,GAAGjH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAAC,IAAI,CAACpG,GAAG,GAAGH,KAAK,IAAIe,IAAI,CAAC,EAAE,CAAC,CAAC;IACpE,IAAI,CAACa,MAAM,GAAGsF,SAAS,EAAE,GAAGC,WAAW,EAAE;IACzC,IAAI,CAAClR,UAAU,GAAGmR,KAAK,CAACF,SAAS,CAAC,CAC7BG,IAAI,CAAC1Q,YAAY,CAAC2Q,MAAM,CAAC,CACzBC,MAAM,CAACH,KAAK,CAACD,WAAW,CAAC,CAACE,IAAI,CAAC1Q,YAAY,CAAC6Q,QAAQ,CAAC,CAAC;EAC/D;EACAT,sBAAsBA,CAAChG,IAAI,EAAE;IACzB,MAAM0G,QAAQ,GAAG,IAAI,CAACpD,SAAS,CAAC,CAAC;IACjC,MAAMqD,UAAU,GAAG,IAAI,CAACrD,SAAS,CAAC3N,SAAS,CAACuE,KAAK,CAAC;IAClD,MAAM0M,2BAA2B,GAAGzH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAACmB,UAAU,GAAG,IAAI,CAACrI,GAAG,IAAI0B,IAAI,CAAC,EAAE,CAAC,CAAC;IAC3F,MAAMmG,SAAS,GAAGhH,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAACkB,QAAQ,GAAGC,UAAU,IAAI3G,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC7E,MAAM6G,wBAAwB,GAAG1H,IAAI,CAACC,GAAG,CAACD,IAAI,CAACqG,KAAK,CAAC,CAAC,IAAI,CAACpG,GAAG,GAAGsH,QAAQ,IAAI1G,IAAI,CAAC,EAAE,CAAC,CAAC;IACtF,IAAI,CAAC9K,UAAU,GAAGmR,KAAK,CAACO,2BAA2B,CAAC,CAC/CN,IAAI,CAAC1Q,YAAY,CAAC6Q,QAAQ,CAAC,CAC3BD,MAAM,CAACH,KAAK,CAACF,SAAS,CAAC,CAACG,IAAI,CAAC1Q,YAAY,CAAC2Q,MAAM,CAAC,EAAEF,KAAK,CAACQ,wBAAwB,CAAC,CAACP,IAAI,CAAC1Q,YAAY,CAAC6Q,QAAQ,CAAC,CAAC;EACxH;EACA;EACA/N,SAASA,CAACC,aAAa,EAAE;IACrB,IAAIA,aAAa,KAAKhD,SAAS,CAACwE,GAAG,IAAI,IAAI,CAAC2M,MAAM,EAAE;MAChD,OAAO,IAAI,CAACA,MAAM;IACtB;IACA,IAAI,IAAI,CAACC,OAAO,EAAE3C,MAAM,EAAE;MACtB,OAAOzL,aAAa,KAAKhD,SAAS,CAACuE,KAAK,GAAG,IAAI,CAAC6M,OAAO,CAAC7K,KAAK,GAAG,IAAI,CAAC6K,OAAO,CAACC,IAAI;IACrF;IACA;EACJ;EACA;EACA/M,SAASA,CAACtB,aAAa,EAAE;IACrB,OAAOA,aAAa,KAAKhD,SAAS,CAACwE,GAAG,GAAG,IAAI,CAAC8M,OAAO,EAAED,IAAI,GAAG,IAAI,CAACC,OAAO,EAAE/K,KAAK;EACrF;EACAgL,cAAcA,CAACC,aAAa,EAAE;IAC1B,IAAI,CAACnG,aAAa,GAAG,CAAC,IAAI,CAACrK,SAAS,CAACyQ,GAAG,IAAID,aAAa,IAAI,CAAC,IAAI,CAAC7M,eAAe;IAClF,IAAI,CAAChE,WAAW,CAACgC,aAAa,CAACX,SAAS,CAACsN,MAAM,CAAC,+BAA+B,EAAE,IAAI,CAACjE,aAAa,CAAC;EACxG;EACA;EACA5J,sBAAsBA,CAACP,KAAK,EAAEG,IAAI,EAAE;IAChC,MAAMyB,MAAM,GAAGzB,IAAI,CAACqQ,KAAK,GAAG,CAAC;IAC7B,MAAMC,OAAO,GAAGtQ,IAAI,CAACuQ,CAAC,GAAG9O,MAAM;IAC/B,MAAM+O,OAAO,GAAGxQ,IAAI,CAACyQ,CAAC,GAAGhP,MAAM;IAC/B,MAAMiP,EAAE,GAAG7Q,KAAK,CAAC8Q,OAAO,GAAGL,OAAO;IAClC,MAAMM,EAAE,GAAG/Q,KAAK,CAACgR,OAAO,GAAGL,OAAO;IAClC,OAAOrI,IAAI,CAAC2I,GAAG,CAACJ,EAAE,EAAE,CAAC,CAAC,GAAGvI,IAAI,CAAC2I,GAAG,CAACF,EAAE,EAAE,CAAC,CAAC,GAAGzI,IAAI,CAAC2I,GAAG,CAACrP,MAAM,EAAE,CAAC,CAAC;EAClE;EACA;IAAS,IAAI,CAACwC,IAAI,YAAA8M,kBAAA5M,CAAA;MAAA,YAAAA,CAAA,IAAwF4C,SAAS,EA7pBnBvM,EAAE,CAAA4J,iBAAA,CA6pBmC5J,EAAE,CAAC8J,MAAM,GA7pB9C9J,EAAE,CAAA4J,iBAAA,CA6pByD5J,EAAE,CAAC6J,iBAAiB,GA7pB/E7J,EAAE,CAAA4J,iBAAA,CA6pB0F5J,EAAE,CAAC+J,UAAU,GA7pBzG/J,EAAE,CAAA4J,iBAAA,CA6pBoH9J,EAAE,CAAC0W,cAAc,MA7pBvIxW,EAAE,CAAA4J,iBAAA,CA6pBkKpI,yBAAyB,MA7pB7LxB,EAAE,CAAA4J,iBAAA,CA6pBwNnJ,qBAAqB;IAAA,CAA4D;EAAE;EAC7Y;IAAS,IAAI,CAACuJ,IAAI,kBA9pB8EhK,EAAE,CAAAiK,iBAAA;MAAAC,IAAA,EA8pBJqC,SAAS;MAAApC,SAAA;MAAAsM,cAAA,WAAAC,yBAAA1U,EAAA,EAAAC,GAAA,EAAA0U,QAAA;QAAA,IAAA3U,EAAA;UA9pBPhC,EAAE,CAAA4W,cAAA,CAAAD,QAAA,EA8pBq6BrS,gBAAgB;UA9pBv7BtE,EAAE,CAAA4W,cAAA,CAAAD,QAAA,EA8pBk/BpS,sBAAsB;QAAA;QAAA,IAAAvC,EAAA;UAAA,IAAAuI,EAAA;UA9pB1gCvK,EAAE,CAAAwK,cAAA,CAAAD,EAAA,GAAFvK,EAAE,CAAAyK,WAAA,QAAAxI,GAAA,CAAAqT,MAAA,GAAA/K,EAAA,CAAAG,KAAA;UAAF1K,EAAE,CAAAwK,cAAA,CAAAD,EAAA,GAAFvK,EAAE,CAAAyK,WAAA,QAAAxI,GAAA,CAAAsT,OAAA,GAAAhL,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAAyM,gBAAA7U,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAAsK,WAAA,CAAA5H,GAAA;UAAF1C,EAAE,CAAAsK,WAAA,CA8pB+pC9F,uBAAuB;QAAA;QAAA,IAAAxC,EAAA;UAAA,IAAAuI,EAAA;UA9pBxrCvK,EAAE,CAAAwK,cAAA,CAAAD,EAAA,GAAFvK,EAAE,CAAAyK,WAAA,QAAAxI,GAAA,CAAAqQ,YAAA,GAAA/H,EAAA,CAAAG,KAAA;UAAF1K,EAAE,CAAAwK,cAAA,CAAAD,EAAA,GAAFvK,EAAE,CAAAyK,WAAA,QAAAxI,GAAA,CAAAwT,OAAA,GAAAlL,EAAA;QAAA;MAAA;MAAAI,SAAA;MAAAmM,QAAA;MAAAC,YAAA,WAAAC,uBAAAhV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAAmD,UAAA,CA8pBJ,MAAM,IAAAlB,GAAA,CAAAgV,KAAA,IAAa,SAAS,CAApB,CAAC;UA9pBPjX,EAAE,CAAAkX,WAAA,sBAAAjV,GAAA,CAAAsG,QA8pBI,CAAC,yBAAAtG,GAAA,CAAAoG,QAAD,CAAC,yBAAApG,GAAA,CAAAgC,QAAD,CAAC,2BAAAhC,GAAA,CAAA+M,aAAD,CAAC,4BAAA/M,GAAA,CAAA6G,eAAD,CAAC;QAAA;MAAA;MAAA8B,MAAA;QAAAvC,QAAA,GA9pBPrI,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,0BA8pBgG1W,gBAAgB;QAAAuD,QAAA,GA9pBlHjE,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,0BA8pBsJ1W,gBAAgB;QAAAsO,aAAA,GA9pBxKhP,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,oCA8pB2N1W,gBAAgB;QAAAoM,GAAA,GA9pB7O9M,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,gBA8pBkQzW,eAAe;QAAAsW,KAAA;QAAAhI,aAAA,GA9pBnRjP,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,oCA8pBsV1W,gBAAgB;QAAAkN,GAAA,GA9pBxW5N,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,gBA8pB6XzW,eAAe;QAAA6N,IAAA,GA9pB9YxO,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,kBA8pBsazW,eAAe;QAAAuO,WAAA;MAAA;MAAAmI,QAAA;MAAAxM,UAAA;MAAAC,QAAA,GA9pBvb9K,EAAE,CAAA+K,kBAAA,CA8pBszB,CAAC;QAAEC,OAAO,EAAE3G,UAAU;QAAE4G,WAAW,EAAEsB;MAAU,CAAC,CAAC,GA9pBz2BvM,EAAE,CAAAsX,wBAAA,EAAFtX,EAAE,CAAAkL,mBAAA;MAAAqM,kBAAA,EAAA5U,GAAA;MAAAwI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkM,mBAAAxV,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhC,EAAE,CAAAyX,eAAA;UAAFzX,EAAE,CAAA0X,YAAA,EA8pByyC,CAAC;UA9pB5yC1X,EAAE,CAAAkC,cAAA,YA8pB81C,CAAC;UA9pBj2ClC,EAAE,CAAA6C,SAAA,YA8pBm5C,CAAC;UA9pBt5C7C,EAAE,CAAAkC,cAAA,YA8pBg8C,CAAC;UA9pBn8ClC,EAAE,CAAA6C,SAAA,eA8pBugD,CAAC;UA9pB1gD7C,EAAE,CAAAoC,YAAA,CA8pBihD,CAAC;UA9pBphDpC,EAAE,CAAA4D,UAAA,IAAAD,gCAAA,gBA8pB0iD,CAAC;UA9pB7iD3D,EAAE,CAAAoC,YAAA,CA8pBs7D,CAAC;UA9pBz7DpC,EAAE,CAAA4D,UAAA,IAAAG,gCAAA,oCA8pB29D,CAAC;UA9pB99D/D,EAAE,CAAA6C,SAAA,gCA8pBsyE,CAAC;QAAA;QAAA,IAAAb,EAAA;UA9pBzyEhC,EAAE,CAAAuC,SAAA,EA8pB86D,CAAC;UA9pBj7DvC,EAAE,CAAA6D,aAAA,IAAA5B,GAAA,CAAA+M,aAAA,SA8pB86D,CAAC;UA9pBj7DhP,EAAE,CAAAuC,SAAA,CA8pBuoE,CAAC;UA9pB1oEvC,EAAE,CAAA6D,aAAA,IAAA5B,GAAA,CAAAsG,QAAA,SA8pBuoE,CAAC;UA9pB1oEvI,EAAE,CAAAuC,SAAA,CA8pB8rE,CAAC;UA9pBjsEvC,EAAE,CAAAgE,UAAA,aAAA/B,GAAA,CAAAgC,QA8pB8rE,CAAC,mBAAwB,CAAC,uBAAAhC,GAAA,CAAAmN,qBAAiD,CAAC;QAAA;MAAA;MAAA5D,YAAA,GAAo8a9G,oBAAoB;MAAA+G,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAsM;EAAE;AAChhgB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAhqBoG5L,EAAE,CAAA6L,iBAAA,CAgqBXU,SAAS,EAAc,CAAC;IACvGrC,IAAI,EAAE/J,SAAS;IACf2L,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,YAAY;MAAEC,IAAI,EAAE;QAC3B,OAAO,EAAE,2BAA2B;QACpC,SAAS,EAAE,+BAA+B;QAC1C,2BAA2B,EAAE,UAAU;QACvC,8BAA8B,EAAE,UAAU;QAC1C,8BAA8B,EAAE,UAAU;QAC1C,gCAAgC,EAAE,eAAe;QACjD,iCAAiC,EAAE;MACvC,CAAC;MAAEqL,QAAQ,EAAE,WAAW;MAAE1L,eAAe,EAAEvL,uBAAuB,CAAC6L,MAAM;MAAEP,aAAa,EAAErL,iBAAiB,CAAC6L,IAAI;MAAEC,SAAS,EAAE,CAAC;QAAEnB,OAAO,EAAE3G,UAAU;QAAE4G,WAAW,EAAEsB;MAAU,CAAC,CAAC;MAAE1B,UAAU,EAAE,IAAI;MAAEuB,OAAO,EAAE,CAAC1H,oBAAoB,CAAC;MAAE4G,QAAQ,EAAE,2iCAA2iC;MAAEG,MAAM,EAAE,CAAC,42aAA42a;IAAE,CAAC;EAC9pd,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvB,IAAI,EAAElK,EAAE,CAAC8J;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAElK,EAAE,CAAC6J;EAAkB,CAAC,EAAE;IAAEK,IAAI,EAAElK,EAAE,CAAC+J;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAEpK,EAAE,CAAC0W,cAAc;IAAElK,UAAU,EAAE,CAAC;MACrIpC,IAAI,EAAEtJ;IACV,CAAC;EAAE,CAAC,EAAE;IAAEsJ,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCpC,IAAI,EAAEtJ;IACV,CAAC,EAAE;MACCsJ,IAAI,EAAE5J,MAAM;MACZwL,IAAI,EAAE,CAACtK,yBAAyB;IACpC,CAAC;EAAE,CAAC,EAAE;IAAE0I,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCpC,IAAI,EAAEtJ;IACV,CAAC,EAAE;MACCsJ,IAAI,EAAE5J,MAAM;MACZwL,IAAI,EAAE,CAACrL,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE6R,YAAY,EAAE,CAAC;MACxCpI,IAAI,EAAE1J,SAAS;MACfsL,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC,CAAC;IAAE2J,OAAO,EAAE,CAAC;MACVvL,IAAI,EAAErJ,YAAY;MAClBiL,IAAI,EAAE,CAACtH,uBAAuB;IAClC,CAAC,CAAC;IAAE8Q,MAAM,EAAE,CAAC;MACTpL,IAAI,EAAEpJ,YAAY;MAClBgL,IAAI,EAAE,CAACxH,gBAAgB;IAC3B,CAAC,CAAC;IAAEiR,OAAO,EAAE,CAAC;MACVrL,IAAI,EAAEnJ,eAAe;MACrB+K,IAAI,EAAE,CAACvH,sBAAsB,EAAE;QAAEoT,WAAW,EAAE;MAAM,CAAC;IACzD,CAAC,CAAC;IAAEtP,QAAQ,EAAE,CAAC;MACX6B,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAEhS;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuD,QAAQ,EAAE,CAAC;MACXiG,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAEhS;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsO,aAAa,EAAE,CAAC;MAChB9E,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAEhS;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoM,GAAG,EAAE,CAAC;MACN5C,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAE/R;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEsW,KAAK,EAAE,CAAC;MACR/M,IAAI,EAAE3J;IACV,CAAC,CAAC;IAAE0O,aAAa,EAAE,CAAC;MAChB/E,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAEhS;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkN,GAAG,EAAE,CAAC;MACN1D,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAE/R;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAE6N,IAAI,EAAE,CAAC;MACPtE,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAE/R;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEuO,WAAW,EAAE,CAAC;MACdhF,IAAI,EAAE3J;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAAS8P,eAAeA,CAACuH,OAAO,EAAEC,eAAe,EAAEC,iBAAiB,EAAE;EAClE,MAAMC,UAAU,GAAG,CAACH,OAAO,IAAIE,iBAAiB,EAAErS,YAAY,CAACuS,YAAY,CAAC,qBAAqB,CAAC;EAClG,MAAMC,QAAQ,GAAGJ,eAAe,CAACpS,YAAY,CAACuS,YAAY,CAACJ,OAAO,GAAG,mBAAmB,GAAG,gBAAgB,CAAC;EAC5G,IAAI,CAACG,UAAU,IAAI,CAACE,QAAQ,EAAE;IAC1BC,oCAAoC,CAAC,CAAC;EAC1C;AACJ;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,MAAMC,KAAK,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG;EACpCpN,OAAO,EAAErJ,iBAAiB;EAC1BsJ,WAAW,EAAEjK,UAAU,CAAC,MAAMqX,cAAc,CAAC;EAC7CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMC,qCAAqC,GAAG;EAC1CvN,OAAO,EAAErJ,iBAAiB;EAC1BsJ,WAAW,EAAEjK,UAAU,CAAC,MAAMwX,mBAAmB,CAAC;EAClDF,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMD,cAAc,CAAC;EACjB,IAAI5K,KAAKA,CAAA,EAAG;IACR,OAAO9M,eAAe,CAAC,IAAI,CAAC8E,YAAY,CAACgI,KAAK,EAAE,CAAC,CAAC;EACtD;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACbA,KAAK,GAAGT,KAAK,CAACS,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IAChC,MAAMgL,WAAW,GAAGhL,KAAK,GAAG,EAAE;IAC9B,IAAI,CAAC,IAAI,CAACiL,mBAAmB,EAAE;MAC3B,IAAI,CAACC,aAAa,GAAGF,WAAW;MAChC;IACJ;IACA,IAAI,IAAI,CAACxT,SAAS,EAAE;MAChB;IACJ;IACA,IAAI,CAAC2T,SAAS,CAACH,WAAW,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIG,SAASA,CAACnL,KAAK,EAAE;IACb,IAAI,CAAChI,YAAY,CAACgI,KAAK,GAAGA,KAAK;IAC/B,IAAI,CAACQ,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAAClJ,OAAO,CAACgJ,cAAc,CAAC,IAAI,CAAC;IACjC,IAAI,CAACnJ,IAAI,CAACwL,aAAa,CAAC,CAAC;IACzB,IAAI,CAACrL,OAAO,CAACH,IAAI,CAACuO,YAAY,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACI,IAAI3B,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAACzM,OAAO,CAAC+H,GAAG,IAAI,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,EAAE;MACtC,IAAI,CAACiL,WAAW,GAAG,IAAI,CAACC,eAAe;MACvC,OAAO,IAAI,CAACD,WAAW;IAC3B;IACA,IAAI,IAAI,CAACA,WAAW,KAAKxM,SAAS,EAAE;MAChC,IAAI,CAACwM,WAAW,GAAG,IAAI,CAACpH,sBAAsB,CAAC,CAAC;IACpD;IACA,OAAO,IAAI,CAACoH,WAAW;EAC3B;EACA,IAAIrH,UAAUA,CAAC/E,CAAC,EAAE;IACd,IAAI,CAACoM,WAAW,GAAGpM,CAAC;EACxB;EACA;EACA,IAAIK,GAAGA,CAAA,EAAG;IACN,OAAOnM,eAAe,CAAC,IAAI,CAAC8E,YAAY,CAACqH,GAAG,EAAE,CAAC,CAAC;EACpD;EACA,IAAIA,GAAGA,CAACL,CAAC,EAAE;IACP,IAAI,CAAChH,YAAY,CAACqH,GAAG,GAAGL,CAAC,GAAG,EAAE;IAC9B,IAAI,CAAC7H,IAAI,CAACwL,aAAa,CAAC,CAAC;EAC7B;EACA;EACA,IAAIxC,GAAGA,CAAA,EAAG;IACN,OAAOjN,eAAe,CAAC,IAAI,CAAC8E,YAAY,CAACmI,GAAG,EAAE,CAAC,CAAC;EACpD;EACA,IAAIA,GAAGA,CAACnB,CAAC,EAAE;IACP,IAAI,CAAChH,YAAY,CAACmI,GAAG,GAAGnB,CAAC,GAAG,EAAE;IAC9B,IAAI,CAAC7H,IAAI,CAACwL,aAAa,CAAC,CAAC;EAC7B;EACA,IAAI5B,IAAIA,CAAA,EAAG;IACP,OAAO7N,eAAe,CAAC,IAAI,CAAC8E,YAAY,CAAC+I,IAAI,EAAE,CAAC,CAAC;EACrD;EACA,IAAIA,IAAIA,CAAC/B,CAAC,EAAE;IACR,IAAI,CAAChH,YAAY,CAAC+I,IAAI,GAAG/B,CAAC,GAAG,EAAE;IAC/B,IAAI,CAAC7H,IAAI,CAACwL,aAAa,CAAC,CAAC;EAC7B;EACA;EACA,IAAI/H,QAAQA,CAAA,EAAG;IACX,OAAO3H,gBAAgB,CAAC,IAAI,CAAC+E,YAAY,CAAC4C,QAAQ,CAAC;EACvD;EACA,IAAIA,QAAQA,CAACoE,CAAC,EAAE;IACZ,IAAI,CAAChH,YAAY,CAAC4C,QAAQ,GAAGoE,CAAC;IAC9B,IAAI,CAAC7H,IAAI,CAACwL,aAAa,CAAC,CAAC;IACzB,IAAI,IAAI,CAACrL,OAAO,CAACsD,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;MACzC,IAAI,CAACtD,OAAO,CAACsD,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACzC;EACJ;EACA;EACA,IAAI4L,UAAUA,CAAA,EAAG;IACb,IAAI,IAAI,CAAClP,OAAO,CAAC+H,GAAG,IAAI,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,EAAE;MACtC,OAAO,IAAI,CAAC7I,OAAO,CAACsK,MAAM,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,OAAO,CAAC,IAAI,CAAC5B,KAAK,GAAG,IAAI,CAAC1I,OAAO,CAAC+H,GAAG,KAAK,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC+H,GAAG,CAAC;EAClF;EACA;EACA,IAAIyH,cAAcA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACxP,OAAO,CAACjB,YAAY,EAAE;MAC5B,OAAO,IAAI,CAACiB,OAAO,CAACsK,MAAM,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,IAAI,IAAI,CAACwJ,WAAW,KAAK,CAAC,EAAE;MACxB,OAAO,CAAC;IACZ;IACA,OAAO,IAAI,CAACrH,UAAU,GAAG,IAAI,CAACzM,OAAO,CAACjB,YAAY;EACtD;EACA;EACAiV,aAAaA,CAACtM,CAAC,EAAE;IACb,IAAI,CAAClH,UAAU,GAAGkH,CAAC;EACvB;EACA9H,WAAWA,CAACE,OAAO,EAAEC,WAAW,EAAEF,IAAI,EAAEG,OAAO,EAAE;IAC7C,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACiU,WAAW,GAAG,IAAI/X,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACgY,SAAS,GAAG,IAAIhY,YAAY,CAAC,CAAC;IACnC;IACA,IAAI,CAACiY,OAAO,GAAG,IAAIjY,YAAY,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACkG,aAAa,GAAGhD,SAAS,CAACwE,GAAG;IAClC;IACA,IAAI,CAACgL,UAAU,GAAGzS,MAAM,CAAC,EAAE,CAAC;IAC5B;IACA,IAAI,CAACwO,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACoJ,eAAe,GAAG,CAAC;IACxB;IACA,IAAI,CAAC7T,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAACM,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmT,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACS,UAAU,GAAG,IAAIvX,OAAO,CAAC,CAAC;IAC/B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACoQ,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACoH,YAAY,GAAG,MAAM,CAAE,CAAC;IAC7B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAAClU,SAAS,GAAGjF,MAAM,CAACH,QAAQ,CAAC;IACjC,IAAI,CAAC0F,YAAY,GAAGX,WAAW,CAACgC,aAAa;IAC7C,IAAI,CAACjC,OAAO,CAACyC,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC7B,YAAY,CAAC8B,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAAC+R,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MACjF,IAAI,CAAC9T,YAAY,CAAC8B,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACnC,cAAc,CAACmU,IAAI,CAAC,IAAI,CAAC,CAAC;MACjF,IAAI,CAAC9T,YAAY,CAAC8B,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACiS,YAAY,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;IACjF,CAAC,CAAC;EACN;EACA/R,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/B,YAAY,CAACgC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC6R,cAAc,CAAC;IACzE,IAAI,CAAC7T,YAAY,CAACgC,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACrC,cAAc,CAAC;IACzE,IAAI,CAACK,YAAY,CAACgC,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC+R,YAAY,CAAC;IACrE,IAAI,CAACL,UAAU,CAACM,IAAI,CAAC,CAAC;IACtB,IAAI,CAACN,UAAU,CAACO,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACT,SAAS,CAACS,QAAQ,CAAC,CAAC;IACzB,IAAI,CAACR,OAAO,CAACQ,QAAQ,CAAC,CAAC;EAC3B;EACA;EACA7I,SAASA,CAAA,EAAG;IACR,IAAI,CAAChD,oBAAoB,CAAC,CAAC;IAC3B;IACA,IAAI,IAAI,CAACxF,QAAQ,KAAK,IAAI,CAACtD,OAAO,CAACsD,QAAQ,EAAE;MACzC;MACA,IAAI,CAACtD,OAAO,CAACsD,QAAQ,GAAG,IAAI;IAChC;IACA,IAAI,CAACmG,IAAI,GAAG,IAAI,CAACzJ,OAAO,CAACyJ,IAAI;IAC7B,IAAI,CAAC1B,GAAG,GAAG,IAAI,CAAC/H,OAAO,CAAC+H,GAAG;IAC3B,IAAI,CAACc,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC6I,GAAG;IAC3B,IAAI,CAAC+L,UAAU,CAAC,CAAC;EACrB;EACA;EACA7I,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC7C,qBAAqB,CAAC,CAAC;EAChC;EACA0L,UAAUA,CAAA,EAAG;IACT,IAAI,CAACjB,mBAAmB,GAAG,IAAI;IAC/B,IAAI,IAAI,CAACC,aAAa,KAAKtM,SAAS,EAAE;MAClC,IAAI,CAACoB,KAAK,GAAG,IAAI,CAACmM,gBAAgB,CAAC,CAAC;IACxC,CAAC,MACI;MACD,IAAI,CAACnU,YAAY,CAACgI,KAAK,GAAG,IAAI,CAACkL,aAAa;MAC5C,IAAI,CAAC1K,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAAClJ,OAAO,CAACgJ,cAAc,CAAC,IAAI,CAAC;MACjC,IAAI,CAACnJ,IAAI,CAACwL,aAAa,CAAC,CAAC;IAC7B;EACJ;EACAwJ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC9M,GAAG;EACnB;EACAzG,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC0S,aAAa,CAAC,KAAK,CAAC;IACzB,IAAI,CAACK,YAAY,CAAC,CAAC;EACvB;EACAnT,QAAQA,CAAA,EAAG;IACP,IAAI,CAAClB,OAAO,CAAC2Q,cAAc,CAAC,KAAK,CAAC;IAClC,IAAI,CAAC3Q,OAAO,CAACmJ,cAAc,CAAC,IAAI,CAAC;IACjC,IAAI,CAAC6K,aAAa,CAAC,IAAI,CAAC;EAC5B;EACAc,SAASA,CAAA,EAAG;IACR,IAAI,CAACb,WAAW,CAACc,IAAI,CAAC,IAAI,CAACrM,KAAK,CAAC;IACjC;IACA;IACA,IAAI,IAAI,CAACxI,SAAS,EAAE;MAChB,IAAI,CAACgJ,qBAAqB,CAAC;QAAE0H,aAAa,EAAE;MAAK,CAAC,CAAC;IACvD;EACJ;EACAoE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,WAAW,GAAG,IAAI,CAACvM,KAAK,CAAC;IAC9B;IACA;IACA,IAAI,IAAI,CAAC1I,OAAO,CAACyJ,IAAI,IAAI,CAAC,IAAI,CAACvJ,SAAS,EAAE;MACtC,IAAI,CAACgJ,qBAAqB,CAAC;QAAE0H,aAAa,EAAE;MAAK,CAAC,CAAC;IACvD;IACA,IAAI,CAAC5Q,OAAO,CAACgJ,cAAc,CAAC,IAAI,CAAC;EACrC;EACAkM,uBAAuBA,CAAA,EAAG;IACtB;IACA;IACA,IAAI,CAAC,IAAI,CAAChV,SAAS,IAAI,CAAC,IAAI,CAACM,UAAU,EAAE;MACrC,IAAI,CAACR,OAAO,CAACgJ,cAAc,CAAC,IAAI,CAAC;MACjC,IAAI,CAACE,qBAAqB,CAAC,CAAC;IAChC;IACA,IAAI,CAAClJ,OAAO,CAACsD,QAAQ,GAAG,IAAI,CAAC6R,YAAY,CAAC7R,QAAQ;EACtD;EACAiR,cAAcA,CAACjU,KAAK,EAAE;IAClB,IAAI,IAAI,CAACgD,QAAQ,IAAIhD,KAAK,CAACoB,MAAM,KAAK,CAAC,EAAE;MACrC;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACtB,SAAS,CAACyQ,GAAG,EAAE;MACpB,MAAMuE,qBAAqB,GAAG,IAAI,CAACpV,OAAO,CAACa,sBAAsB,CAACP,KAAK,EAAE,IAAI,CAACN,OAAO,CAAC0D,SAAS,CAAC,IAAI,CAACtB,aAAa,CAAC,CAAC1B,YAAY,CAACC,qBAAqB,CAAC,CAAC,CAAC;MACzJ,IAAI,CAACT,SAAS,GAAGkV,qBAAqB;MACtC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACrV,OAAO,CAACkL,iBAAiB,CAAC,CAAC;MAChC;IACJ;IACA,IAAI,CAAChL,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC8T,aAAa,CAAC,IAAI,CAAC;IACxB,IAAI,CAACqB,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACrV,OAAO,CAACkL,iBAAiB,CAAC,CAAC;IAChC;IACA;IACA,IAAI,CAAC,IAAI,CAAClL,OAAO,CAACyJ,IAAI,EAAE;MACpB,IAAI,CAAC6L,4BAA4B,CAAChV,KAAK,EAAE;QAAEsQ,aAAa,EAAE;MAAK,CAAC,CAAC;IACrE;IACA,IAAI,CAAC,IAAI,CAACtN,QAAQ,EAAE;MAChB,IAAI,CAACiS,sBAAsB,CAACjV,KAAK,CAAC;MAClC,IAAI,CAAC4T,SAAS,CAACa,IAAI,CAAC;QAAEhH,MAAM,EAAE,IAAI;QAAEyH,MAAM,EAAE,IAAI,CAACxV,OAAO;QAAE0I,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;IAClF;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI6M,sBAAsBA,CAACjV,KAAK,EAAE;IAC1B;IACA;IACA;IACA;IACA,IAAI,CAAC2M,aAAa,GAAG,IAAI;IACzB;IACA;IACA;IACA;IACA;IACAwI,UAAU,CAAC,MAAM;MACb,IAAI,CAACxI,aAAa,GAAG,KAAK;MAC1B,IAAI,CAACyI,SAAS,CAACpV,KAAK,CAAC;IACzB,CAAC,EAAE,CAAC,CAAC;EACT;EACA;EACAoV,SAASA,CAACpV,KAAK,EAAE;IACb,MAAMqV,IAAI,GAAGrV,KAAK,CAAC8Q,OAAO,GAAG,IAAI,CAACpR,OAAO,CAACmN,WAAW;IACrD,MAAM2D,KAAK,GAAG,IAAI,CAAC9Q,OAAO,CAACjB,YAAY;IACvC,MAAM0K,IAAI,GAAG,IAAI,CAACzJ,OAAO,CAACyJ,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAACzJ,OAAO,CAACyJ,IAAI;IAC5D,MAAMmM,QAAQ,GAAGhN,IAAI,CAACqG,KAAK,CAAC,CAAC,IAAI,CAACjP,OAAO,CAAC6I,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC+H,GAAG,IAAI0B,IAAI,CAAC;IACzE,MAAMyF,UAAU,GAAG,IAAI,CAAClP,OAAO,CAACsK,MAAM,GAAG,CAAC,GAAGqL,IAAI,GAAG7E,KAAK,GAAG6E,IAAI,GAAG7E,KAAK;IACxE;IACA,MAAM+E,eAAe,GAAGjN,IAAI,CAACkN,KAAK,CAAC5G,UAAU,GAAG0G,QAAQ,CAAC,GAAGA,QAAQ;IACpE,MAAMG,cAAc,GAAGF,eAAe,IAAI,IAAI,CAAC7V,OAAO,CAAC6I,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC+H,GAAG,CAAC,GAAG,IAAI,CAAC/H,OAAO,CAAC+H,GAAG;IACjG,MAAMW,KAAK,GAAGE,IAAI,CAACkN,KAAK,CAACC,cAAc,GAAGtM,IAAI,CAAC,GAAGA,IAAI;IACtD,MAAMuM,SAAS,GAAG,IAAI,CAACtN,KAAK;IAC5B,IAAIA,KAAK,KAAKsN,SAAS,EAAE;MACrB;MACA;MACA;MACA,IAAI,CAAChW,OAAO,CAACgJ,cAAc,CAAC,IAAI,CAAC;MACjC,IAAI,CAAChJ,OAAO,CAACyJ,IAAI,GAAG,CAAC,GACf,IAAI,CAACP,qBAAqB,CAAC,CAAC,GAC5B,IAAI,CAACoM,4BAA4B,CAAChV,KAAK,EAAE;QAAEsQ,aAAa,EAAE,IAAI,CAAC5Q,OAAO,CAACyK;MAAc,CAAC,CAAC;MAC7F;IACJ;IACA,IAAI,CAAC/B,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACuL,WAAW,CAACc,IAAI,CAAC,IAAI,CAACrM,KAAK,CAAC;IACjC,IAAI,CAACuM,WAAW,GAAG,IAAI,CAACvM,KAAK,CAAC;IAC9B,IAAI,CAAC1I,OAAO,CAACgJ,cAAc,CAAC,IAAI,CAAC;IACjC,IAAI,CAAChJ,OAAO,CAACyJ,IAAI,GAAG,CAAC,GACf,IAAI,CAACP,qBAAqB,CAAC,CAAC,GAC5B,IAAI,CAACoM,4BAA4B,CAAChV,KAAK,EAAE;MAAEsQ,aAAa,EAAE,IAAI,CAAC5Q,OAAO,CAACyK;IAAc,CAAC,CAAC;EACjG;EACApK,cAAcA,CAACC,KAAK,EAAE;IAClB;IACA;IACA,IAAI,CAAC,IAAI,CAACN,OAAO,CAACyJ,IAAI,IAAI,IAAI,CAACvJ,SAAS,EAAE;MACtC,IAAI,CAACoV,4BAA4B,CAAChV,KAAK,CAAC;IAC5C;EACJ;EACAmU,YAAYA,CAAA,EAAG;IACX,IAAI,IAAI,CAACvU,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,IAAI,CAACE,SAAS,CAAC0B,MAAM,EAAE;QACvB,IAAI,CAACkS,aAAa,CAAC,KAAK,CAAC;MAC7B;MACA,IAAI,CAACG,OAAO,CAACY,IAAI,CAAC;QAAEhH,MAAM,EAAE,IAAI;QAAEyH,MAAM,EAAE,IAAI,CAACxV,OAAO;QAAE0I,KAAK,EAAE,IAAI,CAACA;MAAM,CAAC,CAAC;MAC5E;MACA;MACA;MACA;MACA+M,UAAU,CAAC,MAAM,IAAI,CAAC3M,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC1I,SAAS,CAACyQ,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;IAC9E;EACJ;EACAoF,MAAMA,CAACvO,CAAC,EAAE;IACN,MAAMK,GAAG,GAAG,IAAI,CAACgM,eAAe;IAChC,MAAMlL,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAACjB,YAAY,GAAG,IAAI,CAACgV,eAAe;IAC5D,OAAOnL,IAAI,CAACC,GAAG,CAACD,IAAI,CAACb,GAAG,CAACL,CAAC,EAAEmB,GAAG,CAAC,EAAEd,GAAG,CAAC;EAC1C;EACA2E,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC1M,OAAO,CAACsK,MAAM,EAAE;MACrB,OAAQ,CAAC,CAAC,GAAG,IAAI,CAAC4E,UAAU,KAAK,IAAI,CAAClP,OAAO,CAACjB,YAAY,GAAG,IAAI,CAACgV,eAAe,GAAG,CAAC,CAAC,GAClF,IAAI,CAACA,eAAe;IAC5B;IACA,OAAQ,IAAI,CAAC7E,UAAU,IAAI,IAAI,CAAClP,OAAO,CAACjB,YAAY,GAAG,IAAI,CAACgV,eAAe,GAAG,CAAC,CAAC,GAC5E,IAAI,CAACA,eAAe;EAC5B;EACAmC,6BAA6BA,CAAC5V,KAAK,EAAE;IACjC,OAAOA,KAAK,CAAC8Q,OAAO,GAAG,IAAI,CAACpR,OAAO,CAACmN,WAAW;EACnD;EACA;AACJ;AACA;AACA;EACIkI,kBAAkBA,CAAA,EAAG,CAAE;EACvB;AACJ;AACA;AACA;EACIvM,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACpI,YAAY,CAAC8M,KAAK,CAAC2I,OAAO,GAAG,KAAK,IAAI,CAACnW,OAAO,CAACwL,aAAa,IAAI;IACrE,IAAI,CAAC9K,YAAY,CAAC8M,KAAK,CAACsD,KAAK,GAAG,eAAe,IAAI,CAAC9Q,OAAO,CAACwL,aAAa,GAAG,IAAI,CAACuI,eAAe,GAAG,CAAC,KAAK;IACzG,IAAI,CAACrT,YAAY,CAAC8M,KAAK,CAACJ,IAAI,GAAG,IAAI,IAAI,CAACpN,OAAO,CAACoK,aAAa,GAAG,IAAI,CAAC2J,eAAe,IAAI;EAC5F;EACA7K,qBAAqBA,CAACkN,OAAO,EAAE;IAC3B,IAAI,CAAC3J,UAAU,GAAG,IAAI,CAACwJ,MAAM,CAAC,IAAI,CAACvJ,sBAAsB,CAAC,CAAC,CAAC;IAC5D,IAAI,CAACsB,cAAc,CAACoI,OAAO,CAAC;EAChC;EACAd,4BAA4BA,CAAChV,KAAK,EAAE8V,OAAO,EAAE;IACzC,IAAI,CAAC3J,UAAU,GAAG,IAAI,CAACwJ,MAAM,CAAC,IAAI,CAACC,6BAA6B,CAAC5V,KAAK,CAAC,CAAC;IACxE,IAAI,CAAC0N,cAAc,CAACoI,OAAO,CAAC;EAChC;EACApI,cAAcA,CAACoI,OAAO,EAAE;IACpB,IAAI,CAACpW,OAAO,CAAC2Q,cAAc,CAAC,CAAC,CAACyF,OAAO,EAAExF,aAAa,CAAC;IACrD,IAAI,CAAC5Q,OAAO,CAAC8N,mBAAmB,CAAC,IAAI,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;EACIuI,UAAUA,CAAC3N,KAAK,EAAE;IACd,IAAI,IAAI,CAAC4L,qBAAqB,IAAI5L,KAAK,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI4N,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACtB,WAAW,GAAGsB,EAAE;IACrB,IAAI,CAACjC,qBAAqB,GAAG,IAAI;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIkC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAClC,YAAY,GAAGkC,EAAE;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACIE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACpT,QAAQ,GAAGoT,UAAU;EAC9B;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjW,YAAY,CAACiW,KAAK,CAAC,CAAC;EAC7B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAAClW,YAAY,CAACkW,IAAI,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAClS,IAAI,YAAAmS,uBAAAjS,CAAA;MAAA,YAAAA,CAAA,IAAwF0O,cAAc,EAprCxBrY,EAAE,CAAA4J,iBAAA,CAorCwC5J,EAAE,CAAC8J,MAAM,GAprCnD9J,EAAE,CAAA4J,iBAAA,CAorC8D5J,EAAE,CAAC+J,UAAU,GAprC7E/J,EAAE,CAAA4J,iBAAA,CAorCwF5J,EAAE,CAAC6J,iBAAiB,GAprC9G7J,EAAE,CAAA4J,iBAAA,CAorCyHvF,UAAU;IAAA,CAA4C;EAAE;EACnR;IAAS,IAAI,CAACwX,IAAI,kBArrC8E7b,EAAE,CAAA8b,iBAAA;MAAA5R,IAAA,EAqrCJmO,cAAc;MAAAlO,SAAA;MAAAQ,SAAA,WAAoO,OAAO;MAAAmM,QAAA;MAAAC,YAAA,WAAAgF,4BAAA/Z,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArrCvPhC,EAAE,CAAAgc,UAAA,oBAAAC,yCAAA;YAAA,OAqrCJha,GAAA,CAAA4X,SAAA,CAAU,CAAC;UAAA,CAAE,CAAC,mBAAAqC,wCAAA;YAAA,OAAdja,GAAA,CAAA8X,QAAA,CAAS,CAAC;UAAA,CAAG,CAAC,kBAAAoC,uCAAA;YAAA,OAAdla,GAAA,CAAAoE,OAAA,CAAQ,CAAC;UAAA,CAAI,CAAC,mBAAA+V,wCAAA;YAAA,OAAdna,GAAA,CAAAgE,QAAA,CAAS,CAAC;UAAA,CAAG,CAAC;QAAA;QAAA,IAAAjE,EAAA;UArrCZhC,EAAE,CAAAqc,WAAA,mBAqrCJpa,GAAA,CAAA0R,UAAA,CAAW,CAAC;QAAA;MAAA;MAAA/I,MAAA;QAAA6C,KAAA,GArrCVzN,EAAE,CAAAmX,YAAA,CAAAC,0BAAA,oBAqrCuGzW,eAAe;MAAA;MAAA2b,OAAA;QAAAtD,WAAA;QAAAC,SAAA;QAAAC,OAAA;MAAA;MAAA7B,QAAA;MAAAxM,UAAA;MAAAC,QAAA,GArrCxH9K,EAAE,CAAA+K,kBAAA,CAqrC6c,CACviBqN,+BAA+B,EAC/B;QAAEpN,OAAO,EAAE1G,gBAAgB;QAAE2G,WAAW,EAAEoN;MAAe,CAAC,CAC7D,GAxrC2FrY,EAAE,CAAAsX,wBAAA;IAAA,EAwrC9C;EAAE;AAC1D;AACA;EAAA,QAAA1L,SAAA,oBAAAA,SAAA,KA1rCoG5L,EAAE,CAAA6L,iBAAA,CA0rCXwM,cAAc,EAAc,CAAC;IAC5GnO,IAAI,EAAE/I,SAAS;IACf2K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCsL,QAAQ,EAAE,gBAAgB;MAC1BrL,IAAI,EAAE;QACF,OAAO,EAAE,mBAAmB;QAC5B,MAAM,EAAE,OAAO;QACf,uBAAuB,EAAE,cAAc;QACvC,UAAU,EAAE,aAAa;QACzB,SAAS,EAAE,YAAY;QACvB;QACA;QACA,QAAQ,EAAE,WAAW;QACrB,SAAS,EAAE;MACf,CAAC;MACDG,SAAS,EAAE,CACPiM,+BAA+B,EAC/B;QAAEpN,OAAO,EAAE1G,gBAAgB;QAAE2G,WAAW,EAAEoN;MAAe,CAAC,CAC7D;MACDxN,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAElK,EAAE,CAAC8J;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAElK,EAAE,CAAC+J;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAElK,EAAE,CAAC6J;EAAkB,CAAC,EAAE;IAAEK,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7HpC,IAAI,EAAE5J,MAAM;MACZwL,IAAI,EAAE,CAACzH,UAAU;IACrB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEoJ,KAAK,EAAE,CAAC;MACjCvD,IAAI,EAAE3J,KAAK;MACXuL,IAAI,EAAE,CAAC;QAAE4G,SAAS,EAAE/R;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEqY,WAAW,EAAE,CAAC;MACd9O,IAAI,EAAE9I;IACV,CAAC,CAAC;IAAE6X,SAAS,EAAE,CAAC;MACZ/O,IAAI,EAAE9I;IACV,CAAC,CAAC;IAAE8X,OAAO,EAAE,CAAC;MACVhP,IAAI,EAAE9I;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMoX,mBAAmB,SAASH,cAAc,CAAC;EAC7C;EACA/E,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACiJ,QAAQ,EAAE;MAChB,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACxX,OAAO,CAACmC,SAAS,CAAC,IAAI,CAACsV,WAAW,GAAGrY,SAAS,CAACuE,KAAK,GAAGvE,SAAS,CAACwE,GAAG,CAAC;IAC9F;IACA,OAAO,IAAI,CAAC4T,QAAQ;EACxB;EACA;AACJ;AACA;AACA;EACIE,SAASA,CAAA,EAAG;IACR,MAAMjU,OAAO,GAAG,IAAI,CAAC8K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC,IAAI,CAACgB,YAAY,IAAI9L,OAAO,EAAE;MAC/B,OAAOA,OAAO,CAACgJ,UAAU;IAC7B;IACA,OAAO,IAAI,CAACsH,eAAe;EAC/B;EACA;AACJ;AACA;AACA;EACI4D,SAASA,CAAA,EAAG;IACR,MAAMlU,OAAO,GAAG,IAAI,CAAC8K,UAAU,CAAC,CAAC;IACjC,IAAI,IAAI,CAACgB,YAAY,IAAI9L,OAAO,EAAE;MAC9B,OAAOA,OAAO,CAACgJ,UAAU;IAC7B;IACA,OAAO,IAAI,CAACzM,OAAO,CAACjB,YAAY,GAAG,IAAI,CAACgV,eAAe;EAC3D;EACAvH,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC+C,YAAY,GACZ,IAAI,CAACkI,WAAW,IAAI,IAAI,CAACzX,OAAO,CAACsK,MAAM,IAAM,CAAC,IAAI,CAACmN,WAAW,IAAI,CAAC,IAAI,CAACzX,OAAO,CAACsK,MAAO;EAChG;EACA1K,WAAWA,CAACE,OAAO,EAAEE,OAAO,EAAED,WAAW,EAAEF,IAAI,EAAE;IAC7C,KAAK,CAACC,OAAO,EAAEC,WAAW,EAAEF,IAAI,EAAEG,OAAO,CAAC;IAC1C,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC4X,WAAW,GAAG,IAAI,CAAC/W,YAAY,CAACuS,YAAY,CAAC,mBAAmB,CAAC;IACtE,IAAI,CAACzG,eAAe,CAAC,CAAC;IACtB,IAAI,CAACpK,aAAa,GAAG,IAAI,CAACqV,WAAW,GAAGrY,SAAS,CAACwE,GAAG,GAAGxE,SAAS,CAACuE,KAAK;EAC3E;EACAkR,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC4C,WAAW,IAAI,IAAI,CAACzX,OAAO,CAACwD,QAAQ,GAAG,IAAI,CAACqF,GAAG,GAAG,IAAI,CAACd,GAAG;EAC1E;EACAiN,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAAC4C,cAAc,CAAC,CAAC;IACrB,IAAI,CAAC,IAAI,CAAC1X,SAAS,EAAE;MACjB,IAAI,CAAC4I,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACAoM,uBAAuBA,CAAA,EAAG;IACtB,KAAK,CAACA,uBAAuB,CAAC,CAAC;IAC/B,IAAI,CAAC3G,UAAU,CAAC,CAAC,EAAEtC,aAAa,CAAC,CAAC;EACtC;EACAsI,cAAcA,CAACjU,KAAK,EAAE;IAClB,IAAI,IAAI,CAACgD,QAAQ,IAAIhD,KAAK,CAACoB,MAAM,KAAK,CAAC,EAAE;MACrC;IACJ;IACA,IAAI,IAAI,CAAC8V,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACnC,kBAAkB,CAAC,CAAC;MAClC,IAAI,CAACmC,QAAQ,CAAC9W,YAAY,CAACU,SAAS,CAACC,GAAG,CAAC,wCAAwC,CAAC;IACtF;IACA,KAAK,CAACkT,cAAc,CAACjU,KAAK,CAAC;EAC/B;EACAmU,YAAYA,CAAA,EAAG;IACX,KAAK,CAACA,YAAY,CAAC,CAAC;IACpB,IAAI,IAAI,CAAC+C,QAAQ,EAAE;MACf/B,UAAU,CAAC,MAAM;QACb,IAAI,CAAC+B,QAAQ,CAAC1O,oBAAoB,CAAC,CAAC;QACpC,IAAI,CAAC0O,QAAQ,CAAC9W,YAAY,CAACU,SAAS,CAACI,MAAM,CAAC,wCAAwC,CAAC;MACzF,CAAC,CAAC;IACN;EACJ;EACAnB,cAAcA,CAACC,KAAK,EAAE;IAClB,KAAK,CAACD,cAAc,CAACC,KAAK,CAAC;IAC3B,IAAI,CAAC,IAAI,CAACN,OAAO,CAACyJ,IAAI,IAAI,IAAI,CAACvJ,SAAS,EAAE;MACtC,IAAI,CAAC0X,cAAc,CAAC,CAAC;IACzB;EACJ;EACAlC,SAASA,CAACpV,KAAK,EAAE;IACb,KAAK,CAACoV,SAAS,CAACpV,KAAK,CAAC;IACtB,IAAI,CAACkX,QAAQ,EAAEvL,aAAa,CAAC,CAAC;EAClC;EACAgK,MAAMA,CAACvO,CAAC,EAAE;IACN,OAAOkB,IAAI,CAACC,GAAG,CAACD,IAAI,CAACb,GAAG,CAACL,CAAC,EAAE,IAAI,CAACiQ,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAACD,SAAS,CAAC,CAAC,CAAC;EACpE;EACAzL,aAAaA,CAAA,EAAG;IACZ,MAAMxI,OAAO,GAAG,IAAI,CAAC8K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC9K,OAAO,EAAE;MACV;IACJ;IACA,IAAI,IAAI,CAACgU,WAAW,EAAE;MAClB,IAAI,CAAC1P,GAAG,GAAGa,IAAI,CAACC,GAAG,CAAC,IAAI,CAAC7I,OAAO,CAAC+H,GAAG,EAAEtE,OAAO,CAACiF,KAAK,CAAC;MACpD,IAAI,CAACG,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC6I,GAAG;IAC/B,CAAC,MACI;MACD,IAAI,CAACd,GAAG,GAAG,IAAI,CAAC/H,OAAO,CAAC+H,GAAG;MAC3B,IAAI,CAACc,GAAG,GAAGD,IAAI,CAACb,GAAG,CAAC,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,EAAEpF,OAAO,CAACiF,KAAK,CAAC;IACxD;EACJ;EACA2M,kBAAkBA,CAAA,EAAG;IACjB,MAAMwC,QAAQ,GAAG,IAAI,CAAC7X,OAAO,CAACoK,aAAa,GAAG,CAAC,GAAG,IAAI,CAACpK,OAAO,CAACwL,aAAa,GAAG,CAAC;IAChF,MAAMsM,QAAQ,GAAG,IAAI,CAAC9X,OAAO,CAACjB,YAAY,GAAG,IAAI,CAACiB,OAAO,CAACwL,aAAa,GAAGqM,QAAQ,GAAG,IAAI,CAAC9D,eAAe,GAAG,CAAC;IAC7G,MAAM7E,UAAU,GAAG,IAAI,CAAClP,OAAO,CAAC+H,GAAG,GAAG,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,GAChD,CAAC,IAAI,CAACA,GAAG,GAAG,IAAI,CAACd,GAAG,KAAK,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC+H,GAAG,CAAC,GAC7D,CAAC;IACP,MAAM+I,KAAK,GAAGgH,QAAQ,GAAG5I,UAAU,GAAG2I,QAAQ;IAC9C,IAAI,CAACnX,YAAY,CAAC8M,KAAK,CAACsD,KAAK,GAAG,GAAGA,KAAK,IAAI;IAC5C,IAAI,CAACpQ,YAAY,CAAC8M,KAAK,CAAC2I,OAAO,GAAG,KAAK,IAAI,CAACnW,OAAO,CAACwL,aAAa,IAAI;EACzE;EACA1C,oBAAoBA,CAAA,EAAG;IACnB,MAAMrF,OAAO,GAAG,IAAI,CAAC8K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC9K,OAAO,EAAE;MACV;IACJ;IACA,MAAMqU,QAAQ,GAAG,IAAI,CAAC9X,OAAO,CAACjB,YAAY,GAAG,IAAI,CAACgV,eAAe,GAAG,CAAC;IACrE,MAAMgE,QAAQ,GAAG,IAAI,CAACN,WAAW,GAC3B,IAAI,CAAC/O,KAAK,GAAG,CAAC,IAAI,CAACA,KAAK,GAAGjF,OAAO,CAACiF,KAAK,IAAI,CAAC,GAC7C,IAAI,CAACA,KAAK,GAAG,CAACjF,OAAO,CAACiF,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC;IACnD,MAAMsP,WAAW,GAAG,IAAI,CAACP,WAAW,GAC9B,CAAC,IAAI,CAAC5O,GAAG,GAAGkP,QAAQ,KAAK,IAAI,CAAC/X,OAAO,CAAC6I,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC+H,GAAG,CAAC,GAC7D,CAACgQ,QAAQ,GAAG,IAAI,CAAChQ,GAAG,KAAK,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,GAAG,IAAI,CAAC7I,OAAO,CAAC+H,GAAG,CAAC;IACnE,MAAMmH,UAAU,GAAG,IAAI,CAAClP,OAAO,CAAC+H,GAAG,GAAG,IAAI,CAAC/H,OAAO,CAAC6I,GAAG,GAAGmP,WAAW,GAAG,CAAC;IACxE;IACA,IAAIC,aAAa,GAAG,IAAI,CAACjY,OAAO,CAACoK,aAAa;IAC9C;IACA;IACA;IACA,IAAI8E,UAAU,KAAK,CAAC,EAAE;MAClB+I,aAAa,GAAG,EAAE;IACtB,CAAC,MACI,IAAI/I,UAAU,KAAK,CAAC,EAAE;MACvB+I,aAAa,GAAG,CAAC;IACrB;IACA,MAAMnH,KAAK,GAAGgH,QAAQ,GAAG5I,UAAU,GAAG+I,aAAa;IACnD,IAAI,CAACvX,YAAY,CAAC8M,KAAK,CAACsD,KAAK,GAAG,GAAGA,KAAK,IAAI;IAC5C,IAAI,CAACpQ,YAAY,CAAC8M,KAAK,CAAC2I,OAAO,GAAG,KAAK;IACvC,IAAI,IAAI,CAAC5G,YAAY,EAAE;MACnB,IAAI,CAAC7O,YAAY,CAAC8M,KAAK,CAACJ,IAAI,GAAG,IAAI,IAAI,CAACpN,OAAO,CAACoK,aAAa,GAAG,IAAI,CAAC2J,eAAe,IAAI;MACxF,IAAI,CAACrT,YAAY,CAAC8M,KAAK,CAACC,KAAK,GAAG,MAAM;IAC1C,CAAC,MACI;MACD,IAAI,CAAC/M,YAAY,CAAC8M,KAAK,CAACJ,IAAI,GAAG,MAAM;MACrC,IAAI,CAAC1M,YAAY,CAAC8M,KAAK,CAACC,KAAK,GAAG,IAAI,IAAI,CAACzN,OAAO,CAACoK,aAAa,GAAG,IAAI,CAAC2J,eAAe,IAAI;IAC7F;EACJ;EACA7H,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACxL,YAAY,CAACU,SAAS,CAACsN,MAAM,CAAC,yBAAyB,EAAE,CAAC,IAAI,CAACa,YAAY,CAAC;EACrF;EACAqI,cAAcA,CAAA,EAAG;IACb,MAAMnU,OAAO,GAAG,IAAI,CAAC8K,UAAU,CAAC,CAAC;IACjC,IAAI,CAAC9K,OAAO,EAAE;MACV;IACJ;IACAA,OAAO,CAACwI,aAAa,CAAC,CAAC;IACvB,IAAI,IAAI,CAAC/L,SAAS,EAAE;MAChBuD,OAAO,CAAC4R,kBAAkB,CAAC,CAAC;IAChC,CAAC,MACI;MACD5R,OAAO,CAACqF,oBAAoB,CAAC,CAAC;IAClC;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIuN,UAAUA,CAAC3N,KAAK,EAAE;IACd,IAAI,IAAI,CAAC4L,qBAAqB,IAAI5L,KAAK,KAAK,IAAI,EAAE;MAC9C,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACI,oBAAoB,CAAC,CAAC;MAC3B,IAAI,CAAC8O,cAAc,CAAC,CAAC;IACzB;EACJ;EACA/D,SAASA,CAACnL,KAAK,EAAE;IACb,KAAK,CAACmL,SAAS,CAACnL,KAAK,CAAC;IACtB,IAAI,CAACI,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAAC8O,cAAc,CAAC,CAAC;EACzB;EACA;IAAS,IAAI,CAAClT,IAAI,YAAAwT,4BAAAtT,CAAA;MAAA,YAAAA,CAAA,IAAwF6O,mBAAmB,EAj5C7BxY,EAAE,CAAA4J,iBAAA,CAi5C6C5J,EAAE,CAAC8J,MAAM,GAj5CxD9J,EAAE,CAAA4J,iBAAA,CAi5CmEvF,UAAU,GAj5C/ErE,EAAE,CAAA4J,iBAAA,CAi5C0F5J,EAAE,CAAC+J,UAAU,GAj5CzG/J,EAAE,CAAA4J,iBAAA,CAi5CoH5J,EAAE,CAAC6J,iBAAiB;IAAA,CAA4C;EAAE;EACxR;IAAS,IAAI,CAACgS,IAAI,kBAl5C8E7b,EAAE,CAAA8b,iBAAA;MAAA5R,IAAA,EAk5CJsO,mBAAmB;MAAArO,SAAA;MAAAkN,QAAA;MAAAxM,UAAA;MAAAC,QAAA,GAl5CjB9K,EAAE,CAAA+K,kBAAA,CAk5CkH,CAC5MwN,qCAAqC,EACrC;QAAEvN,OAAO,EAAEzG,sBAAsB;QAAE0G,WAAW,EAAEuN;MAAoB,CAAC,CACxE,GAr5C2FxY,EAAE,CAAAkd,0BAAA;IAAA,EAq5ClB;EAAE;AACtF;AACA;EAAA,QAAAtR,SAAA,oBAAAA,SAAA,KAv5CoG5L,EAAE,CAAA6L,iBAAA,CAu5CX2M,mBAAmB,EAAc,CAAC;IACjHtO,IAAI,EAAE/I,SAAS;IACf2K,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sDAAsD;MAChEsL,QAAQ,EAAE,qBAAqB;MAC/BlL,SAAS,EAAE,CACPoM,qCAAqC,EACrC;QAAEvN,OAAO,EAAEzG,sBAAsB;QAAE0G,WAAW,EAAEuN;MAAoB,CAAC,CACxE;MACD3N,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAElK,EAAE,CAAC8J;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAEmC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACpEpC,IAAI,EAAE5J,MAAM;MACZwL,IAAI,EAAE,CAACzH,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAE6F,IAAI,EAAElK,EAAE,CAAC+J;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAElK,EAAE,CAAC6J;EAAkB,CAAC,CAAC;AAAA;AAE9E,MAAMsT,eAAe,CAAC;EAClB;IAAS,IAAI,CAAC1T,IAAI,YAAA2T,wBAAAzT,CAAA;MAAA,YAAAA,CAAA,IAAwFwT,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBAz6C8Erd,EAAE,CAAAsd,gBAAA;MAAApT,IAAA,EAy6CSiT;IAAe,EAK/B;EAAE;EAC7F;IAAS,IAAI,CAACI,IAAI,kBA/6C8Evd,EAAE,CAAAwd,gBAAA;MAAApR,OAAA,GA+6CoC3K,eAAe,EAC7IC,eAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAAkK,SAAA,oBAAAA,SAAA,KAl7CoG5L,EAAE,CAAA6L,iBAAA,CAk7CXsR,eAAe,EAAc,CAAC;IAC7GjT,IAAI,EAAE7I,QAAQ;IACdyK,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CACL3K,eAAe,EACfC,eAAe,EACf6K,SAAS,EACT8L,cAAc,EACdG,mBAAmB,EACnB9T,oBAAoB,CACvB;MACD+Y,OAAO,EAAE,CAAClR,SAAS,EAAE8L,cAAc,EAAEG,mBAAmB;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASjM,SAAS,EAAE9H,eAAe,EAAE0Y,eAAe,EAAE3E,mBAAmB,EAAEH,cAAc,EAAE3T,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}