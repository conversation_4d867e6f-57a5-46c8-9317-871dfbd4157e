{"ast": null, "code": "import { registerPlugin } from '@capacitor/core';\nconst SplashScreen = registerPlugin('SplashScreen', {\n  web: () => import('./web').then(m => new m.SplashScreenWeb())\n});\nexport * from './definitions';\nexport { SplashScreen };", "map": {"version": 3, "names": ["registerPlugin", "SplashScreen", "web", "then", "m", "SplashScreenWeb"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@capacitor/splash-screen/dist/esm/index.js"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\nconst SplashScreen = registerPlugin('SplashScreen', {\n    web: () => import('./web').then(m => new m.SplashScreenWeb()),\n});\nexport * from './definitions';\nexport { SplashScreen };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,iBAAiB;AAChD,MAAMC,YAAY,GAAGD,cAAc,CAAC,cAAc,EAAE;EAChDE,GAAG,EAAEA,CAAA,KAAM,MAAM,CAAC,OAAO,CAAC,CAACC,IAAI,CAACC,CAAC,IAAI,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAC;AAChE,CAAC,CAAC;AACF,cAAc,eAAe;AAC7B,SAASJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}