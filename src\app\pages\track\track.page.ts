import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';

export interface ComplaintData {
  id: string;
  invoiceNumber: string;
  type: string;
  status: string;
  priority: string;
  dateCreated: Date;
  lastUpdated: Date;
  description: string;
}

@Component({
  selector: 'app-track',
  templateUrl: './track.page.html',
  styleUrls: ['./track.page.scss'],
})
export class TrackPage implements OnInit, AfterViewInit {

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  searchForm: FormGroup;
  displayedColumns: string[] = ['id', 'invoiceNumber', 'type', 'status', 'priority', 'dateCreated', 'actions'];
  dataSource = new MatTableDataSource<ComplaintData>();
  isLoading = false;

  // Sample data
  complaints: ComplaintData[] = [
    {
      id: 'AIS1703123456',
      invoiceNumber: 'INV-2024-001',
      type: 'Quality Issue',
      status: 'In Progress',
      priority: 'High',
      dateCreated: new Date('2024-01-15'),
      lastUpdated: new Date('2024-01-16'),
      description: 'Glass surface has scratches and quality issues'
    },
    {
      id: 'AIS1703123457',
      invoiceNumber: 'INV-2024-002',
      type: 'Delivery Issue',
      status: 'Resolved',
      priority: 'Medium',
      dateCreated: new Date('2024-01-10'),
      lastUpdated: new Date('2024-01-14'),
      description: 'Late delivery of glass panels'
    },
    {
      id: 'AIS1703123458',
      invoiceNumber: 'INV-2024-003',
      type: 'Damage',
      status: 'Pending',
      priority: 'Critical',
      dateCreated: new Date('2024-01-18'),
      lastUpdated: new Date('2024-01-18'),
      description: 'Damaged glass during transportation'
    },
    {
      id: 'AIS1703123459',
      invoiceNumber: 'INV-2024-004',
      type: 'Missing Items',
      status: 'In Progress',
      priority: 'Low',
      dateCreated: new Date('2024-01-12'),
      lastUpdated: new Date('2024-01-15'),
      description: 'Some glass panels missing from the shipment'
    }
  ];

  constructor(
    private formBuilder: FormBuilder,
    private router: Router
  ) {
    this.searchForm = this.formBuilder.group({
      searchTerm: ['', Validators.required]
    });
  }

  ngOnInit() {
    this.dataSource.data = this.complaints;
  }

  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  onSearch() {
    const searchTerm = this.searchForm.get('searchTerm')?.value;
    if (searchTerm) {
      this.isLoading = true;
      
      // Simulate search delay
      setTimeout(() => {
        this.dataSource.filter = searchTerm.trim().toLowerCase();
        this.isLoading = false;
      }, 1000);
    }
  }

  clearSearch() {
    this.searchForm.reset();
    this.dataSource.filter = '';
  }

  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'resolved':
        return 'success';
      case 'in progress':
        return 'primary';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'danger';
      default:
        return 'medium';
    }
  }

  getPriorityColor(priority: string): string {
    switch (priority.toLowerCase()) {
      case 'critical':
        return 'danger';
      case 'high':
        return 'warning';
      case 'medium':
        return 'primary';
      case 'low':
        return 'success';
      default:
        return 'medium';
    }
  }

  viewDetails(complaint: ComplaintData) {
    this.router.navigate(['/trackitem'], { 
      queryParams: { id: complaint.id } 
    });
  }

  goBack() {
    this.router.navigate(['/home']);
  }

  refreshData() {
    this.isLoading = true;
    
    // Simulate data refresh
    setTimeout(() => {
      this.dataSource.data = [...this.complaints];
      this.isLoading = false;
    }, 1500);
  }
}
