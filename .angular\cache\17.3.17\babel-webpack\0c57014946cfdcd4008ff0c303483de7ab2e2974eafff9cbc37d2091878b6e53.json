{"ast": null, "code": "import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, numberAttribute, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst _c0 = [\"body\"];\nconst _c1 = [[[\"mat-expansion-panel-header\"]], \"*\", [[\"mat-action-row\"]]];\nconst _c2 = [\"mat-expansion-panel-header\", \"*\", \"mat-action-row\"];\nfunction MatExpansionPanel_ng_template_5_Template(rf, ctx) {}\nconst _c3 = [[[\"mat-panel-title\"]], [[\"mat-panel-description\"]], \"*\"];\nconst _c4 = [\"mat-panel-title\", \"mat-panel-description\", \"*\"];\nfunction MatExpansionPanelHeader_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 1);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 2);\n    i0.ɵɵelement(2, \"path\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"@indicatorRotate\", ctx_r0._getExpandedState());\n  }\n}\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nconst matExpansionAnimations = {\n  /** Animation that rotates the indicator arrow. */\n  indicatorRotate: trigger('indicatorRotate', [state('collapsed, void', style({\n    transform: 'rotate(0deg)'\n  })), state('expanded', style({\n    transform: 'rotate(180deg)'\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))]),\n  /** Animation that expands and collapses the panel content. */\n  bodyExpansion: trigger('bodyExpansion', [state('collapsed, void', style({\n    height: '0px',\n    visibility: 'hidden'\n  })),\n  // Clear the `visibility` while open, otherwise the content will be visible when placed in\n  // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n  // that have a `visibility` of their own (see #27436).\n  state('expanded', style({\n    height: '*',\n    visibility: ''\n  })), transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING))])\n};\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n  constructor(_template, _expansionPanel) {\n    this._template = _template;\n    this._expansionPanel = _expansionPanel;\n  }\n  static {\n    this.ɵfac = function MatExpansionPanelContent_Factory(t) {\n      return new (t || MatExpansionPanelContent)(i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelContent,\n      selectors: [[\"ng-template\", \"matExpansionPanelContent\", \"\"]],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelContent, [{\n    type: Directive,\n    args: [{\n      selector: 'ng-template[matExpansionPanelContent]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_EXPANSION_PANEL]\n    }, {\n      type: Optional\n    }]\n  }], null);\n})();\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n  /** Whether the toggle indicator should be hidden. */\n  get hideToggle() {\n    return this._hideToggle || this.accordion && this.accordion.hideToggle;\n  }\n  set hideToggle(value) {\n    this._hideToggle = value;\n  }\n  /** The position of the expansion indicator. */\n  get togglePosition() {\n    return this._togglePosition || this.accordion && this.accordion.togglePosition;\n  }\n  set togglePosition(value) {\n    this._togglePosition = value;\n  }\n  constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n    super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n    this._viewContainerRef = _viewContainerRef;\n    this._animationMode = _animationMode;\n    this._hideToggle = false;\n    /** An event emitted after the body's expansion animation happens. */\n    this.afterExpand = new EventEmitter();\n    /** An event emitted after the body's collapse animation happens. */\n    this.afterCollapse = new EventEmitter();\n    /** Stream that emits for changes in `@Input` properties. */\n    this._inputChanges = new Subject();\n    /** ID for the associated header element. Used for a11y labelling. */\n    this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n    this.accordion = accordion;\n    this._document = _document;\n    this._animationsDisabled = _animationMode === 'NoopAnimations';\n    if (defaultOptions) {\n      this.hideToggle = defaultOptions.hideToggle;\n    }\n  }\n  /** Determines whether the expansion panel should have spacing between it and its siblings. */\n  _hasSpacing() {\n    if (this.accordion) {\n      return this.expanded && this.accordion.displayMode === 'default';\n    }\n    return false;\n  }\n  /** Gets the expanded state string. */\n  _getExpandedState() {\n    return this.expanded ? 'expanded' : 'collapsed';\n  }\n  /** Toggles the expanded state of the expansion panel. */\n  toggle() {\n    this.expanded = !this.expanded;\n  }\n  /** Sets the expanded state of the expansion panel to false. */\n  close() {\n    this.expanded = false;\n  }\n  /** Sets the expanded state of the expansion panel to true. */\n  open() {\n    this.expanded = true;\n  }\n  ngAfterContentInit() {\n    if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n      // Render the content as soon as the panel becomes open.\n      this.opened.pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1)).subscribe(() => {\n        this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n      });\n    }\n  }\n  ngOnChanges(changes) {\n    this._inputChanges.next(changes);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._inputChanges.complete();\n  }\n  /** Checks whether the expansion panel's content contains the currently-focused element. */\n  _containsFocus() {\n    if (this._body) {\n      const focusedElement = this._document.activeElement;\n      const bodyElement = this._body.nativeElement;\n      return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n    }\n    return false;\n  }\n  /** Called when the expansion animation has started. */\n  _animationStarted(event) {\n    if (!isInitialAnimation(event) && !this._animationsDisabled && this._body) {\n      // Prevent the user from tabbing into the content while it's animating.\n      // TODO(crisbeto): maybe use `inert` to prevent focus from entering while closed as well\n      // instead of `visibility`? Will allow us to clean up some code but needs more testing.\n      this._body?.nativeElement.setAttribute('inert', '');\n    }\n  }\n  /** Called when the expansion animation has finished. */\n  _animationDone(event) {\n    if (!isInitialAnimation(event)) {\n      if (event.toState === 'expanded') {\n        this.afterExpand.emit();\n      } else if (event.toState === 'collapsed') {\n        this.afterCollapse.emit();\n      }\n      // Re-enable tabbing once the animation is finished.\n      if (!this._animationsDisabled && this._body) {\n        this._body.nativeElement.removeAttribute('inert');\n      }\n    }\n  }\n  static {\n    this.ɵfac = function MatExpansionPanel_Factory(t) {\n      return new (t || MatExpansionPanel)(i0.ɵɵdirectiveInject(MAT_ACCORDION, 12), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.UniqueSelectionDispatcher), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanel,\n      selectors: [[\"mat-expansion-panel\"]],\n      contentQueries: function MatExpansionPanel_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelContent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lazyContent = _t.first);\n        }\n      },\n      viewQuery: function MatExpansionPanel_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._body = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-expansion-panel\"],\n      hostVars: 6,\n      hostBindings: function MatExpansionPanel_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-expanded\", ctx.expanded)(\"_mat-animation-noopable\", ctx._animationsDisabled)(\"mat-expansion-panel-spacing\", ctx._hasSpacing());\n        }\n      },\n      inputs: {\n        hideToggle: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        togglePosition: \"togglePosition\"\n      },\n      outputs: {\n        afterExpand: \"afterExpand\",\n        afterCollapse: \"afterCollapse\"\n      },\n      exportAs: [\"matExpansionPanel\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 7,\n      vars: 4,\n      consts: [[\"body\", \"\"], [\"role\", \"region\", 1, \"mat-expansion-panel-content\", 3, \"id\"], [1, \"mat-expansion-panel-body\"], [3, \"cdkPortalOutlet\"]],\n      template: function MatExpansionPanel_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 1, 0);\n          i0.ɵɵlistener(\"@bodyExpansion.start\", function MatExpansionPanel_Template_div_animation_bodyExpansion_start_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._animationStarted($event));\n          })(\"@bodyExpansion.done\", function MatExpansionPanel_Template_div_animation_bodyExpansion_done_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._animationDone($event));\n          });\n          i0.ɵɵelementStart(3, \"div\", 2);\n          i0.ɵɵprojection(4, 1);\n          i0.ɵɵtemplate(5, MatExpansionPanel_ng_template_5_Template, 0, 0, \"ng-template\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(6, 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"@bodyExpansion\", ctx._getExpandedState())(\"id\", ctx.id);\n          i0.ɵɵattribute(\"aria-labelledby\", ctx._headerId);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"cdkPortalOutlet\", ctx._portal);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color);color:var(--mat-expansion-container-text-color);border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font);font-size:var(--mat-expansion-container-text-size);font-weight:var(--mat-expansion-container-text-weight);line-height:var(--mat-expansion-container-text-line-height);letter-spacing:var(--mat-expansion-container-text-tracking)}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color)}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matExpansionAnimations.bodyExpansion]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanel, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel',\n      exportAs: 'matExpansionPanel',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [matExpansionAnimations.bodyExpansion],\n      providers: [\n      // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n      // to the same accordion.\n      {\n        provide: MAT_ACCORDION,\n        useValue: undefined\n      }, {\n        provide: MAT_EXPANSION_PANEL,\n        useExisting: MatExpansionPanel\n      }],\n      host: {\n        'class': 'mat-expansion-panel',\n        '[class.mat-expanded]': 'expanded',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[class.mat-expansion-panel-spacing]': '_hasSpacing()'\n      },\n      standalone: true,\n      imports: [CdkPortalOutlet],\n      template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.start)=\\\"_animationStarted($event)\\\"\\n     (@bodyExpansion.done)=\\\"_animationDone($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\",\n      styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color);color:var(--mat-expansion-container-text-color);border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font);font-size:var(--mat-expansion-container-text-size);font-weight:var(--mat-expansion-container-text-weight);line-height:var(--mat-expansion-container-text-line-height);letter-spacing:var(--mat-expansion-container-text-tracking)}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color)}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [MAT_ACCORDION]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.UniqueSelectionDispatcher\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }], {\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    togglePosition: [{\n      type: Input\n    }],\n    afterExpand: [{\n      type: Output\n    }],\n    afterCollapse: [{\n      type: Output\n    }],\n    _lazyContent: [{\n      type: ContentChild,\n      args: [MatExpansionPanelContent]\n    }],\n    _body: [{\n      type: ViewChild,\n      args: ['body']\n    }]\n  });\n})();\n/** Checks whether an animation is the initial setup animation. */\nfunction isInitialAnimation(event) {\n  return event.fromState === 'void';\n}\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n  static {\n    this.ɵfac = function MatExpansionPanelActionRow_Factory(t) {\n      return new (t || MatExpansionPanelActionRow)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelActionRow,\n      selectors: [[\"mat-action-row\"]],\n      hostAttrs: [1, \"mat-action-row\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelActionRow, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-action-row',\n      host: {\n        class: 'mat-action-row'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader {\n  constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n    this.panel = panel;\n    this._element = _element;\n    this._focusMonitor = _focusMonitor;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    this._parentChangeSubscription = Subscription.EMPTY;\n    /** Tab index of the header. */\n    this.tabIndex = 0;\n    const accordionHideToggleChange = panel.accordion ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition']))) : EMPTY;\n    this.tabIndex = parseInt(tabIndex || '') || 0;\n    // Since the toggle state depends on an @Input on the panel, we\n    // need to subscribe and trigger change detection manually.\n    this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n      return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n    }))).subscribe(() => this._changeDetectorRef.markForCheck());\n    // Avoids focus being lost if the panel contained the focused element and was closed.\n    panel.closed.pipe(filter(() => panel._containsFocus())).subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n    if (defaultOptions) {\n      this.expandedHeight = defaultOptions.expandedHeight;\n      this.collapsedHeight = defaultOptions.collapsedHeight;\n    }\n  }\n  /**\n   * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n   * @docs-private\n   */\n  get disabled() {\n    return this.panel.disabled;\n  }\n  /** Toggles the expanded state of the panel. */\n  _toggle() {\n    if (!this.disabled) {\n      this.panel.toggle();\n    }\n  }\n  /** Gets whether the panel is expanded. */\n  _isExpanded() {\n    return this.panel.expanded;\n  }\n  /** Gets the expanded state string of the panel. */\n  _getExpandedState() {\n    return this.panel._getExpandedState();\n  }\n  /** Gets the panel id. */\n  _getPanelId() {\n    return this.panel.id;\n  }\n  /** Gets the toggle position for the header. */\n  _getTogglePosition() {\n    return this.panel.togglePosition;\n  }\n  /** Gets whether the expand indicator should be shown. */\n  _showToggle() {\n    return !this.panel.hideToggle && !this.panel.disabled;\n  }\n  /**\n   * Gets the current height of the header. Null if no custom height has been\n   * specified, and if the default height from the stylesheet should be used.\n   */\n  _getHeaderHeight() {\n    const isExpanded = this._isExpanded();\n    if (isExpanded && this.expandedHeight) {\n      return this.expandedHeight;\n    } else if (!isExpanded && this.collapsedHeight) {\n      return this.collapsedHeight;\n    }\n    return null;\n  }\n  /** Handle keydown event calling to toggle() if appropriate. */\n  _keydown(event) {\n    switch (event.keyCode) {\n      // Toggle for space and enter keys.\n      case SPACE:\n      case ENTER:\n        if (!hasModifierKey(event)) {\n          event.preventDefault();\n          this._toggle();\n        }\n        break;\n      default:\n        if (this.panel.accordion) {\n          this.panel.accordion._handleHeaderKeydown(event);\n        }\n        return;\n    }\n  }\n  /**\n   * Focuses the panel header. Implemented as a part of `FocusableOption`.\n   * @param origin Origin of the action that triggered the focus.\n   * @docs-private\n   */\n  focus(origin, options) {\n    if (origin) {\n      this._focusMonitor.focusVia(this._element, origin, options);\n    } else {\n      this._element.nativeElement.focus(options);\n    }\n  }\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._element).subscribe(origin => {\n      if (origin && this.panel.accordion) {\n        this.panel.accordion._handleHeaderFocus(this);\n      }\n    });\n  }\n  ngOnDestroy() {\n    this._parentChangeSubscription.unsubscribe();\n    this._focusMonitor.stopMonitoring(this._element);\n  }\n  static {\n    this.ɵfac = function MatExpansionPanelHeader_Factory(t) {\n      return new (t || MatExpansionPanelHeader)(i0.ɵɵdirectiveInject(MatExpansionPanel, 1), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, 8), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵinjectAttribute('tabindex'));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatExpansionPanelHeader,\n      selectors: [[\"mat-expansion-panel-header\"]],\n      hostAttrs: [\"role\", \"button\", 1, \"mat-expansion-panel-header\", \"mat-focus-indicator\"],\n      hostVars: 15,\n      hostBindings: function MatExpansionPanelHeader_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatExpansionPanelHeader_click_HostBindingHandler() {\n            return ctx._toggle();\n          })(\"keydown\", function MatExpansionPanelHeader_keydown_HostBindingHandler($event) {\n            return ctx._keydown($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx.panel._headerId)(\"tabindex\", ctx.disabled ? -1 : ctx.tabIndex)(\"aria-controls\", ctx._getPanelId())(\"aria-expanded\", ctx._isExpanded())(\"aria-disabled\", ctx.panel.disabled);\n          i0.ɵɵstyleProp(\"height\", ctx._getHeaderHeight());\n          i0.ɵɵclassProp(\"mat-expanded\", ctx._isExpanded())(\"mat-expansion-toggle-indicator-after\", ctx._getTogglePosition() === \"after\")(\"mat-expansion-toggle-indicator-before\", ctx._getTogglePosition() === \"before\")(\"_mat-animation-noopable\", ctx._animationMode === \"NoopAnimations\");\n        }\n      },\n      inputs: {\n        expandedHeight: \"expandedHeight\",\n        collapsedHeight: \"collapsedHeight\",\n        tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n      },\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 5,\n      vars: 3,\n      consts: [[1, \"mat-content\"], [1, \"mat-expansion-indicator\"], [\"xmlns\", \"http://www.w3.org/2000/svg\", \"viewBox\", \"0 -960 960 960\", \"aria-hidden\", \"true\", \"focusable\", \"false\"], [\"d\", \"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\"]],\n      template: function MatExpansionPanelHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, MatExpansionPanelHeader_Conditional_4_Template, 3, 1, \"span\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-content-hide-toggle\", !ctx._showToggle());\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(4, ctx._showToggle() ? 4 : -1);\n        }\n      },\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font);font-size:var(--mat-expansion-header-text-size);font-weight:var(--mat-expansion-header-text-weight);line-height:var(--mat-expansion-header-text-line-height);letter-spacing:var(--mat-expansion-header-text-tracking)}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color)}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color)}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color);display:inline-block;display:var(--mat-expansion-legacy-header-indicator-display, inline-block)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color);display:none;display:var(--mat-expansion-header-indicator-display, none)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matExpansionAnimations.indicatorRotate]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-expansion-panel-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      animations: [matExpansionAnimations.indicatorRotate],\n      host: {\n        'class': 'mat-expansion-panel-header mat-focus-indicator',\n        'role': 'button',\n        '[attr.id]': 'panel._headerId',\n        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n        '[attr.aria-controls]': '_getPanelId()',\n        '[attr.aria-expanded]': '_isExpanded()',\n        '[attr.aria-disabled]': 'panel.disabled',\n        '[class.mat-expanded]': '_isExpanded()',\n        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n        '[style.height]': '_getHeaderHeight()',\n        '(click)': '_toggle()',\n        '(keydown)': '_keydown($event)'\n      },\n      standalone: true,\n      template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span [@indicatorRotate]=\\\"_getExpandedState()\\\" class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\",\n      styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font);font-size:var(--mat-expansion-header-text-size);font-weight:var(--mat-expansion-header-text-weight);line-height:var(--mat-expansion-header-text-line-height);letter-spacing:var(--mat-expansion-header-text-tracking)}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color)}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color)}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color);display:inline-block;display:var(--mat-expansion-legacy-header-indicator-display, inline-block)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color);display:none;display:var(--mat-expansion-header-indicator-display, none)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"]\n    }]\n  }], () => [{\n    type: MatExpansionPanel,\n    decorators: [{\n      type: Host\n    }]\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }], {\n    expandedHeight: [{\n      type: Input\n    }],\n    collapsedHeight: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }]\n  });\n})();\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n  static {\n    this.ɵfac = function MatExpansionPanelDescription_Factory(t) {\n      return new (t || MatExpansionPanelDescription)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelDescription,\n      selectors: [[\"mat-panel-description\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-description\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelDescription, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-description',\n      host: {\n        class: 'mat-expansion-panel-header-description'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n  static {\n    this.ɵfac = function MatExpansionPanelTitle_Factory(t) {\n      return new (t || MatExpansionPanelTitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatExpansionPanelTitle,\n      selectors: [[\"mat-panel-title\"]],\n      hostAttrs: [1, \"mat-expansion-panel-header-title\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionPanelTitle, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-panel-title',\n      host: {\n        class: 'mat-expansion-panel-header-title'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n  constructor() {\n    super(...arguments);\n    /** Headers belonging to this accordion. */\n    this._ownHeaders = new QueryList();\n    /** Whether the expansion indicator should be hidden. */\n    this.hideToggle = false;\n    /**\n     * Display mode used for all expansion panels in the accordion. Currently two display\n     * modes exist:\n     *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n     *     panel at a different elevation from the rest of the accordion.\n     *  flat - no spacing is placed around expanded panels, showing all panels at the same\n     *     elevation.\n     */\n    this.displayMode = 'default';\n    /** The position of the expansion indicator. */\n    this.togglePosition = 'after';\n  }\n  ngAfterContentInit() {\n    this._headers.changes.pipe(startWith(this._headers)).subscribe(headers => {\n      this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n      this._ownHeaders.notifyOnChanges();\n    });\n    this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n  }\n  /** Handles keyboard events coming in from the panel headers. */\n  _handleHeaderKeydown(event) {\n    this._keyManager.onKeydown(event);\n  }\n  _handleHeaderFocus(header) {\n    this._keyManager.updateActiveItem(header);\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this._keyManager?.destroy();\n    this._ownHeaders.destroy();\n  }\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatAccordion_BaseFactory;\n      return function MatAccordion_Factory(t) {\n        return (ɵMatAccordion_BaseFactory || (ɵMatAccordion_BaseFactory = i0.ɵɵgetInheritedFactory(MatAccordion)))(t || MatAccordion);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatAccordion,\n      selectors: [[\"mat-accordion\"]],\n      contentQueries: function MatAccordion_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatExpansionPanelHeader, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._headers = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-accordion\"],\n      hostVars: 2,\n      hostBindings: function MatAccordion_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-accordion-multi\", ctx.multi);\n        }\n      },\n      inputs: {\n        hideToggle: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideToggle\", \"hideToggle\", booleanAttribute],\n        displayMode: \"displayMode\",\n        togglePosition: \"togglePosition\"\n      },\n      exportAs: [\"matAccordion\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatAccordion, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-accordion',\n      exportAs: 'matAccordion',\n      providers: [{\n        provide: MAT_ACCORDION,\n        useExisting: MatAccordion\n      }],\n      host: {\n        class: 'mat-accordion',\n        // Class binding which is only used by the test harness as there is no other\n        // way for the harness to detect if multiple panel support is enabled.\n        '[class.mat-accordion-multi]': 'this.multi'\n      },\n      standalone: true\n    }]\n  }], null, {\n    _headers: [{\n      type: ContentChildren,\n      args: [MatExpansionPanelHeader, {\n        descendants: true\n      }]\n    }],\n    hideToggle: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    displayMode: [{\n      type: Input\n    }],\n    togglePosition: [{\n      type: Input\n    }]\n  });\n})();\nclass MatExpansionModule {\n  static {\n    this.ɵfac = function MatExpansionModule_Factory(t) {\n      return new (t || MatExpansionModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatExpansionModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatExpansionModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CdkAccordionModule, PortalModule, MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent],\n      exports: [MatAccordion, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelHeader, MatExpansionPanelTitle, MatExpansionPanelDescription, MatExpansionPanelContent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };", "map": {"version": 3, "names": ["CdkAccordionItem", "CdkAccordion", "CdkAccordionModule", "TemplatePortal", "CdkPortalOutlet", "PortalModule", "i0", "InjectionToken", "Directive", "Inject", "Optional", "EventEmitter", "ANIMATION_MODULE_TYPE", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "SkipSelf", "Input", "Output", "ContentChild", "ViewChild", "numberAttribute", "Host", "Attribute", "QueryList", "ContentChildren", "NgModule", "MatCommonModule", "i2", "FocusKeyManager", "startWith", "filter", "take", "ENTER", "hasModifierKey", "SPACE", "Subject", "Subscription", "EMPTY", "merge", "trigger", "state", "style", "transition", "animate", "i1", "DOCUMENT", "_c0", "_c1", "_c2", "MatExpansionPanel_ng_template_5_Template", "rf", "ctx", "_c3", "_c4", "MatExpansionPanelHeader_Conditional_4_Template", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "_getExpandedState", "MAT_ACCORDION", "EXPANSION_PANEL_ANIMATION_TIMING", "matExpansionAnimations", "indicatorRotate", "transform", "bodyExpansion", "height", "visibility", "MAT_EXPANSION_PANEL", "MatExpansionPanelContent", "constructor", "_template", "_expansionPanel", "ɵfac", "MatExpansionPanelContent_Factory", "t", "ɵɵdirectiveInject", "TemplateRef", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "undefined", "decorators", "uniqueId", "MAT_EXPANSION_PANEL_DEFAULT_OPTIONS", "MatExpansionPanel", "hideToggle", "_hideToggle", "accordion", "value", "togglePosition", "_togglePosition", "_changeDetectorRef", "_uniqueSelectionDispatcher", "_viewContainerRef", "_document", "_animationMode", "defaultOptions", "afterExpand", "afterCollapse", "_inputChanges", "_headerId", "_animationsDisabled", "_hasSpacing", "expanded", "displayMode", "toggle", "close", "open", "ngAfterContentInit", "_lazyContent", "opened", "pipe", "_portal", "subscribe", "ngOnChanges", "changes", "next", "ngOnDestroy", "complete", "_containsFocus", "_body", "focusedElement", "activeElement", "bodyElement", "nativeElement", "contains", "_animationStarted", "event", "isInitialAnimation", "setAttribute", "_animationDone", "toState", "emit", "removeAttribute", "MatExpansionPanel_Factory", "ChangeDetectorRef", "UniqueSelectionDispatcher", "ViewContainerRef", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatExpansionPanel_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatExpansionPanel_Query", "ɵɵviewQuery", "hostAttrs", "hostVars", "hostBindings", "MatExpansionPanel_HostBindings", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "exportAs", "features", "ɵɵProvidersFeature", "provide", "useValue", "useExisting", "ɵɵInputTransformsFeature", "ɵɵInheritDefinitionFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatExpansionPanel_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵprojection", "ɵɵlistener", "MatExpansionPanel_Template_div_animation_bodyExpansion_start_1_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "MatExpansionPanel_Template_div_animation_bodyExpansion_done_1_listener", "ɵɵtemplate", "ɵɵadvance", "id", "ɵɵattribute", "dependencies", "styles", "encapsulation", "data", "animation", "changeDetection", "None", "OnPush", "animations", "providers", "host", "imports", "fromState", "MatExpansionPanelActionRow", "MatExpansionPanelActionRow_Factory", "class", "MatExpansionPanelHeader", "panel", "_element", "_focusMonitor", "tabIndex", "_parentChangeSubscription", "accordionHideToggleChange", "_stateChanges", "parseInt", "closed", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "focusVia", "expandedHeight", "collapsedHeight", "disabled", "_toggle", "_isExpanded", "_getPanelId", "_getTogglePosition", "_showToggle", "_getHeaderHeight", "isExpanded", "_keydown", "keyCode", "preventDefault", "_handleHeaderKeydown", "focus", "origin", "options", "ngAfterViewInit", "monitor", "_handleHeaderFocus", "unsubscribe", "stopMonitoring", "MatExpansionPanelHeader_Factory", "ElementRef", "FocusMonitor", "ɵɵinjectAttribute", "MatExpansionPanelHeader_HostBindings", "MatExpansionPanelHeader_click_HostBindingHandler", "MatExpansionPanelHeader_keydown_HostBindingHandler", "ɵɵstyleProp", "MatExpansionPanelHeader_Template", "ɵɵconditional", "MatExpansionPanelDescription", "MatExpansionPanelDescription_Factory", "MatExpansionPanelTitle", "MatExpansionPanelTitle_Factory", "Mat<PERSON><PERSON>rdi<PERSON>", "arguments", "_ownHeaders", "_headers", "headers", "reset", "header", "notifyOn<PERSON><PERSON>es", "_keyManager", "withWrap", "withHomeAndEnd", "onKeydown", "updateActiveItem", "destroy", "ɵMatAccordion_BaseFactory", "MatAccordion_Factory", "ɵɵgetInheritedFactory", "MatAccordion_ContentQueries", "MatAccordion_HostBindings", "multi", "descendants", "MatExpansionModule", "MatExpansionModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@angular/material/fesm2022/expansion.mjs"], "sourcesContent": ["import { CdkAccordionItem, CdkAccordion, CdkAccordionModule } from '@angular/cdk/accordion';\nimport { TemplatePortal, CdkPortalOutlet, PortalModule } from '@angular/cdk/portal';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Inject, Optional, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, SkipSelf, Input, Output, ContentChild, ViewChild, numberAttribute, Host, Attribute, QueryList, ContentChildren, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i2 from '@angular/cdk/a11y';\nimport { FocusKeyManager } from '@angular/cdk/a11y';\nimport { startWith, filter, take } from 'rxjs/operators';\nimport { ENTER, hasModifierKey, SPACE } from '@angular/cdk/keycodes';\nimport { Subject, Subscription, EMPTY, merge } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport * as i1 from '@angular/cdk/collections';\nimport { DOCUMENT } from '@angular/common';\n\n/**\n * Token used to provide a `MatAccordion` to `MatExpansionPanel`.\n * Used primarily to avoid circular imports between `MatAccordion` and `MatExpansionPanel`.\n */\nconst MAT_ACCORDION = new InjectionToken('MAT_ACCORDION');\n\n/** Time and timing curve for expansion panel animations. */\n// Note: Keep this in sync with the Sass variable for the panel header animation.\nconst EXPANSION_PANEL_ANIMATION_TIMING = '225ms cubic-bezier(0.4,0.0,0.2,1)';\n/**\n * Animations used by the Material expansion panel.\n *\n * A bug in angular animation's `state` when ViewContainers are moved using ViewContainerRef.move()\n * causes the animation state of moved components to become `void` upon exit, and not update again\n * upon reentry into the DOM.  This can lead a to situation for the expansion panel where the state\n * of the panel is `expanded` or `collapsed` but the animation state is `void`.\n *\n * To correctly handle animating to the next state, we animate between `void` and `collapsed` which\n * are defined to have the same styles. Since angular animates from the current styles to the\n * destination state's style definition, in situations where we are moving from `void`'s styles to\n * `collapsed` this acts a noop since no style values change.\n *\n * In the case where angular's animation state is out of sync with the expansion panel's state, the\n * expansion panel being `expanded` and angular animations being `void`, the animation from the\n * `expanded`'s effective styles (though in a `void` animation state) to the collapsed state will\n * occur as expected.\n *\n * Angular Bug: https://github.com/angular/angular/issues/18847\n *\n * @docs-private\n */\nconst matExpansionAnimations = {\n    /** Animation that rotates the indicator arrow. */\n    indicatorRotate: trigger('indicatorRotate', [\n        state('collapsed, void', style({ transform: 'rotate(0deg)' })),\n        state('expanded', style({ transform: 'rotate(180deg)' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n    /** Animation that expands and collapses the panel content. */\n    bodyExpansion: trigger('bodyExpansion', [\n        state('collapsed, void', style({ height: '0px', visibility: 'hidden' })),\n        // Clear the `visibility` while open, otherwise the content will be visible when placed in\n        // a parent that's `visibility: hidden`, because `visibility` doesn't apply to descendants\n        // that have a `visibility` of their own (see #27436).\n        state('expanded', style({ height: '*', visibility: '' })),\n        transition('expanded <=> collapsed, void => collapsed', animate(EXPANSION_PANEL_ANIMATION_TIMING)),\n    ]),\n};\n\n/**\n * Token used to provide a `MatExpansionPanel` to `MatExpansionPanelContent`.\n * Used to avoid circular imports between `MatExpansionPanel` and `MatExpansionPanelContent`.\n */\nconst MAT_EXPANSION_PANEL = new InjectionToken('MAT_EXPANSION_PANEL');\n\n/**\n * Expansion panel content that will be rendered lazily\n * after the panel is opened for the first time.\n */\nclass MatExpansionPanelContent {\n    constructor(_template, _expansionPanel) {\n        this._template = _template;\n        this._expansionPanel = _expansionPanel;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelContent, deps: [{ token: i0.TemplateRef }, { token: MAT_EXPANSION_PANEL, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatExpansionPanelContent, isStandalone: true, selector: \"ng-template[matExpansionPanelContent]\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'ng-template[matExpansionPanelContent]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL]\n                }, {\n                    type: Optional\n                }] }] });\n\n/** Counter for generating unique element ids. */\nlet uniqueId = 0;\n/**\n * Injection token that can be used to configure the default\n * options for the expansion panel component.\n */\nconst MAT_EXPANSION_PANEL_DEFAULT_OPTIONS = new InjectionToken('MAT_EXPANSION_PANEL_DEFAULT_OPTIONS');\n/**\n * This component can be used as a single element to show expandable content, or as one of\n * multiple children of an element with the MatAccordion directive attached.\n */\nclass MatExpansionPanel extends CdkAccordionItem {\n    /** Whether the toggle indicator should be hidden. */\n    get hideToggle() {\n        return this._hideToggle || (this.accordion && this.accordion.hideToggle);\n    }\n    set hideToggle(value) {\n        this._hideToggle = value;\n    }\n    /** The position of the expansion indicator. */\n    get togglePosition() {\n        return this._togglePosition || (this.accordion && this.accordion.togglePosition);\n    }\n    set togglePosition(value) {\n        this._togglePosition = value;\n    }\n    constructor(accordion, _changeDetectorRef, _uniqueSelectionDispatcher, _viewContainerRef, _document, _animationMode, defaultOptions) {\n        super(accordion, _changeDetectorRef, _uniqueSelectionDispatcher);\n        this._viewContainerRef = _viewContainerRef;\n        this._animationMode = _animationMode;\n        this._hideToggle = false;\n        /** An event emitted after the body's expansion animation happens. */\n        this.afterExpand = new EventEmitter();\n        /** An event emitted after the body's collapse animation happens. */\n        this.afterCollapse = new EventEmitter();\n        /** Stream that emits for changes in `@Input` properties. */\n        this._inputChanges = new Subject();\n        /** ID for the associated header element. Used for a11y labelling. */\n        this._headerId = `mat-expansion-panel-header-${uniqueId++}`;\n        this.accordion = accordion;\n        this._document = _document;\n        this._animationsDisabled = _animationMode === 'NoopAnimations';\n        if (defaultOptions) {\n            this.hideToggle = defaultOptions.hideToggle;\n        }\n    }\n    /** Determines whether the expansion panel should have spacing between it and its siblings. */\n    _hasSpacing() {\n        if (this.accordion) {\n            return this.expanded && this.accordion.displayMode === 'default';\n        }\n        return false;\n    }\n    /** Gets the expanded state string. */\n    _getExpandedState() {\n        return this.expanded ? 'expanded' : 'collapsed';\n    }\n    /** Toggles the expanded state of the expansion panel. */\n    toggle() {\n        this.expanded = !this.expanded;\n    }\n    /** Sets the expanded state of the expansion panel to false. */\n    close() {\n        this.expanded = false;\n    }\n    /** Sets the expanded state of the expansion panel to true. */\n    open() {\n        this.expanded = true;\n    }\n    ngAfterContentInit() {\n        if (this._lazyContent && this._lazyContent._expansionPanel === this) {\n            // Render the content as soon as the panel becomes open.\n            this.opened\n                .pipe(startWith(null), filter(() => this.expanded && !this._portal), take(1))\n                .subscribe(() => {\n                this._portal = new TemplatePortal(this._lazyContent._template, this._viewContainerRef);\n            });\n        }\n    }\n    ngOnChanges(changes) {\n        this._inputChanges.next(changes);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._inputChanges.complete();\n    }\n    /** Checks whether the expansion panel's content contains the currently-focused element. */\n    _containsFocus() {\n        if (this._body) {\n            const focusedElement = this._document.activeElement;\n            const bodyElement = this._body.nativeElement;\n            return focusedElement === bodyElement || bodyElement.contains(focusedElement);\n        }\n        return false;\n    }\n    /** Called when the expansion animation has started. */\n    _animationStarted(event) {\n        if (!isInitialAnimation(event) && !this._animationsDisabled && this._body) {\n            // Prevent the user from tabbing into the content while it's animating.\n            // TODO(crisbeto): maybe use `inert` to prevent focus from entering while closed as well\n            // instead of `visibility`? Will allow us to clean up some code but needs more testing.\n            this._body?.nativeElement.setAttribute('inert', '');\n        }\n    }\n    /** Called when the expansion animation has finished. */\n    _animationDone(event) {\n        if (!isInitialAnimation(event)) {\n            if (event.toState === 'expanded') {\n                this.afterExpand.emit();\n            }\n            else if (event.toState === 'collapsed') {\n                this.afterCollapse.emit();\n            }\n            // Re-enable tabbing once the animation is finished.\n            if (!this._animationsDisabled && this._body) {\n                this._body.nativeElement.removeAttribute('inert');\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanel, deps: [{ token: MAT_ACCORDION, optional: true, skipSelf: true }, { token: i0.ChangeDetectorRef }, { token: i1.UniqueSelectionDispatcher }, { token: i0.ViewContainerRef }, { token: DOCUMENT }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatExpansionPanel, isStandalone: true, selector: \"mat-expansion-panel\", inputs: { hideToggle: [\"hideToggle\", \"hideToggle\", booleanAttribute], togglePosition: \"togglePosition\" }, outputs: { afterExpand: \"afterExpand\", afterCollapse: \"afterCollapse\" }, host: { properties: { \"class.mat-expanded\": \"expanded\", \"class._mat-animation-noopable\": \"_animationsDisabled\", \"class.mat-expansion-panel-spacing\": \"_hasSpacing()\" }, classAttribute: \"mat-expansion-panel\" }, providers: [\n            // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n            // to the same accordion.\n            { provide: MAT_ACCORDION, useValue: undefined },\n            { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n        ], queries: [{ propertyName: \"_lazyContent\", first: true, predicate: MatExpansionPanelContent, descendants: true }], viewQueries: [{ propertyName: \"_body\", first: true, predicate: [\"body\"], descendants: true }], exportAs: [\"matExpansionPanel\"], usesInheritance: true, usesOnChanges: true, ngImport: i0, template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.start)=\\\"_animationStarted($event)\\\"\\n     (@bodyExpansion.done)=\\\"_animationDone($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color);color:var(--mat-expansion-container-text-color);border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font);font-size:var(--mat-expansion-container-text-size);font-weight:var(--mat-expansion-container-text-weight);line-height:var(--mat-expansion-container-text-line-height);letter-spacing:var(--mat-expansion-container-text-tracking)}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color)}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], animations: [matExpansionAnimations.bodyExpansion], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanel, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel', exportAs: 'matExpansionPanel', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, animations: [matExpansionAnimations.bodyExpansion], providers: [\n                        // Provide MatAccordion as undefined to prevent nested expansion panels from registering\n                        // to the same accordion.\n                        { provide: MAT_ACCORDION, useValue: undefined },\n                        { provide: MAT_EXPANSION_PANEL, useExisting: MatExpansionPanel },\n                    ], host: {\n                        'class': 'mat-expansion-panel',\n                        '[class.mat-expanded]': 'expanded',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                        '[class.mat-expansion-panel-spacing]': '_hasSpacing()',\n                    }, standalone: true, imports: [CdkPortalOutlet], template: \"<ng-content select=\\\"mat-expansion-panel-header\\\"></ng-content>\\n<div class=\\\"mat-expansion-panel-content\\\"\\n     role=\\\"region\\\"\\n     [@bodyExpansion]=\\\"_getExpandedState()\\\"\\n     (@bodyExpansion.start)=\\\"_animationStarted($event)\\\"\\n     (@bodyExpansion.done)=\\\"_animationDone($event)\\\"\\n     [attr.aria-labelledby]=\\\"_headerId\\\"\\n     [id]=\\\"id\\\"\\n     #body>\\n  <div class=\\\"mat-expansion-panel-body\\\">\\n    <ng-content></ng-content>\\n    <ng-template [cdkPortalOutlet]=\\\"_portal\\\"></ng-template>\\n  </div>\\n  <ng-content select=\\\"mat-action-row\\\"></ng-content>\\n</div>\\n\", styles: [\".mat-expansion-panel{box-sizing:content-box;display:block;margin:0;overflow:hidden;transition:margin 225ms cubic-bezier(0.4, 0, 0.2, 1),box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);position:relative;background:var(--mat-expansion-container-background-color);color:var(--mat-expansion-container-text-color);border-radius:var(--mat-expansion-container-shape)}.mat-expansion-panel:not([class*=mat-elevation-z]){box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12)}.mat-accordion .mat-expansion-panel:not(.mat-expanded),.mat-accordion .mat-expansion-panel:not(.mat-expansion-panel-spacing){border-radius:0}.mat-accordion .mat-expansion-panel:first-of-type{border-top-right-radius:var(--mat-expansion-container-shape);border-top-left-radius:var(--mat-expansion-container-shape)}.mat-accordion .mat-expansion-panel:last-of-type{border-bottom-right-radius:var(--mat-expansion-container-shape);border-bottom-left-radius:var(--mat-expansion-container-shape)}.cdk-high-contrast-active .mat-expansion-panel{outline:solid 1px}.mat-expansion-panel.ng-animate-disabled,.ng-animate-disabled .mat-expansion-panel,.mat-expansion-panel._mat-animation-noopable{transition:none}.mat-expansion-panel-content{display:flex;flex-direction:column;overflow:visible;font-family:var(--mat-expansion-container-text-font);font-size:var(--mat-expansion-container-text-size);font-weight:var(--mat-expansion-container-text-weight);line-height:var(--mat-expansion-container-text-line-height);letter-spacing:var(--mat-expansion-container-text-tracking)}.mat-expansion-panel-content[style*=\\\"visibility: hidden\\\"] *{visibility:hidden !important}.mat-expansion-panel-body{padding:0 24px 16px}.mat-expansion-panel-spacing{margin:16px 0}.mat-accordion>.mat-expansion-panel-spacing:first-child,.mat-accordion>*:first-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-top:0}.mat-accordion>.mat-expansion-panel-spacing:last-child,.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing{margin-bottom:0}.mat-action-row{border-top-style:solid;border-top-width:1px;display:flex;flex-direction:row;justify-content:flex-end;padding:16px 8px 16px 24px;border-top-color:var(--mat-expansion-actions-divider-color)}.mat-action-row .mat-button-base,.mat-action-row .mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-action-row .mat-button-base,[dir=rtl] .mat-action-row .mat-mdc-button-base{margin-left:0;margin-right:8px}\"] }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [MAT_ACCORDION]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1.UniqueSelectionDispatcher }, { type: i0.ViewContainerRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { hideToggle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], togglePosition: [{\n                type: Input\n            }], afterExpand: [{\n                type: Output\n            }], afterCollapse: [{\n                type: Output\n            }], _lazyContent: [{\n                type: ContentChild,\n                args: [MatExpansionPanelContent]\n            }], _body: [{\n                type: ViewChild,\n                args: ['body']\n            }] } });\n/** Checks whether an animation is the initial setup animation. */\nfunction isInitialAnimation(event) {\n    return event.fromState === 'void';\n}\n/**\n * Actions of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelActionRow {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelActionRow, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatExpansionPanelActionRow, isStandalone: true, selector: \"mat-action-row\", host: { classAttribute: \"mat-action-row\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelActionRow, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-action-row',\n                    host: {\n                        class: 'mat-action-row',\n                    },\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * Header element of a `<mat-expansion-panel>`.\n */\nclass MatExpansionPanelHeader {\n    constructor(panel, _element, _focusMonitor, _changeDetectorRef, defaultOptions, _animationMode, tabIndex) {\n        this.panel = panel;\n        this._element = _element;\n        this._focusMonitor = _focusMonitor;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        this._parentChangeSubscription = Subscription.EMPTY;\n        /** Tab index of the header. */\n        this.tabIndex = 0;\n        const accordionHideToggleChange = panel.accordion\n            ? panel.accordion._stateChanges.pipe(filter(changes => !!(changes['hideToggle'] || changes['togglePosition'])))\n            : EMPTY;\n        this.tabIndex = parseInt(tabIndex || '') || 0;\n        // Since the toggle state depends on an @Input on the panel, we\n        // need to subscribe and trigger change detection manually.\n        this._parentChangeSubscription = merge(panel.opened, panel.closed, accordionHideToggleChange, panel._inputChanges.pipe(filter(changes => {\n            return !!(changes['hideToggle'] || changes['disabled'] || changes['togglePosition']);\n        }))).subscribe(() => this._changeDetectorRef.markForCheck());\n        // Avoids focus being lost if the panel contained the focused element and was closed.\n        panel.closed\n            .pipe(filter(() => panel._containsFocus()))\n            .subscribe(() => _focusMonitor.focusVia(_element, 'program'));\n        if (defaultOptions) {\n            this.expandedHeight = defaultOptions.expandedHeight;\n            this.collapsedHeight = defaultOptions.collapsedHeight;\n        }\n    }\n    /**\n     * Whether the associated panel is disabled. Implemented as a part of `FocusableOption`.\n     * @docs-private\n     */\n    get disabled() {\n        return this.panel.disabled;\n    }\n    /** Toggles the expanded state of the panel. */\n    _toggle() {\n        if (!this.disabled) {\n            this.panel.toggle();\n        }\n    }\n    /** Gets whether the panel is expanded. */\n    _isExpanded() {\n        return this.panel.expanded;\n    }\n    /** Gets the expanded state string of the panel. */\n    _getExpandedState() {\n        return this.panel._getExpandedState();\n    }\n    /** Gets the panel id. */\n    _getPanelId() {\n        return this.panel.id;\n    }\n    /** Gets the toggle position for the header. */\n    _getTogglePosition() {\n        return this.panel.togglePosition;\n    }\n    /** Gets whether the expand indicator should be shown. */\n    _showToggle() {\n        return !this.panel.hideToggle && !this.panel.disabled;\n    }\n    /**\n     * Gets the current height of the header. Null if no custom height has been\n     * specified, and if the default height from the stylesheet should be used.\n     */\n    _getHeaderHeight() {\n        const isExpanded = this._isExpanded();\n        if (isExpanded && this.expandedHeight) {\n            return this.expandedHeight;\n        }\n        else if (!isExpanded && this.collapsedHeight) {\n            return this.collapsedHeight;\n        }\n        return null;\n    }\n    /** Handle keydown event calling to toggle() if appropriate. */\n    _keydown(event) {\n        switch (event.keyCode) {\n            // Toggle for space and enter keys.\n            case SPACE:\n            case ENTER:\n                if (!hasModifierKey(event)) {\n                    event.preventDefault();\n                    this._toggle();\n                }\n                break;\n            default:\n                if (this.panel.accordion) {\n                    this.panel.accordion._handleHeaderKeydown(event);\n                }\n                return;\n        }\n    }\n    /**\n     * Focuses the panel header. Implemented as a part of `FocusableOption`.\n     * @param origin Origin of the action that triggered the focus.\n     * @docs-private\n     */\n    focus(origin, options) {\n        if (origin) {\n            this._focusMonitor.focusVia(this._element, origin, options);\n        }\n        else {\n            this._element.nativeElement.focus(options);\n        }\n    }\n    ngAfterViewInit() {\n        this._focusMonitor.monitor(this._element).subscribe(origin => {\n            if (origin && this.panel.accordion) {\n                this.panel.accordion._handleHeaderFocus(this);\n            }\n        });\n    }\n    ngOnDestroy() {\n        this._parentChangeSubscription.unsubscribe();\n        this._focusMonitor.stopMonitoring(this._element);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelHeader, deps: [{ token: MatExpansionPanel, host: true }, { token: i0.ElementRef }, { token: i2.FocusMonitor }, { token: i0.ChangeDetectorRef }, { token: MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: 'tabindex', attribute: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatExpansionPanelHeader, isStandalone: true, selector: \"mat-expansion-panel-header\", inputs: { expandedHeight: \"expandedHeight\", collapsedHeight: \"collapsedHeight\", tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))] }, host: { attributes: { \"role\": \"button\" }, listeners: { \"click\": \"_toggle()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.id\": \"panel._headerId\", \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.aria-controls\": \"_getPanelId()\", \"attr.aria-expanded\": \"_isExpanded()\", \"attr.aria-disabled\": \"panel.disabled\", \"class.mat-expanded\": \"_isExpanded()\", \"class.mat-expansion-toggle-indicator-after\": \"_getTogglePosition() === 'after'\", \"class.mat-expansion-toggle-indicator-before\": \"_getTogglePosition() === 'before'\", \"class._mat-animation-noopable\": \"_animationMode === \\\"NoopAnimations\\\"\", \"style.height\": \"_getHeaderHeight()\" }, classAttribute: \"mat-expansion-panel-header mat-focus-indicator\" }, ngImport: i0, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span [@indicatorRotate]=\\\"_getExpandedState()\\\" class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font);font-size:var(--mat-expansion-header-text-size);font-weight:var(--mat-expansion-header-text-weight);line-height:var(--mat-expansion-header-text-line-height);letter-spacing:var(--mat-expansion-header-text-tracking)}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color)}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color)}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color);display:inline-block;display:var(--mat-expansion-legacy-header-indicator-display, inline-block)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color);display:none;display:var(--mat-expansion-header-indicator-display, none)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"], animations: [matExpansionAnimations.indicatorRotate], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-expansion-panel-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, animations: [matExpansionAnimations.indicatorRotate], host: {\n                        'class': 'mat-expansion-panel-header mat-focus-indicator',\n                        'role': 'button',\n                        '[attr.id]': 'panel._headerId',\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.aria-controls]': '_getPanelId()',\n                        '[attr.aria-expanded]': '_isExpanded()',\n                        '[attr.aria-disabled]': 'panel.disabled',\n                        '[class.mat-expanded]': '_isExpanded()',\n                        '[class.mat-expansion-toggle-indicator-after]': `_getTogglePosition() === 'after'`,\n                        '[class.mat-expansion-toggle-indicator-before]': `_getTogglePosition() === 'before'`,\n                        '[class._mat-animation-noopable]': '_animationMode === \"NoopAnimations\"',\n                        '[style.height]': '_getHeaderHeight()',\n                        '(click)': '_toggle()',\n                        '(keydown)': '_keydown($event)',\n                    }, standalone: true, template: \"<span class=\\\"mat-content\\\" [class.mat-content-hide-toggle]=\\\"!_showToggle()\\\">\\n  <ng-content select=\\\"mat-panel-title\\\"></ng-content>\\n  <ng-content select=\\\"mat-panel-description\\\"></ng-content>\\n  <ng-content></ng-content>\\n</span>\\n\\n@if (_showToggle()) {\\n  <span [@indicatorRotate]=\\\"_getExpandedState()\\\" class=\\\"mat-expansion-indicator\\\">\\n    <svg\\n      xmlns=\\\"http://www.w3.org/2000/svg\\\"\\n      viewBox=\\\"0 -960 960 960\\\"\\n      aria-hidden=\\\"true\\\"\\n      focusable=\\\"false\\\">\\n      <path d=\\\"M480-345 240-585l56-56 184 184 184-184 56 56-240 240Z\\\"/>\\n    </svg>\\n  </span>\\n}\\n\", styles: [\".mat-expansion-panel-header{display:flex;flex-direction:row;align-items:center;padding:0 24px;border-radius:inherit;transition:height 225ms cubic-bezier(0.4, 0, 0.2, 1);height:var(--mat-expansion-header-collapsed-state-height);font-family:var(--mat-expansion-header-text-font);font-size:var(--mat-expansion-header-text-size);font-weight:var(--mat-expansion-header-text-weight);line-height:var(--mat-expansion-header-text-line-height);letter-spacing:var(--mat-expansion-header-text-tracking)}.mat-expansion-panel-header.mat-expanded{height:var(--mat-expansion-header-expanded-state-height)}.mat-expansion-panel-header[aria-disabled=true]{color:var(--mat-expansion-header-disabled-state-text-color)}.mat-expansion-panel-header:not([aria-disabled=true]){cursor:pointer}.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-header-hover-state-layer-color)}@media(hover: none){.mat-expansion-panel:not(.mat-expanded) .mat-expansion-panel-header:not([aria-disabled=true]):hover{background:var(--mat-expansion-container-background-color)}}.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-keyboard-focused,.mat-expansion-panel .mat-expansion-panel-header:not([aria-disabled=true]).cdk-program-focused{background:var(--mat-expansion-header-focus-state-layer-color)}.mat-expansion-panel-header._mat-animation-noopable{transition:none}.mat-expansion-panel-header:focus,.mat-expansion-panel-header:hover{outline:none}.mat-expansion-panel-header.mat-expanded:focus,.mat-expansion-panel-header.mat-expanded:hover{background:inherit}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before{flex-direction:row-reverse}.mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 16px 0 0}[dir=rtl] .mat-expansion-panel-header.mat-expansion-toggle-indicator-before .mat-expansion-indicator{margin:0 0 0 16px}.mat-content{display:flex;flex:1;flex-direction:row;overflow:hidden}.mat-content.mat-content-hide-toggle{margin-right:8px}[dir=rtl] .mat-content.mat-content-hide-toggle{margin-right:0;margin-left:8px}.mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-left:24px;margin-right:0}[dir=rtl] .mat-expansion-toggle-indicator-before .mat-content.mat-content-hide-toggle{margin-right:24px;margin-left:0}.mat-expansion-panel-header-title{color:var(--mat-expansion-header-text-color)}.mat-expansion-panel-header-title,.mat-expansion-panel-header-description{display:flex;flex-grow:1;flex-basis:0;margin-right:16px;align-items:center}[dir=rtl] .mat-expansion-panel-header-title,[dir=rtl] .mat-expansion-panel-header-description{margin-right:0;margin-left:16px}.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-title,.mat-expansion-panel-header[aria-disabled=true] .mat-expansion-panel-header-description{color:inherit}.mat-expansion-panel-header-description{flex-grow:2;color:var(--mat-expansion-header-description-color)}.mat-expansion-indicator::after{border-style:solid;border-width:0 2px 2px 0;content:\\\"\\\";display:inline-block;padding:3px;transform:rotate(45deg);vertical-align:middle;color:var(--mat-expansion-header-indicator-color);display:inline-block;display:var(--mat-expansion-legacy-header-indicator-display, inline-block)}.mat-expansion-indicator svg{width:24px;height:24px;margin:0 -8px;vertical-align:middle;fill:var(--mat-expansion-header-indicator-color);display:none;display:var(--mat-expansion-header-indicator-display, none)}.cdk-high-contrast-active .mat-expansion-panel-content{border-top:1px solid;border-top-left-radius:0;border-top-right-radius:0}\"] }]\n        }], ctorParameters: () => [{ type: MatExpansionPanel, decorators: [{\n                    type: Host\n                }] }, { type: i0.ElementRef }, { type: i2.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_EXPANSION_PANEL_DEFAULT_OPTIONS]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }], propDecorators: { expandedHeight: [{\n                type: Input\n            }], collapsedHeight: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }] } });\n/**\n * Description element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelDescription {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelDescription, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatExpansionPanelDescription, isStandalone: true, selector: \"mat-panel-description\", host: { classAttribute: \"mat-expansion-panel-header-description\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelDescription, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-description',\n                    host: {\n                        class: 'mat-expansion-panel-header-description',\n                    },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Title element of a `<mat-expansion-panel-header>`.\n */\nclass MatExpansionPanelTitle {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatExpansionPanelTitle, isStandalone: true, selector: \"mat-panel-title\", host: { classAttribute: \"mat-expansion-panel-header-title\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionPanelTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-panel-title',\n                    host: {\n                        class: 'mat-expansion-panel-header-title',\n                    },\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * Directive for a Material Design Accordion.\n */\nclass MatAccordion extends CdkAccordion {\n    constructor() {\n        super(...arguments);\n        /** Headers belonging to this accordion. */\n        this._ownHeaders = new QueryList();\n        /** Whether the expansion indicator should be hidden. */\n        this.hideToggle = false;\n        /**\n         * Display mode used for all expansion panels in the accordion. Currently two display\n         * modes exist:\n         *  default - a gutter-like spacing is placed around any expanded panel, placing the expanded\n         *     panel at a different elevation from the rest of the accordion.\n         *  flat - no spacing is placed around expanded panels, showing all panels at the same\n         *     elevation.\n         */\n        this.displayMode = 'default';\n        /** The position of the expansion indicator. */\n        this.togglePosition = 'after';\n    }\n    ngAfterContentInit() {\n        this._headers.changes\n            .pipe(startWith(this._headers))\n            .subscribe((headers) => {\n            this._ownHeaders.reset(headers.filter(header => header.panel.accordion === this));\n            this._ownHeaders.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._ownHeaders).withWrap().withHomeAndEnd();\n    }\n    /** Handles keyboard events coming in from the panel headers. */\n    _handleHeaderKeydown(event) {\n        this._keyManager.onKeydown(event);\n    }\n    _handleHeaderFocus(header) {\n        this._keyManager.updateActiveItem(header);\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this._keyManager?.destroy();\n        this._ownHeaders.destroy();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatAccordion, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatAccordion, isStandalone: true, selector: \"mat-accordion\", inputs: { hideToggle: [\"hideToggle\", \"hideToggle\", booleanAttribute], displayMode: \"displayMode\", togglePosition: \"togglePosition\" }, host: { properties: { \"class.mat-accordion-multi\": \"this.multi\" }, classAttribute: \"mat-accordion\" }, providers: [\n            {\n                provide: MAT_ACCORDION,\n                useExisting: MatAccordion,\n            },\n        ], queries: [{ propertyName: \"_headers\", predicate: MatExpansionPanelHeader, descendants: true }], exportAs: [\"matAccordion\"], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatAccordion, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-accordion',\n                    exportAs: 'matAccordion',\n                    providers: [\n                        {\n                            provide: MAT_ACCORDION,\n                            useExisting: MatAccordion,\n                        },\n                    ],\n                    host: {\n                        class: 'mat-accordion',\n                        // Class binding which is only used by the test harness as there is no other\n                        // way for the harness to detect if multiple panel support is enabled.\n                        '[class.mat-accordion-multi]': 'this.multi',\n                    },\n                    standalone: true,\n                }]\n        }], propDecorators: { _headers: [{\n                type: ContentChildren,\n                args: [MatExpansionPanelHeader, { descendants: true }]\n            }], hideToggle: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], displayMode: [{\n                type: Input\n            }], togglePosition: [{\n                type: Input\n            }] } });\n\nclass MatExpansionModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionModule, imports: [MatCommonModule,\n            CdkAccordionModule,\n            PortalModule,\n            MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent], exports: [MatAccordion,\n            MatExpansionPanel,\n            MatExpansionPanelActionRow,\n            MatExpansionPanelHeader,\n            MatExpansionPanelTitle,\n            MatExpansionPanelDescription,\n            MatExpansionPanelContent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionModule, imports: [MatCommonModule,\n            CdkAccordionModule,\n            PortalModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatExpansionModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        CdkAccordionModule,\n                        PortalModule,\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                    exports: [\n                        MatAccordion,\n                        MatExpansionPanel,\n                        MatExpansionPanelActionRow,\n                        MatExpansionPanelHeader,\n                        MatExpansionPanelTitle,\n                        MatExpansionPanelDescription,\n                        MatExpansionPanelContent,\n                    ],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { EXPANSION_PANEL_ANIMATION_TIMING, MAT_ACCORDION, MAT_EXPANSION_PANEL, MAT_EXPANSION_PANEL_DEFAULT_OPTIONS, MatAccordion, MatExpansionModule, MatExpansionPanel, MatExpansionPanelActionRow, MatExpansionPanelContent, MatExpansionPanelDescription, MatExpansionPanelHeader, MatExpansionPanelTitle, matExpansionAnimations };\n"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,wBAAwB;AAC3F,SAASC,cAAc,EAAEC,eAAe,EAAEC,YAAY,QAAQ,qBAAqB;AACnF,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,eAAe,EAAEC,IAAI,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACnT,SAASC,eAAe,QAAQ,wBAAwB;AACxD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,SAAS,EAAEC,MAAM,EAAEC,IAAI,QAAQ,gBAAgB;AACxD,SAASC,KAAK,EAAEC,cAAc,EAAEC,KAAK,QAAQ,uBAAuB;AACpE,SAASC,OAAO,EAAEC,YAAY,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AAC1D,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,OAAO,KAAKC,EAAE,MAAM,0BAA0B;AAC9C,SAASC,QAAQ,QAAQ,iBAAiB;;AAE1C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,yCAAAC,EAAA,EAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+CAAAJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgEoG9C,EAAE,CAAAmD,cAAA,aA+UmzC,CAAC;IA/UtzCnD,EAAE,CAAAoD,cAAA;IAAFpD,EAAE,CAAAmD,cAAA,YA+Um8C,CAAC;IA/Ut8CnD,EAAE,CAAAqD,SAAA,aA+U8gD,CAAC;IA/UjhDrD,EAAE,CAAAsD,YAAA,CA+U0hD,CAAC,CAAU,CAAC;EAAA;EAAA,IAAAR,EAAA;IAAA,MAAAS,MAAA,GA/UxiDvD,EAAE,CAAAwD,aAAA;IAAFxD,EAAE,CAAAyD,UAAA,qBAAAF,MAAA,CAAAG,iBAAA,EA+UgxC,CAAC;EAAA;AAAA;AA3Yv3C,MAAMC,aAAa,GAAG,IAAI1D,cAAc,CAAC,eAAe,CAAC;;AAEzD;AACA;AACA,MAAM2D,gCAAgC,GAAG,mCAAmC;AAC5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,eAAe,EAAE3B,OAAO,CAAC,iBAAiB,EAAE,CACxCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAE0B,SAAS,EAAE;EAAe,CAAC,CAAC,CAAC,EAC9D3B,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;IAAE0B,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EACzDzB,UAAU,CAAC,2CAA2C,EAAEC,OAAO,CAACqB,gCAAgC,CAAC,CAAC,CACrG,CAAC;EACF;EACAI,aAAa,EAAE7B,OAAO,CAAC,eAAe,EAAE,CACpCC,KAAK,CAAC,iBAAiB,EAAEC,KAAK,CAAC;IAAE4B,MAAM,EAAE,KAAK;IAAEC,UAAU,EAAE;EAAS,CAAC,CAAC,CAAC;EACxE;EACA;EACA;EACA9B,KAAK,CAAC,UAAU,EAAEC,KAAK,CAAC;IAAE4B,MAAM,EAAE,GAAG;IAAEC,UAAU,EAAE;EAAG,CAAC,CAAC,CAAC,EACzD5B,UAAU,CAAC,2CAA2C,EAAEC,OAAO,CAACqB,gCAAgC,CAAC,CAAC,CACrG;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMO,mBAAmB,GAAG,IAAIlE,cAAc,CAAC,qBAAqB,CAAC;;AAErE;AACA;AACA;AACA;AACA,MAAMmE,wBAAwB,CAAC;EAC3BC,WAAWA,CAACC,SAAS,EAAEC,eAAe,EAAE;IACpC,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,iCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,wBAAwB,EAAlCpE,EAAE,CAAA2E,iBAAA,CAAkD3E,EAAE,CAAC4E,WAAW,GAAlE5E,EAAE,CAAA2E,iBAAA,CAA6ER,mBAAmB;IAAA,CAA4D;EAAE;EAChQ;IAAS,IAAI,CAACU,IAAI,kBAD8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EACJX,wBAAwB;MAAAY,SAAA;MAAAC,UAAA;IAAA,EAAwF;EAAE;AACpN;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGlF,EAAE,CAAAmF,iBAAA,CAGXf,wBAAwB,EAAc,CAAC;IACtHW,IAAI,EAAE7E,SAAS;IACfkF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uCAAuC;MACjDJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEF,IAAI,EAAE/E,EAAE,CAAC4E;EAAY,CAAC,EAAE;IAAEG,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACzER,IAAI,EAAE5E,MAAM;MACZiF,IAAI,EAAE,CAACjB,mBAAmB;IAC9B,CAAC,EAAE;MACCY,IAAI,EAAE3E;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA,IAAIoF,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG,IAAIxF,cAAc,CAAC,qCAAqC,CAAC;AACrG;AACA;AACA;AACA;AACA,MAAMyF,iBAAiB,SAAShG,gBAAgB,CAAC;EAC7C;EACA,IAAIiG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAK,IAAI,CAACC,SAAS,IAAI,IAAI,CAACA,SAAS,CAACF,UAAW;EAC5E;EACA,IAAIA,UAAUA,CAACG,KAAK,EAAE;IAClB,IAAI,CAACF,WAAW,GAAGE,KAAK;EAC5B;EACA;EACA,IAAIC,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACC,eAAe,IAAK,IAAI,CAACH,SAAS,IAAI,IAAI,CAACA,SAAS,CAACE,cAAe;EACpF;EACA,IAAIA,cAAcA,CAACD,KAAK,EAAE;IACtB,IAAI,CAACE,eAAe,GAAGF,KAAK;EAChC;EACAzB,WAAWA,CAACwB,SAAS,EAAEI,kBAAkB,EAAEC,0BAA0B,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,cAAc,EAAEC,cAAc,EAAE;IACjI,KAAK,CAACT,SAAS,EAAEI,kBAAkB,EAAEC,0BAA0B,CAAC;IAChE,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACT,WAAW,GAAG,KAAK;IACxB;IACA,IAAI,CAACW,WAAW,GAAG,IAAIlG,YAAY,CAAC,CAAC;IACrC;IACA,IAAI,CAACmG,aAAa,GAAG,IAAInG,YAAY,CAAC,CAAC;IACvC;IACA,IAAI,CAACoG,aAAa,GAAG,IAAI1E,OAAO,CAAC,CAAC;IAClC;IACA,IAAI,CAAC2E,SAAS,GAAG,8BAA8BlB,QAAQ,EAAE,EAAE;IAC3D,IAAI,CAACK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACO,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACO,mBAAmB,GAAGN,cAAc,KAAK,gBAAgB;IAC9D,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACX,UAAU,GAAGW,cAAc,CAACX,UAAU;IAC/C;EACJ;EACA;EACAiB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAACf,SAAS,EAAE;MAChB,OAAO,IAAI,CAACgB,QAAQ,IAAI,IAAI,CAAChB,SAAS,CAACiB,WAAW,KAAK,SAAS;IACpE;IACA,OAAO,KAAK;EAChB;EACA;EACApD,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACmD,QAAQ,GAAG,UAAU,GAAG,WAAW;EACnD;EACA;EACAE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;EAClC;EACA;EACAG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACH,QAAQ,GAAG,KAAK;EACzB;EACA;EACAI,IAAIA,CAAA,EAAG;IACH,IAAI,CAACJ,QAAQ,GAAG,IAAI;EACxB;EACAK,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACC,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC5C,eAAe,KAAK,IAAI,EAAE;MACjE;MACA,IAAI,CAAC6C,MAAM,CACNC,IAAI,CAAC5F,SAAS,CAAC,IAAI,CAAC,EAAEC,MAAM,CAAC,MAAM,IAAI,CAACmF,QAAQ,IAAI,CAAC,IAAI,CAACS,OAAO,CAAC,EAAE3F,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5E4F,SAAS,CAAC,MAAM;QACjB,IAAI,CAACD,OAAO,GAAG,IAAIzH,cAAc,CAAC,IAAI,CAACsH,YAAY,CAAC7C,SAAS,EAAE,IAAI,CAAC6B,iBAAiB,CAAC;MAC1F,CAAC,CAAC;IACN;EACJ;EACAqB,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAI,CAAChB,aAAa,CAACiB,IAAI,CAACD,OAAO,CAAC;EACpC;EACAE,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAAClB,aAAa,CAACmB,QAAQ,CAAC,CAAC;EACjC;EACA;EACAC,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACC,KAAK,EAAE;MACZ,MAAMC,cAAc,GAAG,IAAI,CAAC3B,SAAS,CAAC4B,aAAa;MACnD,MAAMC,WAAW,GAAG,IAAI,CAACH,KAAK,CAACI,aAAa;MAC5C,OAAOH,cAAc,KAAKE,WAAW,IAAIA,WAAW,CAACE,QAAQ,CAACJ,cAAc,CAAC;IACjF;IACA,OAAO,KAAK;EAChB;EACA;EACAK,iBAAiBA,CAACC,KAAK,EAAE;IACrB,IAAI,CAACC,kBAAkB,CAACD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC1B,mBAAmB,IAAI,IAAI,CAACmB,KAAK,EAAE;MACvE;MACA;MACA;MACA,IAAI,CAACA,KAAK,EAAEI,aAAa,CAACK,YAAY,CAAC,OAAO,EAAE,EAAE,CAAC;IACvD;EACJ;EACA;EACAC,cAAcA,CAACH,KAAK,EAAE;IAClB,IAAI,CAACC,kBAAkB,CAACD,KAAK,CAAC,EAAE;MAC5B,IAAIA,KAAK,CAACI,OAAO,KAAK,UAAU,EAAE;QAC9B,IAAI,CAAClC,WAAW,CAACmC,IAAI,CAAC,CAAC;MAC3B,CAAC,MACI,IAAIL,KAAK,CAACI,OAAO,KAAK,WAAW,EAAE;QACpC,IAAI,CAACjC,aAAa,CAACkC,IAAI,CAAC,CAAC;MAC7B;MACA;MACA,IAAI,CAAC,IAAI,CAAC/B,mBAAmB,IAAI,IAAI,CAACmB,KAAK,EAAE;QACzC,IAAI,CAACA,KAAK,CAACI,aAAa,CAACS,eAAe,CAAC,OAAO,CAAC;MACrD;IACJ;EACJ;EACA;IAAS,IAAI,CAACnE,IAAI,YAAAoE,0BAAAlE,CAAA;MAAA,YAAAA,CAAA,IAAwFgB,iBAAiB,EAvI3B1F,EAAE,CAAA2E,iBAAA,CAuI2ChB,aAAa,OAvI1D3D,EAAE,CAAA2E,iBAAA,CAuIqG3E,EAAE,CAAC6I,iBAAiB,GAvI3H7I,EAAE,CAAA2E,iBAAA,CAuIsInC,EAAE,CAACsG,yBAAyB,GAvIpK9I,EAAE,CAAA2E,iBAAA,CAuI+K3E,EAAE,CAAC+I,gBAAgB,GAvIpM/I,EAAE,CAAA2E,iBAAA,CAuI+MlC,QAAQ,GAvIzNzC,EAAE,CAAA2E,iBAAA,CAuIoOrE,qBAAqB,MAvI3PN,EAAE,CAAA2E,iBAAA,CAuIsRc,mCAAmC;IAAA,CAA4D;EAAE;EACzd;IAAS,IAAI,CAACuD,IAAI,kBAxI8EhJ,EAAE,CAAAiJ,iBAAA;MAAAlE,IAAA,EAwIJW,iBAAiB;MAAAV,SAAA;MAAAkE,cAAA,WAAAC,iCAAArG,EAAA,EAAAC,GAAA,EAAAqG,QAAA;QAAA,IAAAtG,EAAA;UAxIf9C,EAAE,CAAAqJ,cAAA,CAAAD,QAAA,EA6IzBhF,wBAAwB;QAAA;QAAA,IAAAtB,EAAA;UAAA,IAAAwG,EAAA;UA7IDtJ,EAAE,CAAAuJ,cAAA,CAAAD,EAAA,GAAFtJ,EAAE,CAAAwJ,WAAA,QAAAzG,GAAA,CAAAoE,YAAA,GAAAmC,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,WAAAC,wBAAA7G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAA4J,WAAA,CAAAlH,GAAA;QAAA;QAAA,IAAAI,EAAA;UAAA,IAAAwG,EAAA;UAAFtJ,EAAE,CAAAuJ,cAAA,CAAAD,EAAA,GAAFtJ,EAAE,CAAAwJ,WAAA,QAAAzG,GAAA,CAAA+E,KAAA,GAAAwB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAI,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,+BAAAlH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAiK,WAAA,iBAAAlH,GAAA,CAAA8D,QAwIY,CAAC,4BAAA9D,GAAA,CAAA4D,mBAAD,CAAC,gCAAjB5D,GAAA,CAAA6D,WAAA,CAAY,CAAI,CAAC;QAAA;MAAA;MAAAsD,MAAA;QAAAvE,UAAA,GAxIf3F,EAAE,CAAAmK,YAAA,CAAAC,0BAAA,8BAwIuH7J,gBAAgB;QAAAwF,cAAA;MAAA;MAAAsE,OAAA;QAAA9D,WAAA;QAAAC,aAAA;MAAA;MAAA8D,QAAA;MAAArF,UAAA;MAAAsF,QAAA,GAxIzIvK,EAAE,CAAAwK,kBAAA,CAwImd;MAC7iB;MACA;MACA;QAAEC,OAAO,EAAE9G,aAAa;QAAE+G,QAAQ,EAAEpF;MAAU,CAAC,EAC/C;QAAEmF,OAAO,EAAEtG,mBAAmB;QAAEwG,WAAW,EAAEjF;MAAkB,CAAC,CACnE,GA7I2F1F,EAAE,CAAA4K,wBAAA,EAAF5K,EAAE,CAAA6K,0BAAA,EAAF7K,EAAE,CAAA8K,oBAAA,EAAF9K,EAAE,CAAA+K,mBAAA;MAAAC,kBAAA,EAAApI,GAAA;MAAAqI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAvI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAwI,GAAA,GAAFtL,EAAE,CAAAuL,gBAAA;UAAFvL,EAAE,CAAAwL,eAAA,CAAA7I,GAAA;UAAF3C,EAAE,CAAAyL,YAAA,EA6I0R,CAAC;UA7I7RzL,EAAE,CAAAmD,cAAA,eA6IukB,CAAC;UA7I1kBnD,EAAE,CAAA0L,UAAA,kCAAAC,wEAAAC,MAAA;YAAF5L,EAAE,CAAA6L,aAAA,CAAAP,GAAA;YAAA,OAAFtL,EAAE,CAAA8L,WAAA,CA6I4a/I,GAAA,CAAAqF,iBAAA,CAAAwD,MAAwB,CAAC;UAAA,CAAC,CAAC,iCAAAG,uEAAAH,MAAA;YA7Izc5L,EAAE,CAAA6L,aAAA,CAAAP,GAAA;YAAA,OAAFtL,EAAE,CAAA8L,WAAA,CA6Ise/I,GAAA,CAAAyF,cAAA,CAAAoD,MAAqB,CAAC;UAAA,CAAC,CAAC;UA7IhgB5L,EAAE,CAAAmD,cAAA,YA6ImnB,CAAC;UA7ItnBnD,EAAE,CAAAyL,YAAA,KA6IkpB,CAAC;UA7IrpBzL,EAAE,CAAAgM,UAAA,IAAAnJ,wCAAA,wBA6ImsB,CAAC;UA7ItsB7C,EAAE,CAAAsD,YAAA,CA6I2tB,CAAC;UA7I9tBtD,EAAE,CAAAyL,YAAA,KA6IkxB,CAAC;UA7IrxBzL,EAAE,CAAAsD,YAAA,CA6I0xB,CAAC;QAAA;QAAA,IAAAR,EAAA;UA7I7xB9C,EAAE,CAAAiM,SAAA,CA6I2Y,CAAC;UA7I9YjM,EAAE,CAAAyD,UAAA,mBAAAV,GAAA,CAAAW,iBAAA,EA6I2Y,CAAC,OAAAX,GAAA,CAAAmJ,EAA8K,CAAC;UA7I7jBlM,EAAE,CAAAmM,WAAA,oBAAApJ,GAAA,CAAA2D,SAAA;UAAF1G,EAAE,CAAAiM,SAAA,EA6IksB,CAAC;UA7IrsBjM,EAAE,CAAAyD,UAAA,oBAAAV,GAAA,CAAAuE,OA6IksB,CAAC;QAAA;MAAA;MAAA8E,YAAA,GAA0kFtM,eAAe;MAAAuM,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAAmI,CAAC3I,sBAAsB,CAACG,aAAa;MAAC;MAAAyI,eAAA;IAAA,EAAiG;EAAE;AAC9oH;AACA;EAAA,QAAAvH,SAAA,oBAAAA,SAAA,KA/IoGlF,EAAE,CAAAmF,iBAAA,CA+IXO,iBAAiB,EAAc,CAAC;IAC/GX,IAAI,EAAEvE,SAAS;IACf4E,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,qBAAqB;MAAEiF,QAAQ,EAAE,mBAAmB;MAAEgC,aAAa,EAAE7L,iBAAiB,CAACiM,IAAI;MAAED,eAAe,EAAE/L,uBAAuB,CAACiM,MAAM;MAAEC,UAAU,EAAE,CAAC/I,sBAAsB,CAACG,aAAa,CAAC;MAAE6I,SAAS,EAAE;MACpN;MACA;MACA;QAAEpC,OAAO,EAAE9G,aAAa;QAAE+G,QAAQ,EAAEpF;MAAU,CAAC,EAC/C;QAAEmF,OAAO,EAAEtG,mBAAmB;QAAEwG,WAAW,EAAEjF;MAAkB,CAAC,CACnE;MAAEoH,IAAI,EAAE;QACL,OAAO,EAAE,qBAAqB;QAC9B,sBAAsB,EAAE,UAAU;QAClC,iCAAiC,EAAE,qBAAqB;QACxD,qCAAqC,EAAE;MAC3C,CAAC;MAAE7H,UAAU,EAAE,IAAI;MAAE8H,OAAO,EAAE,CAACjN,eAAe,CAAC;MAAEsL,QAAQ,EAAE,mkBAAmkB;MAAEiB,MAAM,EAAE,CAAC,u7EAAu7E;IAAE,CAAC;EAC/kG,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtH,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CR,IAAI,EAAE3E;IACV,CAAC,EAAE;MACC2E,IAAI,EAAEpE;IACV,CAAC,EAAE;MACCoE,IAAI,EAAE5E,MAAM;MACZiF,IAAI,EAAE,CAACzB,aAAa;IACxB,CAAC;EAAE,CAAC,EAAE;IAAEoB,IAAI,EAAE/E,EAAE,CAAC6I;EAAkB,CAAC,EAAE;IAAE9D,IAAI,EAAEvC,EAAE,CAACsG;EAA0B,CAAC,EAAE;IAAE/D,IAAI,EAAE/E,EAAE,CAAC+I;EAAiB,CAAC,EAAE;IAAEhE,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACzIR,IAAI,EAAE5E,MAAM;MACZiF,IAAI,EAAE,CAAC3C,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEsC,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCR,IAAI,EAAE3E;IACV,CAAC,EAAE;MACC2E,IAAI,EAAE5E,MAAM;MACZiF,IAAI,EAAE,CAAC9E,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEyE,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCR,IAAI,EAAE5E,MAAM;MACZiF,IAAI,EAAE,CAACK,mCAAmC;IAC9C,CAAC,EAAE;MACCV,IAAI,EAAE3E;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEuF,UAAU,EAAE,CAAC;MACtCZ,IAAI,EAAEnE,KAAK;MACXwE,IAAI,EAAE,CAAC;QAAErB,SAAS,EAAExD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwF,cAAc,EAAE,CAAC;MACjBhB,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAE2F,WAAW,EAAE,CAAC;MACdxB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAE2F,aAAa,EAAE,CAAC;MAChBzB,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEsG,YAAY,EAAE,CAAC;MACfpC,IAAI,EAAEjE,YAAY;MAClBsE,IAAI,EAAE,CAAChB,wBAAwB;IACnC,CAAC,CAAC;IAAE0D,KAAK,EAAE,CAAC;MACR/C,IAAI,EAAEhE,SAAS;MACfqE,IAAI,EAAE,CAAC,MAAM;IACjB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAASkD,kBAAkBA,CAACD,KAAK,EAAE;EAC/B,OAAOA,KAAK,CAAC2E,SAAS,KAAK,MAAM;AACrC;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,CAAC;EAC7B;IAAS,IAAI,CAACzI,IAAI,YAAA0I,mCAAAxI,CAAA;MAAA,YAAAA,CAAA,IAAwFuI,0BAA0B;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACpI,IAAI,kBAzM8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAyMJkI,0BAA0B;MAAAjI,SAAA;MAAA6E,SAAA;MAAA5E,UAAA;IAAA,EAA6G;EAAE;AAC3O;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA3MoGlF,EAAE,CAAAmF,iBAAA,CA2MX8H,0BAA0B,EAAc,CAAC;IACxHlI,IAAI,EAAE7E,SAAS;IACfkF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gBAAgB;MAC1ByH,IAAI,EAAE;QACFK,KAAK,EAAE;MACX,CAAC;MACDlI,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMmI,uBAAuB,CAAC;EAC1B/I,WAAWA,CAACgJ,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEtH,kBAAkB,EAAEK,cAAc,EAAED,cAAc,EAAEmH,QAAQ,EAAE;IACtG,IAAI,CAACH,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACtH,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACI,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACoH,yBAAyB,GAAGzL,YAAY,CAACC,KAAK;IACnD;IACA,IAAI,CAACuL,QAAQ,GAAG,CAAC;IACjB,MAAME,yBAAyB,GAAGL,KAAK,CAACxH,SAAS,GAC3CwH,KAAK,CAACxH,SAAS,CAAC8H,aAAa,CAACtG,IAAI,CAAC3F,MAAM,CAAC+F,OAAO,IAAI,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAC7GxF,KAAK;IACX,IAAI,CAACuL,QAAQ,GAAGI,QAAQ,CAACJ,QAAQ,IAAI,EAAE,CAAC,IAAI,CAAC;IAC7C;IACA;IACA,IAAI,CAACC,yBAAyB,GAAGvL,KAAK,CAACmL,KAAK,CAACjG,MAAM,EAAEiG,KAAK,CAACQ,MAAM,EAAEH,yBAAyB,EAAEL,KAAK,CAAC5G,aAAa,CAACY,IAAI,CAAC3F,MAAM,CAAC+F,OAAO,IAAI;MACrI,OAAO,CAAC,EAAEA,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,gBAAgB,CAAC,CAAC;IACxF,CAAC,CAAC,CAAC,CAAC,CAACF,SAAS,CAAC,MAAM,IAAI,CAACtB,kBAAkB,CAAC6H,YAAY,CAAC,CAAC,CAAC;IAC5D;IACAT,KAAK,CAACQ,MAAM,CACPxG,IAAI,CAAC3F,MAAM,CAAC,MAAM2L,KAAK,CAACxF,cAAc,CAAC,CAAC,CAAC,CAAC,CAC1CN,SAAS,CAAC,MAAMgG,aAAa,CAACQ,QAAQ,CAACT,QAAQ,EAAE,SAAS,CAAC,CAAC;IACjE,IAAIhH,cAAc,EAAE;MAChB,IAAI,CAAC0H,cAAc,GAAG1H,cAAc,CAAC0H,cAAc;MACnD,IAAI,CAACC,eAAe,GAAG3H,cAAc,CAAC2H,eAAe;IACzD;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACb,KAAK,CAACa,QAAQ;EAC9B;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;MAChB,IAAI,CAACb,KAAK,CAACtG,MAAM,CAAC,CAAC;IACvB;EACJ;EACA;EACAqH,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACf,KAAK,CAACxG,QAAQ;EAC9B;EACA;EACAnD,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC2J,KAAK,CAAC3J,iBAAiB,CAAC,CAAC;EACzC;EACA;EACA2K,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAChB,KAAK,CAACnB,EAAE;EACxB;EACA;EACAoC,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACjB,KAAK,CAACtH,cAAc;EACpC;EACA;EACAwI,WAAWA,CAAA,EAAG;IACV,OAAO,CAAC,IAAI,CAAClB,KAAK,CAAC1H,UAAU,IAAI,CAAC,IAAI,CAAC0H,KAAK,CAACa,QAAQ;EACzD;EACA;AACJ;AACA;AACA;EACIM,gBAAgBA,CAAA,EAAG;IACf,MAAMC,UAAU,GAAG,IAAI,CAACL,WAAW,CAAC,CAAC;IACrC,IAAIK,UAAU,IAAI,IAAI,CAACT,cAAc,EAAE;MACnC,OAAO,IAAI,CAACA,cAAc;IAC9B,CAAC,MACI,IAAI,CAACS,UAAU,IAAI,IAAI,CAACR,eAAe,EAAE;MAC1C,OAAO,IAAI,CAACA,eAAe;IAC/B;IACA,OAAO,IAAI;EACf;EACA;EACAS,QAAQA,CAACrG,KAAK,EAAE;IACZ,QAAQA,KAAK,CAACsG,OAAO;MACjB;MACA,KAAK7M,KAAK;MACV,KAAKF,KAAK;QACN,IAAI,CAACC,cAAc,CAACwG,KAAK,CAAC,EAAE;UACxBA,KAAK,CAACuG,cAAc,CAAC,CAAC;UACtB,IAAI,CAACT,OAAO,CAAC,CAAC;QAClB;QACA;MACJ;QACI,IAAI,IAAI,CAACd,KAAK,CAACxH,SAAS,EAAE;UACtB,IAAI,CAACwH,KAAK,CAACxH,SAAS,CAACgJ,oBAAoB,CAACxG,KAAK,CAAC;QACpD;QACA;IACR;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIyG,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACnB,IAAID,MAAM,EAAE;MACR,IAAI,CAACxB,aAAa,CAACQ,QAAQ,CAAC,IAAI,CAACT,QAAQ,EAAEyB,MAAM,EAAEC,OAAO,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAAC1B,QAAQ,CAACpF,aAAa,CAAC4G,KAAK,CAACE,OAAO,CAAC;IAC9C;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC1B,aAAa,CAAC2B,OAAO,CAAC,IAAI,CAAC5B,QAAQ,CAAC,CAAC/F,SAAS,CAACwH,MAAM,IAAI;MAC1D,IAAIA,MAAM,IAAI,IAAI,CAAC1B,KAAK,CAACxH,SAAS,EAAE;QAChC,IAAI,CAACwH,KAAK,CAACxH,SAAS,CAACsJ,kBAAkB,CAAC,IAAI,CAAC;MACjD;IACJ,CAAC,CAAC;EACN;EACAxH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8F,yBAAyB,CAAC2B,WAAW,CAAC,CAAC;IAC5C,IAAI,CAAC7B,aAAa,CAAC8B,cAAc,CAAC,IAAI,CAAC/B,QAAQ,CAAC;EACpD;EACA;IAAS,IAAI,CAAC9I,IAAI,YAAA8K,gCAAA5K,CAAA;MAAA,YAAAA,CAAA,IAAwF0I,uBAAuB,EA9UjCpN,EAAE,CAAA2E,iBAAA,CA8UiDe,iBAAiB,MA9UpE1F,EAAE,CAAA2E,iBAAA,CA8U2F3E,EAAE,CAACuP,UAAU,GA9U1GvP,EAAE,CAAA2E,iBAAA,CA8UqHpD,EAAE,CAACiO,YAAY,GA9UtIxP,EAAE,CAAA2E,iBAAA,CA8UiJ3E,EAAE,CAAC6I,iBAAiB,GA9UvK7I,EAAE,CAAA2E,iBAAA,CA8UkLc,mCAAmC,MA9UvNzF,EAAE,CAAA2E,iBAAA,CA8UkPrE,qBAAqB,MA9UzQN,EAAE,CAAAyP,iBAAA,CA8UoS,UAAU;IAAA,CAA6D;EAAE;EAC/c;IAAS,IAAI,CAACzG,IAAI,kBA/U8EhJ,EAAE,CAAAiJ,iBAAA;MAAAlE,IAAA,EA+UJqI,uBAAuB;MAAApI,SAAA;MAAA6E,SAAA,WAA2Q,QAAQ;MAAAC,QAAA;MAAAC,YAAA,WAAA2F,qCAAA5M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/UxS9C,EAAE,CAAA0L,UAAA,mBAAAiE,iDAAA;YAAA,OA+UJ5M,GAAA,CAAAoL,OAAA,CAAQ,CAAC;UAAA,CAAa,CAAC,qBAAAyB,mDAAAhE,MAAA;YAAA,OAAvB7I,GAAA,CAAA2L,QAAA,CAAA9C,MAAe,CAAC;UAAA,CAAM,CAAC;QAAA;QAAA,IAAA9I,EAAA;UA/UrB9C,EAAE,CAAAmM,WAAA,OAAApJ,GAAA,CAAAsK,KAAA,CAAA3G,SAAA,cAAA3D,GAAA,CAAAmL,QAAA,IA+UQ,CAAC,GAAAnL,GAAA,CAAAyK,QAAA,mBAAbzK,GAAA,CAAAsL,WAAA,CAAY,CAAC,mBAAbtL,GAAA,CAAAqL,WAAA,CAAY,CAAC,mBAAArL,GAAA,CAAAsK,KAAA,CAAAa,QAAA;UA/UXlO,EAAE,CAAA6P,WAAA,WA+UJ9M,GAAA,CAAAyL,gBAAA,CAAiB,CAAK,CAAC;UA/UrBxO,EAAE,CAAAiK,WAAA,iBA+UJlH,GAAA,CAAAqL,WAAA,CAAY,CAAU,CAAC,yCAAvBrL,GAAA,CAAAuL,kBAAA,CAAmB,CAAC,KAAK,OAAH,CAAC,0CAAvBvL,GAAA,CAAAuL,kBAAA,CAAmB,CAAC,KAAK,QAAH,CAAC,4BAAAvL,GAAA,CAAAsD,cAAA,KAAJ,gBAAG,CAAC;QAAA;MAAA;MAAA6D,MAAA;QAAA8D,cAAA;QAAAC,eAAA;QAAAT,QAAA,GA/UrBxN,EAAE,CAAAmK,YAAA,CAAAC,0BAAA,0BA+UqMtE,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG9E,eAAe,CAAC8E,KAAK,CAAE;MAAA;MAAAb,UAAA;MAAAsF,QAAA,GA/U7PvK,EAAE,CAAA4K,wBAAA,EAAF5K,EAAE,CAAA+K,mBAAA;MAAAC,kBAAA,EAAA/H,GAAA;MAAAgI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA0E,iCAAAhN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAwL,eAAA,CAAAxI,GAAA;UAAFhD,EAAE,CAAAmD,cAAA,aA+UuiC,CAAC;UA/U1iCnD,EAAE,CAAAyL,YAAA,EA+U+lC,CAAC;UA/UlmCzL,EAAE,CAAAyL,YAAA,KA+U6pC,CAAC;UA/UhqCzL,EAAE,CAAAyL,YAAA,KA+U0rC,CAAC;UA/U7rCzL,EAAE,CAAAsD,YAAA,CA+UmsC,CAAC;UA/UtsCtD,EAAE,CAAAgM,UAAA,IAAA9I,8CAAA,iBA+U4tC,CAAC;QAAA;QAAA,IAAAJ,EAAA;UA/U/tC9C,EAAE,CAAAiK,WAAA,6BAAAlH,GAAA,CAAAwL,WAAA,EA+UsiC,CAAC;UA/UziCvO,EAAE,CAAAiM,SAAA,EA+UwiD,CAAC;UA/U3iDjM,EAAE,CAAA+P,aAAA,IAAAhN,GAAA,CAAAwL,WAAA,WA+UwiD,CAAC;QAAA;MAAA;MAAAlC,MAAA;MAAAC,aAAA;MAAAC,IAAA;QAAAC,SAAA,EAAgnH,CAAC3I,sBAAsB,CAACC,eAAe;MAAC;MAAA2I,eAAA;IAAA,EAAiG;EAAE;AAC14K;AACA;EAAA,QAAAvH,SAAA,oBAAAA,SAAA,KAjVoGlF,EAAE,CAAAmF,iBAAA,CAiVXiI,uBAAuB,EAAc,CAAC;IACrHrI,IAAI,EAAEvE,SAAS;IACf4E,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,4BAA4B;MAAEiH,aAAa,EAAE7L,iBAAiB,CAACiM,IAAI;MAAED,eAAe,EAAE/L,uBAAuB,CAACiM,MAAM;MAAEC,UAAU,EAAE,CAAC/I,sBAAsB,CAACC,eAAe,CAAC;MAAEgJ,IAAI,EAAE;QACzL,OAAO,EAAE,gDAAgD;QACzD,MAAM,EAAE,QAAQ;QAChB,WAAW,EAAE,iBAAiB;QAC9B,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,eAAe;QACvC,sBAAsB,EAAE,gBAAgB;QACxC,sBAAsB,EAAE,eAAe;QACvC,8CAA8C,EAAE,kCAAkC;QAClF,+CAA+C,EAAE,mCAAmC;QACpF,iCAAiC,EAAE,qCAAqC;QACxE,gBAAgB,EAAE,oBAAoB;QACtC,SAAS,EAAE,WAAW;QACtB,WAAW,EAAE;MACjB,CAAC;MAAE7H,UAAU,EAAE,IAAI;MAAEmG,QAAQ,EAAE,olBAAolB;MAAEiB,MAAM,EAAE,CAAC,mlHAAmlH;IAAE,CAAC;EAChuI,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtH,IAAI,EAAEW,iBAAiB;IAAEH,UAAU,EAAE,CAAC;MACvDR,IAAI,EAAE9D;IACV,CAAC;EAAE,CAAC,EAAE;IAAE8D,IAAI,EAAE/E,EAAE,CAACuP;EAAW,CAAC,EAAE;IAAExK,IAAI,EAAExD,EAAE,CAACiO;EAAa,CAAC,EAAE;IAAEzK,IAAI,EAAE/E,EAAE,CAAC6I;EAAkB,CAAC,EAAE;IAAE9D,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MACtHR,IAAI,EAAE5E,MAAM;MACZiF,IAAI,EAAE,CAACK,mCAAmC;IAC9C,CAAC,EAAE;MACCV,IAAI,EAAE3E;IACV,CAAC;EAAE,CAAC,EAAE;IAAE2E,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCR,IAAI,EAAE3E;IACV,CAAC,EAAE;MACC2E,IAAI,EAAE5E,MAAM;MACZiF,IAAI,EAAE,CAAC9E,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEyE,IAAI,EAAEO,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCR,IAAI,EAAE7D,SAAS;MACfkE,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4I,cAAc,EAAE,CAAC;MAC1CjJ,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAEqN,eAAe,EAAE,CAAC;MAClBlJ,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAE4M,QAAQ,EAAE,CAAC;MACXzI,IAAI,EAAEnE,KAAK;MACXwE,IAAI,EAAE,CAAC;QACCrB,SAAS,EAAG+B,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG9E,eAAe,CAAC8E,KAAK;MACpE,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMkK,4BAA4B,CAAC;EAC/B;IAAS,IAAI,CAACxL,IAAI,YAAAyL,qCAAAvL,CAAA;MAAA,YAAAA,CAAA,IAAwFsL,4BAA4B;IAAA,CAAmD;EAAE;EAC3L;IAAS,IAAI,CAACnL,IAAI,kBAjY8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAiYJiL,4BAA4B;MAAAhL,SAAA;MAAA6E,SAAA;MAAA5E,UAAA;IAAA,EAA4I;EAAE;AAC5Q;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAnYoGlF,EAAE,CAAAmF,iBAAA,CAmYX6K,4BAA4B,EAAc,CAAC;IAC1HjL,IAAI,EAAE7E,SAAS;IACfkF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCyH,IAAI,EAAE;QACFK,KAAK,EAAE;MACX,CAAC;MACDlI,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA,MAAMiL,sBAAsB,CAAC;EACzB;IAAS,IAAI,CAAC1L,IAAI,YAAA2L,+BAAAzL,CAAA;MAAA,YAAAA,CAAA,IAAwFwL,sBAAsB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAACrL,IAAI,kBAlZ8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EAkZJmL,sBAAsB;MAAAlL,SAAA;MAAA6E,SAAA;MAAA5E,UAAA;IAAA,EAAgI;EAAE;AAC1P;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApZoGlF,EAAE,CAAAmF,iBAAA,CAoZX+K,sBAAsB,EAAc,CAAC;IACpHnL,IAAI,EAAE7E,SAAS;IACfkF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3ByH,IAAI,EAAE;QACFK,KAAK,EAAE;MACX,CAAC;MACDlI,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA,MAAMmL,YAAY,SAASzQ,YAAY,CAAC;EACpC0E,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGgM,SAAS,CAAC;IACnB;IACA,IAAI,CAACC,WAAW,GAAG,IAAInP,SAAS,CAAC,CAAC;IAClC;IACA,IAAI,CAACwE,UAAU,GAAG,KAAK;IACvB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACmB,WAAW,GAAG,SAAS;IAC5B;IACA,IAAI,CAACf,cAAc,GAAG,OAAO;EACjC;EACAmB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACqJ,QAAQ,CAAC9I,OAAO,CAChBJ,IAAI,CAAC5F,SAAS,CAAC,IAAI,CAAC8O,QAAQ,CAAC,CAAC,CAC9BhJ,SAAS,CAAEiJ,OAAO,IAAK;MACxB,IAAI,CAACF,WAAW,CAACG,KAAK,CAACD,OAAO,CAAC9O,MAAM,CAACgP,MAAM,IAAIA,MAAM,CAACrD,KAAK,CAACxH,SAAS,KAAK,IAAI,CAAC,CAAC;MACjF,IAAI,CAACyK,WAAW,CAACK,eAAe,CAAC,CAAC;IACtC,CAAC,CAAC;IACF,IAAI,CAACC,WAAW,GAAG,IAAIpP,eAAe,CAAC,IAAI,CAAC8O,WAAW,CAAC,CAACO,QAAQ,CAAC,CAAC,CAACC,cAAc,CAAC,CAAC;EACxF;EACA;EACAjC,oBAAoBA,CAACxG,KAAK,EAAE;IACxB,IAAI,CAACuI,WAAW,CAACG,SAAS,CAAC1I,KAAK,CAAC;EACrC;EACA8G,kBAAkBA,CAACuB,MAAM,EAAE;IACvB,IAAI,CAACE,WAAW,CAACI,gBAAgB,CAACN,MAAM,CAAC;EAC7C;EACA/I,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACiJ,WAAW,EAAEK,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACX,WAAW,CAACW,OAAO,CAAC,CAAC;EAC9B;EACA;IAAS,IAAI,CAACzM,IAAI;MAAA,IAAA0M,yBAAA;MAAA,gBAAAC,qBAAAzM,CAAA;QAAA,QAAAwM,yBAAA,KAAAA,yBAAA,GA1c8ElR,EAAE,CAAAoR,qBAAA,CA0cQhB,YAAY,IAAA1L,CAAA,IAAZ0L,YAAY;MAAA;IAAA,IAAqD;EAAE;EAC7K;IAAS,IAAI,CAACvL,IAAI,kBA3c8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EA2cJqL,YAAY;MAAApL,SAAA;MAAAkE,cAAA,WAAAmI,4BAAAvO,EAAA,EAAAC,GAAA,EAAAqG,QAAA;QAAA,IAAAtG,EAAA;UA3cV9C,EAAE,CAAAqJ,cAAA,CAAAD,QAAA,EAgd1CgE,uBAAuB;QAAA;QAAA,IAAAtK,EAAA;UAAA,IAAAwG,EAAA;UAhdiBtJ,EAAE,CAAAuJ,cAAA,CAAAD,EAAA,GAAFtJ,EAAE,CAAAwJ,WAAA,QAAAzG,GAAA,CAAAwN,QAAA,GAAAjH,EAAA;QAAA;MAAA;MAAAO,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAuH,0BAAAxO,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF9C,EAAE,CAAAiK,WAAA,wBAAAlH,GAAA,CAAAwO,KA2cO,CAAC;QAAA;MAAA;MAAArH,MAAA;QAAAvE,UAAA,GA3cV3F,EAAE,CAAAmK,YAAA,CAAAC,0BAAA,8BA2c4G7J,gBAAgB;QAAAuG,WAAA;QAAAf,cAAA;MAAA;MAAAuE,QAAA;MAAArF,UAAA;MAAAsF,QAAA,GA3c9HvK,EAAE,CAAAwK,kBAAA,CA2cgT,CAC1Y;QACIC,OAAO,EAAE9G,aAAa;QACtBgH,WAAW,EAAEyF;MACjB,CAAC,CACJ,GAhd2FpQ,EAAE,CAAA4K,wBAAA,EAAF5K,EAAE,CAAA6K,0BAAA;IAAA,EAgduE;EAAE;AAC/K;AACA;EAAA,QAAA3F,SAAA,oBAAAA,SAAA,KAldoGlF,EAAE,CAAAmF,iBAAA,CAkdXiL,YAAY,EAAc,CAAC;IAC1GrL,IAAI,EAAE7E,SAAS;IACfkF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBiF,QAAQ,EAAE,cAAc;MACxBuC,SAAS,EAAE,CACP;QACIpC,OAAO,EAAE9G,aAAa;QACtBgH,WAAW,EAAEyF;MACjB,CAAC,CACJ;MACDtD,IAAI,EAAE;QACFK,KAAK,EAAE,eAAe;QACtB;QACA;QACA,6BAA6B,EAAE;MACnC,CAAC;MACDlI,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEsL,QAAQ,EAAE,CAAC;MACzBxL,IAAI,EAAE3D,eAAe;MACrBgE,IAAI,EAAE,CAACgI,uBAAuB,EAAE;QAAEoE,WAAW,EAAE;MAAK,CAAC;IACzD,CAAC,CAAC;IAAE7L,UAAU,EAAE,CAAC;MACbZ,IAAI,EAAEnE,KAAK;MACXwE,IAAI,EAAE,CAAC;QAAErB,SAAS,EAAExD;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuG,WAAW,EAAE,CAAC;MACd/B,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAEmF,cAAc,EAAE,CAAC;MACjBhB,IAAI,EAAEnE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6Q,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACjN,IAAI,YAAAkN,2BAAAhN,CAAA;MAAA,YAAAA,CAAA,IAAwF+M,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACE,IAAI,kBAnf8E3R,EAAE,CAAA4R,gBAAA;MAAA7M,IAAA,EAmfS0M;IAAkB,EAezF;EAAE;EACtC;IAAS,IAAI,CAACI,IAAI,kBAngB8E7R,EAAE,CAAA8R,gBAAA;MAAA/E,OAAA,GAmgBuCzL,eAAe,EAChJ1B,kBAAkB,EAClBG,YAAY;IAAA,EAAI;EAAE;AAC9B;AACA;EAAA,QAAAmF,SAAA,oBAAAA,SAAA,KAvgBoGlF,EAAE,CAAAmF,iBAAA,CAugBXsM,kBAAkB,EAAc,CAAC;IAChH1M,IAAI,EAAE1D,QAAQ;IACd+D,IAAI,EAAE,CAAC;MACC2H,OAAO,EAAE,CACLzL,eAAe,EACf1B,kBAAkB,EAClBG,YAAY,EACZqQ,YAAY,EACZ1K,iBAAiB,EACjBuH,0BAA0B,EAC1BG,uBAAuB,EACvB8C,sBAAsB,EACtBF,4BAA4B,EAC5B5L,wBAAwB,CAC3B;MACD2N,OAAO,EAAE,CACL3B,YAAY,EACZ1K,iBAAiB,EACjBuH,0BAA0B,EAC1BG,uBAAuB,EACvB8C,sBAAsB,EACtBF,4BAA4B,EAC5B5L,wBAAwB;IAEhC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASR,gCAAgC,EAAED,aAAa,EAAEQ,mBAAmB,EAAEsB,mCAAmC,EAAE2K,YAAY,EAAEqB,kBAAkB,EAAE/L,iBAAiB,EAAEuH,0BAA0B,EAAE7I,wBAAwB,EAAE4L,4BAA4B,EAAE5C,uBAAuB,EAAE8C,sBAAsB,EAAErM,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}