{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nlet RegisterPage = class RegisterPage {\n  constructor(formBuilder, router, loadingController, toastController) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.isLoading = false;\n    this.selectedFiles = [];\n    this.selectedInvoice = null;\n    this.invoiceSearchResults = [];\n    this.showInvoiceResults = false;\n    this.complaintTypes = [{\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    }, {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    }, {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    }, {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    }, {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    }, {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }];\n    // Sample invoice data for demonstration\n    this.sampleInvoices = [{\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    }, {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    }, {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    }, {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai'\n    }, {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata'\n    }, {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad'\n    }, {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat'\n    }, {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi'\n    }];\n    this.createForms();\n  }\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      complaintDescription: ['', [Validators.required, Validators.minLength(10)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n  selectComplaintType(type) {\n    this.complaintTypeForm.patchValue({\n      selectedType: type.value\n    });\n  }\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice => invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase()));\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n  selectInvoice(invoice) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: invoice.invoiceNumber\n    });\n  }\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: ''\n    });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n  onSubmitComplaint() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.complaintTypeForm.valid && _this.selectedInvoice && _this.complaintDetailsForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Registering complaint...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate registration process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          const toast = yield _this.toastController.create({\n            message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n            duration: 4000,\n            color: 'success',\n            position: 'top'\n          });\n          yield toast.present();\n          // Navigate to track page\n          _this.router.navigate(['/track']);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please complete all required steps and fill in all required fields.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({\n        attachedFile: files[0]\n      });\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({\n        attachedFile: null\n      });\n    }\n  }\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n  isStepCompleted(step) {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDetailsForm.get('complaintDescription')?.valid || false;\n      case 3:\n        return this.selectedInvoice !== null;\n      case 4:\n        return this.isContactDetailsValid();\n      default:\n        return false;\n    }\n  }\n  isContactDetailsValid() {\n    const contactPersonName = this.complaintDetailsForm.get('contactPersonName');\n    const contactNumber = this.complaintDetailsForm.get('contactNumber');\n    return !!(contactPersonName?.valid && contactNumber?.valid);\n  }\n  getErrorMessage(form, field) {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      selectedType: 'Complaint Type',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      complaintDescription: 'Complaint Description',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  goBackToDescription() {\n    // Reset step 3 and go back to step 2\n    this.selectedInvoice = null;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: ''\n    });\n  }\n  goBackToInvoiceSelection() {\n    // Reset step 4 and go back to step 3\n    // Keep invoice selection but allow changing it\n  }\n  onMoveToContactDetails() {\n    // Trigger when moving from step 3 to step 4\n    // No special action needed, just validation\n  }\n};\nRegisterPage = __decorate([Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss']\n})], RegisterPage);\nexport { RegisterPage };", "map": {"version": 3, "names": ["Component", "Validators", "RegisterPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "isLoading", "selectedFiles", "selectedInvoice", "invoiceSearchResults", "showInvoiceResults", "complaintTypes", "value", "label", "icon", "description", "sampleInvoices", "invoiceNumber", "invoiceDate", "Date", "customerName", "customerAddress", "zone", "operatingUnit", "organization", "billToLocation", "shipToLocation", "createForms", "ngOnInit", "showAllInvoices", "complaintTypeForm", "group", "selectedType", "required", "invoiceSearchForm", "searchTerm", "complaintDetailsForm", "contactPersonName", "<PERSON><PERSON><PERSON><PERSON>", "contactNumber", "pattern", "complaintDescription", "comments", "hasComplaintLetters", "attachedFile", "selectComplaintType", "type", "patchValue", "onInvoiceSearch", "get", "trim", "length", "setTimeout", "filter", "invoice", "toLowerCase", "includes", "selectInvoice", "clearInvoiceSelection", "onSubmitComplaint", "_this", "_asyncToGenerator", "valid", "loading", "create", "message", "duration", "present", "dismiss", "toast", "now", "color", "position", "navigate", "onFileSelected", "event", "files", "target", "Array", "from", "removeFile", "index", "splice", "getSelectedComplaintType", "selected<PERSON><PERSON><PERSON>", "find", "isStepCompleted", "step", "isContactDetailsValid", "getErrorMessage", "form", "field", "control", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "goBack", "goBackToDescription", "goBackToInvoiceSelection", "onMoveToContactDetails", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\n\nexport interface InvoiceData {\n  invoiceNumber: string;\n  invoiceDate: Date;\n  customerName: string;\n  customerAddress: string;\n  zone: string;\n  operatingUnit: string;\n  organization: string;\n  billToLocation: string;\n  shipToLocation: string;\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss'],\n})\nexport class RegisterPage implements OnInit {\n\n  complaintTypeForm!: FormGroup;\n  invoiceSearchForm!: FormGroup;\n  complaintDetailsForm!: FormGroup;\n  isLoading = false;\n  selectedFiles: File[] = [];\n  selectedInvoice: InvoiceData | null = null;\n  invoiceSearchResults: InvoiceData[] = [];\n  showInvoiceResults = false;\n\n  complaintTypes = [\n    {\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    },\n    {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    },\n    {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    },\n    {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    },\n    {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    },\n    {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }\n  ];\n\n  // Sample invoice data for demonstration\n  sampleInvoices: InvoiceData[] = [\n    {\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    },\n    {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    },\n    {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    },\n    {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai'\n    },\n    {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata'\n    },\n    {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad'\n    },\n    {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat'\n    },\n    {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi'\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController\n  ) {\n    this.createForms();\n  }\n\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      complaintDescription: ['', [Validators.required, Validators.minLength(10)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n\n  selectComplaintType(type: any) {\n    this.complaintTypeForm.patchValue({ selectedType: type.value });\n  }\n\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice =>\n          invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n\n  selectInvoice(invoice: InvoiceData) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({ searchTerm: invoice.invoiceNumber });\n  }\n\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({ searchTerm: '' });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n\n  async onSubmitComplaint() {\n    if (this.complaintTypeForm.valid && this.selectedInvoice && this.complaintDetailsForm.valid) {\n      this.isLoading = true;\n\n      const loading = await this.loadingController.create({\n        message: 'Registering complaint...',\n        duration: 3000\n      });\n\n      await loading.present();\n\n      // Simulate registration process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n\n        const toast = await this.toastController.create({\n          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n          duration: 4000,\n          color: 'success',\n          position: 'top'\n        });\n        await toast.present();\n\n        // Navigate to track page\n        this.router.navigate(['/track']);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please complete all required steps and fill in all required fields.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({ attachedFile: files[0] });\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({ attachedFile: null });\n    }\n  }\n\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDetailsForm.get('complaintDescription')?.valid || false;\n      case 3:\n        return this.selectedInvoice !== null;\n      case 4:\n        return this.isContactDetailsValid();\n      default:\n        return false;\n    }\n  }\n\n  isContactDetailsValid(): boolean {\n    const contactPersonName = this.complaintDetailsForm.get('contactPersonName');\n    const contactNumber = this.complaintDetailsForm.get('contactNumber');\n    return !!(contactPersonName?.valid && contactNumber?.valid);\n  }\n\n  getErrorMessage(form: FormGroup, field: string): string {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      selectedType: 'Complaint Type',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      complaintDescription: 'Complaint Description',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n\n  goBackToDescription() {\n    // Reset step 3 and go back to step 2\n    this.selectedInvoice = null;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({ searchTerm: '' });\n  }\n\n  goBackToInvoiceSelection() {\n    // Reset step 4 and go back to step 3\n    // Keep invoice selection but allow changing it\n  }\n\n  onMoveToContactDetails() {\n    // Trigger when moving from step 3 to step 4\n    // No special action needed, just validation\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAAiCC,UAAU,QAAQ,gBAAgB;AAqB5D,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EA8IvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IA7IzB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,eAAe,GAAuB,IAAI;IAC1C,KAAAC,oBAAoB,GAAkB,EAAE;IACxC,KAAAC,kBAAkB,GAAG,KAAK;IAE1B,KAAAC,cAAc,GAAG,CACf;MACEC,KAAK,EAAE,eAAe;MACtBC,KAAK,EAAE,sBAAsB;MAC7BC,IAAI,EAAE,iBAAiB;MACvBC,WAAW,EAAE;KACd,EACD;MACEH,KAAK,EAAE,cAAc;MACrBC,KAAK,EAAE,uBAAuB;MAC9BC,IAAI,EAAE,mBAAmB;MACzBC,WAAW,EAAE;KACd,EACD;MACEH,KAAK,EAAE,iBAAiB;MACxBC,KAAK,EAAE,2BAA2B;MAClCC,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE;KACd,EACD;MACEH,KAAK,EAAE,aAAa;MACpBC,KAAK,EAAE,oBAAoB;MAC3BC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE;KACd,EACD;MACEH,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,iBAAiB;MACxBC,IAAI,EAAE,gBAAgB;MACtBC,WAAW,EAAE;KACd,EACD;MACEH,KAAK,EAAE,SAAS;MAChBC,KAAK,EAAE,yBAAyB;MAChCC,IAAI,EAAE,uBAAuB;MAC7BC,WAAW,EAAE;KACd,CACF;IAED;IACA,KAAAC,cAAc,GAAkB,CAC9B;MACEC,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,uBAAuB;MACrCC,eAAe,EAAE,yDAAyD;MAC1EC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,uBAAuB;MACvCC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,wBAAwB;MACtCC,eAAe,EAAE,mDAAmD;MACpEC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,uBAAuB;MACtCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,8BAA8B;MAC9CC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,wBAAwB;MACtCC,eAAe,EAAE,oDAAoD;MACrEC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,6BAA6B;MAC3CC,eAAe,EAAE,0DAA0D;MAC3EC,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,mBAAmB;MAClCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,+BAA+B;MAC/CC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,kBAAkB;MAChCC,eAAe,EAAE,0CAA0C;MAC3DC,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,oBAAoB;MACnCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,gCAAgC;MAChDC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,oBAAoB;MAClCC,eAAe,EAAE,wDAAwD;MACzEC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,mBAAmB;MACjCC,eAAe,EAAE,2CAA2C;MAC5DC,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,sBAAsB;MACrCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,kCAAkC;MAClDC,cAAc,EAAE;KACjB,EACD;MACET,aAAa,EAAE,cAAc;MAC7BC,WAAW,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACnCC,YAAY,EAAE,wBAAwB;MACtCC,eAAe,EAAE,0DAA0D;MAC3EC,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,yBAAyB;MACzCC,cAAc,EAAE;KACjB,CACF;IAQC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAF,WAAWA,CAAA;IACT,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAAC5B,WAAW,CAAC6B,KAAK,CAAC;MAC9CC,YAAY,EAAE,CAAC,EAAE,EAAEjC,UAAU,CAACkC,QAAQ;KACvC,CAAC;IAEF,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAAChC,WAAW,CAAC6B,KAAK,CAAC;MAC9CI,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;KAClB,CAAC;IAEF,IAAI,CAACC,oBAAoB,GAAG,IAAI,CAAClC,WAAW,CAAC6B,KAAK,CAAC;MACjDM,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACkC,QAAQ,EAAElC,UAAU,CAACuC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACxC,UAAU,CAACkC,QAAQ,EAAElC,UAAU,CAACyC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAC7EC,oBAAoB,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAACkC,QAAQ,EAAElC,UAAU,CAACuC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAC3EI,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,YAAY,EAAE,CAAC,IAAI;KACpB,CAAC;EACJ;EAEAC,mBAAmBA,CAACC,IAAS;IAC3B,IAAI,CAAChB,iBAAiB,CAACiB,UAAU,CAAC;MAAEf,YAAY,EAAEc,IAAI,CAAClC;IAAK,CAAE,CAAC;EACjE;EAEAiB,eAAeA,CAAA;IACb,IAAI,CAACpB,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAACO,cAAc,CAAC;IACpD,IAAI,CAACN,kBAAkB,GAAG,IAAI;EAChC;EAEAsC,eAAeA,CAAA;IACb,MAAMb,UAAU,GAAG,IAAI,CAACD,iBAAiB,CAACe,GAAG,CAAC,YAAY,CAAC,EAAErC,KAAK;IAElE,IAAI,CAACuB,UAAU,IAAIA,UAAU,CAACe,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3C;MACA,IAAI,CAACrB,eAAe,EAAE;MACtB;;IAGF,IAAIM,UAAU,CAACgB,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAAC7C,SAAS,GAAG,IAAI;MAErB;MACA8C,UAAU,CAAC,MAAK;QACd,IAAI,CAAC3C,oBAAoB,GAAG,IAAI,CAACO,cAAc,CAACqC,MAAM,CAACC,OAAO,IAC5DA,OAAO,CAACrC,aAAa,CAACsC,WAAW,EAAE,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,EAAE,CAAC,IACtED,OAAO,CAAClC,YAAY,CAACmC,WAAW,EAAE,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,EAAE,CAAC,IACrED,OAAO,CAAChC,IAAI,CAACiC,WAAW,EAAE,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,EAAE,CAAC,IAC7DD,OAAO,CAAC/B,aAAa,CAACgC,WAAW,EAAE,CAACC,QAAQ,CAACrB,UAAU,CAACoB,WAAW,EAAE,CAAC,CACvE;QACD,IAAI,CAAC7C,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACJ,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEAmD,aAAaA,CAACH,OAAoB;IAChC,IAAI,CAAC9C,eAAe,GAAG8C,OAAO;IAC9B,IAAI,CAAC5C,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACwB,iBAAiB,CAACa,UAAU,CAAC;MAAEZ,UAAU,EAAEmB,OAAO,CAACrC;IAAa,CAAE,CAAC;EAC1E;EAEAyC,qBAAqBA,CAAA;IACnB,IAAI,CAAClD,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC0B,iBAAiB,CAACa,UAAU,CAAC;MAAEZ,UAAU,EAAE;IAAE,CAAE,CAAC;IACrD;IACA,IAAI,CAACN,eAAe,EAAE;EACxB;EAEM8B,iBAAiBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACrB,IAAID,KAAI,CAAC9B,iBAAiB,CAACgC,KAAK,IAAIF,KAAI,CAACpD,eAAe,IAAIoD,KAAI,CAACxB,oBAAoB,CAAC0B,KAAK,EAAE;QAC3FF,KAAI,CAACtD,SAAS,GAAG,IAAI;QAErB,MAAMyD,OAAO,SAASH,KAAI,CAACxD,iBAAiB,CAAC4D,MAAM,CAAC;UAClDC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB;QACAf,UAAU,cAAAS,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAACtD,SAAS,GAAG,KAAK;UACtB,MAAMyD,OAAO,CAACK,OAAO,EAAE;UAEvB,MAAMC,KAAK,SAAST,KAAI,CAACvD,eAAe,CAAC2D,MAAM,CAAC;YAC9CC,OAAO,EAAE,uDAAuD,GAAG9C,IAAI,CAACmD,GAAG,EAAE;YAC7EJ,QAAQ,EAAE,IAAI;YACdK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,MAAMH,KAAK,CAACF,OAAO,EAAE;UAErB;UACAP,KAAI,CAACzD,MAAM,CAACsE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMJ,KAAK,SAAST,KAAI,CAACvD,eAAe,CAAC2D,MAAM,CAAC;UAC9CC,OAAO,EAAE,qEAAqE;UAC9EC,QAAQ,EAAE,IAAI;UACdK,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;SACX,CAAC;QACF,MAAMH,KAAK,CAACF,OAAO,EAAE;;IACtB;EACH;EAEAO,cAAcA,CAACC,KAAU;IACvB,MAAMC,KAAK,GAAGD,KAAK,CAACE,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAACzB,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC5C,aAAa,GAAGuE,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACtC,IAAI,CAACxC,oBAAoB,CAACW,UAAU,CAAC;QAAEH,YAAY,EAAEgC,KAAK,CAAC,CAAC;MAAC,CAAE,CAAC;;EAEpE;EAEAI,UAAUA,CAACC,KAAa;IACtB,IAAI,CAAC1E,aAAa,CAAC2E,MAAM,CAACD,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,IAAI,CAAC1E,aAAa,CAAC4C,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACf,oBAAoB,CAACW,UAAU,CAAC;QAAEH,YAAY,EAAE;MAAI,CAAE,CAAC;;EAEhE;EAEAuC,wBAAwBA,CAAA;IACtB,MAAMC,aAAa,GAAG,IAAI,CAACtD,iBAAiB,CAACmB,GAAG,CAAC,cAAc,CAAC,EAAErC,KAAK;IACvE,OAAO,IAAI,CAACD,cAAc,CAAC0E,IAAI,CAACvC,IAAI,IAAIA,IAAI,CAAClC,KAAK,KAAKwE,aAAa,CAAC;EACvE;EAEAE,eAAeA,CAACC,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO,IAAI,CAACzD,iBAAiB,CAACgC,KAAK;MACrC,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC1B,oBAAoB,CAACa,GAAG,CAAC,sBAAsB,CAAC,EAAEa,KAAK,IAAI,KAAK;MAC9E,KAAK,CAAC;QACJ,OAAO,IAAI,CAACtD,eAAe,KAAK,IAAI;MACtC,KAAK,CAAC;QACJ,OAAO,IAAI,CAACgF,qBAAqB,EAAE;MACrC;QACE,OAAO,KAAK;;EAElB;EAEAA,qBAAqBA,CAAA;IACnB,MAAMnD,iBAAiB,GAAG,IAAI,CAACD,oBAAoB,CAACa,GAAG,CAAC,mBAAmB,CAAC;IAC5E,MAAMV,aAAa,GAAG,IAAI,CAACH,oBAAoB,CAACa,GAAG,CAAC,eAAe,CAAC;IACpE,OAAO,CAAC,EAAEZ,iBAAiB,EAAEyB,KAAK,IAAIvB,aAAa,EAAEuB,KAAK,CAAC;EAC7D;EAEA2B,eAAeA,CAACC,IAAe,EAAEC,KAAa;IAC5C,MAAMC,OAAO,GAAGF,IAAI,CAACzC,GAAG,CAAC0C,KAAK,CAAC;IAC/B,IAAIC,OAAO,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMvD,SAAS,GAAGsD,OAAO,CAACG,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACH,KAAK,CAAC,qBAAqBrD,SAAS,aAAa;;IAEhF,IAAIsD,OAAO,EAAEC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACH,KAAa;IACjC,MAAMM,MAAM,GAA8B;MACxCjE,YAAY,EAAE,gBAAgB;MAC9BG,UAAU,EAAE,aAAa;MACzBE,iBAAiB,EAAE,qBAAqB;MACxCE,aAAa,EAAE,gBAAgB;MAC/BE,oBAAoB,EAAE,uBAAuB;MAC7CC,QAAQ,EAAE;KACX;IACD,OAAOuD,MAAM,CAACN,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAO,MAAMA,CAAA;IACJ,IAAI,CAAC/F,MAAM,CAACsE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA0B,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAAC3F,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACE,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACwB,iBAAiB,CAACa,UAAU,CAAC;MAAEZ,UAAU,EAAE;IAAE,CAAE,CAAC;EACvD;EAEAiE,wBAAwBA,CAAA;IACtB;IACA;EAAA;EAGFC,sBAAsBA,CAAA;IACpB;IACA;EAAA;CAEH;AA7VYrG,YAAY,GAAAsG,UAAA,EALxBxG,SAAS,CAAC;EACTyG,QAAQ,EAAE,cAAc;EACxBC,WAAW,EAAE,sBAAsB;EACnCC,SAAS,EAAE,CAAC,sBAAsB;CACnC,CAAC,C,EACWzG,YAAY,CA6VxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}