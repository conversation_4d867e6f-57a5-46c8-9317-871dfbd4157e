{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { i as inheritAriaAttributes } from './helpers-be245865.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { m as chevronForwardOutline, n as ellipsisHorizontal } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\nconst breadcrumbIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-850, #2d4665);--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--color-active);--background-focused:var(--ion-color-step-50, rgba(233, 237, 243, 0.7));font-size:clamp(16px, 1rem, 22px)}:host(.breadcrumb-active){font-weight:600}.breadcrumb-native{border-radius:4px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:5px;padding-bottom:5px;border:1px solid transparent}:host(.ion-focused) .breadcrumb-native{border-radius:8px}:host(.in-breadcrumbs-color.ion-focused) .breadcrumb-native,:host(.ion-color.ion-focused) .breadcrumb-native{background:rgba(var(--ion-color-base-rgb), 0.1);color:var(--ion-color-base)}:host(.ion-focused) ::slotted(ion-icon),:host(.in-breadcrumbs-color.ion-focused) ::slotted(ion-icon),:host(.ion-color.ion-focused) ::slotted(ion-icon){color:var(--ion-color-step-750, #445b78)}.breadcrumb-separator{color:var(--ion-color-step-550, #73849a)}::slotted(ion-icon){color:var(--ion-color-step-400, #92a0b3);font-size:min(1.125rem, 21.6px)}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, #242d39)}.breadcrumbs-collapsed-indicator{border-radius:4px;background:var(--ion-color-step-100, #e9edf3);color:var(--ion-color-step-550, #73849a)}.breadcrumbs-collapsed-indicator:hover{opacity:0.45}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, #d9e0ea)}.breadcrumbs-collapsed-indicator ion-icon{font-size:min(1.375rem, 22px)}\";\nconst IonBreadcrumbIosStyle0 = breadcrumbIosCss;\nconst breadcrumbMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-600, #677483);--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--ion-color-step-800, #35404e);--background-focused:var(--ion-color-step-50, #fff)}:host(.breadcrumb-active){font-weight:500}.breadcrumb-native{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}.breadcrumb-separator{-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:-1px}:host(.ion-focused) .breadcrumb-native{border-radius:4px;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12)}.breadcrumb-separator{color:var(--ion-color-step-550, #73849a)}::slotted(ion-icon){color:var(--ion-color-step-550, #7d8894);font-size:1.125rem}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, #222d3a)}.breadcrumbs-collapsed-indicator{border-radius:2px;background:var(--ion-color-step-100, #eef1f3);color:var(--ion-color-step-550, #73849a)}.breadcrumbs-collapsed-indicator:hover{opacity:0.7}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, #dfe5e8)}\";\nconst IonBreadcrumbMdStyle0 = breadcrumbMdCss;\nconst Breadcrumb = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionFocus = createEvent(this, \"ionFocus\", 7);\n    this.ionBlur = createEvent(this, \"ionBlur\", 7);\n    this.collapsedClick = createEvent(this, \"collapsedClick\", 7);\n    this.inheritedAttributes = {};\n    this.onFocus = () => {\n      this.ionFocus.emit();\n    };\n    this.onBlur = () => {\n      this.ionBlur.emit();\n    };\n    this.collapsedIndicatorClick = () => {\n      this.collapsedClick.emit({\n        ionShadowTarget: this.collapsedRef\n      });\n    };\n    this.collapsed = false;\n    this.last = undefined;\n    this.showCollapsedIndicator = undefined;\n    this.color = undefined;\n    this.active = false;\n    this.disabled = false;\n    this.download = undefined;\n    this.href = undefined;\n    this.rel = undefined;\n    this.separator = undefined;\n    this.target = undefined;\n    this.routerDirection = 'forward';\n    this.routerAnimation = undefined;\n  }\n  componentWillLoad() {\n    this.inheritedAttributes = inheritAriaAttributes(this.el);\n  }\n  isClickable() {\n    return this.href !== undefined;\n  }\n  render() {\n    const {\n      color,\n      active,\n      collapsed,\n      disabled,\n      download,\n      el,\n      inheritedAttributes,\n      last,\n      routerAnimation,\n      routerDirection,\n      separator,\n      showCollapsedIndicator,\n      target\n    } = this;\n    const clickable = this.isClickable();\n    const TagType = this.href === undefined ? 'span' : 'a';\n    // Links can still be tabbed to when set to disabled if they have an href\n    // in order to truly disable them we can keep it as an anchor but remove the href\n    const href = disabled ? undefined : this.href;\n    const mode = getIonMode(this);\n    const attrs = TagType === 'span' ? {} : {\n      download,\n      href,\n      target\n    };\n    // If the breadcrumb is collapsed, check if it contains the collapsed indicator\n    // to show the separator as long as it isn't also the last breadcrumb\n    // otherwise if not collapsed use the value in separator\n    const showSeparator = last ? false : collapsed ? showCollapsedIndicator && !last ? true : false : separator;\n    return h(Host, {\n      key: '6d354439f90ec3cfab9fbf93cb17a67fb9ca6034',\n      onClick: ev => openURL(href, ev, routerDirection, routerAnimation),\n      \"aria-disabled\": disabled ? 'true' : null,\n      class: createColorClasses(color, {\n        [mode]: true,\n        'breadcrumb-active': active,\n        'breadcrumb-collapsed': collapsed,\n        'breadcrumb-disabled': disabled,\n        'in-breadcrumbs-color': hostContext('ion-breadcrumbs[color]', el),\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'ion-activatable': clickable,\n        'ion-focusable': clickable\n      })\n    }, h(TagType, Object.assign({\n      key: '4782977969bd84af02b1834573a6e51069b798ae'\n    }, attrs, {\n      class: \"breadcrumb-native\",\n      part: \"native\",\n      disabled: disabled,\n      onFocus: this.onFocus,\n      onBlur: this.onBlur\n    }, inheritedAttributes), h(\"slot\", {\n      key: '7d5fb845e463b8195142099773e7f258fc8ed31d',\n      name: \"start\"\n    }), h(\"slot\", {\n      key: '6b642ccb9101c12f72124fed5dd0f6362345fb41'\n    }), h(\"slot\", {\n      key: '9fb0841fed712e21d1e84b187e1bc9159cf80b56',\n      name: \"end\"\n    })), showCollapsedIndicator && h(\"button\", {\n      key: '4b64544d879224d491447a79da8f8672b994af0b',\n      part: \"collapsed-indicator\",\n      \"aria-label\": \"Show more breadcrumbs\",\n      onClick: () => this.collapsedIndicatorClick(),\n      ref: collapsedEl => this.collapsedRef = collapsedEl,\n      class: {\n        'breadcrumbs-collapsed-indicator': true\n      }\n    }, h(\"ion-icon\", {\n      key: '5a2511b237aa8c401f416e967a831f8315423949',\n      \"aria-hidden\": \"true\",\n      icon: ellipsisHorizontal,\n      lazy: false\n    })), showSeparator && (\n    /**\n     * Separators should not be announced by narrators.\n     * We add aria-hidden on the span so that this applies\n     * to any custom separators too.\n     */\n    h(\"span\", {\n      key: '348952855dd79eb92f8d370e5839a8d09aff4097',\n      class: \"breadcrumb-separator\",\n      part: \"separator\",\n      \"aria-hidden\": \"true\"\n    }, h(\"slot\", {\n      key: '0120f416edb1d776fa6900b5986b2b57eef554b3',\n      name: \"separator\"\n    }, mode === 'ios' ? h(\"ion-icon\", {\n      icon: chevronForwardOutline,\n      lazy: false,\n      \"flip-rtl\": true\n    }) : h(\"span\", null, \"/\")))));\n  }\n  get el() {\n    return getElement(this);\n  }\n};\nBreadcrumb.style = {\n  ios: IonBreadcrumbIosStyle0,\n  md: IonBreadcrumbMdStyle0\n};\nconst breadcrumbsIosCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;-ms-flex-pack:center;justify-content:center}\";\nconst IonBreadcrumbsIosStyle0 = breadcrumbsIosCss;\nconst breadcrumbsMdCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}\";\nconst IonBreadcrumbsMdStyle0 = breadcrumbsMdCss;\nconst Breadcrumbs = class {\n  constructor(hostRef) {\n    registerInstance(this, hostRef);\n    this.ionCollapsedClick = createEvent(this, \"ionCollapsedClick\", 7);\n    this.breadcrumbsInit = () => {\n      this.setBreadcrumbSeparator();\n      this.setMaxItems();\n    };\n    this.resetActiveBreadcrumb = () => {\n      const breadcrumbs = this.getBreadcrumbs();\n      // Only reset the active breadcrumb if we were the ones to change it\n      // otherwise use the one set on the component\n      const activeBreadcrumb = breadcrumbs.find(breadcrumb => breadcrumb.active);\n      if (activeBreadcrumb && this.activeChanged) {\n        activeBreadcrumb.active = false;\n      }\n    };\n    this.setMaxItems = () => {\n      const {\n        itemsAfterCollapse,\n        itemsBeforeCollapse,\n        maxItems\n      } = this;\n      const breadcrumbs = this.getBreadcrumbs();\n      for (const breadcrumb of breadcrumbs) {\n        breadcrumb.showCollapsedIndicator = false;\n        breadcrumb.collapsed = false;\n      }\n      // If the number of breadcrumbs exceeds the maximum number of items\n      // that should show and the items before / after collapse do not\n      // exceed the maximum items then we need to collapse the breadcrumbs\n      const shouldCollapse = maxItems !== undefined && breadcrumbs.length > maxItems && itemsBeforeCollapse + itemsAfterCollapse <= maxItems;\n      if (shouldCollapse) {\n        // Show the collapsed indicator in the first breadcrumb that collapses\n        breadcrumbs.forEach((breadcrumb, index) => {\n          if (index === itemsBeforeCollapse) {\n            breadcrumb.showCollapsedIndicator = true;\n          }\n          // Collapse all breadcrumbs that have an index greater than or equal to\n          // the number before collapse and an index less than the total number\n          // of breadcrumbs minus the items that should show after the collapse\n          if (index >= itemsBeforeCollapse && index < breadcrumbs.length - itemsAfterCollapse) {\n            breadcrumb.collapsed = true;\n          }\n        });\n      }\n    };\n    this.setBreadcrumbSeparator = () => {\n      const {\n        itemsAfterCollapse,\n        itemsBeforeCollapse,\n        maxItems\n      } = this;\n      const breadcrumbs = this.getBreadcrumbs();\n      // Check if an active breadcrumb exists already\n      const active = breadcrumbs.find(breadcrumb => breadcrumb.active);\n      // Set the separator on all but the last breadcrumb\n      for (const breadcrumb of breadcrumbs) {\n        // The only time the last breadcrumb changes is when\n        // itemsAfterCollapse is set to 0, in this case the\n        // last breadcrumb will be the collapsed indicator\n        const last = maxItems !== undefined && itemsAfterCollapse === 0 ? breadcrumb === breadcrumbs[itemsBeforeCollapse] : breadcrumb === breadcrumbs[breadcrumbs.length - 1];\n        breadcrumb.last = last;\n        // If the breadcrumb has defined whether or not to show the\n        // separator then use that value, otherwise check if it's the\n        // last breadcrumb\n        const separator = breadcrumb.separator !== undefined ? breadcrumb.separator : last ? undefined : true;\n        breadcrumb.separator = separator;\n        // If there is not an active breadcrumb already\n        // set the last one to active\n        if (!active && last) {\n          breadcrumb.active = true;\n          this.activeChanged = true;\n        }\n      }\n    };\n    this.getBreadcrumbs = () => {\n      return Array.from(this.el.querySelectorAll('ion-breadcrumb'));\n    };\n    this.slotChanged = () => {\n      this.resetActiveBreadcrumb();\n      this.breadcrumbsInit();\n    };\n    this.collapsed = undefined;\n    this.activeChanged = undefined;\n    this.color = undefined;\n    this.maxItems = undefined;\n    this.itemsBeforeCollapse = 1;\n    this.itemsAfterCollapse = 1;\n  }\n  onCollapsedClick(ev) {\n    const breadcrumbs = this.getBreadcrumbs();\n    const collapsedBreadcrumbs = breadcrumbs.filter(breadcrumb => breadcrumb.collapsed);\n    this.ionCollapsedClick.emit(Object.assign(Object.assign({}, ev.detail), {\n      collapsedBreadcrumbs\n    }));\n  }\n  maxItemsChanged() {\n    this.resetActiveBreadcrumb();\n    this.breadcrumbsInit();\n  }\n  componentWillLoad() {\n    this.breadcrumbsInit();\n  }\n  render() {\n    const {\n      color,\n      collapsed\n    } = this;\n    const mode = getIonMode(this);\n    return h(Host, {\n      key: 'cd288d4bf5498f86d086eb999b506993818642f3',\n      class: createColorClasses(color, {\n        [mode]: true,\n        'in-toolbar': hostContext('ion-toolbar', this.el),\n        'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n        'breadcrumbs-collapsed': collapsed\n      })\n    }, h(\"slot\", {\n      key: '73c802065511dbebef3867b01ce929313d24cba2',\n      onSlotchange: this.slotChanged\n    }));\n  }\n  get el() {\n    return getElement(this);\n  }\n  static get watchers() {\n    return {\n      \"maxItems\": [\"maxItemsChanged\"],\n      \"itemsBeforeCollapse\": [\"maxItemsChanged\"],\n      \"itemsAfterCollapse\": [\"maxItemsChanged\"]\n    };\n  }\n};\nBreadcrumbs.style = {\n  ios: IonBreadcrumbsIosStyle0,\n  md: IonBreadcrumbsMdStyle0\n};\nexport { Breadcrumb as ion_breadcrumb, Breadcrumbs as ion_breadcrumbs };", "map": {"version": 3, "names": ["r", "registerInstance", "d", "createEvent", "h", "H", "Host", "f", "getElement", "i", "inheritAriaAttributes", "o", "openURL", "c", "createColorClasses", "hostContext", "m", "chevronForwardOutline", "n", "ellipsisHorizontal", "b", "getIonMode", "breadcrumbIosCss", "IonBreadcrumbIosStyle0", "breadcrumbMdCss", "IonBreadcrumbMdStyle0", "Breadcrumb", "constructor", "hostRef", "ionFocus", "ionBlur", "collapsedClick", "inheritedAttributes", "onFocus", "emit", "onBlur", "collapsedIndicatorClick", "ionShadowTarget", "collapsedRef", "collapsed", "last", "undefined", "showCollapsedIndicator", "color", "active", "disabled", "download", "href", "rel", "separator", "target", "routerDirection", "routerAnimation", "componentWillLoad", "el", "isClickable", "render", "clickable", "TagType", "mode", "attrs", "showSeparator", "key", "onClick", "ev", "class", "Object", "assign", "part", "name", "ref", "collapsedEl", "icon", "lazy", "style", "ios", "md", "breadcrumbsIosCss", "IonBreadcrumbsIosStyle0", "breadcrumbsMdCss", "IonBreadcrumbsMdStyle0", "Breadcrumbs", "ionCollapsedClick", "breadcrumbsInit", "setBreadcrumbSeparator", "setMaxItems", "resetActiveBreadcrumb", "breadcrumbs", "getBreadcrumbs", "activeBreadcrumb", "find", "breadcrumb", "activeChanged", "itemsAfterCollapse", "itemsBeforeCollapse", "maxItems", "shouldCollapse", "length", "for<PERSON>ach", "index", "Array", "from", "querySelectorAll", "slotChanged", "onCollapsedClick", "collapsedBreadcrumbs", "filter", "detail", "maxItemsChanged", "onSlotchange", "watchers", "ion_breadcrumb", "ion_breadcrumbs"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/ion-breadcrumb_2.entry.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { r as registerInstance, d as createEvent, h, H as Host, f as getElement } from './index-a1a47f01.js';\nimport { i as inheritAriaAttributes } from './helpers-be245865.js';\nimport { o as openURL, c as createColorClasses, h as hostContext } from './theme-01f3f29c.js';\nimport { m as chevronForwardOutline, n as ellipsisHorizontal } from './index-f7dc70ba.js';\nimport { b as getIonMode } from './ionic-global-94f25d1b.js';\n\nconst breadcrumbIosCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-850, #2d4665);--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--color-active);--background-focused:var(--ion-color-step-50, rgba(233, 237, 243, 0.7));font-size:clamp(16px, 1rem, 22px)}:host(.breadcrumb-active){font-weight:600}.breadcrumb-native{border-radius:4px;-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:5px;padding-bottom:5px;border:1px solid transparent}:host(.ion-focused) .breadcrumb-native{border-radius:8px}:host(.in-breadcrumbs-color.ion-focused) .breadcrumb-native,:host(.ion-color.ion-focused) .breadcrumb-native{background:rgba(var(--ion-color-base-rgb), 0.1);color:var(--ion-color-base)}:host(.ion-focused) ::slotted(ion-icon),:host(.in-breadcrumbs-color.ion-focused) ::slotted(ion-icon),:host(.ion-color.ion-focused) ::slotted(ion-icon){color:var(--ion-color-step-750, #445b78)}.breadcrumb-separator{color:var(--ion-color-step-550, #73849a)}::slotted(ion-icon){color:var(--ion-color-step-400, #92a0b3);font-size:min(1.125rem, 21.6px)}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, #242d39)}.breadcrumbs-collapsed-indicator{border-radius:4px;background:var(--ion-color-step-100, #e9edf3);color:var(--ion-color-step-550, #73849a)}.breadcrumbs-collapsed-indicator:hover{opacity:0.45}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, #d9e0ea)}.breadcrumbs-collapsed-indicator ion-icon{font-size:min(1.375rem, 22px)}\";\nconst IonBreadcrumbIosStyle0 = breadcrumbIosCss;\n\nconst breadcrumbMdCss = \":host{display:-ms-flexbox;display:flex;-ms-flex:0 0 auto;flex:0 0 auto;-ms-flex-align:center;align-items:center;color:var(--color);font-size:1rem;font-weight:400;line-height:1.5}.breadcrumb-native{font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;width:100%;outline:none;background:inherit}:host(.breadcrumb-disabled){cursor:default;opacity:0.5;pointer-events:none}:host(.breadcrumb-active){color:var(--color-active)}:host(.ion-focused){color:var(--color-focused)}:host(.ion-focused) .breadcrumb-native{background:var(--background-focused)}@media (any-hover: hover){:host(.ion-activatable:hover){color:var(--color-hover)}:host(.ion-activatable.in-breadcrumbs-color:hover),:host(.ion-activatable.ion-color:hover){color:var(--ion-color-shade)}}.breadcrumb-separator{display:-ms-inline-flexbox;display:inline-flex}:host(.breadcrumb-collapsed) .breadcrumb-native{display:none}:host(.in-breadcrumbs-color),:host(.in-breadcrumbs-color.breadcrumb-active){color:var(--ion-color-base)}:host(.in-breadcrumbs-color) .breadcrumb-separator{color:var(--ion-color-base)}:host(.ion-color){color:var(--ion-color-base)}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumb-separator{color:rgba(var(--ion-color-contrast-rgb), 0.8)}:host(.in-toolbar-color.breadcrumb-active){color:var(--ion-color-contrast)}.breadcrumbs-collapsed-indicator{padding-left:0;padding-right:0;padding-top:0;padding-bottom:0;-webkit-margin-start:14px;margin-inline-start:14px;-webkit-margin-end:14px;margin-inline-end:14px;margin-top:0;margin-bottom:0;display:-ms-flexbox;display:flex;-ms-flex:1 1 100%;flex:1 1 100%;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:32px;height:18px;border:0;outline:none;cursor:pointer;-webkit-appearance:none;-moz-appearance:none;appearance:none}.breadcrumbs-collapsed-indicator ion-icon{margin-top:1px;font-size:1.375rem}:host{--color:var(--ion-color-step-600, #677483);--color-active:var(--ion-text-color, #03060b);--color-hover:var(--ion-text-color, #03060b);--color-focused:var(--ion-color-step-800, #35404e);--background-focused:var(--ion-color-step-50, #fff)}:host(.breadcrumb-active){font-weight:500}.breadcrumb-native{-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:6px;padding-bottom:6px}.breadcrumb-separator{-webkit-margin-start:10px;margin-inline-start:10px;-webkit-margin-end:10px;margin-inline-end:10px;margin-top:-1px}:host(.ion-focused) .breadcrumb-native{border-radius:4px;-webkit-box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12);box-shadow:0px 1px 2px rgba(0, 0, 0, 0.2), 0px 2px 8px rgba(0, 0, 0, 0.12)}.breadcrumb-separator{color:var(--ion-color-step-550, #73849a)}::slotted(ion-icon){color:var(--ion-color-step-550, #7d8894);font-size:1.125rem}::slotted(ion-icon[slot=start]){-webkit-margin-end:8px;margin-inline-end:8px}::slotted(ion-icon[slot=end]){-webkit-margin-start:8px;margin-inline-start:8px}:host(.breadcrumb-active) ::slotted(ion-icon){color:var(--ion-color-step-850, #222d3a)}.breadcrumbs-collapsed-indicator{border-radius:2px;background:var(--ion-color-step-100, #eef1f3);color:var(--ion-color-step-550, #73849a)}.breadcrumbs-collapsed-indicator:hover{opacity:0.7}.breadcrumbs-collapsed-indicator:focus{background:var(--ion-color-step-150, #dfe5e8)}\";\nconst IonBreadcrumbMdStyle0 = breadcrumbMdCss;\n\nconst Breadcrumb = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionFocus = createEvent(this, \"ionFocus\", 7);\n        this.ionBlur = createEvent(this, \"ionBlur\", 7);\n        this.collapsedClick = createEvent(this, \"collapsedClick\", 7);\n        this.inheritedAttributes = {};\n        this.onFocus = () => {\n            this.ionFocus.emit();\n        };\n        this.onBlur = () => {\n            this.ionBlur.emit();\n        };\n        this.collapsedIndicatorClick = () => {\n            this.collapsedClick.emit({ ionShadowTarget: this.collapsedRef });\n        };\n        this.collapsed = false;\n        this.last = undefined;\n        this.showCollapsedIndicator = undefined;\n        this.color = undefined;\n        this.active = false;\n        this.disabled = false;\n        this.download = undefined;\n        this.href = undefined;\n        this.rel = undefined;\n        this.separator = undefined;\n        this.target = undefined;\n        this.routerDirection = 'forward';\n        this.routerAnimation = undefined;\n    }\n    componentWillLoad() {\n        this.inheritedAttributes = inheritAriaAttributes(this.el);\n    }\n    isClickable() {\n        return this.href !== undefined;\n    }\n    render() {\n        const { color, active, collapsed, disabled, download, el, inheritedAttributes, last, routerAnimation, routerDirection, separator, showCollapsedIndicator, target, } = this;\n        const clickable = this.isClickable();\n        const TagType = this.href === undefined ? 'span' : 'a';\n        // Links can still be tabbed to when set to disabled if they have an href\n        // in order to truly disable them we can keep it as an anchor but remove the href\n        const href = disabled ? undefined : this.href;\n        const mode = getIonMode(this);\n        const attrs = TagType === 'span'\n            ? {}\n            : {\n                download,\n                href,\n                target,\n            };\n        // If the breadcrumb is collapsed, check if it contains the collapsed indicator\n        // to show the separator as long as it isn't also the last breadcrumb\n        // otherwise if not collapsed use the value in separator\n        const showSeparator = last ? false : collapsed ? (showCollapsedIndicator && !last ? true : false) : separator;\n        return (h(Host, { key: '6d354439f90ec3cfab9fbf93cb17a67fb9ca6034', onClick: (ev) => openURL(href, ev, routerDirection, routerAnimation), \"aria-disabled\": disabled ? 'true' : null, class: createColorClasses(color, {\n                [mode]: true,\n                'breadcrumb-active': active,\n                'breadcrumb-collapsed': collapsed,\n                'breadcrumb-disabled': disabled,\n                'in-breadcrumbs-color': hostContext('ion-breadcrumbs[color]', el),\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'ion-activatable': clickable,\n                'ion-focusable': clickable,\n            }) }, h(TagType, Object.assign({ key: '4782977969bd84af02b1834573a6e51069b798ae' }, attrs, { class: \"breadcrumb-native\", part: \"native\", disabled: disabled, onFocus: this.onFocus, onBlur: this.onBlur }, inheritedAttributes), h(\"slot\", { key: '7d5fb845e463b8195142099773e7f258fc8ed31d', name: \"start\" }), h(\"slot\", { key: '6b642ccb9101c12f72124fed5dd0f6362345fb41' }), h(\"slot\", { key: '9fb0841fed712e21d1e84b187e1bc9159cf80b56', name: \"end\" })), showCollapsedIndicator && (h(\"button\", { key: '4b64544d879224d491447a79da8f8672b994af0b', part: \"collapsed-indicator\", \"aria-label\": \"Show more breadcrumbs\", onClick: () => this.collapsedIndicatorClick(), ref: (collapsedEl) => (this.collapsedRef = collapsedEl), class: {\n                'breadcrumbs-collapsed-indicator': true,\n            } }, h(\"ion-icon\", { key: '5a2511b237aa8c401f416e967a831f8315423949', \"aria-hidden\": \"true\", icon: ellipsisHorizontal, lazy: false }))), showSeparator && (\n        /**\n         * Separators should not be announced by narrators.\n         * We add aria-hidden on the span so that this applies\n         * to any custom separators too.\n         */\n        h(\"span\", { key: '348952855dd79eb92f8d370e5839a8d09aff4097', class: \"breadcrumb-separator\", part: \"separator\", \"aria-hidden\": \"true\" }, h(\"slot\", { key: '0120f416edb1d776fa6900b5986b2b57eef554b3', name: \"separator\" }, mode === 'ios' ? (h(\"ion-icon\", { icon: chevronForwardOutline, lazy: false, \"flip-rtl\": true })) : (h(\"span\", null, \"/\")))))));\n    }\n    get el() { return getElement(this); }\n};\nBreadcrumb.style = {\n    ios: IonBreadcrumbIosStyle0,\n    md: IonBreadcrumbMdStyle0\n};\n\nconst breadcrumbsIosCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;-ms-flex-pack:center;justify-content:center}\";\nconst IonBreadcrumbsIosStyle0 = breadcrumbsIosCss;\n\nconst breadcrumbsMdCss = \":host{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:center;align-items:center}:host(.in-toolbar-color),:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator ion-icon{color:var(--ion-color-contrast)}:host(.in-toolbar-color) .breadcrumbs-collapsed-indicator{background:rgba(var(--ion-color-contrast-rgb), 0.11)}:host(.in-toolbar){-webkit-padding-start:8px;padding-inline-start:8px;-webkit-padding-end:8px;padding-inline-end:8px;padding-top:0;padding-bottom:0}\";\nconst IonBreadcrumbsMdStyle0 = breadcrumbsMdCss;\n\nconst Breadcrumbs = class {\n    constructor(hostRef) {\n        registerInstance(this, hostRef);\n        this.ionCollapsedClick = createEvent(this, \"ionCollapsedClick\", 7);\n        this.breadcrumbsInit = () => {\n            this.setBreadcrumbSeparator();\n            this.setMaxItems();\n        };\n        this.resetActiveBreadcrumb = () => {\n            const breadcrumbs = this.getBreadcrumbs();\n            // Only reset the active breadcrumb if we were the ones to change it\n            // otherwise use the one set on the component\n            const activeBreadcrumb = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n            if (activeBreadcrumb && this.activeChanged) {\n                activeBreadcrumb.active = false;\n            }\n        };\n        this.setMaxItems = () => {\n            const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n            const breadcrumbs = this.getBreadcrumbs();\n            for (const breadcrumb of breadcrumbs) {\n                breadcrumb.showCollapsedIndicator = false;\n                breadcrumb.collapsed = false;\n            }\n            // If the number of breadcrumbs exceeds the maximum number of items\n            // that should show and the items before / after collapse do not\n            // exceed the maximum items then we need to collapse the breadcrumbs\n            const shouldCollapse = maxItems !== undefined && breadcrumbs.length > maxItems && itemsBeforeCollapse + itemsAfterCollapse <= maxItems;\n            if (shouldCollapse) {\n                // Show the collapsed indicator in the first breadcrumb that collapses\n                breadcrumbs.forEach((breadcrumb, index) => {\n                    if (index === itemsBeforeCollapse) {\n                        breadcrumb.showCollapsedIndicator = true;\n                    }\n                    // Collapse all breadcrumbs that have an index greater than or equal to\n                    // the number before collapse and an index less than the total number\n                    // of breadcrumbs minus the items that should show after the collapse\n                    if (index >= itemsBeforeCollapse && index < breadcrumbs.length - itemsAfterCollapse) {\n                        breadcrumb.collapsed = true;\n                    }\n                });\n            }\n        };\n        this.setBreadcrumbSeparator = () => {\n            const { itemsAfterCollapse, itemsBeforeCollapse, maxItems } = this;\n            const breadcrumbs = this.getBreadcrumbs();\n            // Check if an active breadcrumb exists already\n            const active = breadcrumbs.find((breadcrumb) => breadcrumb.active);\n            // Set the separator on all but the last breadcrumb\n            for (const breadcrumb of breadcrumbs) {\n                // The only time the last breadcrumb changes is when\n                // itemsAfterCollapse is set to 0, in this case the\n                // last breadcrumb will be the collapsed indicator\n                const last = maxItems !== undefined && itemsAfterCollapse === 0\n                    ? breadcrumb === breadcrumbs[itemsBeforeCollapse]\n                    : breadcrumb === breadcrumbs[breadcrumbs.length - 1];\n                breadcrumb.last = last;\n                // If the breadcrumb has defined whether or not to show the\n                // separator then use that value, otherwise check if it's the\n                // last breadcrumb\n                const separator = breadcrumb.separator !== undefined ? breadcrumb.separator : last ? undefined : true;\n                breadcrumb.separator = separator;\n                // If there is not an active breadcrumb already\n                // set the last one to active\n                if (!active && last) {\n                    breadcrumb.active = true;\n                    this.activeChanged = true;\n                }\n            }\n        };\n        this.getBreadcrumbs = () => {\n            return Array.from(this.el.querySelectorAll('ion-breadcrumb'));\n        };\n        this.slotChanged = () => {\n            this.resetActiveBreadcrumb();\n            this.breadcrumbsInit();\n        };\n        this.collapsed = undefined;\n        this.activeChanged = undefined;\n        this.color = undefined;\n        this.maxItems = undefined;\n        this.itemsBeforeCollapse = 1;\n        this.itemsAfterCollapse = 1;\n    }\n    onCollapsedClick(ev) {\n        const breadcrumbs = this.getBreadcrumbs();\n        const collapsedBreadcrumbs = breadcrumbs.filter((breadcrumb) => breadcrumb.collapsed);\n        this.ionCollapsedClick.emit(Object.assign(Object.assign({}, ev.detail), { collapsedBreadcrumbs }));\n    }\n    maxItemsChanged() {\n        this.resetActiveBreadcrumb();\n        this.breadcrumbsInit();\n    }\n    componentWillLoad() {\n        this.breadcrumbsInit();\n    }\n    render() {\n        const { color, collapsed } = this;\n        const mode = getIonMode(this);\n        return (h(Host, { key: 'cd288d4bf5498f86d086eb999b506993818642f3', class: createColorClasses(color, {\n                [mode]: true,\n                'in-toolbar': hostContext('ion-toolbar', this.el),\n                'in-toolbar-color': hostContext('ion-toolbar[color]', this.el),\n                'breadcrumbs-collapsed': collapsed,\n            }) }, h(\"slot\", { key: '73c802065511dbebef3867b01ce929313d24cba2', onSlotchange: this.slotChanged })));\n    }\n    get el() { return getElement(this); }\n    static get watchers() { return {\n        \"maxItems\": [\"maxItemsChanged\"],\n        \"itemsBeforeCollapse\": [\"maxItemsChanged\"],\n        \"itemsAfterCollapse\": [\"maxItemsChanged\"]\n    }; }\n};\nBreadcrumbs.style = {\n    ios: IonBreadcrumbsIosStyle0,\n    md: IonBreadcrumbsMdStyle0\n};\n\nexport { Breadcrumb as ion_breadcrumb, Breadcrumbs as ion_breadcrumbs };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,WAAW,EAAEC,CAAC,EAAEC,CAAC,IAAIC,IAAI,EAAEC,CAAC,IAAIC,UAAU,QAAQ,qBAAqB;AAC5G,SAASC,CAAC,IAAIC,qBAAqB,QAAQ,uBAAuB;AAClE,SAASC,CAAC,IAAIC,OAAO,EAAEC,CAAC,IAAIC,kBAAkB,EAAEV,CAAC,IAAIW,WAAW,QAAQ,qBAAqB;AAC7F,SAASC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,kBAAkB,QAAQ,qBAAqB;AACzF,SAASC,CAAC,IAAIC,UAAU,QAAQ,4BAA4B;AAE5D,MAAMC,gBAAgB,GAAG,u3HAAu3H;AACh5H,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,eAAe,GAAG,wnHAAwnH;AAChpH,MAAMC,qBAAqB,GAAGD,eAAe;AAE7C,MAAME,UAAU,GAAG,MAAM;EACrBC,WAAWA,CAACC,OAAO,EAAE;IACjB3B,gBAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACC,QAAQ,GAAG1B,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC;IAChD,IAAI,CAAC2B,OAAO,GAAG3B,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC4B,cAAc,GAAG5B,WAAW,CAAC,IAAI,EAAE,gBAAgB,EAAE,CAAC,CAAC;IAC5D,IAAI,CAAC6B,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,OAAO,GAAG,MAAM;MACjB,IAAI,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAAC;IACxB,CAAC;IACD,IAAI,CAACC,MAAM,GAAG,MAAM;MAChB,IAAI,CAACL,OAAO,CAACI,IAAI,CAAC,CAAC;IACvB,CAAC;IACD,IAAI,CAACE,uBAAuB,GAAG,MAAM;MACjC,IAAI,CAACL,cAAc,CAACG,IAAI,CAAC;QAAEG,eAAe,EAAE,IAAI,CAACC;MAAa,CAAC,CAAC;IACpE,CAAC;IACD,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,IAAI,GAAGC,SAAS;IACrB,IAAI,CAACC,sBAAsB,GAAGD,SAAS;IACvC,IAAI,CAACE,KAAK,GAAGF,SAAS;IACtB,IAAI,CAACG,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,QAAQ,GAAGL,SAAS;IACzB,IAAI,CAACM,IAAI,GAAGN,SAAS;IACrB,IAAI,CAACO,GAAG,GAAGP,SAAS;IACpB,IAAI,CAACQ,SAAS,GAAGR,SAAS;IAC1B,IAAI,CAACS,MAAM,GAAGT,SAAS;IACvB,IAAI,CAACU,eAAe,GAAG,SAAS;IAChC,IAAI,CAACC,eAAe,GAAGX,SAAS;EACpC;EACAY,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACrB,mBAAmB,GAAGtB,qBAAqB,CAAC,IAAI,CAAC4C,EAAE,CAAC;EAC7D;EACAC,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACR,IAAI,KAAKN,SAAS;EAClC;EACAe,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEb,KAAK;MAAEC,MAAM;MAAEL,SAAS;MAAEM,QAAQ;MAAEC,QAAQ;MAAEQ,EAAE;MAAEtB,mBAAmB;MAAEQ,IAAI;MAAEY,eAAe;MAAED,eAAe;MAAEF,SAAS;MAAEP,sBAAsB;MAAEQ;IAAQ,CAAC,GAAG,IAAI;IAC1K,MAAMO,SAAS,GAAG,IAAI,CAACF,WAAW,CAAC,CAAC;IACpC,MAAMG,OAAO,GAAG,IAAI,CAACX,IAAI,KAAKN,SAAS,GAAG,MAAM,GAAG,GAAG;IACtD;IACA;IACA,MAAMM,IAAI,GAAGF,QAAQ,GAAGJ,SAAS,GAAG,IAAI,CAACM,IAAI;IAC7C,MAAMY,IAAI,GAAGtC,UAAU,CAAC,IAAI,CAAC;IAC7B,MAAMuC,KAAK,GAAGF,OAAO,KAAK,MAAM,GAC1B,CAAC,CAAC,GACF;MACEZ,QAAQ;MACRC,IAAI;MACJG;IACJ,CAAC;IACL;IACA;IACA;IACA,MAAMW,aAAa,GAAGrB,IAAI,GAAG,KAAK,GAAGD,SAAS,GAAIG,sBAAsB,IAAI,CAACF,IAAI,GAAG,IAAI,GAAG,KAAK,GAAIS,SAAS;IAC7G,OAAQ7C,CAAC,CAACE,IAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEC,OAAO,EAAGC,EAAE,IAAKpD,OAAO,CAACmC,IAAI,EAAEiB,EAAE,EAAEb,eAAe,EAAEC,eAAe,CAAC;MAAE,eAAe,EAAEP,QAAQ,GAAG,MAAM,GAAG,IAAI;MAAEoB,KAAK,EAAEnD,kBAAkB,CAAC6B,KAAK,EAAE;QAC7M,CAACgB,IAAI,GAAG,IAAI;QACZ,mBAAmB,EAAEf,MAAM;QAC3B,sBAAsB,EAAEL,SAAS;QACjC,qBAAqB,EAAEM,QAAQ;QAC/B,sBAAsB,EAAE9B,WAAW,CAAC,wBAAwB,EAAEuC,EAAE,CAAC;QACjE,YAAY,EAAEvC,WAAW,CAAC,aAAa,EAAE,IAAI,CAACuC,EAAE,CAAC;QACjD,kBAAkB,EAAEvC,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAACuC,EAAE,CAAC;QAC9D,iBAAiB,EAAEG,SAAS;QAC5B,eAAe,EAAEA;MACrB,CAAC;IAAE,CAAC,EAAErD,CAAC,CAACsD,OAAO,EAAEQ,MAAM,CAACC,MAAM,CAAC;MAAEL,GAAG,EAAE;IAA2C,CAAC,EAAEF,KAAK,EAAE;MAAEK,KAAK,EAAE,mBAAmB;MAAEG,IAAI,EAAE,QAAQ;MAAEvB,QAAQ,EAAEA,QAAQ;MAAEZ,OAAO,EAAE,IAAI,CAACA,OAAO;MAAEE,MAAM,EAAE,IAAI,CAACA;IAAO,CAAC,EAAEH,mBAAmB,CAAC,EAAE5B,CAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE;IAAQ,CAAC,CAAC,EAAEjE,CAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE;IAA2C,CAAC,CAAC,EAAE1D,CAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE;IAAM,CAAC,CAAC,CAAC,EAAE3B,sBAAsB,IAAKtC,CAAC,CAAC,QAAQ,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEM,IAAI,EAAE,qBAAqB;MAAE,YAAY,EAAE,uBAAuB;MAAEL,OAAO,EAAEA,CAAA,KAAM,IAAI,CAAC3B,uBAAuB,CAAC,CAAC;MAAEkC,GAAG,EAAGC,WAAW,IAAM,IAAI,CAACjC,YAAY,GAAGiC,WAAY;MAAEN,KAAK,EAAE;QACvsB,iCAAiC,EAAE;MACvC;IAAE,CAAC,EAAE7D,CAAC,CAAC,UAAU,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE,aAAa,EAAE,MAAM;MAAEU,IAAI,EAAErD,kBAAkB;MAAEsD,IAAI,EAAE;IAAM,CAAC,CAAC,CAAE,EAAEZ,aAAa;IAC1J;AACR;AACA;AACA;AACA;IACQzD,CAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAE,sBAAsB;MAAEG,IAAI,EAAE,WAAW;MAAE,aAAa,EAAE;IAAO,CAAC,EAAEhE,CAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAEO,IAAI,EAAE;IAAY,CAAC,EAAEV,IAAI,KAAK,KAAK,GAAIvD,CAAC,CAAC,UAAU,EAAE;MAAEoE,IAAI,EAAEvD,qBAAqB;MAAEwD,IAAI,EAAE,KAAK;MAAE,UAAU,EAAE;IAAK,CAAC,CAAC,GAAKrE,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAE,CAAC,CAAC,CAAC,CAAC;EAC3V;EACA,IAAIkD,EAAEA,CAAA,EAAG;IAAE,OAAO9C,UAAU,CAAC,IAAI,CAAC;EAAE;AACxC,CAAC;AACDkB,UAAU,CAACgD,KAAK,GAAG;EACfC,GAAG,EAAEpD,sBAAsB;EAC3BqD,EAAE,EAAEnD;AACR,CAAC;AAED,MAAMoD,iBAAiB,GAAG,wmBAAwmB;AACloB,MAAMC,uBAAuB,GAAGD,iBAAiB;AAEjD,MAAME,gBAAgB,GAAG,wjBAAwjB;AACjlB,MAAMC,sBAAsB,GAAGD,gBAAgB;AAE/C,MAAME,WAAW,GAAG,MAAM;EACtBtD,WAAWA,CAACC,OAAO,EAAE;IACjB3B,gBAAgB,CAAC,IAAI,EAAE2B,OAAO,CAAC;IAC/B,IAAI,CAACsD,iBAAiB,GAAG/E,WAAW,CAAC,IAAI,EAAE,mBAAmB,EAAE,CAAC,CAAC;IAClE,IAAI,CAACgF,eAAe,GAAG,MAAM;MACzB,IAAI,CAACC,sBAAsB,CAAC,CAAC;MAC7B,IAAI,CAACC,WAAW,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,CAACC,qBAAqB,GAAG,MAAM;MAC/B,MAAMC,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC;MACA;MACA,MAAMC,gBAAgB,GAAGF,WAAW,CAACG,IAAI,CAAEC,UAAU,IAAKA,UAAU,CAAC/C,MAAM,CAAC;MAC5E,IAAI6C,gBAAgB,IAAI,IAAI,CAACG,aAAa,EAAE;QACxCH,gBAAgB,CAAC7C,MAAM,GAAG,KAAK;MACnC;IACJ,CAAC;IACD,IAAI,CAACyC,WAAW,GAAG,MAAM;MACrB,MAAM;QAAEQ,kBAAkB;QAAEC,mBAAmB;QAAEC;MAAS,CAAC,GAAG,IAAI;MAClE,MAAMR,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC,KAAK,MAAMG,UAAU,IAAIJ,WAAW,EAAE;QAClCI,UAAU,CAACjD,sBAAsB,GAAG,KAAK;QACzCiD,UAAU,CAACpD,SAAS,GAAG,KAAK;MAChC;MACA;MACA;MACA;MACA,MAAMyD,cAAc,GAAGD,QAAQ,KAAKtD,SAAS,IAAI8C,WAAW,CAACU,MAAM,GAAGF,QAAQ,IAAID,mBAAmB,GAAGD,kBAAkB,IAAIE,QAAQ;MACtI,IAAIC,cAAc,EAAE;QAChB;QACAT,WAAW,CAACW,OAAO,CAAC,CAACP,UAAU,EAAEQ,KAAK,KAAK;UACvC,IAAIA,KAAK,KAAKL,mBAAmB,EAAE;YAC/BH,UAAU,CAACjD,sBAAsB,GAAG,IAAI;UAC5C;UACA;UACA;UACA;UACA,IAAIyD,KAAK,IAAIL,mBAAmB,IAAIK,KAAK,GAAGZ,WAAW,CAACU,MAAM,GAAGJ,kBAAkB,EAAE;YACjFF,UAAU,CAACpD,SAAS,GAAG,IAAI;UAC/B;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IACD,IAAI,CAAC6C,sBAAsB,GAAG,MAAM;MAChC,MAAM;QAAES,kBAAkB;QAAEC,mBAAmB;QAAEC;MAAS,CAAC,GAAG,IAAI;MAClE,MAAMR,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;MACzC;MACA,MAAM5C,MAAM,GAAG2C,WAAW,CAACG,IAAI,CAAEC,UAAU,IAAKA,UAAU,CAAC/C,MAAM,CAAC;MAClE;MACA,KAAK,MAAM+C,UAAU,IAAIJ,WAAW,EAAE;QAClC;QACA;QACA;QACA,MAAM/C,IAAI,GAAGuD,QAAQ,KAAKtD,SAAS,IAAIoD,kBAAkB,KAAK,CAAC,GACzDF,UAAU,KAAKJ,WAAW,CAACO,mBAAmB,CAAC,GAC/CH,UAAU,KAAKJ,WAAW,CAACA,WAAW,CAACU,MAAM,GAAG,CAAC,CAAC;QACxDN,UAAU,CAACnD,IAAI,GAAGA,IAAI;QACtB;QACA;QACA;QACA,MAAMS,SAAS,GAAG0C,UAAU,CAAC1C,SAAS,KAAKR,SAAS,GAAGkD,UAAU,CAAC1C,SAAS,GAAGT,IAAI,GAAGC,SAAS,GAAG,IAAI;QACrGkD,UAAU,CAAC1C,SAAS,GAAGA,SAAS;QAChC;QACA;QACA,IAAI,CAACL,MAAM,IAAIJ,IAAI,EAAE;UACjBmD,UAAU,CAAC/C,MAAM,GAAG,IAAI;UACxB,IAAI,CAACgD,aAAa,GAAG,IAAI;QAC7B;MACJ;IACJ,CAAC;IACD,IAAI,CAACJ,cAAc,GAAG,MAAM;MACxB,OAAOY,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC/C,EAAE,CAACgD,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;IACjE,CAAC;IACD,IAAI,CAACC,WAAW,GAAG,MAAM;MACrB,IAAI,CAACjB,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAACH,eAAe,CAAC,CAAC;IAC1B,CAAC;IACD,IAAI,CAAC5C,SAAS,GAAGE,SAAS;IAC1B,IAAI,CAACmD,aAAa,GAAGnD,SAAS;IAC9B,IAAI,CAACE,KAAK,GAAGF,SAAS;IACtB,IAAI,CAACsD,QAAQ,GAAGtD,SAAS;IACzB,IAAI,CAACqD,mBAAmB,GAAG,CAAC;IAC5B,IAAI,CAACD,kBAAkB,GAAG,CAAC;EAC/B;EACAW,gBAAgBA,CAACxC,EAAE,EAAE;IACjB,MAAMuB,WAAW,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;IACzC,MAAMiB,oBAAoB,GAAGlB,WAAW,CAACmB,MAAM,CAAEf,UAAU,IAAKA,UAAU,CAACpD,SAAS,CAAC;IACrF,IAAI,CAAC2C,iBAAiB,CAAChD,IAAI,CAACgC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,EAAE,CAAC2C,MAAM,CAAC,EAAE;MAAEF;IAAqB,CAAC,CAAC,CAAC;EACtG;EACAG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACtB,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACH,eAAe,CAAC,CAAC;EAC1B;EACA9B,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC8B,eAAe,CAAC,CAAC;EAC1B;EACA3B,MAAMA,CAAA,EAAG;IACL,MAAM;MAAEb,KAAK;MAAEJ;IAAU,CAAC,GAAG,IAAI;IACjC,MAAMoB,IAAI,GAAGtC,UAAU,CAAC,IAAI,CAAC;IAC7B,OAAQjB,CAAC,CAACE,IAAI,EAAE;MAAEwD,GAAG,EAAE,0CAA0C;MAAEG,KAAK,EAAEnD,kBAAkB,CAAC6B,KAAK,EAAE;QAC5F,CAACgB,IAAI,GAAG,IAAI;QACZ,YAAY,EAAE5C,WAAW,CAAC,aAAa,EAAE,IAAI,CAACuC,EAAE,CAAC;QACjD,kBAAkB,EAAEvC,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAACuC,EAAE,CAAC;QAC9D,uBAAuB,EAAEf;MAC7B,CAAC;IAAE,CAAC,EAAEnC,CAAC,CAAC,MAAM,EAAE;MAAE0D,GAAG,EAAE,0CAA0C;MAAE+C,YAAY,EAAE,IAAI,CAACN;IAAY,CAAC,CAAC,CAAC;EAC7G;EACA,IAAIjD,EAAEA,CAAA,EAAG;IAAE,OAAO9C,UAAU,CAAC,IAAI,CAAC;EAAE;EACpC,WAAWsG,QAAQA,CAAA,EAAG;IAAE,OAAO;MAC3B,UAAU,EAAE,CAAC,iBAAiB,CAAC;MAC/B,qBAAqB,EAAE,CAAC,iBAAiB,CAAC;MAC1C,oBAAoB,EAAE,CAAC,iBAAiB;IAC5C,CAAC;EAAE;AACP,CAAC;AACD7B,WAAW,CAACP,KAAK,GAAG;EAChBC,GAAG,EAAEG,uBAAuB;EAC5BF,EAAE,EAAEI;AACR,CAAC;AAED,SAAStD,UAAU,IAAIqF,cAAc,EAAE9B,WAAW,IAAI+B,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}