<!-- Ultra-Modern Compact Header -->
<mat-toolbar color="primary" class="ultra-modern-toolbar">
  <button mat-icon-button (click)="goBack()">
    <mat-icon>arrow_back</mat-icon>
  </button>
  <div class="toolbar-content">
    <span class="toolbar-title">Register Complaint</span>
    <div class="mini-progress">
      <div class="progress-bar" [style.width.%]="(currentStep / 4) * 100"></div>
    </div>
  </div>
  <button mat-icon-button>
    <mat-icon>help_outline</mat-icon>
  </button>
</mat-toolbar>

<!-- Ultra-Compact Main Container -->
<div class="ultra-compact-container">
  <!-- Inline Progress Stepper -->
  <div class="inline-stepper">
    <div class="step-progress">
      <div class="step-dot" [class.active]="currentStep >= 1" [class.completed]="currentStep > 1">
        <mat-icon *ngIf="currentStep > 1">check</mat-icon>
        <span *ngIf="currentStep <= 1">1</span>
      </div>
      <div class="step-line" [class.active]="currentStep > 1"></div>
      <div class="step-dot" [class.active]="currentStep >= 2" [class.completed]="currentStep > 2">
        <mat-icon *ngIf="currentStep > 2">check</mat-icon>
        <span *ngIf="currentStep <= 2">2</span>
      </div>
      <div class="step-line" [class.active]="currentStep > 2"></div>
      <div class="step-dot" [class.active]="currentStep >= 3" [class.completed]="currentStep > 3">
        <mat-icon *ngIf="currentStep > 3">check</mat-icon>
        <span *ngIf="currentStep <= 3">3</span>
      </div>
      <div class="step-line" [class.active]="currentStep > 3"></div>
      <div class="step-dot" [class.active]="currentStep >= 4" [class.completed]="currentStep > 4">
        <mat-icon *ngIf="currentStep > 4">check</mat-icon>
        <span *ngIf="currentStep <= 4">4</span>
      </div>
    </div>
    <div class="step-labels">
      <span [class.active]="currentStep === 1">Type</span>
      <span [class.active]="currentStep === 2">Description</span>
      <span [class.active]="currentStep === 3">Invoice</span>
      <span [class.active]="currentStep === 4">Submit</span>
    </div>
  </div>

  <!-- Breadcrumb Summary (Shows completed steps) -->
  <div class="breadcrumb-summary" *ngIf="currentStep > 1">
    <mat-chip-set>
      <mat-chip *ngIf="getSelectedComplaintType()" color="primary" selected>
        <mat-icon matChipAvatar>{{ getSelectedComplaintType()?.icon }}</mat-icon>
        {{ getSelectedComplaintType()?.label }}
      </mat-chip>
      <mat-chip *ngIf="getSelectedComplaintDescription()" color="accent" selected>
        {{ getSelectedComplaintDescription()?.label }}
      </mat-chip>
      <mat-chip *ngIf="selectedInvoice" color="warn" selected>
        {{ selectedInvoice.invoiceNumber }}
      </mat-chip>
    </mat-chip-set>
  </div>

  <!-- Ultra-Compact Step Content -->
  <div class="ultra-compact-step">
    
    <!-- Step 1: Complaint Type Selection -->
    <div *ngIf="currentStep === 1" class="step-container">
      <div class="step-header">
        <h2>Select Complaint Type</h2>
        <p>Choose the category that best describes your issue</p>
      </div>
      
      <form [formGroup]="complaintTypeForm">
        <div class="compact-grid">
          <mat-card 
            class="type-option" 
            *ngFor="let type of complaintTypes"
            [class.selected]="complaintTypeForm.get('selectedType')?.value === type.value"
            (click)="selectComplaintType(type)"
            matRipple>
            <mat-card-content>
              <div class="option-layout">
                <mat-icon class="option-icon" [color]="complaintTypeForm.get('selectedType')?.value === type.value ? 'primary' : ''">
                  {{ type.icon }}
                </mat-icon>
                <div class="option-content">
                  <h3>{{ type.label }}</h3>
                  <p>{{ type.description }}</p>
                </div>
                <mat-radio-button 
                  [value]="type.value" 
                  formControlName="selectedType"
                  color="primary">
                </mat-radio-button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </form>
    </div>

    <!-- Step 2: Complaint Description Selection -->
    <div *ngIf="currentStep === 2" class="step-container">
      <div class="step-header">
        <h2>Select Specific Issue</h2>
        <p>Choose the description that best matches your complaint</p>
      </div>
      
      <form [formGroup]="complaintDescriptionForm">
        <div class="compact-list">
          <mat-radio-group 
            formControlName="selectedDescription" 
            (change)="onDescriptionChange($event)">
            <mat-card 
              class="description-option"
              *ngFor="let desc of getComplaintDescriptions()"
              [class.selected]="complaintDescriptionForm.get('selectedDescription')?.value === desc.value"
              matRipple>
              <mat-card-content>
                <div class="radio-layout">
                  <mat-radio-button 
                    [value]="desc.value" 
                    color="primary">
                  </mat-radio-button>
                  <div class="radio-content">
                    <h4>{{ desc.label }}</h4>
                    <p>{{ desc.description }}</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </mat-radio-group>
        </div>
      </form>
    </div>

    <!-- Step 3: Invoice Selection -->
    <div *ngIf="currentStep === 3" class="step-container">
      <div class="step-header">
        <h2>Select Invoice</h2>
        <p>Search and select the invoice related to your complaint</p>
      </div>
      
      <form [formGroup]="invoiceSearchForm">
        <!-- Compact Search -->
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search Invoice</mat-label>
          <input matInput formControlName="searchTerm" 
                 placeholder="Invoice number, customer name, or leave empty for all" 
                 (input)="onInvoiceSearch()">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <!-- Compact Invoice List -->
        <div class="invoice-list" *ngIf="showInvoiceResults && invoiceSearchResults.length > 0">
          <div class="results-info">{{ invoiceSearchResults.length }} invoices found</div>
          
          <div class="compact-invoice-grid">
            <mat-card 
              class="invoice-option" 
              *ngFor="let invoice of invoiceSearchResults" 
              (click)="selectInvoice(invoice)"
              [class.selected]="selectedInvoice?.invoiceNumber === invoice.invoiceNumber"
              matRipple>
              <mat-card-content>
                <div class="invoice-layout">
                  <div class="invoice-main">
                    <div class="invoice-number">{{ invoice.invoiceNumber }}</div>
                    <div class="invoice-date">{{ invoice.invoiceDate | date:'MMM dd' }}</div>
                  </div>
                  <div class="invoice-details">
                    <div class="customer">{{ invoice.customerName }}</div>
                    <div class="location">{{ invoice.zone }}</div>
                  </div>
                  <mat-icon class="select-icon">
                    {{ selectedInvoice?.invoiceNumber === invoice.invoiceNumber ? 'check_circle' : 'radio_button_unchecked' }}
                  </mat-icon>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>

        <!-- Selected Invoice Preview -->
        <mat-card class="selected-preview" *ngIf="selectedInvoice">
          <mat-card-header>
            <mat-card-title>
              <mat-icon color="primary">check_circle</mat-icon>
              Selected: {{ selectedInvoice.invoiceNumber }}
            </mat-card-title>
          </mat-card-header>
          <mat-card-content>
            <div class="preview-grid">
              <div><strong>Customer:</strong> {{ selectedInvoice.customerName }}</div>
              <div><strong>Date:</strong> {{ selectedInvoice.invoiceDate | date:'MMM dd, yyyy' }}</div>
              <div><strong>Zone:</strong> {{ selectedInvoice.zone }}</div>
              <div><strong>Items:</strong> {{ selectedInvoice.items.length }} items</div>
            </div>
            <button mat-button color="warn" (click)="clearInvoiceSelection()">
              <mat-icon>clear</mat-icon>
              Change Selection
            </button>
          </mat-card-content>
        </mat-card>

        <!-- No Results -->
        <div class="no-results" *ngIf="showInvoiceResults && invoiceSearchResults.length === 0">
          <mat-icon>search_off</mat-icon>
          <p>No invoices found</p>
          <p class="hint">Try different keywords or clear search</p>
        </div>
      </form>
    </div>

    <!-- Step 4: Final Details & Submit -->
    <div *ngIf="currentStep === 4" class="step-container">
      <div class="step-header">
        <h2>Contact Details & Submit</h2>
        <p>Provide your contact information and submit your complaint</p>
      </div>
      
      <!-- Compact Final Summary -->
      <mat-card class="final-summary">
        <mat-card-content>
          <div class="summary-title">
            <mat-icon>assignment</mat-icon>
            <span>Complaint Summary</span>
          </div>
          <div class="summary-chips">
            <mat-chip-set>
              <mat-chip color="primary" selected>
                <mat-icon matChipAvatar>{{ getSelectedComplaintType()?.icon }}</mat-icon>
                {{ getSelectedComplaintType()?.label }}
              </mat-chip>
              <mat-chip color="accent" selected>
                {{ getSelectedComplaintDescription()?.label }}
              </mat-chip>
              <mat-chip color="warn" selected *ngIf="selectedInvoice">
                {{ selectedInvoice.invoiceNumber }} • {{ selectedInvoice.customerName }}
              </mat-chip>
            </mat-chip-set>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Compact Contact Form -->
      <mat-card class="contact-form">
        <mat-card-content>
          <form [formGroup]="complaintDetailsForm">
            <div class="form-grid">
              <mat-form-field appearance="outline">
                <mat-label>Contact Person *</mat-label>
                <input matInput formControlName="contactPersonName" placeholder="Your name">
                <mat-icon matSuffix>person</mat-icon>
                <mat-error *ngIf="complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched">
                  {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Contact Number *</mat-label>
                <input matInput formControlName="contactNumber" placeholder="10-digit number" type="tel">
                <mat-icon matSuffix>phone</mat-icon>
                <mat-error *ngIf="complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched">
                  {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}
                </mat-error>
              </mat-form-field>
            </div>

            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Additional Comments</mat-label>
              <textarea matInput formControlName="comments" rows="2" placeholder="Optional additional information"></textarea>
              <mat-icon matSuffix>comment</mat-icon>
            </mat-form-field>

            <!-- Compact File Upload -->
            <div class="upload-section">
              <mat-checkbox formControlName="hasComplaintLetters" color="primary">
                Attach supporting documents
              </mat-checkbox>
              
              <div class="upload-area" *ngIf="complaintDetailsForm.get('hasComplaintLetters')?.value">
                <input type="file" #fileInput (change)="onFileSelected($event)" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="display: none;">
                <button mat-stroked-button color="primary" (click)="fileInput.click()">
                  <mat-icon>attach_file</mat-icon>
                  Choose Files
                </button>
                <span class="upload-hint">PDF, JPG, PNG, DOC (Max 5MB each)</span>
                
                <div class="file-chips" *ngIf="selectedFiles.length > 0">
                  <mat-chip-set>
                    <mat-chip *ngFor="let file of selectedFiles; let i = index" [removable]="true" (removed)="removeFile(i)">
                      <mat-icon matChipAvatar>description</mat-icon>
                      {{ file.name }}
                      <mat-icon matChipRemove>cancel</mat-icon>
                    </mat-chip>
                  </mat-chip-set>
                </div>
              </div>
            </div>
          </form>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Fixed Bottom Navigation -->
  <div class="bottom-navigation">
    <button mat-button 
            *ngIf="currentStep > 1" 
            (click)="goToPreviousStep()" 
            class="nav-button">
      <mat-icon>arrow_back</mat-icon>
      Back
    </button>
    
    <div class="spacer"></div>
    
    <button mat-raised-button 
            color="primary" 
            (click)="goToNextStep()" 
            [disabled]="!canProceedToNextStep()"
            class="nav-button">
      <span *ngIf="currentStep < 4">Continue</span>
      <span *ngIf="currentStep === 4">
        <mat-icon *ngIf="!isLoading">send</mat-icon>
        <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
        {{ isLoading ? 'Submitting...' : 'Submit Complaint' }}
      </span>
      <mat-icon *ngIf="currentStep < 4">arrow_forward</mat-icon>
    </button>
  </div>

  <!-- Success State -->
  <div class="success-state" *ngIf="isStepCompleted(4)">
    <mat-card class="success-card">
      <mat-card-content>
        <mat-icon class="success-icon" color="primary">check_circle</mat-icon>
        <h2>Complaint Submitted Successfully!</h2>
        <p>Your complaint has been registered and will be processed shortly.</p>
        <button mat-raised-button color="primary" (click)="goBack()">
          <mat-icon>home</mat-icon>
          Back to Home
        </button>
      </mat-card-content>
    </mat-card>
  </div>
</div>
