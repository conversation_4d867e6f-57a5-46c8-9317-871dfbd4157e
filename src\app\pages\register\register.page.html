<ion-header [translucent]="true" class="material-header">
  <mat-toolbar class="material-toolbar">
    <button mat-icon-button (click)="goBack()" aria-label="Back">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <span class="toolbar-title">Register Complaint</span>
    <span class="spacer"></span>
  </mat-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="material-content">
  <div class="register-container">
    <mat-card class="register-card">
      <mat-card-header>
        <mat-card-title class="register-title">
          <mat-icon class="title-icon">assignment</mat-icon>
          New Complaint Registration
        </mat-card-title>
        <mat-card-subtitle>Please fill in all the required information</mat-card-subtitle>
      </mat-card-header>

      <mat-card-content>
        <mat-stepper #stepper orientation="vertical" linear="true" class="register-stepper">
          
          <!-- Step 1: Personal Information -->
          <mat-step [stepControl]="personalInfoForm" label="Personal Information" state="person">
            <ng-template matStepLabel>Personal Information</ng-template>
            
            <form [formGroup]="personalInfoForm" class="step-form">
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>First Name</mat-label>
                  <input matInput formControlName="firstName" placeholder="Enter first name">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="personalInfoForm.get('firstName')?.invalid && personalInfoForm.get('firstName')?.touched">
                    {{ getErrorMessage(personalInfoForm, 'firstName') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Last Name</mat-label>
                  <input matInput formControlName="lastName" placeholder="Enter last name">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="personalInfoForm.get('lastName')?.invalid && personalInfoForm.get('lastName')?.touched">
                    {{ getErrorMessage(personalInfoForm, 'lastName') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Email</mat-label>
                <input matInput formControlName="email" placeholder="Enter email address" type="email">
                <mat-icon matSuffix>email</mat-icon>
                <mat-error *ngIf="personalInfoForm.get('email')?.invalid && personalInfoForm.get('email')?.touched">
                  {{ getErrorMessage(personalInfoForm, 'email') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Phone Number</mat-label>
                <input matInput formControlName="phone" placeholder="Enter 10-digit phone number" type="tel">
                <mat-icon matSuffix>phone</mat-icon>
                <mat-error *ngIf="personalInfoForm.get('phone')?.invalid && personalInfoForm.get('phone')?.touched">
                  {{ getErrorMessage(personalInfoForm, 'phone') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Company</mat-label>
                <input matInput formControlName="company" placeholder="Enter company name">
                <mat-icon matSuffix>business</mat-icon>
                <mat-error *ngIf="personalInfoForm.get('company')?.invalid && personalInfoForm.get('company')?.touched">
                  {{ getErrorMessage(personalInfoForm, 'company') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Address</mat-label>
                <textarea matInput formControlName="address" placeholder="Enter complete address" rows="3"></textarea>
                <mat-icon matSuffix>location_on</mat-icon>
                <mat-error *ngIf="personalInfoForm.get('address')?.invalid && personalInfoForm.get('address')?.touched">
                  {{ getErrorMessage(personalInfoForm, 'address') }}
                </mat-error>
              </mat-form-field>
            </form>

            <div class="step-actions">
              <button mat-raised-button color="primary" matStepperNext [disabled]="personalInfoForm.invalid">
                Next
                <mat-icon>arrow_forward</mat-icon>
              </button>
            </div>
          </mat-step>

          <!-- Step 2: Complaint Details -->
          <mat-step [stepControl]="complaintForm" label="Complaint Details" state="assignment">
            <ng-template matStepLabel>Complaint Details</ng-template>
            
            <form [formGroup]="complaintForm" class="step-form">
              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Invoice Number</mat-label>
                <input matInput formControlName="invoiceNumber" placeholder="Enter invoice number">
                <mat-icon matSuffix>receipt</mat-icon>
                <mat-error *ngIf="complaintForm.get('invoiceNumber')?.invalid && complaintForm.get('invoiceNumber')?.touched">
                  {{ getErrorMessage(complaintForm, 'invoiceNumber') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Complaint Type</mat-label>
                <mat-select formControlName="complaintType">
                  <mat-option *ngFor="let type of complaintTypes" [value]="type.value">
                    {{ type.label }}
                  </mat-option>
                </mat-select>
                <mat-icon matSuffix>category</mat-icon>
                <mat-error *ngIf="complaintForm.get('complaintType')?.invalid && complaintForm.get('complaintType')?.touched">
                  {{ getErrorMessage(complaintForm, 'complaintType') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Urgency Level</mat-label>
                <mat-select formControlName="urgency">
                  <mat-option value="low">Low</mat-option>
                  <mat-option value="medium">Medium</mat-option>
                  <mat-option value="high">High</mat-option>
                  <mat-option value="critical">Critical</mat-option>
                </mat-select>
                <mat-icon matSuffix>priority_high</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Description</mat-label>
                <textarea 
                  matInput 
                  formControlName="description" 
                  placeholder="Describe your complaint in detail" 
                  rows="4">
                </textarea>
                <mat-icon matSuffix>description</mat-icon>
                <mat-error *ngIf="complaintForm.get('description')?.invalid && complaintForm.get('description')?.touched">
                  {{ getErrorMessage(complaintForm, 'description') }}
                </mat-error>
              </mat-form-field>

              <!-- File Upload -->
              <div class="file-upload-section">
                <h4>Attachments (Optional)</h4>
                <input 
                  type="file" 
                  #fileInput 
                  (change)="onFileSelected($event)" 
                  multiple 
                  accept="image/*,.pdf,.doc,.docx"
                  style="display: none;">
                
                <button 
                  mat-stroked-button 
                  color="primary" 
                  (click)="fileInput.click()"
                  class="upload-button">
                  <mat-icon>attach_file</mat-icon>
                  Choose Files
                </button>

                <div *ngIf="selectedFiles.length > 0" class="selected-files">
                  <div *ngFor="let file of selectedFiles; let i = index" class="file-item">
                    <mat-icon>insert_drive_file</mat-icon>
                    <span>{{ file.name }}</span>
                    <button mat-icon-button (click)="removeFile(i)" color="warn">
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>
                </div>
              </div>

              <!-- Terms and Conditions -->
              <div class="terms-section">
                <mat-checkbox formControlName="agreedToTerms" color="primary">
                  I agree to the <a href="#" class="terms-link">Terms and Conditions</a>
                </mat-checkbox>
              </div>
            </form>

            <div class="step-actions">
              <button mat-button matStepperPrevious>
                <mat-icon>arrow_back</mat-icon>
                Back
              </button>
              <button mat-raised-button color="primary" matStepperNext [disabled]="complaintForm.invalid">
                Next
                <mat-icon>arrow_forward</mat-icon>
              </button>
            </div>
          </mat-step>

          <!-- Step 3: Review and Submit -->
          <mat-step label="Review & Submit" state="done">
            <ng-template matStepLabel>Review & Submit</ng-template>
            
            <div class="review-section">
              <h4>Review Your Information</h4>
              
              <div class="review-card">
                <h5>Personal Information</h5>
                <p><strong>Name:</strong> {{ personalInfoForm.get('firstName')?.value }} {{ personalInfoForm.get('lastName')?.value }}</p>
                <p><strong>Email:</strong> {{ personalInfoForm.get('email')?.value }}</p>
                <p><strong>Phone:</strong> {{ personalInfoForm.get('phone')?.value }}</p>
                <p><strong>Company:</strong> {{ personalInfoForm.get('company')?.value }}</p>
              </div>

              <div class="review-card">
                <h5>Complaint Details</h5>
                <p><strong>Invoice Number:</strong> {{ complaintForm.get('invoiceNumber')?.value }}</p>
                <p><strong>Type:</strong> {{ complaintForm.get('complaintType')?.value }}</p>
                <p><strong>Urgency:</strong> {{ complaintForm.get('urgency')?.value }}</p>
                <p><strong>Description:</strong> {{ complaintForm.get('description')?.value }}</p>
                <p *ngIf="selectedFiles.length > 0"><strong>Attachments:</strong> {{ selectedFiles.length }} file(s)</p>
              </div>
            </div>

            <div class="step-actions">
              <button mat-button matStepperPrevious>
                <mat-icon>arrow_back</mat-icon>
                Back
              </button>
              <button 
                mat-raised-button 
                color="primary" 
                (click)="onSubmit()"
                [disabled]="isLoading || personalInfoForm.invalid || complaintForm.invalid"
                class="submit-button">
                <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
                <mat-icon *ngIf="!isLoading">send</mat-icon>
                <span *ngIf="!isLoading">Submit Complaint</span>
                <span *ngIf="isLoading">Submitting...</span>
              </button>
            </div>
          </mat-step>
        </mat-stepper>
      </mat-card-content>
    </mat-card>
  </div>
</ion-content>
