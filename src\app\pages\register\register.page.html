<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Register Complaint</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="register-content">
  <div class="container">
    <!-- Header Section -->
    <div class="header-section">
      <div class="header-content">
        <h1>Register New Complaint</h1>
        <p>Follow the steps below to register your complaint</p>
      </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-section">
      <div class="progress-steps">
        <div class="step" [class.active]="true" [class.completed]="isStepCompleted(1)">
          <div class="step-number">1</div>
          <div class="step-label">Complaint Type</div>
        </div>
        <div class="step-connector" [class.completed]="isStepCompleted(1)"></div>
        <div class="step" [class.active]="isStepCompleted(1)" [class.completed]="isStepCompleted(2)">
          <div class="step-number">2</div>
          <div class="step-label">Invoice Search</div>
        </div>
        <div class="step-connector" [class.completed]="isStepCompleted(2)"></div>
        <div class="step" [class.active]="isStepCompleted(2)" [class.completed]="isStepCompleted(3)">
          <div class="step-number">3</div>
          <div class="step-label">Complaint Details</div>
        </div>
      </div>
    </div>

    <!-- Step 1: Complaint Type Selection -->
    <div class="step-content" *ngIf="!isStepCompleted(1)">
      <form [formGroup]="complaintTypeForm">
        <div class="form-section">
          <h3>Select Complaint Type</h3>
          <p class="section-description">Choose the category that best describes your complaint</p>

          <div class="complaint-types-grid">
            <div class="complaint-type-card"
                 *ngFor="let type of complaintTypes"
                 [class.selected]="complaintTypeForm.get('selectedType')?.value === type.value"
                 (click)="selectComplaintType(type)">
              <div class="card-icon">
                <ion-icon [name]="type.icon"></ion-icon>
              </div>
              <div class="card-content">
                <h4>{{ type.label }}</h4>
                <p>{{ type.description }}</p>
              </div>
              <div class="card-radio">
                <ion-radio [value]="type.value" formControlName="selectedType"></ion-radio>
              </div>
            </div>
          </div>

          <div class="step-actions" *ngIf="complaintTypeForm.get('selectedType')?.value">
            <button mat-raised-button color="primary" (click)="isStepCompleted(1) ? null : null">
              Continue to Invoice Search
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Step 2: Invoice Search -->
    <div class="step-content" *ngIf="isStepCompleted(1) && !isStepCompleted(2)">
      <form [formGroup]="invoiceSearchForm">
        <div class="form-section">
          <h3>Search Invoice</h3>
          <p class="section-description">Search for your invoice by invoice number or customer name</p>

          <div class="search-section">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search Invoice</mat-label>
              <input matInput formControlName="searchTerm" placeholder="Enter invoice number, customer name, or leave empty to see all" (input)="onInvoiceSearch()">
              <mat-icon matSuffix>search</mat-icon>
              <mat-error *ngIf="invoiceSearchForm.get('searchTerm')?.invalid && invoiceSearchForm.get('searchTerm')?.touched">
                {{ getErrorMessage(invoiceSearchForm, 'searchTerm') }}
              </mat-error>
            </mat-form-field>

            <div class="search-results" *ngIf="showInvoiceResults && invoiceSearchResults.length > 0">
              <h4>
                <span *ngIf="invoiceSearchForm.get('searchTerm')?.value && invoiceSearchForm.get('searchTerm')?.value.trim() !== ''">
                  Search Results ({{ invoiceSearchResults.length }} found)
                </span>
                <span *ngIf="!invoiceSearchForm.get('searchTerm')?.value || invoiceSearchForm.get('searchTerm')?.value.trim() === ''">
                  All Available Invoices ({{ invoiceSearchResults.length }} total)
                </span>
              </h4>
              <div class="invoice-list">
                <div class="invoice-item" *ngFor="let invoice of invoiceSearchResults" (click)="selectInvoice(invoice)">
                  <div class="invoice-header">
                    <strong>{{ invoice.invoiceNumber }}</strong>
                    <span class="invoice-date">{{ invoice.invoiceDate | date:'dd/MM/yyyy' }}</span>
                  </div>
                  <div class="invoice-customer">{{ invoice.customerName }}</div>
                  <div class="invoice-zone">{{ invoice.zone }} - {{ invoice.operatingUnit }}</div>
                </div>
              </div>
            </div>

            <div class="no-results" *ngIf="showInvoiceResults && invoiceSearchResults.length === 0">
              <p>No invoices found matching your search criteria.</p>
              <p class="search-hint">Try searching with different keywords or clear the search to see all invoices.</p>
            </div>
          </div>

          <!-- Selected Invoice Display -->
          <div class="selected-invoice" *ngIf="selectedInvoice">
            <h4>Selected Invoice Details</h4>
            <mat-card class="invoice-details-card">
              <mat-card-content>
                <div class="invoice-details-grid">
                  <div class="detail-item">
                    <label>Invoice Number:</label>
                    <span>{{ selectedInvoice.invoiceNumber }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Invoice Date:</label>
                    <span>{{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Customer Name:</label>
                    <span>{{ selectedInvoice.customerName }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Customer Address:</label>
                    <span>{{ selectedInvoice.customerAddress }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Zone:</label>
                    <span>{{ selectedInvoice.zone }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Operating Unit:</label>
                    <span>{{ selectedInvoice.operatingUnit }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Organization:</label>
                    <span>{{ selectedInvoice.organization }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Bill To Location:</label>
                    <span>{{ selectedInvoice.billToLocation }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Ship To Location:</label>
                    <span>{{ selectedInvoice.shipToLocation }}</span>
                  </div>
                </div>
                <div class="invoice-actions">
                  <button mat-button color="warn" (click)="clearInvoiceSelection()">
                    <mat-icon>clear</mat-icon>
                    Clear Selection
                  </button>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <div class="step-actions" *ngIf="selectedInvoice">
            <button mat-raised-button color="primary">
              Continue to Complaint Details
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Step 3: Complaint Details -->
    <div class="step-content" *ngIf="isStepCompleted(2) && !isStepCompleted(3)">
      <div class="form-section">
        <h3>Complaint Details</h3>
        <p class="section-description">Review invoice details and provide your complaint information</p>

        <!-- Selected Complaint Type Display -->
        <div class="selected-type-display" *ngIf="getSelectedComplaintType()">
          <h4>Selected Complaint Type</h4>
          <div class="type-display-card">
            <ion-icon [name]="getSelectedComplaintType()?.icon"></ion-icon>
            <div>
              <strong>{{ getSelectedComplaintType()?.label }}</strong>
              <p>{{ getSelectedComplaintType()?.description }}</p>
            </div>
          </div>
        </div>

        <!-- Read-Only Invoice Details Section -->
        <div class="invoice-details-section" *ngIf="selectedInvoice">
          <h4>
            <mat-icon>receipt</mat-icon>
            Invoice Details (Read-Only)
          </h4>
          <div class="readonly-invoice-card">
            <div class="invoice-details-grid">
              <div class="detail-group">
                <div class="detail-item">
                  <label>Invoice Number</label>
                  <div class="readonly-field">
                    <mat-icon>confirmation_number</mat-icon>
                    <span>{{ selectedInvoice.invoiceNumber }}</span>
                  </div>
                </div>
                <div class="detail-item">
                  <label>Invoice Date</label>
                  <div class="readonly-field">
                    <mat-icon>calendar_today</mat-icon>
                    <span>{{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}</span>
                  </div>
                </div>
              </div>

              <div class="detail-group">
                <div class="detail-item">
                  <label>Customer Name</label>
                  <div class="readonly-field">
                    <mat-icon>business</mat-icon>
                    <span>{{ selectedInvoice.customerName }}</span>
                  </div>
                </div>
                <div class="detail-item">
                  <label>Customer Address</label>
                  <div class="readonly-field">
                    <mat-icon>location_on</mat-icon>
                    <span>{{ selectedInvoice.customerAddress }}</span>
                  </div>
                </div>
              </div>

              <div class="detail-group">
                <div class="detail-item">
                  <label>Zone</label>
                  <div class="readonly-field">
                    <mat-icon>map</mat-icon>
                    <span>{{ selectedInvoice.zone }}</span>
                  </div>
                </div>
                <div class="detail-item">
                  <label>Operating Unit</label>
                  <div class="readonly-field">
                    <mat-icon>domain</mat-icon>
                    <span>{{ selectedInvoice.operatingUnit }}</span>
                  </div>
                </div>
              </div>

              <div class="detail-group">
                <div class="detail-item">
                  <label>Organization</label>
                  <div class="readonly-field">
                    <mat-icon>corporate_fare</mat-icon>
                    <span>{{ selectedInvoice.organization }}</span>
                  </div>
                </div>
                <div class="detail-item">
                  <label>Bill To Location</label>
                  <div class="readonly-field">
                    <mat-icon>account_balance</mat-icon>
                    <span>{{ selectedInvoice.billToLocation }}</span>
                  </div>
                </div>
              </div>

              <div class="detail-group">
                <div class="detail-item full-width">
                  <label>Ship To Location</label>
                  <div class="readonly-field">
                    <mat-icon>local_shipping</mat-icon>
                    <span>{{ selectedInvoice.shipToLocation }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Editable Complaint Form Section -->
        <form [formGroup]="complaintDetailsForm">
          <div class="complaint-form-section">
            <h4>
              <mat-icon>edit</mat-icon>
              Complaint Information (Required)
            </h4>

            <div class="editable-form-card">
              <div class="form-row">
                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Contact Person Name *</mat-label>
                  <input matInput formControlName="contactPersonName" placeholder="Enter contact person name">
                  <mat-icon matSuffix>person</mat-icon>
                  <mat-error *ngIf="complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched">
                    {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}
                  </mat-error>
                </mat-form-field>

                <mat-form-field appearance="outline" class="half-width">
                  <mat-label>Contact Number *</mat-label>
                  <input matInput formControlName="contactNumber" placeholder="Enter 10-digit contact number" type="tel">
                  <mat-icon matSuffix>phone</mat-icon>
                  <mat-error *ngIf="complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched">
                    {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}
                  </mat-error>
                </mat-form-field>
              </div>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Complaint Description *</mat-label>
                <textarea matInput formControlName="complaintDescription" rows="4" placeholder="Describe your complaint in detail (minimum 10 characters)"></textarea>
                <mat-icon matSuffix>description</mat-icon>
                <mat-hint>Please provide a detailed description of the issue</mat-hint>
                <mat-error *ngIf="complaintDetailsForm.get('complaintDescription')?.invalid && complaintDetailsForm.get('complaintDescription')?.touched">
                  {{ getErrorMessage(complaintDetailsForm, 'complaintDescription') }}
                </mat-error>
              </mat-form-field>

              <mat-form-field appearance="outline" class="full-width">
                <mat-label>Additional Comments (Optional)</mat-label>
                <textarea matInput formControlName="comments" rows="3" placeholder="Any additional comments or information"></textarea>
                <mat-icon matSuffix>comment</mat-icon>
                <mat-hint>Optional: Add any additional information that might be helpful</mat-hint>
              </mat-form-field>

              <!-- Complaint Letters Checkbox -->
              <div class="complaint-letters-section">
                <mat-checkbox formControlName="hasComplaintLetters" color="primary">
                  <strong>Do you have complaint letters to attach?</strong>
                </mat-checkbox>
                <p class="checkbox-hint">Check this box if you have supporting documents, photos, or letters related to your complaint</p>
              </div>

              <!-- File Upload Section -->
              <div class="file-upload-section" *ngIf="complaintDetailsForm.get('hasComplaintLetters')?.value">
                <h5>
                  <mat-icon>attach_file</mat-icon>
                  Upload Supporting Documents
                </h5>
                <div class="upload-area">
                  <input type="file" #fileInput (change)="onFileSelected($event)" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="display: none;">

                  <button mat-stroked-button color="primary" (click)="fileInput.click()" class="upload-button">
                    <mat-icon>cloud_upload</mat-icon>
                    Choose Files
                  </button>
                  <p class="upload-hint">
                    <mat-icon>info</mat-icon>
                    Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each)
                  </p>
                </div>

                <div class="selected-files" *ngIf="selectedFiles.length > 0">
                  <h6>Selected Files ({{ selectedFiles.length }}):</h6>
                  <div class="file-list">
                    <div class="file-item" *ngFor="let file of selectedFiles; let i = index">
                      <mat-icon class="file-icon">description</mat-icon>
                      <div class="file-info">
                        <span class="file-name">{{ file.name }}</span>
                        <span class="file-size">({{ (file.size / 1024 / 1024).toFixed(2) }} MB)</span>
                      </div>
                      <button mat-icon-button (click)="removeFile(i)" class="remove-file" color="warn">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="step-actions">
            <button mat-button (click)="clearInvoiceSelection()" class="back-button">
              <mat-icon>arrow_back</mat-icon>
              Change Invoice
            </button>
            <button mat-raised-button color="primary" (click)="onSubmitComplaint()" [disabled]="isLoading || !complaintDetailsForm.valid" class="submit-button">
              <mat-icon *ngIf="!isLoading">send</mat-icon>
              <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
              {{ isLoading ? 'Submitting Complaint...' : 'Submit Complaint' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</ion-content>
