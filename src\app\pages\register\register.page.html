<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button (click)="goBack()">
        <ion-icon name="arrow-back"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>Register Complaint</ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="register-content">
  <div class="container">
    <!-- Header Section -->
    <div class="header-section">
      <div class="header-content">
        <h1>Register New Complaint</h1>
        <p>Follow the steps below to register your complaint</p>
      </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-section">
      <div class="progress-steps">
        <div class="step" [class.active]="true" [class.completed]="isStepCompleted(1)">
          <div class="step-number">1</div>
          <div class="step-label">Complaint Type</div>
        </div>
        <div class="step-connector" [class.completed]="isStepCompleted(1)"></div>
        <div class="step" [class.active]="isStepCompleted(1)" [class.completed]="isStepCompleted(2)">
          <div class="step-number">2</div>
          <div class="step-label">Invoice Search</div>
        </div>
        <div class="step-connector" [class.completed]="isStepCompleted(2)"></div>
        <div class="step" [class.active]="isStepCompleted(2)" [class.completed]="isStepCompleted(3)">
          <div class="step-number">3</div>
          <div class="step-label">Complaint Details</div>
        </div>
      </div>
    </div>

    <!-- Step 1: Complaint Type Selection -->
    <div class="step-content" *ngIf="!isStepCompleted(1)">
      <form [formGroup]="complaintTypeForm">
        <div class="form-section">
          <h3>Select Complaint Type</h3>
          <p class="section-description">Choose the category that best describes your complaint</p>

          <div class="complaint-types-grid">
            <div class="complaint-type-card"
                 *ngFor="let type of complaintTypes"
                 [class.selected]="complaintTypeForm.get('selectedType')?.value === type.value"
                 (click)="selectComplaintType(type)">
              <div class="card-icon">
                <ion-icon [name]="type.icon"></ion-icon>
              </div>
              <div class="card-content">
                <h4>{{ type.label }}</h4>
                <p>{{ type.description }}</p>
              </div>
              <div class="card-radio">
                <ion-radio [value]="type.value" formControlName="selectedType"></ion-radio>
              </div>
            </div>
          </div>

          <div class="step-actions" *ngIf="complaintTypeForm.get('selectedType')?.value">
            <button mat-raised-button color="primary" (click)="isStepCompleted(1) ? null : null">
              Continue to Invoice Search
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Step 2: Invoice Search -->
    <div class="step-content" *ngIf="isStepCompleted(1) && !isStepCompleted(2)">
      <form [formGroup]="invoiceSearchForm">
        <div class="form-section">
          <h3>Search Invoice</h3>
          <p class="section-description">Search for your invoice by invoice number or customer name</p>

          <div class="search-section">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search Invoice</mat-label>
              <input matInput formControlName="searchTerm" placeholder="Enter invoice number or customer name" (input)="onInvoiceSearch()">
              <mat-icon matSuffix>search</mat-icon>
              <mat-error *ngIf="invoiceSearchForm.get('searchTerm')?.invalid && invoiceSearchForm.get('searchTerm')?.touched">
                {{ getErrorMessage(invoiceSearchForm, 'searchTerm') }}
              </mat-error>
            </mat-form-field>

            <div class="search-results" *ngIf="showInvoiceResults && invoiceSearchResults.length > 0">
              <h4>Search Results</h4>
              <div class="invoice-list">
                <div class="invoice-item" *ngFor="let invoice of invoiceSearchResults" (click)="selectInvoice(invoice)">
                  <div class="invoice-header">
                    <strong>{{ invoice.invoiceNumber }}</strong>
                    <span class="invoice-date">{{ invoice.invoiceDate | date:'dd/MM/yyyy' }}</span>
                  </div>
                  <div class="invoice-customer">{{ invoice.customerName }}</div>
                  <div class="invoice-zone">{{ invoice.zone }} - {{ invoice.operatingUnit }}</div>
                </div>
              </div>
            </div>

            <div class="no-results" *ngIf="showInvoiceResults && invoiceSearchResults.length === 0">
              <p>No invoices found matching your search criteria.</p>
            </div>
          </div>

          <!-- Selected Invoice Display -->
          <div class="selected-invoice" *ngIf="selectedInvoice">
            <h4>Selected Invoice Details</h4>
            <mat-card class="invoice-details-card">
              <mat-card-content>
                <div class="invoice-details-grid">
                  <div class="detail-item">
                    <label>Invoice Number:</label>
                    <span>{{ selectedInvoice.invoiceNumber }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Invoice Date:</label>
                    <span>{{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Customer Name:</label>
                    <span>{{ selectedInvoice.customerName }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Customer Address:</label>
                    <span>{{ selectedInvoice.customerAddress }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Zone:</label>
                    <span>{{ selectedInvoice.zone }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Operating Unit:</label>
                    <span>{{ selectedInvoice.operatingUnit }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Organization:</label>
                    <span>{{ selectedInvoice.organization }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Bill To Location:</label>
                    <span>{{ selectedInvoice.billToLocation }}</span>
                  </div>
                  <div class="detail-item">
                    <label>Ship To Location:</label>
                    <span>{{ selectedInvoice.shipToLocation }}</span>
                  </div>
                </div>
                <div class="invoice-actions">
                  <button mat-button color="warn" (click)="clearInvoiceSelection()">
                    <mat-icon>clear</mat-icon>
                    Clear Selection
                  </button>
                </div>
              </mat-card-content>
            </mat-card>
          </div>

          <div class="step-actions" *ngIf="selectedInvoice">
            <button mat-raised-button color="primary">
              Continue to Complaint Details
              <mat-icon>arrow_forward</mat-icon>
            </button>
          </div>
        </div>
      </form>
    </div>

    <!-- Step 3: Complaint Details -->
    <div class="step-content" *ngIf="isStepCompleted(2) && !isStepCompleted(3)">
      <form [formGroup]="complaintDetailsForm">
        <div class="form-section">
          <h3>Complaint Details</h3>
          <p class="section-description">Provide detailed information about your complaint</p>

          <div class="selected-type-display" *ngIf="getSelectedComplaintType()">
            <h4>Selected Complaint Type</h4>
            <div class="type-display-card">
              <ion-icon [name]="getSelectedComplaintType()?.icon"></ion-icon>
              <div>
                <strong>{{ getSelectedComplaintType()?.label }}</strong>
                <p>{{ getSelectedComplaintType()?.description }}</p>
              </div>
            </div>
          </div>

          <div class="form-row">
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Contact Person Name</mat-label>
              <input matInput formControlName="contactPersonName" placeholder="Enter contact person name">
              <mat-icon matSuffix>person</mat-icon>
              <mat-error *ngIf="complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched">
                {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}
              </mat-error>
            </mat-form-field>

            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Contact Number</mat-label>
              <input matInput formControlName="contactNumber" placeholder="Enter 10-digit contact number">
              <mat-icon matSuffix>phone</mat-icon>
              <mat-error *ngIf="complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched">
                {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}
              </mat-error>
            </mat-form-field>
          </div>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Complaint Description</mat-label>
            <textarea matInput formControlName="complaintDescription" rows="4" placeholder="Describe your complaint in detail"></textarea>
            <mat-icon matSuffix>description</mat-icon>
            <mat-error *ngIf="complaintDetailsForm.get('complaintDescription')?.invalid && complaintDetailsForm.get('complaintDescription')?.touched">
              {{ getErrorMessage(complaintDetailsForm, 'complaintDescription') }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="full-width">
            <mat-label>Additional Comments (Optional)</mat-label>
            <textarea matInput formControlName="comments" rows="3" placeholder="Any additional comments or information"></textarea>
            <mat-icon matSuffix>comment</mat-icon>
          </mat-form-field>

          <div class="complaint-letters-section">
            <mat-checkbox formControlName="hasComplaintLetters" color="primary">
              Do you have complaint letters to attach?
            </mat-checkbox>
          </div>

          <!-- File Upload Section -->
          <div class="file-upload-section" *ngIf="complaintDetailsForm.get('hasComplaintLetters')?.value">
            <h4>Upload Complaint Letters/Documents</h4>
            <input type="file" #fileInput (change)="onFileSelected($event)" multiple accept=".pdf,.jpg,.jpeg,.png,.doc,.docx" style="display: none;">

            <button mat-stroked-button color="primary" (click)="fileInput.click()" class="upload-button">
              <mat-icon>attach_file</mat-icon>
              Choose Files
            </button>
            <p class="upload-hint">Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each)</p>

            <div class="selected-files" *ngIf="selectedFiles.length > 0">
              <h5>Selected Files:</h5>
              <div class="file-list">
                <div class="file-item" *ngFor="let file of selectedFiles; let i = index">
                  <mat-icon>description</mat-icon>
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">({{ (file.size / 1024 / 1024).toFixed(2) }} MB)</span>
                  <button mat-icon-button (click)="removeFile(i)" class="remove-file">
                    <mat-icon>close</mat-icon>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="step-actions">
            <button mat-raised-button color="primary" (click)="onSubmitComplaint()" [disabled]="isLoading || !complaintDetailsForm.valid">
              <mat-icon *ngIf="!isLoading">send</mat-icon>
              <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
              {{ isLoading ? 'Submitting...' : 'Submit Complaint' }}
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</ion-content>
