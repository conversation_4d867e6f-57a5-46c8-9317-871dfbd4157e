import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path: 'home',
    loadChildren: () => import('./pages/home/<USER>').then( m => m.HomePageModule)
  },
  {
    path: 'login',
    loadChildren: () => import('./pages/login/login.module').then( m => m.LoginPageModule)
  },
  {
    path: 'register',
    loadChildren: () => import('./pages/register/register.module').then( m => m.RegisterPageModule)
  },
  {
    path: 'track',
    loadChildren: () => import('./pages/track/track.module').then( m => m.TrackPageModule)
  },
  {
    path: 'trackitem',
    loadChildren: () => import('./pages/trackitem/trackitem.module').then( m => m.TrackitemPageModule)
  },
  {
    path: 'newcomplaint',
    loadChildren: () => import('./pages/newcomplaint/newcomplaint.module').then( m => m.NewcomplaintPageModule)
  },
  {
    path: 'feedback',
    loadChildren: () => import('./pages/feedback/feedback.module').then( m => m.FeedbackPageModule)
  },
  {
    path: 'creditnote',
    loadChildren: () => import('./pages/creditnote/creditnote.module').then( m => m.CreditnotePageModule)
  },
  {
    path: 'videoplayer',
    loadChildren: () => import('./pages/videoplayer/videoplayer.module').then( m => m.VideoplayerPageModule)
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full'
  },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })
  ],
  exports: [RouterModule]
})
export class AppRoutingModule { }
