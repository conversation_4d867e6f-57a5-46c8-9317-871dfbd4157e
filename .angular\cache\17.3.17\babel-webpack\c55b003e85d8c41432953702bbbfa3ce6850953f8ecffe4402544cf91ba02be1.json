{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\n// Material Design Modules\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { RegisterPageRoutingModule } from './register-routing.module';\nimport { RegisterPage } from './register.page';\nimport * as i0 from \"@angular/core\";\nexport class RegisterPageModule {\n  static {\n    this.ɵfac = function RegisterPageModule_Factory(t) {\n      return new (t || RegisterPageModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: RegisterPageModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, RegisterPageRoutingModule, MatStepperModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatCardModule, MatIconModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatCheckboxModule, MatProgressSpinnerModule, MatToolbarModule, MatRadioModule, MatChipsModule, MatRippleModule, MatExpansionModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(RegisterPageModule, {\n    declarations: [RegisterPage],\n    imports: [CommonModule, FormsModule, ReactiveFormsModule, IonicModule, RegisterPageRoutingModule, MatStepperModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatCardModule, MatIconModule, MatSelectModule, MatDatepickerModule, MatNativeDateModule, MatCheckboxModule, MatProgressSpinnerModule, MatToolbarModule, MatRadioModule, MatChipsModule, MatRippleModule, MatExpansionModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "ReactiveFormsModule", "IonicModule", "MatStepperModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatCardModule", "MatIconModule", "MatSelectModule", "MatDatepickerModule", "MatNativeDateModule", "MatCheckboxModule", "MatProgressSpinnerModule", "MatToolbarModule", "MatRadioModule", "MatChipsModule", "MatRippleModule", "MatExpansionModule", "RegisterPageRoutingModule", "RegisterPage", "RegisterPageModule", "declarations", "imports"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { IonicModule } from '@ionic/angular';\n\n// Material Design Modules\nimport { MatStepperModule } from '@angular/material/stepper';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatExpansionModule } from '@angular/material/expansion';\n\nimport { RegisterPageRoutingModule } from './register-routing.module';\nimport { RegisterPage } from './register.page';\n\n@NgModule({\n  imports: [\n    CommonModule,\n    FormsModule,\n    ReactiveFormsModule,\n    IonicModule,\n    RegisterPageRoutingModule,\n    MatStepperModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatCardModule,\n    MatIconModule,\n    MatSelectModule,\n    MatDatepickerModule,\n    MatNativeDateModule,\n    MatCheckboxModule,\n    MatProgressSpinnerModule,\n    MatToolbarModule,\n    MatRadioModule,\n    MatChipsModule,\n    MatRippleModule,\n    MatExpansionModule\n  ],\n  declarations: [RegisterPage]\n})\nexport class RegisterPageModule {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACjE,SAASC,WAAW,QAAQ,gBAAgB;AAE5C;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAEhE,SAASC,yBAAyB,QAAQ,2BAA2B;AACrE,SAASC,YAAY,QAAQ,iBAAiB;;AA4B9C,OAAM,MAAOC,kBAAkB;;;uBAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA;IAAkB;EAAA;;;gBAxB3BtB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXiB,yBAAyB,EACzBhB,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,kBAAkB;IAAA;EAAA;;;2EAITG,kBAAkB;IAAAC,YAAA,GAFdF,YAAY;IAAAG,OAAA,GAtBzBxB,YAAY,EACZC,WAAW,EACXC,mBAAmB,EACnBC,WAAW,EACXiB,yBAAyB,EACzBhB,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,mBAAmB,EACnBC,mBAAmB,EACnBC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB,EAChBC,cAAc,EACdC,cAAc,EACdC,eAAe,EACfC,kBAAkB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}