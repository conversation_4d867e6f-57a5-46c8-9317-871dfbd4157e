{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/toolbar\";\nimport * as i13 from \"@angular/material/radio\";\nimport * as i14 from \"@angular/material/chips\";\nimport * as i15 from \"@angular/material/core\";\nimport * as i16 from \"@angular/material/expansion\";\nfunction RegisterPage_mat_icon_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"2\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 20);\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"4\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_44_mat_card_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 28);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_44_mat_card_12_Template_mat_card_click_0_listener() {\n      const type_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectComplaintType(type_r2));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-content\", 29)(2, \"div\", 30)(3, \"mat-icon\", 31);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 32)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 33);\n    i0.ɵɵelement(11, \"mat-radio-button\", 34);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const type_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r2.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_3_0.value) === type_r2.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ((tmp_4_0 = ctx_r2.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_4_0.value) === type_r2.value ? \"primary\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r2.icon, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(type_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(type_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", type_r2.value);\n  }\n}\nfunction RegisterPage_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"mat-card\", 23)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\", 24);\n    i0.ɵɵtext(5, \"report_problem\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Select Complaint Type \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"mat-card-subtitle\");\n    i0.ɵɵtext(8, \"Choose the category that best describes your issue\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 25)(11, \"div\", 26);\n    i0.ɵɵtemplate(12, RegisterPage_div_44_mat_card_12_Template, 12, 7, \"mat-card\", 27);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintTypeForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.complaintTypes);\n  }\n}\nfunction RegisterPage_div_45_mat_card_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 39)(1, \"mat-card-content\", 40)(2, \"div\", 41)(3, \"mat-icon\", 42);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 43)(6, \"h4\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-chip\", 44);\n    i0.ɵɵtext(11, \"Selected\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_3_0.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_4_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_4_0.description);\n  }\n}\nfunction RegisterPage_div_45_mat_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 45)(1, \"mat-card-content\", 46);\n    i0.ɵɵelement(2, \"mat-radio-button\", 47);\n    i0.ɵɵelementStart(3, \"div\", 48)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const desc_r5 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r2.complaintDescriptionForm.get(\"selectedDescription\")) == null ? null : tmp_3_0.value) === desc_r5.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", desc_r5.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(desc_r5.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(desc_r5.description);\n  }\n}\nfunction RegisterPage_div_45_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 35);\n    i0.ɵɵtemplate(2, RegisterPage_div_45_mat_card_2_Template, 12, 3, \"mat-card\", 36);\n    i0.ɵɵelementStart(3, \"mat-card\", 23)(4, \"mat-card-header\")(5, \"mat-card-title\")(6, \"mat-icon\", 24);\n    i0.ɵɵtext(7, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8, \" Select Specific Issue \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-card-subtitle\");\n    i0.ɵɵtext(10, \"Choose the description that best matches your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"form\", 25)(13, \"mat-radio-group\", 37);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_45_Template_mat_radio_group_change_13_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDescriptionChange($event));\n    });\n    i0.ɵɵtemplate(14, RegisterPage_div_45_mat_card_14_Template, 8, 5, \"mat-card\", 38);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getSelectedComplaintType());\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintDescriptionForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getComplaintDescriptions());\n  }\n}\nfunction RegisterPage_div_46_div_29_mat_card_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 64);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_46_div_29_mat_card_5_Template_mat_card_click_0_listener() {\n      const invoice_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectInvoice(invoice_r8));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-content\", 65)(2, \"div\", 66)(3, \"div\", 67);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵpipe(7, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 69)(9, \"div\", 70);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 71);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"mat-icon\", 72);\n    i0.ɵɵtext(14, \"chevron_right\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const invoice_r8 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(invoice_r8.invoiceNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(7, 5, invoice_r8.invoiceDate, \"MMM dd, yyyy\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(invoice_r8.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", invoice_r8.zone, \" \\u2022 \", invoice_r8.operatingUnit, \"\");\n  }\n}\nfunction RegisterPage_div_46_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 60)(2, \"span\", 61);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 62);\n    i0.ɵɵtemplate(5, RegisterPage_div_46_div_29_mat_card_5_Template, 15, 8, \"mat-card\", 63);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.invoiceSearchResults.length, \" invoices found\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.invoiceSearchResults);\n  }\n}\nfunction RegisterPage_div_46_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 73)(1, \"mat-icon\", 74);\n    i0.ɵɵtext(2, \"search_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No invoices found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 75);\n    i0.ɵɵtext(6, \"Try different keywords or clear search\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterPage_div_46_mat_card_31_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 89)(1, \"div\", 90)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-chip\", 91);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 92);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 93)(9, \"span\", 94);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 94);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 94);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 94);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r10 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r10.itemCode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r10.quantity, \" pcs\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r10.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", item_r10.thickness, \"mm\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", item_r10.width, \"\\u00D7\", item_r10.height, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r10.csqm, \" CSQM\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", item_r10.receivedBoxes, \" boxes\");\n  }\n}\nfunction RegisterPage_div_46_mat_card_31_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 76)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\", 77);\n    i0.ɵɵtext(4, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Invoice Selected \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"mat-expansion-panel\", 78)(10, \"mat-expansion-panel-header\")(11, \"mat-panel-title\")(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"visibility\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" View Complete Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"mat-panel-description\");\n    i0.ɵɵtext(16, \" Customer info, locations & items \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 79)(18, \"h4\")(19, \"mat-icon\");\n    i0.ɵɵtext(20, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(21, \" Customer Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 80)(23, \"div\", 81)(24, \"span\", 82);\n    i0.ɵɵtext(25, \"Invoice:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 83);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 81)(29, \"span\", 82);\n    i0.ɵɵtext(30, \"Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 83);\n    i0.ɵɵtext(32);\n    i0.ɵɵpipe(33, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 81)(35, \"span\", 82);\n    i0.ɵɵtext(36, \"Customer:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\", 83);\n    i0.ɵɵtext(38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 84)(40, \"span\", 82);\n    i0.ɵɵtext(41, \"Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"span\", 83);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"div\", 81)(45, \"span\", 82);\n    i0.ɵɵtext(46, \"Zone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\", 83);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(49, \"div\", 81)(50, \"span\", 82);\n    i0.ɵɵtext(51, \"Operating Unit:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"span\", 83);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 84)(55, \"span\", 82);\n    i0.ɵɵtext(56, \"Organization:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 83);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(59, \"div\", 84)(60, \"span\", 82);\n    i0.ɵɵtext(61, \"Bill To:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"span\", 83);\n    i0.ɵɵtext(63);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 84)(65, \"span\", 82);\n    i0.ɵɵtext(66, \"Ship To:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(67, \"span\", 83);\n    i0.ɵɵtext(68);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(69, \"div\", 79)(70, \"h4\")(71, \"mat-icon\");\n    i0.ɵɵtext(72, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(73, \" Item Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 85);\n    i0.ɵɵtemplate(75, RegisterPage_div_46_mat_card_31_div_75_Template, 17, 8, \"div\", 86);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(76, \"div\", 87)(77, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_46_mat_card_31_Template_button_click_77_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearInvoiceSelection());\n    });\n    i0.ɵɵelementStart(78, \"mat-icon\");\n    i0.ɵɵtext(79, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(80, \" Change Selection \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.selectedInvoice.invoiceNumber, \" \\u2022 \", ctx_r2.selectedInvoice.customerName, \"\");\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.invoiceNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 12, ctx_r2.selectedInvoice.invoiceDate, \"MMM dd, yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.zone);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.operatingUnit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.organization);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.billToLocation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.shipToLocation);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedInvoice.items);\n  }\n}\nfunction RegisterPage_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 35)(2, \"mat-card\", 49)(3, \"mat-card-content\", 50)(4, \"mat-chip-set\")(5, \"mat-chip\", 44)(6, \"mat-icon\", 51);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip\", 52)(10, \"mat-icon\", 51);\n    i0.ɵɵtext(11, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"mat-card\", 23)(14, \"mat-card-header\")(15, \"mat-card-title\")(16, \"mat-icon\", 24);\n    i0.ɵɵtext(17, \"receipt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18, \" Select Invoice \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-card-subtitle\");\n    i0.ɵɵtext(20, \"Search and select the invoice related to your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"mat-card-content\")(22, \"form\", 25)(23, \"mat-form-field\", 53)(24, \"mat-label\");\n    i0.ɵɵtext(25, \"Search Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"input\", 54);\n    i0.ɵɵlistener(\"input\", function RegisterPage_div_46_Template_input_input_26_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInvoiceSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"mat-icon\", 55);\n    i0.ɵɵtext(28, \"search\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(29, RegisterPage_div_46_div_29_Template, 6, 2, \"div\", 56)(30, RegisterPage_div_46_div_30_Template, 7, 0, \"div\", 57)(31, RegisterPage_div_46_mat_card_31_Template, 81, 15, \"mat-card\", 58);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate((tmp_1_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_1_0.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.label, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r2.getSelectedComplaintDescription()) == null ? null : tmp_3_0.label, \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.invoiceSearchForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n  }\n}\nfunction RegisterPage_div_47_mat_chip_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 117)(1, \"mat-icon\", 51);\n    i0.ɵɵtext(2, \"receipt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedInvoice.invoiceNumber, \" \");\n  }\n}\nfunction RegisterPage_div_47_mat_expansion_panel_22_mat_chip_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 91);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const item_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", item_r12.itemCode, \" (\", item_r12.quantity, \") \");\n  }\n}\nfunction RegisterPage_div_47_mat_expansion_panel_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 118)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"receipt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" Invoice Details \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 119)(9, \"div\", 120)(10, \"div\", 121)(11, \"span\", 82);\n    i0.ɵɵtext(12, \"Customer:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 83);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 121)(16, \"span\", 82);\n    i0.ɵɵtext(17, \"Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 83);\n    i0.ɵɵtext(19);\n    i0.ɵɵpipe(20, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 121)(22, \"span\", 82);\n    i0.ɵɵtext(23, \"Zone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 83);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"div\", 121)(27, \"span\", 82);\n    i0.ɵɵtext(28, \"Operating Unit:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"span\", 83);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 122)(32, \"span\", 82);\n    i0.ɵɵtext(33, \"Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\", 83);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 122)(37, \"span\", 82);\n    i0.ɵɵtext(38, \"Organization:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 83);\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 122)(42, \"span\", 82);\n    i0.ɵɵtext(43, \"Bill To:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"span\", 83);\n    i0.ɵɵtext(45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 122)(47, \"span\", 82);\n    i0.ɵɵtext(48, \"Ship To:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"span\", 83);\n    i0.ɵɵtext(50);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 123)(52, \"h5\")(53, \"mat-icon\");\n    i0.ɵɵtext(54, \"inventory\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"div\", 124)(57, \"mat-chip-set\");\n    i0.ɵɵtemplate(58, RegisterPage_div_47_mat_expansion_panel_22_mat_chip_58_Template, 2, 2, \"mat-chip\", 125);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.selectedInvoice.customerName, \" \\u2022 \", ctx_r2.selectedInvoice.items.length, \" items \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(20, 12, ctx_r2.selectedInvoice.invoiceDate, \"MMM dd, yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.zone);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.operatingUnit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.organization);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.billToLocation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.shipToLocation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Items (\", ctx_r2.selectedInvoice.items.length, \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedInvoice.items);\n  }\n}\nfunction RegisterPage_div_47_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactPersonName\"), \" \");\n  }\n}\nfunction RegisterPage_div_47_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactNumber\"), \" \");\n  }\n}\nfunction RegisterPage_div_47_div_57_div_9_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip\", 133);\n    i0.ɵɵlistener(\"removed\", function RegisterPage_div_47_div_57_div_9_mat_chip_2_Template_mat_chip_removed_0_listener() {\n      const i_r16 = i0.ɵɵrestoreView(_r15).index;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r16));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 51);\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"mat-icon\", 134);\n    i0.ɵɵtext(5, \"cancel\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r17 = ctx.$implicit;\n    i0.ɵɵproperty(\"removable\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", file_r17.name, \" \");\n  }\n}\nfunction RegisterPage_div_47_div_57_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 131)(1, \"mat-chip-set\");\n    i0.ɵɵtemplate(2, RegisterPage_div_47_div_57_div_9_mat_chip_2_Template, 6, 2, \"mat-chip\", 132);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedFiles);\n  }\n}\nfunction RegisterPage_div_47_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 126)(1, \"input\", 127, 0);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_47_div_57_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 128);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_47_div_57_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const fileInput_r14 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(fileInput_r14.click());\n    });\n    i0.ɵɵelementStart(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Choose Files \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 129);\n    i0.ɵɵtext(8, \"PDF, JPG, PNG, DOC (Max 5MB each)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, RegisterPage_div_47_div_57_div_9_Template, 3, 1, \"div\", 130);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFiles.length > 0);\n  }\n}\nfunction RegisterPage_div_47_mat_icon_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_47_mat_spinner_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 135);\n  }\n}\nfunction RegisterPage_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 95)(2, \"mat-card\", 96)(3, \"mat-card-header\")(4, \"mat-card-title\")(5, \"mat-icon\", 97);\n    i0.ɵɵtext(6, \"assignment_turned_in\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" Final Review \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"mat-card-subtitle\");\n    i0.ɵɵtext(9, \"Review your complaint details and submit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"div\", 98)(12, \"mat-chip-set\")(13, \"mat-chip\", 44)(14, \"mat-icon\", 51);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"mat-chip\", 52)(18, \"mat-icon\", 51);\n    i0.ɵɵtext(19, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, RegisterPage_div_47_mat_chip_21_Template, 4, 1, \"mat-chip\", 99);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(22, RegisterPage_div_47_mat_expansion_panel_22_Template, 59, 15, \"mat-expansion-panel\", 100);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"mat-card\", 101)(24, \"mat-card-header\")(25, \"mat-card-title\")(26, \"mat-icon\", 24);\n    i0.ɵɵtext(27, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Contact Information \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"mat-card-subtitle\");\n    i0.ɵɵtext(30, \"Provide your contact details for follow-up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"mat-card-content\")(32, \"form\", 102)(33, \"div\", 103)(34, \"mat-form-field\", 104)(35, \"mat-label\");\n    i0.ɵɵtext(36, \"Contact Person *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"input\", 105);\n    i0.ɵɵelementStart(38, \"mat-icon\", 55);\n    i0.ɵɵtext(39, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, RegisterPage_div_47_mat_error_40_Template, 2, 1, \"mat-error\", 106);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-form-field\", 104)(42, \"mat-label\");\n    i0.ɵɵtext(43, \"Contact Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"input\", 107);\n    i0.ɵɵelementStart(45, \"mat-icon\", 55);\n    i0.ɵɵtext(46, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, RegisterPage_div_47_mat_error_47_Template, 2, 1, \"mat-error\", 106);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"mat-form-field\", 108)(49, \"mat-label\");\n    i0.ɵɵtext(50, \"Additional Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"textarea\", 109);\n    i0.ɵɵelementStart(52, \"mat-icon\", 55);\n    i0.ɵɵtext(53, \"comment\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 110)(55, \"mat-checkbox\", 111);\n    i0.ɵɵtext(56, \" Attach supporting documents \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(57, RegisterPage_div_47_div_57_Template, 10, 1, \"div\", 112);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"mat-card-actions\", 113)(59, \"button\", 114);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_47_Template_button_click_59_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBackToStep3());\n    });\n    i0.ɵɵelementStart(60, \"mat-icon\");\n    i0.ɵɵtext(61, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(62, \" Back \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(63, \"div\", 4);\n    i0.ɵɵelementStart(64, \"button\", 115);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_47_Template_button_click_64_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmitComplaint());\n    });\n    i0.ɵɵtemplate(65, RegisterPage_div_47_mat_icon_65_Template, 2, 0, \"mat-icon\", 106)(66, RegisterPage_div_47_mat_spinner_66_Template, 1, 0, \"mat-spinner\", 116);\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(15);\n    i0.ɵɵtextInterpolate((tmp_1_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_1_0.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.label, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r2.getSelectedComplaintDescription()) == null ? null : tmp_3_0.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintDetailsForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_8_0.invalid) && ((tmp_8_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_8_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx_r2.complaintDetailsForm.get(\"hasComplaintLetters\")) == null ? null : tmp_9_0.value);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading || !ctx_r2.complaintDetailsForm.valid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isLoading ? \"Submitting...\" : \"Submit Complaint\", \" \");\n  }\n}\nfunction RegisterPage_div_48_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 136)(1, \"mat-card\", 137)(2, \"mat-card-content\", 138)(3, \"mat-icon\", 77);\n    i0.ɵɵtext(4, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Complaint Submitted Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Your complaint has been registered and will be processed shortly.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 139);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_48_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r18);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBack());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"home\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Back to Home \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class RegisterPage {\n  constructor(formBuilder, router, loadingController, toastController) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.isLoading = false;\n    this.selectedFiles = [];\n    this.selectedInvoice = null;\n    this.invoiceSearchResults = [];\n    this.showInvoiceResults = false;\n    this.currentStep = 1;\n    this.complaintTypes = [{\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    }, {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    }, {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    }, {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    }, {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    }, {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }];\n    this.complaintDescriptions = {\n      'glass_quality': [{\n        value: 'scratches',\n        label: 'Scratches on Glass Surface',\n        description: 'Visible scratches or marks on the glass surface'\n      }, {\n        value: 'cracks',\n        label: 'Cracks or Chips',\n        description: 'Cracks, chips, or fractures in the glass'\n      }, {\n        value: 'bubbles',\n        label: 'Air Bubbles',\n        description: 'Air bubbles or inclusions within the glass'\n      }, {\n        value: 'discoloration',\n        label: 'Discoloration',\n        description: 'Color variations or discoloration in the glass'\n      }, {\n        value: 'thickness',\n        label: 'Thickness Issues',\n        description: 'Incorrect thickness or uneven glass thickness'\n      }],\n      'installation': [{\n        value: 'alignment',\n        label: 'Alignment Problems',\n        description: 'Glass not properly aligned during installation'\n      }, {\n        value: 'sealing',\n        label: 'Sealing Issues',\n        description: 'Poor sealing or gaps around the glass'\n      }, {\n        value: 'hardware',\n        label: 'Hardware Problems',\n        description: 'Issues with hinges, handles, or other hardware'\n      }, {\n        value: 'fitting',\n        label: 'Poor Fitting',\n        description: 'Glass does not fit properly in the frame'\n      }, {\n        value: 'damage_during',\n        label: 'Damage During Installation',\n        description: 'Glass damaged during the installation process'\n      }],\n      'delivery_damage': [{\n        value: 'broken_transit',\n        label: 'Broken in Transit',\n        description: 'Glass broken during transportation'\n      }, {\n        value: 'packaging',\n        label: 'Poor Packaging',\n        description: 'Inadequate packaging causing damage'\n      }, {\n        value: 'handling',\n        label: 'Rough Handling',\n        description: 'Damage due to rough handling during delivery'\n      }, {\n        value: 'delayed',\n        label: 'Delayed Delivery',\n        description: 'Delivery was significantly delayed'\n      }, {\n        value: 'wrong_item',\n        label: 'Wrong Item Delivered',\n        description: 'Incorrect glass type or specifications delivered'\n      }],\n      'measurement': [{\n        value: 'wrong_size',\n        label: 'Wrong Size',\n        description: 'Glass delivered in incorrect dimensions'\n      }, {\n        value: 'measurement_error',\n        label: 'Measurement Error',\n        description: 'Error in initial measurements taken'\n      }, {\n        value: 'specification',\n        label: 'Specification Mismatch',\n        description: 'Glass does not match ordered specifications'\n      }, {\n        value: 'template',\n        label: 'Template Issues',\n        description: 'Problems with measurement template or pattern'\n      }],\n      'service': [{\n        value: 'communication',\n        label: 'Poor Communication',\n        description: 'Lack of proper communication from service team'\n      }, {\n        value: 'response_time',\n        label: 'Slow Response Time',\n        description: 'Delayed response to queries or complaints'\n      }, {\n        value: 'unprofessional',\n        label: 'Unprofessional Behavior',\n        description: 'Unprofessional conduct by service personnel'\n      }, {\n        value: 'incomplete_work',\n        label: 'Incomplete Work',\n        description: 'Service work left incomplete or unfinished'\n      }],\n      'billing': [{\n        value: 'wrong_amount',\n        label: 'Incorrect Amount',\n        description: 'Wrong amount charged in the invoice'\n      }, {\n        value: 'missing_details',\n        label: 'Missing Details',\n        description: 'Important details missing from invoice'\n      }, {\n        value: 'duplicate',\n        label: 'Duplicate Billing',\n        description: 'Charged multiple times for the same service'\n      }, {\n        value: 'tax_error',\n        label: 'Tax Calculation Error',\n        description: 'Incorrect tax calculation or application'\n      }]\n    };\n    // Sample invoice data for demonstration\n    this.sampleInvoices = [{\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP',\n      items: [{\n        itemCode: 'FGDGAGA100.36602440LN',\n        description: 'DARK GREY 100-3660x2440 DARK GREY 100',\n        thickness: 10,\n        width: 2440,\n        height: 3660,\n        quantity: 176,\n        csqm: 392.9376,\n        receivedBoxes: 4\n      }, {\n        itemCode: 'FGDGAGA120.36602440LN',\n        description: 'DARK GREY 120-3660x2440 DARK GREY 120',\n        thickness: 12,\n        width: 2440,\n        height: 3660,\n        quantity: 212,\n        csqm: 160.7472,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab',\n      items: [{\n        itemCode: 'FGCGAGA120.36602770LN',\n        description: 'CLEAR GREY 120-3660x2770 CLEAR GREY 120',\n        thickness: 12,\n        width: 2770,\n        height: 3660,\n        quantity: 150,\n        csqm: 278.5420,\n        receivedBoxes: 3\n      }, {\n        itemCode: 'FGTGAGA080.24401830LN',\n        description: 'TINTED GREY 080-2440x1830 TINTED GREY 080',\n        thickness: 8,\n        width: 2440,\n        height: 1830,\n        quantity: 95,\n        csqm: 124.3680,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore',\n      items: [{\n        itemCode: 'FGBGAGA060.18302440LN',\n        description: 'BLUE GREY 060-1830x2440 BLUE GREY 060',\n        thickness: 6,\n        width: 1830,\n        height: 2440,\n        quantity: 88,\n        csqm: 195.2640,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai',\n      items: [{\n        itemCode: 'FGGGAGA100.24403660LN',\n        description: 'GREEN GREY 100-2440x3660 GREEN GREY 100',\n        thickness: 10,\n        width: 2440,\n        height: 3660,\n        quantity: 120,\n        csqm: 267.8880,\n        receivedBoxes: 3\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata',\n      items: [{\n        itemCode: 'FGRGAGA080.18302440LN',\n        description: 'RED GREY 080-1830x2440 RED GREY 080',\n        thickness: 8,\n        width: 1830,\n        height: 2440,\n        quantity: 75,\n        csqm: 133.4400,\n        receivedBoxes: 2\n      }, {\n        itemCode: 'FGWGAGA120.36602440LN',\n        description: 'WHITE GREY 120-3660x2440 WHITE GREY 120',\n        thickness: 12,\n        width: 3660,\n        height: 2440,\n        quantity: 95,\n        csqm: 218.5680,\n        receivedBoxes: 1\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad',\n      items: [{\n        itemCode: 'FGYGAGA060.24401830LN',\n        description: 'YELLOW GREY 060-2440x1830 YELLOW GREY 060',\n        thickness: 6,\n        width: 2440,\n        height: 1830,\n        quantity: 65,\n        csqm: 145.2720,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat',\n      items: [{\n        itemCode: 'FGPGAGA100.36602770LN',\n        description: 'PURPLE GREY 100-3660x2770 PURPLE GREY 100',\n        thickness: 10,\n        width: 3660,\n        height: 2770,\n        quantity: 180,\n        csqm: 364.4520,\n        receivedBoxes: 4\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi',\n      items: [{\n        itemCode: 'FGOGAGA080.24402440LN',\n        description: 'ORANGE GREY 080-2440x2440 ORANGE GREY 080',\n        thickness: 8,\n        width: 2440,\n        height: 2440,\n        quantity: 110,\n        csqm: 195.3760,\n        receivedBoxes: 3\n      }, {\n        itemCode: 'FGSGAGA120.18303660LN',\n        description: 'SILVER GREY 120-1830x3660 SILVER GREY 120',\n        thickness: 12,\n        width: 1830,\n        height: 3660,\n        quantity: 85,\n        csqm: 142.8180,\n        receivedBoxes: 2\n      }]\n    }];\n    this.createForms();\n  }\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n  selectComplaintType(type) {\n    this.complaintTypeForm.patchValue({\n      selectedType: type.value\n    });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n  onDescriptionChange(event) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice => invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase()));\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n  selectInvoice(invoice) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: invoice.invoiceNumber\n    });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: ''\n    });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n  onSubmitComplaint() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.complaintTypeForm.valid && _this.complaintDescriptionForm.valid && _this.selectedInvoice && _this.complaintDetailsForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Registering complaint...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate registration process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          const toast = yield _this.toastController.create({\n            message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n            duration: 4000,\n            color: 'success',\n            position: 'top'\n          });\n          yield toast.present();\n          // Navigate to track page\n          _this.router.navigate(['/track']);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please complete all required steps and fill in all required fields.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({\n        attachedFile: files[0]\n      });\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({\n        attachedFile: null\n      });\n    }\n  }\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n  isStepCompleted(step) {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n  getErrorMessage(form, field) {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function RegisterPage_Factory(t) {\n      return new (t || RegisterPage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.LoadingController), i0.ɵɵdirectiveInject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterPage,\n      selectors: [[\"app-register\"]],\n      decls: 49,\n      vars: 35,\n      consts: [[\"fileInput\", \"\"], [\"color\", \"primary\", 1, \"modern-toolbar\"], [\"mat-icon-button\", \"\", 1, \"back-button\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [1, \"help-icon\"], [1, \"modern-content\"], [1, \"modern-header\"], [1, \"page-title\"], [1, \"page-subtitle\"], [1, \"modern-stepper\"], [1, \"stepper-container\"], [1, \"step-item\"], [1, \"step-circle\"], [\"class\", \"check-icon\", 4, \"ngIf\"], [\"class\", \"step-number\", 4, \"ngIf\"], [1, \"step-label\"], [1, \"step-line\"], [\"class\", \"modern-step-content\", 4, \"ngIf\"], [\"class\", \"success-content\", 4, \"ngIf\"], [1, \"check-icon\"], [1, \"step-number\"], [1, \"modern-step-content\"], [1, \"step-card\"], [1, \"step-icon\"], [3, \"formGroup\"], [1, \"modern-options-grid\"], [\"class\", \"option-card\", \"matRipple\", \"\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"matRipple\", \"\", 1, \"option-card\", 3, \"click\"], [1, \"option-content\"], [1, \"option-icon\"], [3, \"color\"], [1, \"option-text\"], [1, \"option-radio\"], [\"formControlName\", \"selectedType\", \"color\", \"primary\", 3, \"value\"], [1, \"step-layout\"], [\"class\", \"summary-card\", 4, \"ngIf\"], [\"formControlName\", \"selectedDescription\", 1, \"modern-radio-group\", 3, \"change\"], [\"class\", \"radio-option-card\", \"matRipple\", \"\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [1, \"summary-card\"], [1, \"summary-content\"], [1, \"summary-icon\"], [\"color\", \"primary\"], [1, \"summary-text\"], [\"color\", \"primary\", \"selected\", \"\"], [\"matRipple\", \"\", 1, \"radio-option-card\"], [1, \"radio-option-content\"], [\"color\", \"primary\", 1, \"radio-button\", 3, \"value\"], [1, \"radio-text\"], [1, \"compact-summary\"], [1, \"summary-chips\"], [\"matChipAvatar\", \"\"], [\"color\", \"accent\", \"selected\", \"\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"formControlName\", \"searchTerm\", \"placeholder\", \"Invoice number, customer name, or leave empty for all\", 3, \"input\"], [\"matSuffix\", \"\"], [\"class\", \"invoice-results\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"selected-invoice-card\", 4, \"ngIf\"], [1, \"invoice-results\"], [1, \"results-header\"], [1, \"results-count\"], [1, \"modern-invoice-list\"], [\"class\", \"invoice-card\", \"matRipple\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"matRipple\", \"\", 1, \"invoice-card\", 3, \"click\"], [1, \"invoice-content\"], [1, \"invoice-main\"], [1, \"invoice-number\"], [1, \"invoice-date\"], [1, \"invoice-details\"], [1, \"customer-name\"], [1, \"location-info\"], [1, \"select-icon\"], [1, \"no-results\"], [1, \"no-results-icon\"], [1, \"search-hint\"], [1, \"selected-invoice-card\"], [\"color\", \"primary\", 1, \"success-icon\"], [1, \"invoice-expansion\"], [1, \"details-section\"], [1, \"details-grid\"], [1, \"detail-row\"], [1, \"label\"], [1, \"value\"], [1, \"detail-row\", \"full-width\"], [1, \"items-list\"], [\"class\", \"item-summary\", 4, \"ngFor\", \"ngForOf\"], [1, \"invoice-actions\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"item-summary\"], [1, \"item-header\"], [1, \"item-chip\"], [1, \"item-desc\"], [1, \"item-specs\"], [1, \"spec\"], [1, \"final-layout\"], [1, \"final-summary-card\"], [\"color\", \"primary\", 1, \"step-icon\"], [1, \"final-summary-chips\"], [\"color\", \"warn\", \"selected\", \"\", 4, \"ngIf\"], [\"class\", \"invoice-details-expansion\", 4, \"ngIf\"], [1, \"contact-form-card\"], [1, \"contact-form\", 3, \"formGroup\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"form-field\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonName\", \"placeholder\", \"Your name\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"contactNumber\", \"placeholder\", \"10-digit number\", \"type\", \"tel\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"comments\", \"rows\", \"2\", \"placeholder\", \"Optional additional information\"], [1, \"upload-section\"], [\"formControlName\", \"hasComplaintLetters\", \"color\", \"primary\"], [\"class\", \"upload-area\", 4, \"ngIf\"], [1, \"card-actions\"], [\"mat-button\", \"\", 1, \"back-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"submit-btn\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"color\", \"warn\", \"selected\", \"\"], [1, \"invoice-details-expansion\"], [1, \"compact-invoice-details\"], [1, \"info-grid\"], [1, \"info-item\"], [1, \"info-item\", \"full-width\"], [1, \"compact-items\"], [1, \"items-summary\"], [\"class\", \"item-chip\", 4, \"ngFor\", \"ngForOf\"], [1, \"upload-area\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \".pdf,.jpg,.jpeg,.png,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"upload-btn\", 3, \"click\"], [1, \"upload-hint\"], [\"class\", \"file-chips\", 4, \"ngIf\"], [1, \"file-chips\"], [3, \"removable\", \"removed\", 4, \"ngFor\", \"ngForOf\"], [3, \"removed\", \"removable\"], [\"matChipRemove\", \"\"], [\"diameter\", \"20\"], [1, \"success-content\"], [1, \"success-card\"], [1, \"success-content-inner\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function RegisterPage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 1)(1, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_button_click_1_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"span\", 3);\n          i0.ɵɵtext(5, \"Register Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(6, \"span\", 4);\n          i0.ɵɵelementStart(7, \"mat-icon\", 5);\n          i0.ɵɵtext(8, \"help_outline\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"div\", 7)(11, \"h1\", 8);\n          i0.ɵɵtext(12, \"Register New Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\", 9);\n          i0.ɵɵtext(14, \"Complete your complaint in 4 simple steps\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13);\n          i0.ɵɵtemplate(19, RegisterPage_mat_icon_19_Template, 2, 0, \"mat-icon\", 14)(20, RegisterPage_span_20_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 16);\n          i0.ɵɵtext(22, \"Type\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(23, \"div\", 17);\n          i0.ɵɵelementStart(24, \"div\", 12)(25, \"div\", 13);\n          i0.ɵɵtemplate(26, RegisterPage_mat_icon_26_Template, 2, 0, \"mat-icon\", 14)(27, RegisterPage_span_27_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 16);\n          i0.ɵɵtext(29, \"Description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(30, \"div\", 17);\n          i0.ɵɵelementStart(31, \"div\", 12)(32, \"div\", 13);\n          i0.ɵɵtemplate(33, RegisterPage_mat_icon_33_Template, 2, 0, \"mat-icon\", 14)(34, RegisterPage_span_34_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 16);\n          i0.ɵɵtext(36, \"Invoice\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(37, \"div\", 17);\n          i0.ɵɵelementStart(38, \"div\", 12)(39, \"div\", 13);\n          i0.ɵɵtemplate(40, RegisterPage_mat_icon_40_Template, 2, 0, \"mat-icon\", 14)(41, RegisterPage_span_41_Template, 2, 0, \"span\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 16);\n          i0.ɵɵtext(43, \"Submit\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(44, RegisterPage_div_44_Template, 13, 2, \"div\", 18)(45, RegisterPage_div_45_Template, 15, 3, \"div\", 18)(46, RegisterPage_div_46_Template, 32, 7, \"div\", 18)(47, RegisterPage_div_47_Template, 68, 13, \"div\", 18)(48, RegisterPage_div_48_Template, 13, 0, \"div\", 19);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(17);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(1))(\"active\", ctx.currentStep === 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(1));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(2))(\"active\", ctx.currentStep === 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(2));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(3))(\"active\", ctx.currentStep === 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(3));\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(4));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(4))(\"active\", ctx.currentStep === 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(4));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(4));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(1) && !ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(2) && !ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(3) && !ctx.isStepCompleted(4));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(4));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatSuffix, i6.MatInput, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardActions, i8.MatCardContent, i8.MatCardHeader, i8.MatCardSubtitle, i8.MatCardTitle, i9.MatIcon, i10.MatCheckbox, i11.MatProgressSpinner, i12.MatToolbar, i13.MatRadioGroup, i13.MatRadioButton, i14.MatChip, i14.MatChipAvatar, i14.MatChipRemove, i14.MatChipSet, i15.MatRipple, i16.MatExpansionPanel, i16.MatExpansionPanelHeader, i16.MatExpansionPanelTitle, i16.MatExpansionPanelDescription, i4.DatePipe],\n      styles: [\".register-content[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n}\\n.register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  color: #999;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.active[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: #e0e0e0;\\n  margin: 0 16px;\\n  margin-bottom: 32px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 32px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0 0 32px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 12px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card.selected[_ngcontent-%COMP%] {\\n  border-color: #1976d2;\\n  background: #e3f2fd;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]   ion-radio[_ngcontent-%COMP%] {\\n  --color: #1976d2;\\n  --color-checked: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: white;\\n  transition: all 0.3s ease;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]:hover {\\n  border-color: #ff9800;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option.mat-radio-checked[_ngcontent-%COMP%] {\\n  border-color: #ff9800;\\n  background: #fff3e0;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%] {\\n  margin-left: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #f3e5f5;\\n  border: 2px solid #9c27b0;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 22px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   .final-review-badge[_ngcontent-%COMP%] {\\n  background: #1565c0;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-left: auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 2px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 20px;\\n  margin-top: 16px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%] {\\n  margin: 16px 0 12px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #c8e6c9;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2e7d32;\\n  font-weight: 600;\\n  font-size: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 11px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 4px;\\n  padding: 8px;\\n  color: #1b5e20;\\n  font-size: 13px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-item-header[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-weight: 600;\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  padding-bottom: 4px;\\n  border-bottom: 1px solid #e8f5e8;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-item-description[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-weight: 500;\\n  font-size: 13px;\\n  margin-bottom: 12px;\\n  background: #f1f8e9;\\n  padding: 6px;\\n  border-radius: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 3px;\\n  padding: 4px 6px;\\n  color: #1b5e20;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding-top: 12px;\\n  border-top: 1px solid #c8e6c9;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 11px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n  display: block;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%]   .defect-value[_ngcontent-%COMP%] {\\n  background: #ffebee;\\n  border: 1px solid #f8bbd9;\\n  border-radius: 4px;\\n  padding: 6px 8px;\\n  color: #c62828;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: inline-block;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"CONTACT DETAILS\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   .invoice-date[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-customer[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-zone[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p.search-hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  font-style: italic;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin: 20px 0 16px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #666;\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  padding: 12px 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1976d2;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-description[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 16px;\\n  padding: 8px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #666;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%]   .spec-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 0.9rem;\\n  padding: 6px 8px;\\n  background-color: #e3f2fd;\\n  border-radius: 4px;\\n  text-align: center;\\n  border: 1px solid #bbdefb;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .defect-selection[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .defect-selection[_ngcontent-%COMP%]   .defect-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e0e0e0;\\n  padding-top: 16px;\\n  text-align: right;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-size: 14px;\\n  font-weight: 500;\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"EDITABLE\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  padding: 16px;\\n  background: #f3e5f5;\\n  border: 1px solid #9c27b0;\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   .checkbox-hint[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 32px;\\n  color: #666;\\n  font-size: 13px;\\n  font-style: italic;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%] {\\n  margin: 24px 0 0 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border: 2px dashed #9c27b0;\\n  border-radius: 8px;\\n  background: #fce4ec;\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  border-color: #9c27b0;\\n  color: #7b1fa2;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(156, 39, 176, 0.04);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n  margin: 8px 0 0 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: white;\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  border: 1px solid #e1bee7;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  border-color: #9c27b0;\\n  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-size[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: space-between;\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  border-color: #ccc;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #999;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);\\n  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);\\n  transform: translateY(-1px);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  color: #999;\\n  box-shadow: none;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n    padding: 24px 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    margin: 0 8px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "RegisterPage_div_44_mat_card_12_Template_mat_card_click_0_listener", "type_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectComplaintType", "ɵɵelement", "ɵɵclassProp", "tmp_3_0", "complaintTypeForm", "get", "value", "ɵɵadvance", "ɵɵproperty", "tmp_4_0", "ɵɵtextInterpolate1", "icon", "ɵɵtextInterpolate", "label", "description", "ɵɵtemplate", "RegisterPage_div_44_mat_card_12_Template", "complaintTypes", "tmp_2_0", "getSelectedComplaintType", "complaintDescriptionForm", "desc_r5", "RegisterPage_div_45_mat_card_2_Template", "RegisterPage_div_45_Template_mat_radio_group_change_13_listener", "$event", "_r4", "onDescriptionChange", "RegisterPage_div_45_mat_card_14_Template", "getComplaintDescriptions", "RegisterPage_div_46_div_29_mat_card_5_Template_mat_card_click_0_listener", "invoice_r8", "_r7", "selectInvoice", "invoiceNumber", "ɵɵpipeBind2", "invoiceDate", "customerName", "ɵɵtextInterpolate2", "zone", "operatingUnit", "RegisterPage_div_46_div_29_mat_card_5_Template", "invoiceSearchResults", "length", "item_r10", "itemCode", "quantity", "thickness", "width", "height", "csqm", "receivedBoxes", "RegisterPage_div_46_mat_card_31_div_75_Template", "RegisterPage_div_46_mat_card_31_Template_button_click_77_listener", "_r9", "clearInvoiceSelection", "selectedInvoice", "customerAddress", "organization", "billToLocation", "shipToLocation", "items", "RegisterPage_div_46_Template_input_input_26_listener", "_r6", "onInvoiceSearch", "RegisterPage_div_46_div_29_Template", "RegisterPage_div_46_div_30_Template", "RegisterPage_div_46_mat_card_31_Template", "tmp_1_0", "getSelectedComplaintDescription", "invoiceSearchForm", "showInvoiceResults", "item_r12", "RegisterPage_div_47_mat_expansion_panel_22_mat_chip_58_Template", "getErrorMessage", "complaintDetailsForm", "RegisterPage_div_47_div_57_div_9_mat_chip_2_Template_mat_chip_removed_0_listener", "i_r16", "_r15", "index", "removeFile", "file_r17", "name", "RegisterPage_div_47_div_57_div_9_mat_chip_2_Template", "selectedFiles", "RegisterPage_div_47_div_57_Template_input_change_1_listener", "_r13", "onFileSelected", "RegisterPage_div_47_div_57_Template_button_click_3_listener", "fileInput_r14", "ɵɵreference", "click", "RegisterPage_div_47_div_57_div_9_Template", "RegisterPage_div_47_mat_chip_21_Template", "RegisterPage_div_47_mat_expansion_panel_22_Template", "RegisterPage_div_47_mat_error_40_Template", "RegisterPage_div_47_mat_error_47_Template", "RegisterPage_div_47_div_57_Template", "RegisterPage_div_47_Template_button_click_59_listener", "_r11", "goBackToStep3", "RegisterPage_div_47_Template_button_click_64_listener", "onSubmitComplaint", "RegisterPage_div_47_mat_icon_65_Template", "RegisterPage_div_47_mat_spinner_66_Template", "tmp_7_0", "invalid", "touched", "tmp_8_0", "tmp_9_0", "isLoading", "valid", "RegisterPage_div_48_Template_button_click_9_listener", "_r18", "goBack", "RegisterPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "currentStep", "complaintDescriptions", "sampleInvoices", "Date", "createForms", "ngOnInit", "showAllInvoices", "group", "selectedType", "required", "selectedDescription", "searchTerm", "contactPersonName", "<PERSON><PERSON><PERSON><PERSON>", "contactNumber", "pattern", "comments", "hasComplaintLetters", "attachedFile", "type", "patchValue", "setTimeout", "goToStep2", "selected<PERSON><PERSON><PERSON>", "descriptions", "find", "desc", "event", "goToStep3", "goToStep4", "goBackToStep1", "goBackToStep2", "trim", "filter", "invoice", "toLowerCase", "includes", "_this", "_asyncToGenerator", "loading", "create", "message", "duration", "present", "dismiss", "toast", "now", "color", "position", "navigate", "files", "target", "Array", "from", "splice", "isStepCompleted", "step", "form", "field", "control", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "LoadingController", "ToastController", "selectors", "decls", "vars", "consts", "template", "RegisterPage_Template", "rf", "ctx", "RegisterPage_Template_button_click_1_listener", "RegisterPage_mat_icon_19_Template", "RegisterPage_span_20_Template", "RegisterPage_mat_icon_26_Template", "RegisterPage_span_27_Template", "RegisterPage_mat_icon_33_Template", "RegisterPage_span_34_Template", "RegisterPage_mat_icon_40_Template", "RegisterPage_span_41_Template", "RegisterPage_div_44_Template", "RegisterPage_div_45_Template", "RegisterPage_div_46_Template", "RegisterPage_div_47_Template", "RegisterPage_div_48_Template"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\n\nexport interface ItemInfo {\n  itemCode: string;\n  description: string;\n  thickness: number;\n  width: number;\n  height: number;\n  quantity: number;\n  csqm: number;\n  receivedBoxes: number;\n  defectedBarCode?: string;\n}\n\nexport interface InvoiceData {\n  invoiceNumber: string;\n  invoiceDate: Date;\n  customerName: string;\n  customerAddress: string;\n  zone: string;\n  operatingUnit: string;\n  organization: string;\n  billToLocation: string;\n  shipToLocation: string;\n  items: ItemInfo[];\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss'],\n})\nexport class RegisterPage implements OnInit {\n\n  complaintTypeForm!: FormGroup;\n  complaintDescriptionForm!: FormGroup;\n  invoiceSearchForm!: FormGroup;\n  complaintDetailsForm!: FormGroup;\n  isLoading = false;\n  selectedFiles: File[] = [];\n  selectedInvoice: InvoiceData | null = null;\n  invoiceSearchResults: InvoiceData[] = [];\n  showInvoiceResults = false;\n  currentStep = 1;\n\n  complaintTypes = [\n    {\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    },\n    {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    },\n    {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    },\n    {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    },\n    {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    },\n    {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }\n  ];\n\n  complaintDescriptions: { [key: string]: any[] } = {\n    'glass_quality': [\n      { value: 'scratches', label: 'Scratches on Glass Surface', description: 'Visible scratches or marks on the glass surface' },\n      { value: 'cracks', label: 'Cracks or Chips', description: 'Cracks, chips, or fractures in the glass' },\n      { value: 'bubbles', label: 'Air Bubbles', description: 'Air bubbles or inclusions within the glass' },\n      { value: 'discoloration', label: 'Discoloration', description: 'Color variations or discoloration in the glass' },\n      { value: 'thickness', label: 'Thickness Issues', description: 'Incorrect thickness or uneven glass thickness' }\n    ],\n    'installation': [\n      { value: 'alignment', label: 'Alignment Problems', description: 'Glass not properly aligned during installation' },\n      { value: 'sealing', label: 'Sealing Issues', description: 'Poor sealing or gaps around the glass' },\n      { value: 'hardware', label: 'Hardware Problems', description: 'Issues with hinges, handles, or other hardware' },\n      { value: 'fitting', label: 'Poor Fitting', description: 'Glass does not fit properly in the frame' },\n      { value: 'damage_during', label: 'Damage During Installation', description: 'Glass damaged during the installation process' }\n    ],\n    'delivery_damage': [\n      { value: 'broken_transit', label: 'Broken in Transit', description: 'Glass broken during transportation' },\n      { value: 'packaging', label: 'Poor Packaging', description: 'Inadequate packaging causing damage' },\n      { value: 'handling', label: 'Rough Handling', description: 'Damage due to rough handling during delivery' },\n      { value: 'delayed', label: 'Delayed Delivery', description: 'Delivery was significantly delayed' },\n      { value: 'wrong_item', label: 'Wrong Item Delivered', description: 'Incorrect glass type or specifications delivered' }\n    ],\n    'measurement': [\n      { value: 'wrong_size', label: 'Wrong Size', description: 'Glass delivered in incorrect dimensions' },\n      { value: 'measurement_error', label: 'Measurement Error', description: 'Error in initial measurements taken' },\n      { value: 'specification', label: 'Specification Mismatch', description: 'Glass does not match ordered specifications' },\n      { value: 'template', label: 'Template Issues', description: 'Problems with measurement template or pattern' }\n    ],\n    'service': [\n      { value: 'communication', label: 'Poor Communication', description: 'Lack of proper communication from service team' },\n      { value: 'response_time', label: 'Slow Response Time', description: 'Delayed response to queries or complaints' },\n      { value: 'unprofessional', label: 'Unprofessional Behavior', description: 'Unprofessional conduct by service personnel' },\n      { value: 'incomplete_work', label: 'Incomplete Work', description: 'Service work left incomplete or unfinished' }\n    ],\n    'billing': [\n      { value: 'wrong_amount', label: 'Incorrect Amount', description: 'Wrong amount charged in the invoice' },\n      { value: 'missing_details', label: 'Missing Details', description: 'Important details missing from invoice' },\n      { value: 'duplicate', label: 'Duplicate Billing', description: 'Charged multiple times for the same service' },\n      { value: 'tax_error', label: 'Tax Calculation Error', description: 'Incorrect tax calculation or application' }\n    ]\n  };\n\n  // Sample invoice data for demonstration\n  sampleInvoices: InvoiceData[] = [\n    {\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP',\n      items: [\n        {\n          itemCode: 'FGDGAGA100.36602440LN',\n          description: 'DARK GREY 100-3660x2440 DARK GREY 100',\n          thickness: 10,\n          width: 2440,\n          height: 3660,\n          quantity: 176,\n          csqm: 392.9376,\n          receivedBoxes: 4\n        },\n        {\n          itemCode: 'FGDGAGA120.36602440LN',\n          description: 'DARK GREY 120-3660x2440 DARK GREY 120',\n          thickness: 12,\n          width: 2440,\n          height: 3660,\n          quantity: 212,\n          csqm: 160.7472,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab',\n      items: [\n        {\n          itemCode: 'FGCGAGA120.36602770LN',\n          description: 'CLEAR GREY 120-3660x2770 CLEAR GREY 120',\n          thickness: 12,\n          width: 2770,\n          height: 3660,\n          quantity: 150,\n          csqm: 278.5420,\n          receivedBoxes: 3\n        },\n        {\n          itemCode: 'FGTGAGA080.24401830LN',\n          description: 'TINTED GREY 080-2440x1830 TINTED GREY 080',\n          thickness: 8,\n          width: 2440,\n          height: 1830,\n          quantity: 95,\n          csqm: 124.3680,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore',\n      items: [\n        {\n          itemCode: 'FGBGAGA060.18302440LN',\n          description: 'BLUE GREY 060-1830x2440 BLUE GREY 060',\n          thickness: 6,\n          width: 1830,\n          height: 2440,\n          quantity: 88,\n          csqm: 195.2640,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai',\n      items: [\n        {\n          itemCode: 'FGGGAGA100.24403660LN',\n          description: 'GREEN GREY 100-2440x3660 GREEN GREY 100',\n          thickness: 10,\n          width: 2440,\n          height: 3660,\n          quantity: 120,\n          csqm: 267.8880,\n          receivedBoxes: 3\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata',\n      items: [\n        {\n          itemCode: 'FGRGAGA080.18302440LN',\n          description: 'RED GREY 080-1830x2440 RED GREY 080',\n          thickness: 8,\n          width: 1830,\n          height: 2440,\n          quantity: 75,\n          csqm: 133.4400,\n          receivedBoxes: 2\n        },\n        {\n          itemCode: 'FGWGAGA120.36602440LN',\n          description: 'WHITE GREY 120-3660x2440 WHITE GREY 120',\n          thickness: 12,\n          width: 3660,\n          height: 2440,\n          quantity: 95,\n          csqm: 218.5680,\n          receivedBoxes: 1\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad',\n      items: [\n        {\n          itemCode: 'FGYGAGA060.24401830LN',\n          description: 'YELLOW GREY 060-2440x1830 YELLOW GREY 060',\n          thickness: 6,\n          width: 2440,\n          height: 1830,\n          quantity: 65,\n          csqm: 145.2720,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat',\n      items: [\n        {\n          itemCode: 'FGPGAGA100.36602770LN',\n          description: 'PURPLE GREY 100-3660x2770 PURPLE GREY 100',\n          thickness: 10,\n          width: 3660,\n          height: 2770,\n          quantity: 180,\n          csqm: 364.4520,\n          receivedBoxes: 4\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi',\n      items: [\n        {\n          itemCode: 'FGOGAGA080.24402440LN',\n          description: 'ORANGE GREY 080-2440x2440 ORANGE GREY 080',\n          thickness: 8,\n          width: 2440,\n          height: 2440,\n          quantity: 110,\n          csqm: 195.3760,\n          receivedBoxes: 3\n        },\n        {\n          itemCode: 'FGSGAGA120.18303660LN',\n          description: 'SILVER GREY 120-1830x3660 SILVER GREY 120',\n          thickness: 12,\n          width: 1830,\n          height: 3660,\n          quantity: 85,\n          csqm: 142.8180,\n          receivedBoxes: 2\n        }\n      ]\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController\n  ) {\n    this.createForms();\n  }\n\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n\n  selectComplaintType(type: any) {\n    this.complaintTypeForm.patchValue({ selectedType: type.value });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n\n  onDescriptionChange(event: any) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice =>\n          invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n\n  selectInvoice(invoice: InvoiceData) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({ searchTerm: invoice.invoiceNumber });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({ searchTerm: '' });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n\n  async onSubmitComplaint() {\n    if (this.complaintTypeForm.valid && this.complaintDescriptionForm.valid && this.selectedInvoice && this.complaintDetailsForm.valid) {\n      this.isLoading = true;\n\n      const loading = await this.loadingController.create({\n        message: 'Registering complaint...',\n        duration: 3000\n      });\n\n      await loading.present();\n\n      // Simulate registration process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n\n        const toast = await this.toastController.create({\n          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n          duration: 4000,\n          color: 'success',\n          position: 'top'\n        });\n        await toast.present();\n\n        // Navigate to track page\n        this.router.navigate(['/track']);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please complete all required steps and fill in all required fields.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({ attachedFile: files[0] });\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({ attachedFile: null });\n    }\n  }\n\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n\n  getErrorMessage(form: FormGroup, field: string): string {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n}\n", "<!-- Modern Material Design Header -->\n<mat-toolbar color=\"primary\" class=\"modern-toolbar\">\n  <button mat-icon-button (click)=\"goBack()\" class=\"back-button\">\n    <mat-icon>arrow_back</mat-icon>\n  </button>\n  <span class=\"toolbar-title\">Register Complaint</span>\n  <span class=\"spacer\"></span>\n  <mat-icon class=\"help-icon\">help_outline</mat-icon>\n</mat-toolbar>\n\n<!-- Modern Content Container -->\n<div class=\"modern-content\">\n  <!-- Compact Header -->\n  <div class=\"modern-header\">\n    <h1 class=\"page-title\">Register New Complaint</h1>\n    <p class=\"page-subtitle\">Complete your complaint in 4 simple steps</p>\n  </div>\n\n  <!-- Modern Horizontal Stepper -->\n  <div class=\"modern-stepper\">\n    <div class=\"stepper-container\">\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(1)\" [class.active]=\"currentStep === 1\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(1)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(1)\" class=\"step-number\">1</span>\n        </div>\n        <div class=\"step-label\">Type</div>\n      </div>\n\n      <div class=\"step-line\" [class.completed]=\"isStepCompleted(2)\"></div>\n\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(2)\" [class.active]=\"currentStep === 2\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(2)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(2)\" class=\"step-number\">2</span>\n        </div>\n        <div class=\"step-label\">Description</div>\n      </div>\n\n      <div class=\"step-line\" [class.completed]=\"isStepCompleted(3)\"></div>\n\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(3)\" [class.active]=\"currentStep === 3\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(3)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(3)\" class=\"step-number\">3</span>\n        </div>\n        <div class=\"step-label\">Invoice</div>\n      </div>\n\n      <div class=\"step-line\" [class.completed]=\"isStepCompleted(4)\"></div>\n\n      <div class=\"step-item\" [class.completed]=\"isStepCompleted(4)\" [class.active]=\"currentStep === 4\">\n        <div class=\"step-circle\">\n          <mat-icon *ngIf=\"isStepCompleted(4)\" class=\"check-icon\">check</mat-icon>\n          <span *ngIf=\"!isStepCompleted(4)\" class=\"step-number\">4</span>\n        </div>\n        <div class=\"step-label\">Submit</div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Modern Step 1: Complaint Type Selection -->\n  <div class=\"modern-step-content\" *ngIf=\"!isStepCompleted(1)\">\n    <mat-card class=\"step-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon class=\"step-icon\">report_problem</mat-icon>\n          Select Complaint Type\n        </mat-card-title>\n        <mat-card-subtitle>Choose the category that best describes your issue</mat-card-subtitle>\n      </mat-card-header>\n\n      <mat-card-content>\n        <form [formGroup]=\"complaintTypeForm\">\n          <div class=\"modern-options-grid\">\n            <mat-card\n              class=\"option-card\"\n              *ngFor=\"let type of complaintTypes\"\n              [class.selected]=\"complaintTypeForm.get('selectedType')?.value === type.value\"\n              (click)=\"selectComplaintType(type)\"\n              matRipple>\n              <mat-card-content class=\"option-content\">\n                <div class=\"option-icon\">\n                  <mat-icon [color]=\"complaintTypeForm.get('selectedType')?.value === type.value ? 'primary' : ''\">\n                    {{ type.icon }}\n                  </mat-icon>\n                </div>\n                <div class=\"option-text\">\n                  <h3>{{ type.label }}</h3>\n                  <p>{{ type.description }}</p>\n                </div>\n                <div class=\"option-radio\">\n                  <mat-radio-button\n                    [value]=\"type.value\"\n                    formControlName=\"selectedType\"\n                    color=\"primary\">\n                  </mat-radio-button>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </form>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Modern Step 2: Complaint Description Selection -->\n  <div class=\"modern-step-content\" *ngIf=\"isStepCompleted(1) && !isStepCompleted(2)\">\n    <div class=\"step-layout\">\n      <!-- Selected Type Summary -->\n      <mat-card class=\"summary-card\" *ngIf=\"getSelectedComplaintType()\">\n        <mat-card-content class=\"summary-content\">\n          <div class=\"summary-icon\">\n            <mat-icon color=\"primary\">{{ getSelectedComplaintType()?.icon }}</mat-icon>\n          </div>\n          <div class=\"summary-text\">\n            <h4>{{ getSelectedComplaintType()?.label }}</h4>\n            <p>{{ getSelectedComplaintType()?.description }}</p>\n          </div>\n          <mat-chip color=\"primary\" selected>Selected</mat-chip>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Description Selection -->\n      <mat-card class=\"step-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon class=\"step-icon\">description</mat-icon>\n            Select Specific Issue\n          </mat-card-title>\n          <mat-card-subtitle>Choose the description that best matches your complaint</mat-card-subtitle>\n        </mat-card-header>\n\n        <mat-card-content>\n          <form [formGroup]=\"complaintDescriptionForm\">\n            <mat-radio-group\n              formControlName=\"selectedDescription\"\n              class=\"modern-radio-group\"\n              (change)=\"onDescriptionChange($event)\">\n              <mat-card\n                class=\"radio-option-card\"\n                *ngFor=\"let desc of getComplaintDescriptions()\"\n                [class.selected]=\"complaintDescriptionForm.get('selectedDescription')?.value === desc.value\"\n                matRipple>\n                <mat-card-content class=\"radio-option-content\">\n                  <mat-radio-button\n                    [value]=\"desc.value\"\n                    color=\"primary\"\n                    class=\"radio-button\">\n                  </mat-radio-button>\n                  <div class=\"radio-text\">\n                    <h4>{{ desc.label }}</h4>\n                    <p>{{ desc.description }}</p>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </mat-radio-group>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Modern Step 3: Invoice Selection -->\n  <div class=\"modern-step-content\" *ngIf=\"isStepCompleted(2) && !isStepCompleted(3)\">\n    <div class=\"step-layout\">\n      <!-- Compact Summary -->\n      <mat-card class=\"compact-summary\">\n        <mat-card-content class=\"summary-chips\">\n          <mat-chip-set>\n            <mat-chip color=\"primary\" selected>\n              <mat-icon matChipAvatar>{{ getSelectedComplaintType()?.icon }}</mat-icon>\n              {{ getSelectedComplaintType()?.label }}\n            </mat-chip>\n            <mat-chip color=\"accent\" selected>\n              <mat-icon matChipAvatar>description</mat-icon>\n              {{ getSelectedComplaintDescription()?.label }}\n            </mat-chip>\n          </mat-chip-set>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Invoice Search & Selection -->\n      <mat-card class=\"step-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon class=\"step-icon\">receipt</mat-icon>\n            Select Invoice\n          </mat-card-title>\n          <mat-card-subtitle>Search and select the invoice related to your complaint</mat-card-subtitle>\n        </mat-card-header>\n\n        <mat-card-content>\n          <form [formGroup]=\"invoiceSearchForm\">\n            <!-- Compact Search -->\n            <mat-form-field appearance=\"outline\" class=\"search-field\">\n              <mat-label>Search Invoice</mat-label>\n              <input matInput formControlName=\"searchTerm\"\n                     placeholder=\"Invoice number, customer name, or leave empty for all\"\n                     (input)=\"onInvoiceSearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n            </mat-form-field>\n\n            <!-- Compact Invoice List -->\n            <div class=\"invoice-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length > 0\">\n              <div class=\"results-header\">\n                <span class=\"results-count\">{{ invoiceSearchResults.length }} invoices found</span>\n              </div>\n\n              <div class=\"modern-invoice-list\">\n                <mat-card\n                  class=\"invoice-card\"\n                  *ngFor=\"let invoice of invoiceSearchResults\"\n                  (click)=\"selectInvoice(invoice)\"\n                  matRipple>\n                  <mat-card-content class=\"invoice-content\">\n                    <div class=\"invoice-main\">\n                      <div class=\"invoice-number\">{{ invoice.invoiceNumber }}</div>\n                      <div class=\"invoice-date\">{{ invoice.invoiceDate | date:'MMM dd, yyyy' }}</div>\n                    </div>\n                    <div class=\"invoice-details\">\n                      <div class=\"customer-name\">{{ invoice.customerName }}</div>\n                      <div class=\"location-info\">{{ invoice.zone }} • {{ invoice.operatingUnit }}</div>\n                    </div>\n                    <mat-icon class=\"select-icon\">chevron_right</mat-icon>\n                  </mat-card-content>\n                </mat-card>\n              </div>\n            </div>\n\n            <div class=\"no-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length === 0\">\n              <mat-icon class=\"no-results-icon\">search_off</mat-icon>\n              <p>No invoices found</p>\n              <p class=\"search-hint\">Try different keywords or clear search</p>\n            </div>\n\n            <!-- Selected Invoice Preview -->\n            <mat-card class=\"selected-invoice-card\" *ngIf=\"selectedInvoice\">\n              <mat-card-header>\n                <mat-card-title>\n                  <mat-icon class=\"success-icon\" color=\"primary\">check_circle</mat-icon>\n                  Invoice Selected\n                </mat-card-title>\n                <mat-card-subtitle>{{ selectedInvoice.invoiceNumber }} • {{ selectedInvoice.customerName }}</mat-card-subtitle>\n              </mat-card-header>\n\n              <mat-card-content>\n                <!-- Expandable Details -->\n                <mat-expansion-panel class=\"invoice-expansion\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <mat-icon>visibility</mat-icon>\n                      View Complete Details\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      Customer info, locations & items\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n\n                  <!-- Customer Information -->\n                  <div class=\"details-section\">\n                    <h4><mat-icon>business</mat-icon> Customer Information</h4>\n                    <div class=\"details-grid\">\n                      <div class=\"detail-row\">\n                        <span class=\"label\">Invoice:</span>\n                        <span class=\"value\">{{ selectedInvoice.invoiceNumber }}</span>\n                      </div>\n                      <div class=\"detail-row\">\n                        <span class=\"label\">Date:</span>\n                        <span class=\"value\">{{ selectedInvoice.invoiceDate | date:'MMM dd, yyyy' }}</span>\n                      </div>\n                      <div class=\"detail-row\">\n                        <span class=\"label\">Customer:</span>\n                        <span class=\"value\">{{ selectedInvoice.customerName }}</span>\n                      </div>\n                      <div class=\"detail-row full-width\">\n                        <span class=\"label\">Address:</span>\n                        <span class=\"value\">{{ selectedInvoice.customerAddress }}</span>\n                      </div>\n                      <div class=\"detail-row\">\n                        <span class=\"label\">Zone:</span>\n                        <span class=\"value\">{{ selectedInvoice.zone }}</span>\n                      </div>\n                      <div class=\"detail-row\">\n                        <span class=\"label\">Operating Unit:</span>\n                        <span class=\"value\">{{ selectedInvoice.operatingUnit }}</span>\n                      </div>\n                      <div class=\"detail-row full-width\">\n                        <span class=\"label\">Organization:</span>\n                        <span class=\"value\">{{ selectedInvoice.organization }}</span>\n                      </div>\n                      <div class=\"detail-row full-width\">\n                        <span class=\"label\">Bill To:</span>\n                        <span class=\"value\">{{ selectedInvoice.billToLocation }}</span>\n                      </div>\n                      <div class=\"detail-row full-width\">\n                        <span class=\"label\">Ship To:</span>\n                        <span class=\"value\">{{ selectedInvoice.shipToLocation }}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- Item Information -->\n                  <div class=\"details-section\">\n                    <h4><mat-icon>inventory</mat-icon> Item Information</h4>\n                    <div class=\"items-list\">\n                      <div class=\"item-summary\" *ngFor=\"let item of selectedInvoice.items; let i = index\">\n                        <div class=\"item-header\">\n                          <strong>{{ item.itemCode }}</strong>\n                          <mat-chip class=\"item-chip\">{{ item.quantity }} pcs</mat-chip>\n                        </div>\n                        <div class=\"item-desc\">{{ item.description }}</div>\n                        <div class=\"item-specs\">\n                          <span class=\"spec\">{{ item.thickness }}mm</span>\n                          <span class=\"spec\">{{ item.width }}×{{ item.height }}</span>\n                          <span class=\"spec\">{{ item.csqm }} CSQM</span>\n                          <span class=\"spec\">{{ item.receivedBoxes }} boxes</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n\n                <div class=\"invoice-actions\">\n                  <button mat-stroked-button color=\"warn\" (click)=\"clearInvoiceSelection()\">\n                    <mat-icon>clear</mat-icon>\n                    Change Selection\n                  </button>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Modern Step 4: Final Review & Submit -->\n  <div class=\"modern-step-content\" *ngIf=\"isStepCompleted(3) && !isStepCompleted(4)\">\n    <div class=\"final-layout\">\n      <!-- Compact Final Summary -->\n      <mat-card class=\"final-summary-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon class=\"step-icon\" color=\"primary\">assignment_turned_in</mat-icon>\n            Final Review\n          </mat-card-title>\n          <mat-card-subtitle>Review your complaint details and submit</mat-card-subtitle>\n        </mat-card-header>\n\n        <mat-card-content>\n          <!-- Summary Chips -->\n          <div class=\"final-summary-chips\">\n            <mat-chip-set>\n              <mat-chip color=\"primary\" selected>\n                <mat-icon matChipAvatar>{{ getSelectedComplaintType()?.icon }}</mat-icon>\n                {{ getSelectedComplaintType()?.label }}\n              </mat-chip>\n              <mat-chip color=\"accent\" selected>\n                <mat-icon matChipAvatar>description</mat-icon>\n                {{ getSelectedComplaintDescription()?.label }}\n              </mat-chip>\n              <mat-chip color=\"warn\" selected *ngIf=\"selectedInvoice\">\n                <mat-icon matChipAvatar>receipt</mat-icon>\n                {{ selectedInvoice.invoiceNumber }}\n              </mat-chip>\n            </mat-chip-set>\n          </div>\n\n          <!-- Expandable Invoice Details -->\n          <mat-expansion-panel class=\"invoice-details-expansion\" *ngIf=\"selectedInvoice\">\n            <mat-expansion-panel-header>\n              <mat-panel-title>\n                <mat-icon>receipt</mat-icon>\n                Invoice Details\n              </mat-panel-title>\n              <mat-panel-description>\n                {{ selectedInvoice.customerName }} • {{ selectedInvoice.items.length }} items\n              </mat-panel-description>\n            </mat-expansion-panel-header>\n\n            <!-- Compact Invoice Info -->\n            <div class=\"compact-invoice-details\">\n              <div class=\"info-grid\">\n                <div class=\"info-item\">\n                  <span class=\"label\">Customer:</span>\n                  <span class=\"value\">{{ selectedInvoice.customerName }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"label\">Date:</span>\n                  <span class=\"value\">{{ selectedInvoice.invoiceDate | date:'MMM dd, yyyy' }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"label\">Zone:</span>\n                  <span class=\"value\">{{ selectedInvoice.zone }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"label\">Operating Unit:</span>\n                  <span class=\"value\">{{ selectedInvoice.operatingUnit }}</span>\n                </div>\n                <div class=\"info-item full-width\">\n                  <span class=\"label\">Address:</span>\n                  <span class=\"value\">{{ selectedInvoice.customerAddress }}</span>\n                </div>\n                <div class=\"info-item full-width\">\n                  <span class=\"label\">Organization:</span>\n                  <span class=\"value\">{{ selectedInvoice.organization }}</span>\n                </div>\n                <div class=\"info-item full-width\">\n                  <span class=\"label\">Bill To:</span>\n                  <span class=\"value\">{{ selectedInvoice.billToLocation }}</span>\n                </div>\n                <div class=\"info-item full-width\">\n                  <span class=\"label\">Ship To:</span>\n                  <span class=\"value\">{{ selectedInvoice.shipToLocation }}</span>\n                </div>\n              </div>\n\n              <!-- Compact Items -->\n              <div class=\"compact-items\">\n                <h5><mat-icon>inventory</mat-icon> Items ({{ selectedInvoice.items.length }})</h5>\n                <div class=\"items-summary\">\n                  <mat-chip-set>\n                    <mat-chip *ngFor=\"let item of selectedInvoice.items\" class=\"item-chip\">\n                      {{ item.itemCode }} ({{ item.quantity }})\n                    </mat-chip>\n                  </mat-chip-set>\n                </div>\n              </div>\n            </div>\n          </mat-expansion-panel>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Contact Form -->\n      <mat-card class=\"contact-form-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon class=\"step-icon\">contact_phone</mat-icon>\n            Contact Information\n          </mat-card-title>\n          <mat-card-subtitle>Provide your contact details for follow-up</mat-card-subtitle>\n        </mat-card-header>\n\n        <mat-card-content>\n          <form [formGroup]=\"complaintDetailsForm\" class=\"contact-form\">\n            <!-- Contact Fields -->\n            <div class=\"form-row\">\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Contact Person *</mat-label>\n                <input matInput formControlName=\"contactPersonName\" placeholder=\"Your name\">\n                <mat-icon matSuffix>person</mat-icon>\n                <mat-error *ngIf=\"complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched\">\n                  {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\" class=\"form-field\">\n                <mat-label>Contact Number *</mat-label>\n                <input matInput formControlName=\"contactNumber\" placeholder=\"10-digit number\" type=\"tel\">\n                <mat-icon matSuffix>phone</mat-icon>\n                <mat-error *ngIf=\"complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched\">\n                  {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Additional Comments</mat-label>\n              <textarea matInput formControlName=\"comments\" rows=\"2\" placeholder=\"Optional additional information\"></textarea>\n              <mat-icon matSuffix>comment</mat-icon>\n            </mat-form-field>\n\n            <!-- File Upload -->\n            <div class=\"upload-section\">\n              <mat-checkbox formControlName=\"hasComplaintLetters\" color=\"primary\">\n                Attach supporting documents\n              </mat-checkbox>\n\n              <div class=\"upload-area\" *ngIf=\"complaintDetailsForm.get('hasComplaintLetters')?.value\">\n                <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" multiple accept=\".pdf,.jpg,.jpeg,.png,.doc,.docx\" style=\"display: none;\">\n                <button mat-stroked-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-btn\">\n                  <mat-icon>attach_file</mat-icon>\n                  Choose Files\n                </button>\n                <span class=\"upload-hint\">PDF, JPG, PNG, DOC (Max 5MB each)</span>\n\n                <div class=\"file-chips\" *ngIf=\"selectedFiles.length > 0\">\n                  <mat-chip-set>\n                    <mat-chip *ngFor=\"let file of selectedFiles; let i = index\" [removable]=\"true\" (removed)=\"removeFile(i)\">\n                      <mat-icon matChipAvatar>description</mat-icon>\n                      {{ file.name }}\n                      <mat-icon matChipRemove>cancel</mat-icon>\n                    </mat-chip>\n                  </mat-chip-set>\n                </div>\n              </div>\n            </div>\n          </form>\n        </mat-card-content>\n\n        <!-- Action Buttons -->\n        <mat-card-actions class=\"card-actions\">\n          <button mat-button (click)=\"goBackToStep3()\" class=\"back-btn\">\n            <mat-icon>arrow_back</mat-icon>\n            Back\n          </button>\n          <div class=\"spacer\"></div>\n          <button mat-raised-button color=\"primary\"\n                  (click)=\"onSubmitComplaint()\"\n                  [disabled]=\"isLoading || !complaintDetailsForm.valid\"\n                  class=\"submit-btn\">\n            <mat-icon *ngIf=\"!isLoading\">send</mat-icon>\n            <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n            {{ isLoading ? 'Submitting...' : 'Submit Complaint' }}\n          </button>\n        </mat-card-actions>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Success Message -->\n  <div class=\"success-content\" *ngIf=\"isStepCompleted(4)\">\n    <mat-card class=\"success-card\">\n      <mat-card-content class=\"success-content-inner\">\n        <mat-icon class=\"success-icon\" color=\"primary\">check_circle</mat-icon>\n        <h2>Complaint Submitted Successfully!</h2>\n        <p>Your complaint has been registered and will be processed shortly.</p>\n        <button mat-raised-button color=\"primary\" (click)=\"goBack()\">\n          <mat-icon>home</mat-icon>\n          Back to Home\n        </button>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;ICsBzDC,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS9DH,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS9DH,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAS9DH,EAAA,CAAAC,cAAA,mBAAwD;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IACxEH,EAAA,CAAAC,cAAA,eAAsD;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IAqB5DH,EAAA,CAAAC,cAAA,mBAKY;IADVD,EAAA,CAAAI,UAAA,mBAAAC,mEAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,mBAAA,CAAAP,OAAA,CAAyB;IAAA,EAAC;IAI/BN,EAFJ,CAAAC,cAAA,2BAAyC,cACd,mBAC0E;IAC/FD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAAyB,SACnB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAc,SAAA,4BAImB;IAGzBd,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;;;;;;;IArBTH,EAAA,CAAAe,WAAA,eAAAC,OAAA,GAAAN,MAAA,CAAAO,iBAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,KAAA,MAAAb,OAAA,CAAAa,KAAA,CAA8E;IAKhEnB,EAAA,CAAAoB,SAAA,GAAsF;IAAtFpB,EAAA,CAAAqB,UAAA,YAAAC,OAAA,GAAAZ,MAAA,CAAAO,iBAAA,CAAAC,GAAA,mCAAAI,OAAA,CAAAH,KAAA,MAAAb,OAAA,CAAAa,KAAA,kBAAsF;IAC9FnB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAjB,OAAA,CAAAkB,IAAA,MACF;IAGIxB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAyB,iBAAA,CAAAnB,OAAA,CAAAoB,KAAA,CAAgB;IACjB1B,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAyB,iBAAA,CAAAnB,OAAA,CAAAqB,WAAA,CAAsB;IAIvB3B,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAqB,UAAA,UAAAf,OAAA,CAAAa,KAAA,CAAoB;;;;;IA3B9BnB,EAJR,CAAAC,cAAA,cAA6D,mBAC/B,sBACT,qBACC,mBACc;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,yDAAkD;IACvEF,EADuE,CAAAG,YAAA,EAAoB,EACzE;IAIdH,EAFJ,CAAAC,cAAA,uBAAkB,gBACsB,eACH;IAC/BD,EAAA,CAAA4B,UAAA,KAAAC,wCAAA,wBAKY;IAwBtB7B,EAJQ,CAAAG,YAAA,EAAM,EACD,EACU,EACV,EACP;;;;IA/BMH,EAAA,CAAAoB,SAAA,IAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAAO,iBAAA,CAA+B;IAIdjB,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAAoB,cAAA,CAAiB;;;;;IAoCpC9B,EAHN,CAAAC,cAAA,mBAAkE,2BACtB,cACd,mBACE;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAClEF,EADkE,CAAAG,YAAA,EAAW,EACvE;IAEJH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAClDF,EADkD,CAAAG,YAAA,EAAI,EAChD;IACNH,EAAA,CAAAC,cAAA,oBAAmC;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAE/CF,EAF+C,CAAAG,YAAA,EAAW,EACrC,EACV;;;;;;;IARqBH,EAAA,CAAAoB,SAAA,GAAsC;IAAtCpB,EAAA,CAAAyB,iBAAA,EAAAM,OAAA,GAAArB,MAAA,CAAAsB,wBAAA,qBAAAD,OAAA,CAAAP,IAAA,CAAsC;IAG5DxB,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAyB,iBAAA,EAAAT,OAAA,GAAAN,MAAA,CAAAsB,wBAAA,qBAAAhB,OAAA,CAAAU,KAAA,CAAuC;IACxC1B,EAAA,CAAAoB,SAAA,GAA6C;IAA7CpB,EAAA,CAAAyB,iBAAA,EAAAH,OAAA,GAAAZ,MAAA,CAAAsB,wBAAA,qBAAAV,OAAA,CAAAK,WAAA,CAA6C;;;;;IA2B5C3B,EALF,CAAAC,cAAA,mBAIY,2BACqC;IAC7CD,EAAA,CAAAc,SAAA,2BAImB;IAEjBd,EADF,CAAAC,cAAA,cAAwB,SAClB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAG/BF,EAH+B,CAAAG,YAAA,EAAI,EACzB,EACW,EACV;;;;;;IAbTH,EAAA,CAAAe,WAAA,eAAAC,OAAA,GAAAN,MAAA,CAAAuB,wBAAA,CAAAf,GAAA,0CAAAF,OAAA,CAAAG,KAAA,MAAAe,OAAA,CAAAf,KAAA,CAA4F;IAIxFnB,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAqB,UAAA,UAAAa,OAAA,CAAAf,KAAA,CAAoB;IAKhBnB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAyB,iBAAA,CAAAS,OAAA,CAAAR,KAAA,CAAgB;IACjB1B,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAyB,iBAAA,CAAAS,OAAA,CAAAP,WAAA,CAAsB;;;;;;IA5CzC3B,EADF,CAAAC,cAAA,cAAmF,cACxD;IAEvBD,EAAA,CAAA4B,UAAA,IAAAO,uCAAA,wBAAkE;IAiB5DnC,EAHN,CAAAC,cAAA,mBAA4B,sBACT,qBACC,mBACc;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClDH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,+DAAuD;IAC5EF,EAD4E,CAAAG,YAAA,EAAoB,EAC9E;IAIdH,EAFJ,CAAAC,cAAA,wBAAkB,gBAC6B,2BAIF;IAAvCD,EAAA,CAAAI,UAAA,oBAAAgC,gEAAAC,MAAA;MAAArC,EAAA,CAAAO,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAA6B,mBAAA,CAAAF,MAAA,CAA2B;IAAA,EAAC;IACtCrC,EAAA,CAAA4B,UAAA,KAAAY,wCAAA,uBAIY;IAkBxBxC,EALU,CAAAG,YAAA,EAAkB,EACb,EACU,EACV,EACP,EACF;;;;IAnD8BH,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAsB,wBAAA,GAAgC;IAwBtDhC,EAAA,CAAAoB,SAAA,IAAsC;IAAtCpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAAuB,wBAAA,CAAsC;IAOrBjC,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAA+B,wBAAA,GAA6B;;;;;;IAqE9CzC,EAAA,CAAAC,cAAA,mBAIY;IADVD,EAAA,CAAAI,UAAA,mBAAAsC,yEAAA;MAAA,MAAAC,UAAA,GAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA,EAAAnC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAmC,aAAA,CAAAF,UAAA,CAAsB;IAAA,EAAC;IAI5B3C,EAFJ,CAAAC,cAAA,2BAA0C,cACd,cACI;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAA+C;;IAC3EF,EAD2E,CAAAG,YAAA,EAAM,EAC3E;IAEJH,EADF,CAAAC,cAAA,cAA6B,cACA;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC3DH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAgD;IAC7EF,EAD6E,CAAAG,YAAA,EAAM,EAC7E;IACNH,EAAA,CAAAC,cAAA,oBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAW,EACrC,EACV;;;;IATuBH,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAAyB,iBAAA,CAAAkB,UAAA,CAAAG,aAAA,CAA2B;IAC7B9C,EAAA,CAAAoB,SAAA,GAA+C;IAA/CpB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA+C,WAAA,OAAAJ,UAAA,CAAAK,WAAA,kBAA+C;IAG9ChD,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAyB,iBAAA,CAAAkB,UAAA,CAAAM,YAAA,CAA0B;IAC1BjD,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAkD,kBAAA,KAAAP,UAAA,CAAAQ,IAAA,cAAAR,UAAA,CAAAS,aAAA,KAAgD;;;;;IAhBjFpD,EAFJ,CAAAC,cAAA,cAA2F,cAC7D,eACE;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAC9EF,EAD8E,CAAAG,YAAA,EAAO,EAC/E;IAENH,EAAA,CAAAC,cAAA,cAAiC;IAC/BD,EAAA,CAAA4B,UAAA,IAAAyB,8CAAA,wBAIY;IAchBrD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAtB0BH,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAuB,kBAAA,KAAAb,MAAA,CAAA4C,oBAAA,CAAAC,MAAA,oBAAgD;IAMtDvD,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAA4C,oBAAA,CAAuB;;;;;IAmB/CtD,EADF,CAAAC,cAAA,cAAwF,mBACpD;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACvDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxBH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IAC/DF,EAD+D,CAAAG,YAAA,EAAI,EAC7D;;;;;IA0EQH,EAFJ,CAAAC,cAAA,cAAoF,cACzD,aACf;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,mBAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IACrDF,EADqD,CAAAG,YAAA,EAAW,EAC1D;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEjDH,EADF,CAAAC,cAAA,cAAwB,eACH;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5DH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9CH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAErDF,EAFqD,CAAAG,YAAA,EAAO,EACpD,EACF;;;;IAVMH,EAAA,CAAAoB,SAAA,GAAmB;IAAnBpB,EAAA,CAAAyB,iBAAA,CAAA+B,QAAA,CAAAC,QAAA,CAAmB;IACCzD,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAuB,kBAAA,KAAAiC,QAAA,CAAAE,QAAA,SAAuB;IAE9B1D,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAyB,iBAAA,CAAA+B,QAAA,CAAA7B,WAAA,CAAsB;IAExB3B,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAuB,kBAAA,KAAAiC,QAAA,CAAAG,SAAA,OAAsB;IACtB3D,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAkD,kBAAA,KAAAM,QAAA,CAAAI,KAAA,YAAAJ,QAAA,CAAAK,MAAA,KAAkC;IAClC7D,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAuB,kBAAA,KAAAiC,QAAA,CAAAM,IAAA,UAAoB;IACpB9D,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAuB,kBAAA,KAAAiC,QAAA,CAAAO,aAAA,WAA8B;;;;;;IA5EzD/D,EAHN,CAAAC,cAAA,mBAAgE,sBAC7C,qBACC,mBACiC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtEH,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAwE;IAC7FF,EAD6F,CAAAG,YAAA,EAAoB,EAC/F;IAOVH,EALR,CAAAC,cAAA,uBAAkB,8BAE+B,kCACjB,uBACT,gBACL;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,+BACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,6BAAuB;IACrBD,EAAA,CAAAE,MAAA,0CACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAIvBH,EADN,CAAAC,cAAA,eAA6B,UACvB,gBAAU;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,6BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGvDH,EAFJ,CAAAC,cAAA,eAA0B,eACA,gBACF;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;IAEJH,EADF,CAAAC,cAAA,eAAwB,gBACF;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAuD;;IAC7EF,EAD6E,CAAAG,YAAA,EAAO,EAC9E;IAEJH,EADF,CAAAC,cAAA,eAAwB,gBACF;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAEJH,EADF,CAAAC,cAAA,eAAmC,gBACb;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IAEJH,EADF,CAAAC,cAAA,eAAwB,gBACF;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAChDF,EADgD,CAAAG,YAAA,EAAO,EACjD;IAEJH,EADF,CAAAC,cAAA,eAAwB,gBACF;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;IAEJH,EADF,CAAAC,cAAA,eAAmC,gBACb;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAEJH,EADF,CAAAC,cAAA,eAAmC,gBACb;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAEJH,EADF,CAAAC,cAAA,eAAmC,gBACb;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAG9DF,EAH8D,CAAAG,YAAA,EAAO,EAC3D,EACF,EACF;IAIAH,EADN,CAAAC,cAAA,eAA6B,UACvB,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,yBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxDH,EAAA,CAAAC,cAAA,eAAwB;IACtBD,EAAA,CAAA4B,UAAA,KAAAoC,+CAAA,mBAAoF;IAe1FhE,EAFI,CAAAG,YAAA,EAAM,EACF,EACc;IAGpBH,EADF,CAAAC,cAAA,eAA6B,kBAC+C;IAAlCD,EAAA,CAAAI,UAAA,mBAAA6D,kEAAA;MAAAjE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAxD,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAyD,qBAAA,EAAuB;IAAA,EAAC;IACvEnE,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,0BACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACW,EACV;;;;IAvFYH,EAAA,CAAAoB,SAAA,GAAwE;IAAxEpB,EAAA,CAAAkD,kBAAA,KAAAxC,MAAA,CAAA0D,eAAA,CAAAtB,aAAA,cAAApC,MAAA,CAAA0D,eAAA,CAAAnB,YAAA,KAAwE;IAsB/DjD,EAAA,CAAAoB,SAAA,IAAmC;IAAnCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAtB,aAAA,CAAmC;IAInC9C,EAAA,CAAAoB,SAAA,GAAuD;IAAvDpB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA+C,WAAA,SAAArC,MAAA,CAAA0D,eAAA,CAAApB,WAAA,kBAAuD;IAIvDhD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAnB,YAAA,CAAkC;IAIlCjD,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAC,eAAA,CAAqC;IAIrCrE,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAjB,IAAA,CAA0B;IAI1BnD,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAhB,aAAA,CAAmC;IAInCpD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAE,YAAA,CAAkC;IAIlCtE,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAG,cAAA,CAAoC;IAIpCvE,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAI,cAAA,CAAoC;IASfxE,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAA0D,eAAA,CAAAK,KAAA,CAA0B;;;;;;IAvI7EzE,EAPZ,CAAAC,cAAA,cAAmF,cACxD,mBAEW,2BACQ,mBACxB,mBACuB,mBACT;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzEH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAETH,EADF,CAAAC,cAAA,mBAAkC,oBACR;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAW,EACE,EACE,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAA4B,uBACT,sBACC,oBACc;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,+DAAuD;IAC5EF,EAD4E,CAAAG,YAAA,EAAoB,EAC9E;IAMZH,EAJN,CAAAC,cAAA,wBAAkB,gBACsB,0BAEsB,iBAC7C;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAC,cAAA,iBAEmC;IAA5BD,EAAA,CAAAI,UAAA,mBAAAsE,qDAAA;MAAA1E,EAAA,CAAAO,aAAA,CAAAoE,GAAA;MAAA,MAAAjE,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAkE,eAAA,EAAiB;IAAA,EAAC;IAFlC5E,EAAA,CAAAG,YAAA,EAEmC;IACnCH,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;IAoCjBH,EAjCA,CAAA4B,UAAA,KAAAiD,mCAAA,kBAA2F,KAAAC,mCAAA,kBA0BH,KAAAC,wCAAA,yBAOxB;IAkG1E/E,EAJQ,CAAAG,YAAA,EAAO,EACU,EACV,EACP,EACF;;;;;;;IApK8BH,EAAA,CAAAoB,SAAA,GAAsC;IAAtCpB,EAAA,CAAAyB,iBAAA,EAAAuD,OAAA,GAAAtE,MAAA,CAAAsB,wBAAA,qBAAAgD,OAAA,CAAAxD,IAAA,CAAsC;IAC9DxB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,OAAAQ,OAAA,GAAArB,MAAA,CAAAsB,wBAAA,qBAAAD,OAAA,CAAAL,KAAA,MACF;IAGE1B,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,OAAAP,OAAA,GAAAN,MAAA,CAAAuE,+BAAA,qBAAAjE,OAAA,CAAAU,KAAA,MACF;IAgBI1B,EAAA,CAAAoB,SAAA,IAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAAwE,iBAAA,CAA+B;IAWLlF,EAAA,CAAAoB,SAAA,GAA2D;IAA3DpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAyE,kBAAA,IAAAzE,MAAA,CAAA4C,oBAAA,CAAAC,MAAA,KAA2D;IA0BhEvD,EAAA,CAAAoB,SAAA,EAA6D;IAA7DpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAyE,kBAAA,IAAAzE,MAAA,CAAA4C,oBAAA,CAAAC,MAAA,OAA6D;IAO7CvD,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAA0D,eAAA,CAAqB;;;;;IA8H1DpE,EADF,CAAAC,cAAA,oBAAwD,mBAC9B;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAA0D,eAAA,CAAAtB,aAAA,MACF;;;;;IA0DM9C,EAAA,CAAAC,cAAA,mBAAuE;IACrED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAkD,kBAAA,MAAAkC,QAAA,CAAA3B,QAAA,QAAA2B,QAAA,CAAA1B,QAAA,OACF;;;;;IApDJ1D,EAHN,CAAAC,cAAA,+BAA+E,iCACjD,sBACT,eACL;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAMvBH,EAHN,CAAAC,cAAA,eAAqC,eACZ,gBACE,gBACD;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAEJH,EADF,CAAAC,cAAA,gBAAuB,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAuD;;IAC7EF,EAD6E,CAAAG,YAAA,EAAO,EAC9E;IAEJH,EADF,CAAAC,cAAA,gBAAuB,gBACD;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAChDF,EADgD,CAAAG,YAAA,EAAO,EACjD;IAEJH,EADF,CAAAC,cAAA,gBAAuB,gBACD;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACzDF,EADyD,CAAAG,YAAA,EAAO,EAC1D;IAEJH,EADF,CAAAC,cAAA,gBAAkC,gBACZ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IAEJH,EADF,CAAAC,cAAA,gBAAkC,gBACZ;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAEJH,EADF,CAAAC,cAAA,gBAAkC,gBACZ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAEJH,EADF,CAAAC,cAAA,gBAAkC,gBACZ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnCH,EAAA,CAAAC,cAAA,gBAAoB;IAAAD,EAAA,CAAAE,MAAA,IAAoC;IAE5DF,EAF4D,CAAAG,YAAA,EAAO,EAC3D,EACF;IAIAH,EADN,CAAAC,cAAA,gBAA2B,UACrB,gBAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAACH,EAAA,CAAAE,MAAA,IAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEhFH,EADF,CAAAC,cAAA,gBAA2B,oBACX;IACZD,EAAA,CAAA4B,UAAA,KAAAyD,+DAAA,wBAAuE;IAOjFrF,EAJQ,CAAAG,YAAA,EAAe,EACX,EACF,EACF,EACc;;;;IArDhBH,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAkD,kBAAA,MAAAxC,MAAA,CAAA0D,eAAA,CAAAnB,YAAA,cAAAvC,MAAA,CAAA0D,eAAA,CAAAK,KAAA,CAAAlB,MAAA,YACF;IAQwBvD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAnB,YAAA,CAAkC;IAIlCjD,EAAA,CAAAoB,SAAA,GAAuD;IAAvDpB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA+C,WAAA,SAAArC,MAAA,CAAA0D,eAAA,CAAApB,WAAA,kBAAuD;IAIvDhD,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAjB,IAAA,CAA0B;IAI1BnD,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAhB,aAAA,CAAmC;IAInCpD,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAC,eAAA,CAAqC;IAIrCrE,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAE,YAAA,CAAkC;IAIlCtE,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAG,cAAA,CAAoC;IAIpCvE,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAyB,iBAAA,CAAAf,MAAA,CAAA0D,eAAA,CAAAI,cAAA,CAAoC;IAMvBxE,EAAA,CAAAoB,SAAA,GAA0C;IAA1CpB,EAAA,CAAAuB,kBAAA,aAAAb,MAAA,CAAA0D,eAAA,CAAAK,KAAA,CAAAlB,MAAA,MAA0C;IAG9CvD,EAAA,CAAAoB,SAAA,GAAwB;IAAxBpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAA0D,eAAA,CAAAK,KAAA,CAAwB;;;;;IA6BvDzE,EAAA,CAAAC,cAAA,gBAAoI;IAClID,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAA4E,eAAA,CAAA5E,MAAA,CAAA6E,oBAAA,4BACF;;;;;IAOAvF,EAAA,CAAAC,cAAA,gBAA4H;IAC1HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAA4E,eAAA,CAAA5E,MAAA,CAAA6E,oBAAA,wBACF;;;;;;IA0BIvF,EAAA,CAAAC,cAAA,oBAAyG;IAA1BD,EAAA,CAAAI,UAAA,qBAAAoF,iFAAA;MAAA,MAAAC,KAAA,GAAAzF,EAAA,CAAAO,aAAA,CAAAmF,IAAA,EAAAC,KAAA;MAAA,MAAAjF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAWF,MAAA,CAAAkF,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IACtGzF,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,oBAAwB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAChCF,EADgC,CAAAG,YAAA,EAAW,EAChC;;;;IAJiDH,EAAA,CAAAqB,UAAA,mBAAkB;IAE5ErB,EAAA,CAAAoB,SAAA,GACA;IADApB,EAAA,CAAAuB,kBAAA,MAAAsE,QAAA,CAAAC,IAAA,MACA;;;;;IAJJ9F,EADF,CAAAC,cAAA,eAAyD,mBACzC;IACZD,EAAA,CAAA4B,UAAA,IAAAmE,oDAAA,wBAAyG;IAM7G/F,EADE,CAAAG,YAAA,EAAe,EACX;;;;IANyBH,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAqB,UAAA,YAAAX,MAAA,CAAAsF,aAAA,CAAkB;;;;;;IATjDhG,EADF,CAAAC,cAAA,eAAwF,oBACmD;IAA3GD,EAAA,CAAAI,UAAA,oBAAA6F,4DAAA5D,MAAA;MAAArC,EAAA,CAAAO,aAAA,CAAA2F,IAAA;MAAA,MAAAxF,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAAUF,MAAA,CAAAyF,cAAA,CAAA9D,MAAA,CAAsB;IAAA,EAAC;IAA/DrC,EAAA,CAAAG,YAAA,EAAyI;IACzIH,EAAA,CAAAC,cAAA,kBAA0F;IAA/CD,EAAA,CAAAI,UAAA,mBAAAgG,4DAAA;MAAApG,EAAA,CAAAO,aAAA,CAAA2F,IAAA;MAAA,MAAAG,aAAA,GAAArG,EAAA,CAAAsG,WAAA;MAAA,OAAAtG,EAAA,CAAAY,WAAA,CAASyF,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IACpEvG,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElEH,EAAA,CAAA4B,UAAA,IAAA4E,yCAAA,mBAAyD;IAS3DxG,EAAA,CAAAG,YAAA,EAAM;;;;IATqBH,EAAA,CAAAoB,SAAA,GAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAsF,aAAA,CAAAzC,MAAA,KAA8B;;;;;IAyB3DvD,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC5CH,EAAA,CAAAc,SAAA,uBAA2D;;;;;;IAzK3Dd,EANV,CAAAC,cAAA,cAAmF,cACvD,mBAEa,sBAClB,qBACC,mBAC8B;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3EH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,+CAAwC;IAC7DF,EAD6D,CAAAG,YAAA,EAAoB,EAC/D;IAOVH,EALR,CAAAC,cAAA,wBAAkB,eAEiB,oBACjB,oBACuB,oBACT;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzEH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAETH,EADF,CAAAC,cAAA,oBAAkC,oBACR;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAA4B,UAAA,KAAA6E,wCAAA,uBAAwD;IAK5DzG,EADE,CAAAG,YAAA,EAAe,EACX;IAGNH,EAAA,CAAA4B,UAAA,KAAA8E,mDAAA,qCAA+E;IA8DnF1G,EADE,CAAAG,YAAA,EAAmB,EACV;IAMLH,EAHN,CAAAC,cAAA,qBAAoC,uBACjB,sBACC,oBACc;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,yBAAmB;IAAAD,EAAA,CAAAE,MAAA,kDAA0C;IAC/DF,EAD+D,CAAAG,YAAA,EAAoB,EACjE;IAOVH,EALR,CAAAC,cAAA,wBAAkB,iBAC8C,gBAEtC,2BACoC,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAc,SAAA,kBAA4E;IAC5Ed,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrCH,EAAA,CAAA4B,UAAA,KAAA+E,yCAAA,yBAAoI;IAGtI3G,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,2BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAc,SAAA,kBAAyF;IACzFd,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAA4B,UAAA,KAAAgF,yCAAA,yBAA4H;IAIhI5G,EADE,CAAAG,YAAA,EAAiB,EACb;IAGJH,EADF,CAAAC,cAAA,2BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAAc,SAAA,qBAAgH;IAChHd,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAC7BF,EAD6B,CAAAG,YAAA,EAAW,EACvB;IAIfH,EADF,CAAAC,cAAA,gBAA4B,yBAC0C;IAClED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;IAEfH,EAAA,CAAA4B,UAAA,KAAAiF,mCAAA,oBAAwF;IAoB9F7G,EAFI,CAAAG,YAAA,EAAM,EACD,EACU;IAIjBH,EADF,CAAAC,cAAA,6BAAuC,mBACyB;IAA3CD,EAAA,CAAAI,UAAA,mBAAA0G,sDAAA;MAAA9G,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAArG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAsG,aAAA,EAAe;IAAA,EAAC;IAC1ChH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAc,SAAA,cAA0B;IAC1Bd,EAAA,CAAAC,cAAA,mBAG2B;IAFnBD,EAAA,CAAAI,UAAA,mBAAA6G,sDAAA;MAAAjH,EAAA,CAAAO,aAAA,CAAAwG,IAAA;MAAA,MAAArG,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAwG,iBAAA,EAAmB;IAAA,EAAC;IAInClH,EADA,CAAA4B,UAAA,KAAAuF,wCAAA,wBAA6B,KAAAC,2CAAA,2BACgB;IAC7CpH,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACQ,EACV,EACP,EACF;;;;;;;;;;IApKgCH,EAAA,CAAAoB,SAAA,IAAsC;IAAtCpB,EAAA,CAAAyB,iBAAA,EAAAuD,OAAA,GAAAtE,MAAA,CAAAsB,wBAAA,qBAAAgD,OAAA,CAAAxD,IAAA,CAAsC;IAC9DxB,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,OAAAQ,OAAA,GAAArB,MAAA,CAAAsB,wBAAA,qBAAAD,OAAA,CAAAL,KAAA,MACF;IAGE1B,EAAA,CAAAoB,SAAA,GACF;IADEpB,EAAA,CAAAuB,kBAAA,OAAAP,OAAA,GAAAN,MAAA,CAAAuE,+BAAA,qBAAAjE,OAAA,CAAAU,KAAA,MACF;IACiC1B,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAA0D,eAAA,CAAqB;IAQFpE,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAA0D,eAAA,CAAqB;IA2EvEpE,EAAA,CAAAoB,SAAA,IAAkC;IAAlCpB,EAAA,CAAAqB,UAAA,cAAAX,MAAA,CAAA6E,oBAAA,CAAkC;IAOtBvF,EAAA,CAAAoB,SAAA,GAAsH;IAAtHpB,EAAA,CAAAqB,UAAA,WAAAgG,OAAA,GAAA3G,MAAA,CAAA6E,oBAAA,CAAArE,GAAA,wCAAAmG,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAA3G,MAAA,CAAA6E,oBAAA,CAAArE,GAAA,wCAAAmG,OAAA,CAAAE,OAAA,EAAsH;IAStHvH,EAAA,CAAAoB,SAAA,GAA8G;IAA9GpB,EAAA,CAAAqB,UAAA,WAAAmG,OAAA,GAAA9G,MAAA,CAAA6E,oBAAA,CAAArE,GAAA,oCAAAsG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA9G,MAAA,CAAA6E,oBAAA,CAAArE,GAAA,oCAAAsG,OAAA,CAAAD,OAAA,EAA8G;IAkBlGvH,EAAA,CAAAoB,SAAA,IAA4D;IAA5DpB,EAAA,CAAAqB,UAAA,UAAAoG,OAAA,GAAA/G,MAAA,CAAA6E,oBAAA,CAAArE,GAAA,0CAAAuG,OAAA,CAAAtG,KAAA,CAA4D;IA+BlFnB,EAAA,CAAAoB,SAAA,GAAqD;IAArDpB,EAAA,CAAAqB,UAAA,aAAAX,MAAA,CAAAgH,SAAA,KAAAhH,MAAA,CAAA6E,oBAAA,CAAAoC,KAAA,CAAqD;IAEhD3H,EAAA,CAAAoB,SAAA,EAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,UAAAX,MAAA,CAAAgH,SAAA,CAAgB;IACb1H,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAqB,UAAA,SAAAX,MAAA,CAAAgH,SAAA,CAAe;IAC7B1H,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAuB,kBAAA,MAAAb,MAAA,CAAAgH,SAAA,6CACF;;;;;;IAUF1H,EAHN,CAAAC,cAAA,eAAwD,oBACvB,4BACmB,mBACC;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtEH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wEAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,kBAA6D;IAAnBD,EAAA,CAAAI,UAAA,mBAAAwH,qDAAA;MAAA5H,EAAA,CAAAO,aAAA,CAAAsH,IAAA;MAAA,MAAAnH,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAoH,MAAA,EAAQ;IAAA,EAAC;IAC1D9H,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACQ,EACV,EACP;;;ADnfR,OAAM,MAAO4H,YAAY;EAkUvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IAhUzB,KAAAV,SAAS,GAAG,KAAK;IACjB,KAAA1B,aAAa,GAAW,EAAE;IAC1B,KAAA5B,eAAe,GAAuB,IAAI;IAC1C,KAAAd,oBAAoB,GAAkB,EAAE;IACxC,KAAA6B,kBAAkB,GAAG,KAAK;IAC1B,KAAAkD,WAAW,GAAG,CAAC;IAEf,KAAAvG,cAAc,GAAG,CACf;MACEX,KAAK,EAAE,eAAe;MACtBO,KAAK,EAAE,sBAAsB;MAC7BF,IAAI,EAAE,iBAAiB;MACvBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,cAAc;MACrBO,KAAK,EAAE,uBAAuB;MAC9BF,IAAI,EAAE,mBAAmB;MACzBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,iBAAiB;MACxBO,KAAK,EAAE,2BAA2B;MAClCF,IAAI,EAAE,aAAa;MACnBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,aAAa;MACpBO,KAAK,EAAE,oBAAoB;MAC3BF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,SAAS;MAChBO,KAAK,EAAE,iBAAiB;MACxBF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACER,KAAK,EAAE,SAAS;MAChBO,KAAK,EAAE,yBAAyB;MAChCF,IAAI,EAAE,uBAAuB;MAC7BG,WAAW,EAAE;KACd,CACF;IAED,KAAA2G,qBAAqB,GAA6B;MAChD,eAAe,EAAE,CACf;QAAEnH,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,4BAA4B;QAAEC,WAAW,EAAE;MAAiD,CAAE,EAC3H;QAAER,KAAK,EAAE,QAAQ;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA0C,CAAE,EACtG;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,aAAa;QAAEC,WAAW,EAAE;MAA4C,CAAE,EACrG;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,eAAe;QAAEC,WAAW,EAAE;MAAgD,CAAE,EACjH;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAChH;MACD,cAAc,EAAE,CACd;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EAClH;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAAuC,CAAE,EACnG;QAAER,KAAK,EAAE,UAAU;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EAChH;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,cAAc;QAAEC,WAAW,EAAE;MAA0C,CAAE,EACpG;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,4BAA4B;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAC9H;MACD,iBAAiB,EAAE,CACjB;QAAER,KAAK,EAAE,gBAAgB;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAoC,CAAE,EAC1G;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EACnG;QAAER,KAAK,EAAE,UAAU;QAAEO,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAA8C,CAAE,EAC3G;QAAER,KAAK,EAAE,SAAS;QAAEO,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAAoC,CAAE,EAClG;QAAER,KAAK,EAAE,YAAY;QAAEO,KAAK,EAAE,sBAAsB;QAAEC,WAAW,EAAE;MAAkD,CAAE,CACxH;MACD,aAAa,EAAE,CACb;QAAER,KAAK,EAAE,YAAY;QAAEO,KAAK,EAAE,YAAY;QAAEC,WAAW,EAAE;MAAyC,CAAE,EACpG;QAAER,KAAK,EAAE,mBAAmB;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EAC9G;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,wBAAwB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EACvH;QAAER,KAAK,EAAE,UAAU;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAC9G;MACD,SAAS,EAAE,CACT;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EACtH;QAAER,KAAK,EAAE,eAAe;QAAEO,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAA2C,CAAE,EACjH;QAAER,KAAK,EAAE,gBAAgB;QAAEO,KAAK,EAAE,yBAAyB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EACzH;QAAER,KAAK,EAAE,iBAAiB;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA4C,CAAE,CAClH;MACD,SAAS,EAAE,CACT;QAAER,KAAK,EAAE,cAAc;QAAEO,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EACxG;QAAER,KAAK,EAAE,iBAAiB;QAAEO,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAAwC,CAAE,EAC7G;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EAC9G;QAAER,KAAK,EAAE,WAAW;QAAEO,KAAK,EAAE,uBAAuB;QAAEC,WAAW,EAAE;MAA0C,CAAE;KAElH;IAED;IACA,KAAA4G,cAAc,GAAkB,CAC9B;MACEzF,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,uBAAuB;MACrCoB,eAAe,EAAE,yDAAyD;MAC1ElB,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,uBAAuB;MACvCC,cAAc,EAAE,yBAAyB;MACzCC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,uCAAuC;QACpDgC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,GAAG;QACbI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,uCAAuC;QACpDgC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,GAAG;QACbI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEjB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,wBAAwB;MACtCoB,eAAe,EAAE,mDAAmD;MACpElB,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,uBAAuB;MACtCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,8BAA8B;MAC9CC,cAAc,EAAE,+BAA+B;MAC/CC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,yCAAyC;QACtDgC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,GAAG;QACbI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,2CAA2C;QACxDgC,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,EAAE;QACZI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEjB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,wBAAwB;MACtCoB,eAAe,EAAE,oDAAoD;MACrElB,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE,yCAAyC;MACzDC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,uCAAuC;QACpDgC,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,EAAE;QACZI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEjB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,6BAA6B;MAC3CoB,eAAe,EAAE,0DAA0D;MAC3ElB,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,mBAAmB;MAClCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,+BAA+B;MAC/CC,cAAc,EAAE,gCAAgC;MAChDC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,yCAAyC;QACtDgC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,GAAG;QACbI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEjB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,kBAAkB;MAChCoB,eAAe,EAAE,0CAA0C;MAC3DlB,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,oBAAoB;MACnCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,gCAAgC;MAChDC,cAAc,EAAE,uCAAuC;MACvDC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,qCAAqC;QAClDgC,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,EAAE;QACZI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,yCAAyC;QACtDgC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,EAAE;QACZI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEjB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,oBAAoB;MAClCoB,eAAe,EAAE,wDAAwD;MACzElB,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE,0CAA0C;MAC1DC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,2CAA2C;QACxDgC,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,EAAE;QACZI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEjB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,mBAAmB;MACjCoB,eAAe,EAAE,2CAA2C;MAC5DlB,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,sBAAsB;MACrCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,kCAAkC;MAClDC,cAAc,EAAE,yCAAyC;MACzDC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,2CAA2C;QACxDgC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,GAAG;QACbI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEjB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAIwF,IAAI,CAAC,YAAY,CAAC;MACnCvF,YAAY,EAAE,wBAAwB;MACtCoB,eAAe,EAAE,0DAA0D;MAC3ElB,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCkB,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,yBAAyB;MACzCC,cAAc,EAAE,oCAAoC;MACpDC,KAAK,EAAE,CACL;QACEhB,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,2CAA2C;QACxDgC,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,GAAG;QACbI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjC9B,WAAW,EAAE,2CAA2C;QACxDgC,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZH,QAAQ,EAAE,EAAE;QACZI,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,CACF;IAQC,IAAI,CAAC0E,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAF,WAAWA,CAAA;IACT,IAAI,CAACxH,iBAAiB,GAAG,IAAI,CAACgH,WAAW,CAACW,KAAK,CAAC;MAC9CC,YAAY,EAAE,CAAC,EAAE,EAAE9I,UAAU,CAAC+I,QAAQ;KACvC,CAAC;IAEF,IAAI,CAAC7G,wBAAwB,GAAG,IAAI,CAACgG,WAAW,CAACW,KAAK,CAAC;MACrDG,mBAAmB,EAAE,CAAC,EAAE,EAAEhJ,UAAU,CAAC+I,QAAQ;KAC9C,CAAC;IAEF,IAAI,CAAC5D,iBAAiB,GAAG,IAAI,CAAC+C,WAAW,CAACW,KAAK,CAAC;MAC9CI,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;KAClB,CAAC;IAEF,IAAI,CAACzD,oBAAoB,GAAG,IAAI,CAAC0C,WAAW,CAACW,KAAK,CAAC;MACjDK,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAClJ,UAAU,CAAC+I,QAAQ,EAAE/I,UAAU,CAACmJ,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,aAAa,EAAE,CAAC,EAAE,EAAE,CAACpJ,UAAU,CAAC+I,QAAQ,EAAE/I,UAAU,CAACqJ,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAC7EC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,YAAY,EAAE,CAAC,IAAI;KACpB,CAAC;EACJ;EAEA1I,mBAAmBA,CAAC2I,IAAS;IAC3B,IAAI,CAACvI,iBAAiB,CAACwI,UAAU,CAAC;MAAEZ,YAAY,EAAEW,IAAI,CAACrI;IAAK,CAAE,CAAC;IAC/D;IACAuI,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAlH,wBAAwBA,CAAA;IACtB,MAAMoG,YAAY,GAAG,IAAI,CAAC5H,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACtE,OAAO,IAAI,CAACmH,qBAAqB,CAACO,YAAY,CAAC,IAAI,EAAE;EACvD;EAEA5D,+BAA+BA,CAAA;IAC7B,MAAM2E,aAAa,GAAG,IAAI,CAAC3H,wBAAwB,CAACf,GAAG,CAAC,qBAAqB,CAAC,EAAEC,KAAK;IACrF,MAAM0I,YAAY,GAAG,IAAI,CAACpH,wBAAwB,EAAE;IACpD,OAAOoH,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC5I,KAAK,KAAKyI,aAAa,CAAC;EAChE;EAEArH,mBAAmBA,CAACyH,KAAU;IAC5B;IACAN,UAAU,CAAC,MAAK;MACd,IAAI,CAACO,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAN,SAASA,CAAA;IACP,IAAI,IAAI,CAAC1I,iBAAiB,CAAC0G,KAAK,EAAE;MAChC,IAAI,CAACU,WAAW,GAAG,CAAC;;EAExB;EAEA4B,SAASA,CAAA;IACP,IAAI,IAAI,CAAChI,wBAAwB,CAAC0F,KAAK,EAAE;MACvC,IAAI,CAACU,WAAW,GAAG,CAAC;MACpB,IAAI,CAACM,eAAe,EAAE;;EAE1B;EAEAuB,SAASA,CAAA;IACP,IAAI,IAAI,CAAC9F,eAAe,EAAE;MACxB,IAAI,CAACiE,WAAW,GAAG,CAAC;;EAExB;EAEA8B,aAAaA,CAAA;IACX,IAAI,CAAC9B,WAAW,GAAG,CAAC;EACtB;EAEA+B,aAAaA,CAAA;IACX,IAAI,CAAC/B,WAAW,GAAG,CAAC;EACtB;EAEArB,aAAaA,CAAA;IACX,IAAI,CAACqB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACM,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACrF,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAACiF,cAAc,CAAC;IACpD,IAAI,CAACpD,kBAAkB,GAAG,IAAI;EAChC;EAEAP,eAAeA,CAAA;IACb,MAAMoE,UAAU,GAAG,IAAI,CAAC9D,iBAAiB,CAAChE,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAElE,IAAI,CAAC6H,UAAU,IAAIA,UAAU,CAACqB,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3C;MACA,IAAI,CAAC1B,eAAe,EAAE;MACtB;;IAGF,IAAIK,UAAU,CAACzF,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACmE,SAAS,GAAG,IAAI;MAErB;MACAgC,UAAU,CAAC,MAAK;QACd,IAAI,CAACpG,oBAAoB,GAAG,IAAI,CAACiF,cAAc,CAAC+B,MAAM,CAACC,OAAO,IAC5DA,OAAO,CAACzH,aAAa,CAAC0H,WAAW,EAAE,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,EAAE,CAAC,IACtED,OAAO,CAACtH,YAAY,CAACuH,WAAW,EAAE,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,EAAE,CAAC,IACrED,OAAO,CAACpH,IAAI,CAACqH,WAAW,EAAE,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,EAAE,CAAC,IAC7DD,OAAO,CAACnH,aAAa,CAACoH,WAAW,EAAE,CAACC,QAAQ,CAACzB,UAAU,CAACwB,WAAW,EAAE,CAAC,CACvE;QACD,IAAI,CAACrF,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACuC,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEA7E,aAAaA,CAAC0H,OAAoB;IAChC,IAAI,CAACnG,eAAe,GAAGmG,OAAO;IAC9B,IAAI,CAACpF,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACD,iBAAiB,CAACuE,UAAU,CAAC;MAAET,UAAU,EAAEuB,OAAO,CAACzH;IAAa,CAAE,CAAC;IACxE;IACA4G,UAAU,CAAC,MAAK;MACd,IAAI,CAACQ,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA/F,qBAAqBA,CAAA;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACc,iBAAiB,CAACuE,UAAU,CAAC;MAAET,UAAU,EAAE;IAAE,CAAE,CAAC;IACrD;IACA,IAAI,CAACL,eAAe,EAAE;EACxB;EAEMzB,iBAAiBA,CAAA;IAAA,IAAAwD,KAAA;IAAA,OAAAC,iBAAA;MACrB,IAAID,KAAI,CAACzJ,iBAAiB,CAAC0G,KAAK,IAAI+C,KAAI,CAACzI,wBAAwB,CAAC0F,KAAK,IAAI+C,KAAI,CAACtG,eAAe,IAAIsG,KAAI,CAACnF,oBAAoB,CAACoC,KAAK,EAAE;QAClI+C,KAAI,CAAChD,SAAS,GAAG,IAAI;QAErB,MAAMkD,OAAO,SAASF,KAAI,CAACvC,iBAAiB,CAAC0C,MAAM,CAAC;UAClDC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB;QACAtB,UAAU,cAAAiB,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAAChD,SAAS,GAAG,KAAK;UACtB,MAAMkD,OAAO,CAACK,OAAO,EAAE;UAEvB,MAAMC,KAAK,SAASR,KAAI,CAACtC,eAAe,CAACyC,MAAM,CAAC;YAC9CC,OAAO,EAAE,uDAAuD,GAAGtC,IAAI,CAAC2C,GAAG,EAAE;YAC7EJ,QAAQ,EAAE,IAAI;YACdK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,MAAMH,KAAK,CAACF,OAAO,EAAE;UAErB;UACAN,KAAI,CAACxC,MAAM,CAACoD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMJ,KAAK,SAASR,KAAI,CAACtC,eAAe,CAACyC,MAAM,CAAC;UAC9CC,OAAO,EAAE,qEAAqE;UAC9EC,QAAQ,EAAE,IAAI;UACdK,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;SACX,CAAC;QACF,MAAMH,KAAK,CAACF,OAAO,EAAE;;IACtB;EACH;EAEA7E,cAAcA,CAAC6D,KAAU;IACvB,MAAMuB,KAAK,GAAGvB,KAAK,CAACwB,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAAChI,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACyC,aAAa,GAAGyF,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACtC,IAAI,CAAChG,oBAAoB,CAACkE,UAAU,CAAC;QAAEF,YAAY,EAAEgC,KAAK,CAAC,CAAC;MAAC,CAAE,CAAC;;EAEpE;EAEA3F,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACK,aAAa,CAAC2F,MAAM,CAAChG,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,IAAI,CAACK,aAAa,CAACzC,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACgC,oBAAoB,CAACkE,UAAU,CAAC;QAAEF,YAAY,EAAE;MAAI,CAAE,CAAC;;EAEhE;EAEAvH,wBAAwBA,CAAA;IACtB,MAAM4H,aAAa,GAAG,IAAI,CAAC3I,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACvE,OAAO,IAAI,CAACW,cAAc,CAACgI,IAAI,CAACN,IAAI,IAAIA,IAAI,CAACrI,KAAK,KAAKyI,aAAa,CAAC;EACvE;EAEAgC,eAAeA,CAACC,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC5K,iBAAiB,CAAC0G,KAAK,IAAI,IAAI,CAACU,WAAW,GAAG,CAAC;MAC7D,KAAK,CAAC;QACJ,OAAO,IAAI,CAACpG,wBAAwB,CAAC0F,KAAK,IAAI,IAAI,CAACU,WAAW,GAAG,CAAC;MACpE,KAAK,CAAC;QACJ,OAAO,IAAI,CAACjE,eAAe,KAAK,IAAI,IAAI,IAAI,CAACiE,WAAW,GAAG,CAAC;MAC9D,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC9C,oBAAoB,CAACoC,KAAK,IAAI,IAAI,CAACU,WAAW,GAAG,CAAC;MAChE;QACE,OAAO,KAAK;;EAElB;EAEA/C,eAAeA,CAACwG,IAAe,EAAEC,KAAa;IAC5C,MAAMC,OAAO,GAAGF,IAAI,CAAC5K,GAAG,CAAC6K,KAAK,CAAC;IAC/B,IAAIC,OAAO,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAM/C,SAAS,GAAG8C,OAAO,CAACG,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACH,KAAK,CAAC,qBAAqB7C,SAAS,aAAa;;IAEhF,IAAI8C,OAAO,EAAEC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACH,KAAa;IACjC,MAAMM,MAAM,GAA8B;MACxCxD,YAAY,EAAE,gBAAgB;MAC9BE,mBAAmB,EAAE,uBAAuB;MAC5CC,UAAU,EAAE,aAAa;MACzBC,iBAAiB,EAAE,qBAAqB;MACxCE,aAAa,EAAE,gBAAgB;MAC/BE,QAAQ,EAAE;KACX;IACD,OAAOgD,MAAM,CAACN,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAjE,MAAMA,CAAA;IACJ,IAAI,CAACI,MAAM,CAACoD,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBAvjBWvD,YAAY,EAAA/H,EAAA,CAAAsM,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxM,EAAA,CAAAsM,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA1M,EAAA,CAAAsM,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAA5M,EAAA,CAAAsM,iBAAA,CAAAK,EAAA,CAAAE,eAAA;IAAA;EAAA;;;YAAZ9E,YAAY;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCvBpN,EADF,CAAAC,cAAA,qBAAoD,gBACa;UAAvCD,EAAA,CAAAI,UAAA,mBAAAkN,8CAAA;YAAA,OAASD,GAAA,CAAAvF,MAAA,EAAQ;UAAA,EAAC;UACxC9H,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UACTH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrDH,EAAA,CAAAc,SAAA,cAA4B;UAC5Bd,EAAA,CAAAC,cAAA,kBAA4B;UAAAD,EAAA,CAAAE,MAAA,mBAAY;UAC1CF,EAD0C,CAAAG,YAAA,EAAW,EACvC;UAMVH,EAHJ,CAAAC,cAAA,aAA4B,cAEC,aACF;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClDH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,iDAAyC;UACpEF,EADoE,CAAAG,YAAA,EAAI,EAClE;UAMAH,EAHN,CAAAC,cAAA,eAA4B,eACK,eACoE,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAA2L,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxDxN,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAC9BF,EAD8B,CAAAG,YAAA,EAAM,EAC9B;UAENH,EAAA,CAAAc,SAAA,eAAoE;UAGlEd,EADF,CAAAC,cAAA,eAAiG,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAA6L,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxD1N,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACrCF,EADqC,CAAAG,YAAA,EAAM,EACrC;UAENH,EAAA,CAAAc,SAAA,eAAoE;UAGlEd,EADF,CAAAC,cAAA,eAAiG,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAA+L,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxD5N,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UACjCF,EADiC,CAAAG,YAAA,EAAM,EACjC;UAENH,EAAA,CAAAc,SAAA,eAAoE;UAGlEd,EADF,CAAAC,cAAA,eAAiG,eACtE;UAEvBD,EADA,CAAA4B,UAAA,KAAAiM,iCAAA,uBAAwD,KAAAC,6BAAA,mBACF;UACxD9N,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAGpCF,EAHoC,CAAAG,YAAA,EAAM,EAChC,EACF,EACF;UA+cNH,EA5cA,CAAA4B,UAAA,KAAAmM,4BAAA,mBAA6D,KAAAC,4BAAA,mBA6CsB,KAAAC,4BAAA,mBAyDA,KAAAC,4BAAA,oBA8KA,KAAAC,4BAAA,mBAwL3B;UAa1DnO,EAAA,CAAAG,YAAA,EAAM;;;UAlgBuBH,EAAA,CAAAoB,SAAA,IAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAsM,GAAA,CAAAzB,eAAA,IAAsC,WAAAyB,GAAA,CAAAhF,WAAA,OAAmC;UAEjFrI,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,IAAwB;UAC5B5L,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAAgM,GAAA,CAAAzB,eAAA,IAAyB;UAKb5L,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAsM,GAAA,CAAAzB,eAAA,IAAsC;UAEtC5L,EAAA,CAAAoB,SAAA,EAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAsM,GAAA,CAAAzB,eAAA,IAAsC,WAAAyB,GAAA,CAAAhF,WAAA,OAAmC;UAEjFrI,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,IAAwB;UAC5B5L,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAAgM,GAAA,CAAAzB,eAAA,IAAyB;UAKb5L,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAsM,GAAA,CAAAzB,eAAA,IAAsC;UAEtC5L,EAAA,CAAAoB,SAAA,EAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAsM,GAAA,CAAAzB,eAAA,IAAsC,WAAAyB,GAAA,CAAAhF,WAAA,OAAmC;UAEjFrI,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,IAAwB;UAC5B5L,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAAgM,GAAA,CAAAzB,eAAA,IAAyB;UAKb5L,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAAsM,GAAA,CAAAzB,eAAA,IAAsC;UAEtC5L,EAAA,CAAAoB,SAAA,EAAsC;UAACpB,EAAvC,CAAAe,WAAA,cAAAsM,GAAA,CAAAzB,eAAA,IAAsC,WAAAyB,GAAA,CAAAhF,WAAA,OAAmC;UAEjFrI,EAAA,CAAAoB,SAAA,GAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,IAAwB;UAC5B5L,EAAA,CAAAoB,SAAA,EAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAAgM,GAAA,CAAAzB,eAAA,IAAyB;UAQN5L,EAAA,CAAAoB,SAAA,GAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAAgM,GAAA,CAAAzB,eAAA,IAAyB;UA6CzB5L,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,QAAAyB,GAAA,CAAAzB,eAAA,IAA+C;UAyD/C5L,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,QAAAyB,GAAA,CAAAzB,eAAA,IAA+C;UA8K/C5L,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,QAAAyB,GAAA,CAAAzB,eAAA,IAA+C;UAwLnD5L,EAAA,CAAAoB,SAAA,EAAwB;UAAxBpB,EAAA,CAAAqB,UAAA,SAAAgM,GAAA,CAAAzB,eAAA,IAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}