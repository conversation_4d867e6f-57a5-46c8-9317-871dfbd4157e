{"ast": null, "code": "import { PreloadAllModules, RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'home',\n  loadChildren: () => import('./pages/home/<USER>').then(m => m.HomePageModule)\n}, {\n  path: 'login',\n  loadChildren: () => import('./pages/login/login.module').then(m => m.LoginPageModule)\n}, {\n  path: 'register',\n  loadChildren: () => import('./pages/register/register.module').then(m => m.RegisterPageModule)\n}, {\n  path: 'track',\n  loadChildren: () => import('./pages/track/track.module').then(m => m.TrackPageModule)\n}, {\n  path: 'trackitem',\n  loadChildren: () => import('./pages/trackitem/trackitem.module').then(m => m.TrackitemPageModule)\n}, {\n  path: 'newcomplaint',\n  loadChildren: () => import('./pages/newcomplaint/newcomplaint.module').then(m => m.NewcomplaintPageModule)\n}, {\n  path: 'feedback',\n  loadChildren: () => import('./pages/feedback/feedback.module').then(m => m.FeedbackPageModule)\n}, {\n  path: 'creditnote',\n  loadChildren: () => import('./pages/creditnote/creditnote.module').then(m => m.CreditnotePageModule)\n}, {\n  path: 'videoplayer',\n  loadChildren: () => import('./pages/videoplayer/videoplayer.module').then(m => m.VideoplayerPageModule)\n}, {\n  path: '',\n  redirectTo: 'home',\n  pathMatch: 'full'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, {\n        preloadingStrategy: PreloadAllModules\n      }), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["PreloadAllModules", "RouterModule", "routes", "path", "loadChildren", "then", "m", "HomePageModule", "LoginPageModule", "RegisterPageModule", "TrackPageModule", "TrackitemPageModule", "NewcomplaintPageModule", "FeedbackPageModule", "CreditnotePageModule", "VideoplayerPageModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "preloadingStrategy", "imports", "i1", "exports"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { PreloadAllModules, RouterModule, Routes } from '@angular/router';\n\nconst routes: Routes = [\n  {\n    path: 'home',\n    loadChildren: () => import('./pages/home/<USER>').then( m => m.HomePageModule)\n  },\n  {\n    path: 'login',\n    loadChildren: () => import('./pages/login/login.module').then( m => m.LoginPageModule)\n  },\n  {\n    path: 'register',\n    loadChildren: () => import('./pages/register/register.module').then( m => m.RegisterPageModule)\n  },\n  {\n    path: 'track',\n    loadChildren: () => import('./pages/track/track.module').then( m => m.TrackPageModule)\n  },\n  {\n    path: 'trackitem',\n    loadChildren: () => import('./pages/trackitem/trackitem.module').then( m => m.TrackitemPageModule)\n  },\n  {\n    path: 'newcomplaint',\n    loadChildren: () => import('./pages/newcomplaint/newcomplaint.module').then( m => m.NewcomplaintPageModule)\n  },\n  {\n    path: 'feedback',\n    loadChildren: () => import('./pages/feedback/feedback.module').then( m => m.FeedbackPageModule)\n  },\n  {\n    path: 'creditnote',\n    loadChildren: () => import('./pages/creditnote/creditnote.module').then( m => m.CreditnotePageModule)\n  },\n  {\n    path: 'videoplayer',\n    loadChildren: () => import('./pages/videoplayer/videoplayer.module').then( m => m.VideoplayerPageModule)\n  },\n  {\n    path: '',\n    redirectTo: 'home',\n    pathMatch: 'full'\n  },\n];\n\n@NgModule({\n  imports: [\n    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })\n  ],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,iBAAiB,EAAEC,YAAY,QAAgB,iBAAiB;;;AAEzE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACC,cAAc;CACnF,EACD;EACEJ,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACE,eAAe;CACtF,EACD;EACEL,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACG,kBAAkB;CAC/F,EACD;EACEN,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACI,eAAe;CACtF,EACD;EACEP,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACK,mBAAmB;CAClG,EACD;EACER,IAAI,EAAE,cAAc;EACpBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0CAA0C,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACM,sBAAsB;CAC3G,EACD;EACET,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACO,kBAAkB;CAC/F,EACD;EACEV,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACQ,oBAAoB;CACrG,EACD;EACEX,IAAI,EAAE,aAAa;EACnBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,wCAAwC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACS,qBAAqB;CACxG,EACD;EACEZ,IAAI,EAAE,EAAE;EACRa,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE;CACZ,CACF;AAQD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAJzBjB,YAAY,CAACkB,OAAO,CAACjB,MAAM,EAAE;QAAEkB,kBAAkB,EAAEpB;MAAiB,CAAE,CAAC,EAE/DC,YAAY;IAAA;EAAA;;;2EAEXiB,gBAAgB;IAAAG,OAAA,GAAAC,EAAA,CAAArB,YAAA;IAAAsB,OAAA,GAFjBtB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}