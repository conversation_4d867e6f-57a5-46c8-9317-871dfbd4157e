{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst doc = typeof document !== 'undefined' ? document : undefined;\nexport { doc as d, win as w };", "map": {"version": 3, "names": ["win", "window", "undefined", "doc", "document", "d", "w"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/index-a5d50daf.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nconst win = typeof window !== 'undefined' ? window : undefined;\nconst doc = typeof document !== 'undefined' ? document : undefined;\n\nexport { doc as d, win as w };\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,GAAG,GAAG,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAGC,SAAS;AAC9D,MAAMC,GAAG,GAAG,OAAOC,QAAQ,KAAK,WAAW,GAAGA,QAAQ,GAAGF,SAAS;AAElE,SAASC,GAAG,IAAIE,CAAC,EAAEL,GAAG,IAAIM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}