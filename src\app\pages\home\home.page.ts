import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MenuController } from '@ionic/angular';

@Component({
  selector: 'app-home',
  templateUrl: './home.page.html',
  styleUrls: ['./home.page.scss'],
})
export class HomePage implements OnInit {

  activeTab: string = 'home';
  
  menuItems = [
    {
      title: 'AIS Quality Policy',
      description: 'Learn about our quality standards and policies',
      icon: 'document-text',
      color: 'primary',
      route: '/videoplayer',
      data: 'AIS Quality Policy'
    },
    {
      title: 'Glass Unloading Guidelines',
      description: 'Proper procedures for glass handling and unloading',
      icon: 'cube',
      color: 'secondary',
      route: '/videoplayer',
      data: 'Glass Unloading Guidelines'
    },
    {
      title: 'Requirements to Register a Complaint',
      description: 'What you need to know before registering a complaint',
      icon: 'clipboard',
      color: 'tertiary',
      route: '/videoplayer',
      data: 'Requirements to Register a Complaint'
    },
    {
      title: 'Complaint Handling Procedure',
      description: 'How we process and resolve your complaints',
      icon: 'settings',
      color: 'success',
      route: '/videoplayer',
      data: 'Complaint handling procedure'
    },
    {
      title: 'Glass Installation Guideline',
      description: 'Best practices for glass installation',
      icon: 'construct',
      color: 'warning',
      route: '/videoplayer',
      data: 'Glass Installation Guideline'
    }
  ];

  constructor(
    private router: Router,
    private menuController: MenuController
  ) { }

  ngOnInit() {
    this.menuController.enable(true);
  }

  onTabChange(tab: string) {
    this.activeTab = tab;
    switch (tab) {
      case 'register':
        this.router.navigate(['/register']);
        break;
      case 'track':
        this.router.navigate(['/track']);
        break;
      case 'feedback':
        this.router.navigate(['/feedback']);
        break;
      default:
        this.activeTab = 'home';
        break;
    }
  }

  onCardClick(item: any) {
    this.router.navigate([item.route], { 
      queryParams: { data: item.data } 
    });
  }

  openMenu() {
    this.menuController.open();
  }
}
