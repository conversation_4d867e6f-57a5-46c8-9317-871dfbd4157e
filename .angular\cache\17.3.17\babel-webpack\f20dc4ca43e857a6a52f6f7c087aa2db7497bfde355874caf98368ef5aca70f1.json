{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { c as config } from './ionic-global-94f25d1b.js';\nimport './index-a1a47f01.js';\n\n/**\n * CloseWatcher is a newer API that lets\n * use detect the hardware back button event\n * in a web browser: https://caniuse.com/?search=closewatcher\n * However, not every browser supports it yet.\n *\n * This needs to be a function so that we can\n * check the config once it has been set.\n * Otherwise, this code would be evaluated the\n * moment this file is evaluated which could be\n * before the config is set.\n */\nconst shouldUseCloseWatcher = () => config.get('experimentalCloseWatcher', false) && win !== undefined && 'CloseWatcher' in win;\n/**\n * When hardwareBackButton: false in config,\n * we need to make sure we also block the default\n * webview behavior. If we don't then it will be\n * possible for users to navigate backward while\n * an overlay is still open. Additionally, it will\n * give the appearance that the hardwareBackButton\n * config is not working as the page transition\n * will still happen.\n */\nconst blockHardwareBackButton = () => {\n  document.addEventListener('backbutton', () => {}); // eslint-disable-line\n};\nconst startHardwareBackButton = () => {\n  const doc = document;\n  let busy = false;\n  const backButtonCallback = () => {\n    if (busy) {\n      return;\n    }\n    let index = 0;\n    let handlers = [];\n    const ev = new CustomEvent('ionBackButton', {\n      bubbles: false,\n      detail: {\n        register(priority, handler) {\n          handlers.push({\n            priority,\n            handler,\n            id: index++\n          });\n        }\n      }\n    });\n    doc.dispatchEvent(ev);\n    const executeAction = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (handlerRegister) {\n        try {\n          if (handlerRegister === null || handlerRegister === void 0 ? void 0 : handlerRegister.handler) {\n            const result = handlerRegister.handler(processHandlers);\n            if (result != null) {\n              yield result;\n            }\n          }\n        } catch (e) {\n          console.error(e);\n        }\n      });\n      return function executeAction(_x) {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    const processHandlers = () => {\n      if (handlers.length > 0) {\n        let selectedHandler = {\n          priority: Number.MIN_SAFE_INTEGER,\n          handler: () => undefined,\n          id: -1\n        };\n        handlers.forEach(handler => {\n          if (handler.priority >= selectedHandler.priority) {\n            selectedHandler = handler;\n          }\n        });\n        busy = true;\n        handlers = handlers.filter(handler => handler.id !== selectedHandler.id);\n        executeAction(selectedHandler).then(() => busy = false);\n      }\n    };\n    processHandlers();\n  };\n  /**\n   * If the CloseWatcher is defined then\n   * we don't want to also listen for the native\n   * backbutton event otherwise we may get duplicate\n   * events firing.\n   */\n  if (shouldUseCloseWatcher()) {\n    let watcher;\n    const configureWatcher = () => {\n      watcher === null || watcher === void 0 ? void 0 : watcher.destroy();\n      watcher = new win.CloseWatcher();\n      /**\n       * Once a close request happens\n       * the watcher gets destroyed.\n       * As a result, we need to re-configure\n       * the watcher so we can respond to other\n       * close requests.\n       */\n      watcher.onclose = () => {\n        backButtonCallback();\n        configureWatcher();\n      };\n    };\n    configureWatcher();\n  } else {\n    doc.addEventListener('backbutton', backButtonCallback);\n  }\n};\nconst OVERLAY_BACK_BUTTON_PRIORITY = 100;\nconst MENU_BACK_BUTTON_PRIORITY = 99; // 1 less than overlay priority since menu is displayed behind overlays\n\nexport { MENU_BACK_BUTTON_PRIORITY, OVERLAY_BACK_BUTTON_PRIORITY, blockHardwareBackButton, shouldUseCloseWatcher, startHardwareBackButton };", "map": {"version": 3, "names": ["w", "win", "c", "config", "shouldUseCloseWatcher", "get", "undefined", "blockHardwareBackButton", "document", "addEventListener", "startHardwareBackButton", "doc", "busy", "back<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "handlers", "ev", "CustomEvent", "bubbles", "detail", "register", "priority", "handler", "push", "id", "dispatchEvent", "executeAction", "_ref", "_asyncToGenerator", "handler<PERSON>eg<PERSON>", "result", "processHandlers", "e", "console", "error", "_x", "apply", "arguments", "length", "<PERSON><PERSON><PERSON><PERSON>", "Number", "MIN_SAFE_INTEGER", "for<PERSON>ach", "filter", "then", "watcher", "configure<PERSON><PERSON><PERSON>", "destroy", "CloseWatcher", "onclose", "OVERLAY_BACK_BUTTON_PRIORITY", "MENU_BACK_BUTTON_PRIORITY"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/hardware-back-button-6107a37c.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { w as win } from './index-a5d50daf.js';\nimport { c as config } from './ionic-global-94f25d1b.js';\nimport './index-a1a47f01.js';\n\n/**\n * CloseWatcher is a newer API that lets\n * use detect the hardware back button event\n * in a web browser: https://caniuse.com/?search=closewatcher\n * However, not every browser supports it yet.\n *\n * This needs to be a function so that we can\n * check the config once it has been set.\n * Otherwise, this code would be evaluated the\n * moment this file is evaluated which could be\n * before the config is set.\n */\nconst shouldUseCloseWatcher = () => config.get('experimentalCloseWatcher', false) && win !== undefined && 'CloseWatcher' in win;\n/**\n * When hardwareBackButton: false in config,\n * we need to make sure we also block the default\n * webview behavior. If we don't then it will be\n * possible for users to navigate backward while\n * an overlay is still open. Additionally, it will\n * give the appearance that the hardwareBackButton\n * config is not working as the page transition\n * will still happen.\n */\nconst blockHardwareBackButton = () => {\n    document.addEventListener('backbutton', () => { }); // eslint-disable-line\n};\nconst startHardwareBackButton = () => {\n    const doc = document;\n    let busy = false;\n    const backButtonCallback = () => {\n        if (busy) {\n            return;\n        }\n        let index = 0;\n        let handlers = [];\n        const ev = new CustomEvent('ionBackButton', {\n            bubbles: false,\n            detail: {\n                register(priority, handler) {\n                    handlers.push({ priority, handler, id: index++ });\n                },\n            },\n        });\n        doc.dispatchEvent(ev);\n        const executeAction = async (handlerRegister) => {\n            try {\n                if (handlerRegister === null || handlerRegister === void 0 ? void 0 : handlerRegister.handler) {\n                    const result = handlerRegister.handler(processHandlers);\n                    if (result != null) {\n                        await result;\n                    }\n                }\n            }\n            catch (e) {\n                console.error(e);\n            }\n        };\n        const processHandlers = () => {\n            if (handlers.length > 0) {\n                let selectedHandler = {\n                    priority: Number.MIN_SAFE_INTEGER,\n                    handler: () => undefined,\n                    id: -1,\n                };\n                handlers.forEach((handler) => {\n                    if (handler.priority >= selectedHandler.priority) {\n                        selectedHandler = handler;\n                    }\n                });\n                busy = true;\n                handlers = handlers.filter((handler) => handler.id !== selectedHandler.id);\n                executeAction(selectedHandler).then(() => (busy = false));\n            }\n        };\n        processHandlers();\n    };\n    /**\n     * If the CloseWatcher is defined then\n     * we don't want to also listen for the native\n     * backbutton event otherwise we may get duplicate\n     * events firing.\n     */\n    if (shouldUseCloseWatcher()) {\n        let watcher;\n        const configureWatcher = () => {\n            watcher === null || watcher === void 0 ? void 0 : watcher.destroy();\n            watcher = new win.CloseWatcher();\n            /**\n             * Once a close request happens\n             * the watcher gets destroyed.\n             * As a result, we need to re-configure\n             * the watcher so we can respond to other\n             * close requests.\n             */\n            watcher.onclose = () => {\n                backButtonCallback();\n                configureWatcher();\n            };\n        };\n        configureWatcher();\n    }\n    else {\n        doc.addEventListener('backbutton', backButtonCallback);\n    }\n};\nconst OVERLAY_BACK_BUTTON_PRIORITY = 100;\nconst MENU_BACK_BUTTON_PRIORITY = 99; // 1 less than overlay priority since menu is displayed behind overlays\n\nexport { MENU_BACK_BUTTON_PRIORITY, OVERLAY_BACK_BUTTON_PRIORITY, blockHardwareBackButton, shouldUseCloseWatcher, startHardwareBackButton };\n"], "mappings": ";AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,CAAC,IAAIC,MAAM,QAAQ,4BAA4B;AACxD,OAAO,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAMD,MAAM,CAACE,GAAG,CAAC,0BAA0B,EAAE,KAAK,CAAC,IAAIJ,GAAG,KAAKK,SAAS,IAAI,cAAc,IAAIL,GAAG;AAC/H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,uBAAuB,GAAGA,CAAA,KAAM;EAClCC,QAAQ,CAACC,gBAAgB,CAAC,YAAY,EAAE,MAAM,CAAE,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC;AACD,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EAClC,MAAMC,GAAG,GAAGH,QAAQ;EACpB,IAAII,IAAI,GAAG,KAAK;EAChB,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAID,IAAI,EAAE;MACN;IACJ;IACA,IAAIE,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAG,EAAE;IACjB,MAAMC,EAAE,GAAG,IAAIC,WAAW,CAAC,eAAe,EAAE;MACxCC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAE;QACJC,QAAQA,CAACC,QAAQ,EAAEC,OAAO,EAAE;UACxBP,QAAQ,CAACQ,IAAI,CAAC;YAAEF,QAAQ;YAAEC,OAAO;YAAEE,EAAE,EAAEV,KAAK;UAAG,CAAC,CAAC;QACrD;MACJ;IACJ,CAAC,CAAC;IACFH,GAAG,CAACc,aAAa,CAACT,EAAE,CAAC;IACrB,MAAMU,aAAa;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,WAAOC,eAAe,EAAK;QAC7C,IAAI;UACA,IAAIA,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACP,OAAO,EAAE;YAC3F,MAAMQ,MAAM,GAAGD,eAAe,CAACP,OAAO,CAACS,eAAe,CAAC;YACvD,IAAID,MAAM,IAAI,IAAI,EAAE;cAChB,MAAMA,MAAM;YAChB;UACJ;QACJ,CAAC,CACD,OAAOE,CAAC,EAAE;UACNC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;QACpB;MACJ,CAAC;MAAA,gBAZKN,aAAaA,CAAAS,EAAA;QAAA,OAAAR,IAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;IAAA,GAYlB;IACD,MAAMN,eAAe,GAAGA,CAAA,KAAM;MAC1B,IAAIhB,QAAQ,CAACuB,MAAM,GAAG,CAAC,EAAE;QACrB,IAAIC,eAAe,GAAG;UAClBlB,QAAQ,EAAEmB,MAAM,CAACC,gBAAgB;UACjCnB,OAAO,EAAEA,CAAA,KAAMhB,SAAS;UACxBkB,EAAE,EAAE,CAAC;QACT,CAAC;QACDT,QAAQ,CAAC2B,OAAO,CAAEpB,OAAO,IAAK;UAC1B,IAAIA,OAAO,CAACD,QAAQ,IAAIkB,eAAe,CAAClB,QAAQ,EAAE;YAC9CkB,eAAe,GAAGjB,OAAO;UAC7B;QACJ,CAAC,CAAC;QACFV,IAAI,GAAG,IAAI;QACXG,QAAQ,GAAGA,QAAQ,CAAC4B,MAAM,CAAErB,OAAO,IAAKA,OAAO,CAACE,EAAE,KAAKe,eAAe,CAACf,EAAE,CAAC;QAC1EE,aAAa,CAACa,eAAe,CAAC,CAACK,IAAI,CAAC,MAAOhC,IAAI,GAAG,KAAM,CAAC;MAC7D;IACJ,CAAC;IACDmB,eAAe,CAAC,CAAC;EACrB,CAAC;EACD;AACJ;AACA;AACA;AACA;AACA;EACI,IAAI3B,qBAAqB,CAAC,CAAC,EAAE;IACzB,IAAIyC,OAAO;IACX,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC3BD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,OAAO,CAAC,CAAC;MACnEF,OAAO,GAAG,IAAI5C,GAAG,CAAC+C,YAAY,CAAC,CAAC;MAChC;AACZ;AACA;AACA;AACA;AACA;AACA;MACYH,OAAO,CAACI,OAAO,GAAG,MAAM;QACpBpC,kBAAkB,CAAC,CAAC;QACpBiC,gBAAgB,CAAC,CAAC;MACtB,CAAC;IACL,CAAC;IACDA,gBAAgB,CAAC,CAAC;EACtB,CAAC,MACI;IACDnC,GAAG,CAACF,gBAAgB,CAAC,YAAY,EAAEI,kBAAkB,CAAC;EAC1D;AACJ,CAAC;AACD,MAAMqC,4BAA4B,GAAG,GAAG;AACxC,MAAMC,yBAAyB,GAAG,EAAE,CAAC,CAAC;;AAEtC,SAASA,yBAAyB,EAAED,4BAA4B,EAAE3C,uBAAuB,EAAEH,qBAAqB,EAAEM,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}