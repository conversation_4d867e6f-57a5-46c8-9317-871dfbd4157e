/* Material Design Track Page Styles */

.material-header {
  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2),
              0px 4px 5px 0px rgba(0, 0, 0, 0.14),
              0px 1px 10px 0px rgba(0, 0, 0, 0.12);
}

.material-toolbar {
  height: 64px;
  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
  color: white;
  
  .toolbar-title {
    font-size: 20px;
    font-weight: 500;
    margin-left: 16px;
  }
  
  .spacer {
    flex: 1 1 auto;
  }
  
  .spinning {
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.material-content {
  --background: #f5f5f5;
  padding: 20px;
}

.track-container {
  max-width: 1200px;
  margin: 0 auto;
}

/* Search Card */
.search-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  
  .mat-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    
    .search-title {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      color: #333;
      margin: 0;
      
      .title-icon {
        margin-right: 12px;
        font-size: 24px;
        color: #1976d2;
      }
    }
  }
}

.search-form {
  .search-row {
    display: flex;
    align-items: center;
    gap: 16px;
    
    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
    }
    
    .search-field {
      flex: 1;
      min-width: 300px;
      
      @media (max-width: 768px) {
        min-width: auto;
        width: 100%;
      }
    }
    
    .search-button,
    .clear-button {
      border-radius: 8px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      
      @media (max-width: 768px) {
        width: 100%;
      }
    }
  }
}

/* Results Card */
.results-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  
  .mat-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    
    .results-title {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 500;
      color: #333;
      margin: 0;
      
      .title-icon {
        margin-right: 12px;
        font-size: 24px;
        color: #1976d2;
      }
      
      .results-count {
        margin-left: 8px;
      }
    }
  }
  
  .table-content {
    padding: 0;
  }
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  
  p {
    margin-top: 16px;
    color: #666;
    font-size: 16px;
  }
}

/* Desktop Table View */
.desktop-view {
  display: block;
  
  @media (max-width: 768px) {
    display: none;
  }
}

.table-container {
  overflow-x: auto;
}

.complaints-table {
  width: 100%;
  
  .mat-header-cell {
    background-color: #f5f5f5;
    color: #333;
    font-weight: 600;
    font-size: 14px;
  }
  
  .mat-cell {
    font-size: 14px;
    color: #333;
  }
  
  .complaint-row {
    cursor: pointer;
    transition: background-color 0.3s ease;
    
    &:hover {
      background-color: rgba(25, 118, 210, 0.04);
    }
  }
  
  .id-cell {
    .complaint-id {
      font-family: 'Courier New', monospace;
      font-weight: 600;
      color: #1976d2;
    }
  }
  
  .status-badge,
  .priority-badge {
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .type-chip {
    background-color: #1976d2;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
  }
}

.table-paginator {
  border-top: 1px solid #e0e0e0;
}

/* Mobile Card View */
.mobile-view {
  display: none;
  
  @media (max-width: 768px) {
    display: block;
  }
}

.complaint-card {
  margin-bottom: 16px;
  border-radius: 8px;
  
  .mat-expansion-panel-header {
    padding: 16px;
    
    .card-title {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      
      .complaint-id {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #1976d2;
        font-size: 14px;
        margin-bottom: 4px;
      }
    }
    
    .card-description {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      
      span {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
    }
  }
  
  .card-content {
    padding: 16px;
    
    .info-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 12px;
      
      .label {
        font-weight: 500;
        color: #666;
        font-size: 14px;
      }
      
      .value {
        color: #333;
        font-size: 14px;
        text-align: right;
        flex: 1;
        margin-left: 16px;
      }
    }
    
    .card-divider {
      margin: 16px 0;
    }
    
    .card-actions {
      display: flex;
      justify-content: center;
      
      button {
        border-radius: 8px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }
}

/* No Data */
.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  
  .no-data-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 16px;
  }
  
  h3 {
    font-size: 20px;
    font-weight: 500;
    color: #333;
    margin: 0 0 8px 0;
  }
  
  p {
    color: #666;
    font-size: 14px;
    margin: 0 0 24px 0;
  }
  
  button {
    border-radius: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .material-content {
    padding: 12px;
  }
  
  .search-card,
  .results-card {
    border-radius: 8px;
    margin-bottom: 16px;
    
    .mat-card-header {
      padding: 16px;
      
      .search-title,
      .results-title {
        font-size: 18px;
        
        .title-icon {
          font-size: 20px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .material-toolbar .toolbar-title {
    font-size: 18px;
  }
  
  .search-card .mat-card-header .search-title,
  .results-card .mat-card-header .results-title {
    font-size: 16px;
    flex-direction: column;
    text-align: center;
    
    .title-icon {
      margin-right: 0;
      margin-bottom: 8px;
    }
  }
}
