{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-be245865.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = el => {\n  const controlEl = el;\n  let legacyControl;\n  const hasLegacyControl = () => {\n    if (legacyControl === undefined) {\n      /**\n       * Detect if developers are using the legacy form control syntax\n       * so a deprecation warning is logged. This warning can be disabled\n       * by either using the new `label` property or setting `aria-label`\n       * on the control.\n       * Alternatively, components that use a slot for the label\n       * can check to see if the component has slotted text\n       * in the light DOM.\n       */\n      const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n      const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n      // Shadow DOM form controls cannot use aria-labelledby\n      controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null;\n      const legacyItemLabel = findItemLabel(controlEl);\n      /**\n       * Developers can manually opt-out of the modern form markup\n       * by setting `legacy=\"true\"` on components.\n       */\n      legacyControl = controlEl.legacy === true || !hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null;\n    }\n    return legacyControl;\n  };\n  return {\n    hasLegacyControl\n  };\n};\nconst hasLabelSlot = controlEl => {\n  /**\n   * Components that have a named label slot\n   * also have other slots, so we need to query for\n   * anything that is explicitly passed to slot=\"label\"\n   */\n  if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n    return true;\n  }\n  /**\n   * Components that have an unnamed slot for the label\n   * have no other slots, so we can check the textContent\n   * of the element.\n   */\n  if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n    return true;\n  }\n  return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-INPUT', 'ION-TEXTAREA', 'ION-SELECT', 'ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\nexport { createLegacyFormController as c };", "map": {"version": 3, "names": ["h", "findItemLabel", "createLegacyFormController", "el", "controlEl", "legacyControl", "hasLegacyControl", "undefined", "hasLabelProp", "label", "hasLabelSlot", "hasAriaLabelAttribute", "hasAttribute", "shadowRoot", "legacyItemLabel", "legacy", "NAMED_LABEL_SLOT_COMPONENTS", "includes", "tagName", "querySelector", "UNNAMED_LABEL_SLOT_COMPONENTS", "textContent", "c"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/form-controller-21dd62b1.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { h as findItemLabel } from './helpers-be245865.js';\n\n/**\n * Creates a controller that tracks whether a form control is using the legacy or modern syntax. This should be removed when the legacy form control syntax is removed.\n *\n * @internal\n * @prop el: The Ionic form component to reference\n */\nconst createLegacyFormController = (el) => {\n    const controlEl = el;\n    let legacyControl;\n    const hasLegacyControl = () => {\n        if (legacyControl === undefined) {\n            /**\n             * Detect if developers are using the legacy form control syntax\n             * so a deprecation warning is logged. This warning can be disabled\n             * by either using the new `label` property or setting `aria-label`\n             * on the control.\n             * Alternatively, components that use a slot for the label\n             * can check to see if the component has slotted text\n             * in the light DOM.\n             */\n            const hasLabelProp = controlEl.label !== undefined || hasLabelSlot(controlEl);\n            const hasAriaLabelAttribute = controlEl.hasAttribute('aria-label') ||\n                // Shadow DOM form controls cannot use aria-labelledby\n                (controlEl.hasAttribute('aria-labelledby') && controlEl.shadowRoot === null);\n            const legacyItemLabel = findItemLabel(controlEl);\n            /**\n             * Developers can manually opt-out of the modern form markup\n             * by setting `legacy=\"true\"` on components.\n             */\n            legacyControl =\n                controlEl.legacy === true || (!hasLabelProp && !hasAriaLabelAttribute && legacyItemLabel !== null);\n        }\n        return legacyControl;\n    };\n    return { hasLegacyControl };\n};\nconst hasLabelSlot = (controlEl) => {\n    /**\n     * Components that have a named label slot\n     * also have other slots, so we need to query for\n     * anything that is explicitly passed to slot=\"label\"\n     */\n    if (NAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.querySelector('[slot=\"label\"]') !== null) {\n        return true;\n    }\n    /**\n     * Components that have an unnamed slot for the label\n     * have no other slots, so we can check the textContent\n     * of the element.\n     */\n    if (UNNAMED_LABEL_SLOT_COMPONENTS.includes(controlEl.tagName) && controlEl.textContent !== '') {\n        return true;\n    }\n    return false;\n};\nconst NAMED_LABEL_SLOT_COMPONENTS = ['ION-INPUT', 'ION-TEXTAREA', 'ION-SELECT', 'ION-RANGE'];\nconst UNNAMED_LABEL_SLOT_COMPONENTS = ['ION-TOGGLE', 'ION-CHECKBOX', 'ION-RADIO'];\n\nexport { createLegacyFormController as c };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,aAAa,QAAQ,uBAAuB;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,0BAA0B,GAAIC,EAAE,IAAK;EACvC,MAAMC,SAAS,GAAGD,EAAE;EACpB,IAAIE,aAAa;EACjB,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC3B,IAAID,aAAa,KAAKE,SAAS,EAAE;MAC7B;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACY,MAAMC,YAAY,GAAGJ,SAAS,CAACK,KAAK,KAAKF,SAAS,IAAIG,YAAY,CAACN,SAAS,CAAC;MAC7E,MAAMO,qBAAqB,GAAGP,SAAS,CAACQ,YAAY,CAAC,YAAY,CAAC;MAC9D;MACCR,SAAS,CAACQ,YAAY,CAAC,iBAAiB,CAAC,IAAIR,SAAS,CAACS,UAAU,KAAK,IAAK;MAChF,MAAMC,eAAe,GAAGb,aAAa,CAACG,SAAS,CAAC;MAChD;AACZ;AACA;AACA;MACYC,aAAa,GACTD,SAAS,CAACW,MAAM,KAAK,IAAI,IAAK,CAACP,YAAY,IAAI,CAACG,qBAAqB,IAAIG,eAAe,KAAK,IAAK;IAC1G;IACA,OAAOT,aAAa;EACxB,CAAC;EACD,OAAO;IAAEC;EAAiB,CAAC;AAC/B,CAAC;AACD,MAAMI,YAAY,GAAIN,SAAS,IAAK;EAChC;AACJ;AACA;AACA;AACA;EACI,IAAIY,2BAA2B,CAACC,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACe,aAAa,CAAC,gBAAgB,CAAC,KAAK,IAAI,EAAE;IAC/G,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,6BAA6B,CAACH,QAAQ,CAACb,SAAS,CAACc,OAAO,CAAC,IAAId,SAAS,CAACiB,WAAW,KAAK,EAAE,EAAE;IAC3F,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AAChB,CAAC;AACD,MAAML,2BAA2B,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC;AAC5F,MAAMI,6BAA6B,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;AAEjF,SAASlB,0BAA0B,IAAIoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}