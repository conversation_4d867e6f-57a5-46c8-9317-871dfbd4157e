{"ast": null, "code": "import { PreloadAllModules, RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: 'home',\n  loadChildren: () => import('./pages/home/<USER>').then(m => m.HomePageModule)\n}, {\n  path: 'login',\n  loadChildren: () => import('./pages/login/login.module').then(m => m.LoginPageModule)\n}, {\n  path: 'register',\n  loadChildren: () => import('./pages/register/register.module').then(m => m.RegisterPageModule)\n}, {\n  path: 'track',\n  loadChildren: () => import('./pages/track/track.module').then(m => m.TrackPageModule)\n}, {\n  path: 'feedback',\n  loadChildren: () => import('./pages/feedback/feedback.module').then(m => m.FeedbackPageModule)\n}, {\n  path: 'creditnote',\n  loadChildren: () => import('./pages/creditnote/creditnote.module').then(m => m.CreditnotePageModule)\n}, {\n  path: '',\n  redirectTo: 'home',\n  pathMatch: 'full'\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes, {\n        preloadingStrategy: PreloadAllModules\n      }), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["PreloadAllModules", "RouterModule", "routes", "path", "loadChildren", "then", "m", "HomePageModule", "LoginPageModule", "RegisterPageModule", "TrackPageModule", "FeedbackPageModule", "CreditnotePageModule", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "preloadingStrategy", "imports", "i1", "exports"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { PreloadAllModules, RouterModule, Routes } from '@angular/router';\n\nconst routes: Routes = [\n  {\n    path: 'home',\n    loadChildren: () => import('./pages/home/<USER>').then( m => m.HomePageModule)\n  },\n  {\n    path: 'login',\n    loadChildren: () => import('./pages/login/login.module').then( m => m.LoginPageModule)\n  },\n  {\n    path: 'register',\n    loadChildren: () => import('./pages/register/register.module').then( m => m.RegisterPageModule)\n  },\n  {\n    path: 'track',\n    loadChildren: () => import('./pages/track/track.module').then( m => m.TrackPageModule)\n  },\n  {\n    path: 'feedback',\n    loadChildren: () => import('./pages/feedback/feedback.module').then( m => m.FeedbackPageModule)\n  },\n  {\n    path: 'creditnote',\n    loadChildren: () => import('./pages/creditnote/creditnote.module').then( m => m.CreditnotePageModule)\n  },\n  {\n    path: '',\n    redirectTo: 'home',\n    pathMatch: 'full'\n  },\n];\n\n@NgModule({\n  imports: [\n    RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })\n  ],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,iBAAiB,EAAEC,YAAY,QAAgB,iBAAiB;;;AAEzE,MAAMC,MAAM,GAAW,CACrB;EACEC,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACC,cAAc;CACnF,EACD;EACEJ,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACE,eAAe;CACtF,EACD;EACEL,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACG,kBAAkB;CAC/F,EACD;EACEN,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACI,eAAe;CACtF,EACD;EACEP,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACK,kBAAkB;CAC/F,EACD;EACER,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAIA,CAAC,CAACM,oBAAoB;CACrG,EACD;EACET,IAAI,EAAE,EAAE;EACRU,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE;CACZ,CACF;AAQD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAJzBd,YAAY,CAACe,OAAO,CAACd,MAAM,EAAE;QAAEe,kBAAkB,EAAEjB;MAAiB,CAAE,CAAC,EAE/DC,YAAY;IAAA;EAAA;;;2EAEXc,gBAAgB;IAAAG,OAAA,GAAAC,EAAA,CAAAlB,YAAA;IAAAmB,OAAA,GAFjBnB,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}