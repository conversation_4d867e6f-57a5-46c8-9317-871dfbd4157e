{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { WebPlugin } from '@capacitor/core';\nexport class SplashScreenWeb extends WebPlugin {\n  show(_options) {\n    return _asyncToGenerator(function* () {\n      return undefined;\n    })();\n  }\n  hide(_options) {\n    return _asyncToGenerator(function* () {\n      return undefined;\n    })();\n  }\n}", "map": {"version": 3, "names": ["WebPlugin", "SplashScreenWeb", "show", "_options", "_asyncToGenerator", "undefined", "hide"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@capacitor/splash-screen/dist/esm/web.js"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\nexport class SplashScreenWeb extends WebPlugin {\n    async show(_options) {\n        return undefined;\n    }\n    async hide(_options) {\n        return undefined;\n    }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,iBAAiB;AAC3C,OAAO,MAAMC,eAAe,SAASD,SAAS,CAAC;EACrCE,IAAIA,CAACC,QAAQ,EAAE;IAAA,OAAAC,iBAAA;MACjB,OAAOC,SAAS;IAAC;EACrB;EACMC,IAAIA,CAACH,QAAQ,EAAE;IAAA,OAAAC,iBAAA;MACjB,OAAOC,SAAS;IAAC;EACrB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}