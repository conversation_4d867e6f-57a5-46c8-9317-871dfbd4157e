<ion-header [translucent]="true" class="material-header">
  <mat-toolbar class="material-toolbar">
    <button mat-icon-button (click)="goBack()" aria-label="Back">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <span class="toolbar-title">Track Complaints</span>
    <span class="spacer"></span>
    <button mat-icon-button (click)="refreshData()" aria-label="Refresh" [disabled]="isLoading">
      <mat-icon [class.spinning]="isLoading">refresh</mat-icon>
    </button>
  </mat-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="material-content">
  <div class="track-container">
    
    <!-- Search Section -->
    <mat-card class="search-card">
      <mat-card-header>
        <mat-card-title class="search-title">
          <mat-icon class="title-icon">search</mat-icon>
          Search Complaints
        </mat-card-title>
        <mat-card-subtitle>Enter complaint ID or invoice number to search</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <form [formGroup]="searchForm" (ngSubmit)="onSearch()" class="search-form">
          <div class="search-row">
            <mat-form-field appearance="outline" class="search-field">
              <mat-label>Search</mat-label>
              <input 
                matInput 
                formControlName="searchTerm" 
                placeholder="Enter complaint ID or invoice number">
              <mat-icon matSuffix>search</mat-icon>
            </mat-form-field>
            
            <button 
              mat-raised-button 
              color="primary" 
              type="submit"
              [disabled]="searchForm.invalid || isLoading"
              class="search-button">
              <mat-icon *ngIf="isLoading" class="spinning">refresh</mat-icon>
              <mat-icon *ngIf="!isLoading">search</mat-icon>
              Search
            </button>
            
            <button 
              mat-button 
              type="button"
              (click)="clearSearch()"
              class="clear-button">
              <mat-icon>clear</mat-icon>
              Clear
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>

    <!-- Results Section -->
    <mat-card class="results-card">
      <mat-card-header>
        <mat-card-title class="results-title">
          <mat-icon class="title-icon">list</mat-icon>
          Your Complaints
          <span class="results-count" matBadge="{{ dataSource.filteredData.length }}" matBadgeColor="primary">
          </span>
        </mat-card-title>
        <mat-card-subtitle>Click on any complaint to view detailed information</mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content class="table-content">
        <!-- Loading Spinner -->
        <div *ngIf="isLoading" class="loading-container">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading complaints...</p>
        </div>

        <!-- Desktop Table View -->
        <div class="table-container desktop-view" *ngIf="!isLoading">
          <table mat-table [dataSource]="dataSource" matSort class="complaints-table">
            
            <!-- ID Column -->
            <ng-container matColumnDef="id">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Complaint ID</th>
              <td mat-cell *matCellDef="let complaint" class="id-cell">
                <span class="complaint-id">{{ complaint.id }}</span>
              </td>
            </ng-container>

            <!-- Invoice Number Column -->
            <ng-container matColumnDef="invoiceNumber">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Invoice Number</th>
              <td mat-cell *matCellDef="let complaint">{{ complaint.invoiceNumber }}</td>
            </ng-container>

            <!-- Type Column -->
            <ng-container matColumnDef="type">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Type</th>
              <td mat-cell *matCellDef="let complaint">
                <mat-chip-list>
                  <mat-chip color="primary" selected>{{ complaint.type }}</mat-chip>
                </mat-chip-list>
              </td>
            </ng-container>

            <!-- Status Column -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
              <td mat-cell *matCellDef="let complaint">
                <ion-badge [color]="getStatusColor(complaint.status)" class="status-badge">
                  {{ complaint.status }}
                </ion-badge>
              </td>
            </ng-container>

            <!-- Priority Column -->
            <ng-container matColumnDef="priority">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Priority</th>
              <td mat-cell *matCellDef="let complaint">
                <ion-badge [color]="getPriorityColor(complaint.priority)" class="priority-badge">
                  {{ complaint.priority }}
                </ion-badge>
              </td>
            </ng-container>

            <!-- Date Created Column -->
            <ng-container matColumnDef="dateCreated">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Date Created</th>
              <td mat-cell *matCellDef="let complaint">{{ complaint.dateCreated | date:'short' }}</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let complaint">
                <button 
                  mat-icon-button 
                  color="primary" 
                  (click)="viewDetails(complaint)"
                  matTooltip="View Details">
                  <mat-icon>visibility</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;" 
                class="complaint-row"
                (click)="viewDetails(row)"></tr>
          </table>

          <mat-paginator 
            [pageSizeOptions]="[5, 10, 20]" 
            showFirstLastButtons
            class="table-paginator">
          </mat-paginator>
        </div>

        <!-- Mobile Card View -->
        <div class="mobile-view" *ngIf="!isLoading">
          <mat-expansion-panel 
            *ngFor="let complaint of dataSource.filteredData" 
            class="complaint-card">
            
            <mat-expansion-panel-header>
              <mat-panel-title>
                <div class="card-title">
                  <span class="complaint-id">{{ complaint.id }}</span>
                  <ion-badge [color]="getStatusColor(complaint.status)" class="status-badge">
                    {{ complaint.status }}
                  </ion-badge>
                </div>
              </mat-panel-title>
              <mat-panel-description>
                <div class="card-description">
                  <span>{{ complaint.type }}</span>
                  <ion-badge [color]="getPriorityColor(complaint.priority)" class="priority-badge">
                    {{ complaint.priority }}
                  </ion-badge>
                </div>
              </mat-panel-description>
            </mat-expansion-panel-header>

            <div class="card-content">
              <div class="info-row">
                <span class="label">Invoice Number:</span>
                <span class="value">{{ complaint.invoiceNumber }}</span>
              </div>
              <div class="info-row">
                <span class="label">Date Created:</span>
                <span class="value">{{ complaint.dateCreated | date:'short' }}</span>
              </div>
              <div class="info-row">
                <span class="label">Last Updated:</span>
                <span class="value">{{ complaint.lastUpdated | date:'short' }}</span>
              </div>
              <div class="info-row">
                <span class="label">Description:</span>
                <span class="value">{{ complaint.description }}</span>
              </div>
              
              <mat-divider class="card-divider"></mat-divider>
              
              <div class="card-actions">
                <button 
                  mat-raised-button 
                  color="primary" 
                  (click)="viewDetails(complaint)">
                  <mat-icon>visibility</mat-icon>
                  View Details
                </button>
              </div>
            </div>
          </mat-expansion-panel>
        </div>

        <!-- No Data Message -->
        <div *ngIf="!isLoading && dataSource.filteredData.length === 0" class="no-data">
          <mat-icon class="no-data-icon">inbox</mat-icon>
          <h3>No complaints found</h3>
          <p>Try adjusting your search criteria or create a new complaint.</p>
          <button mat-raised-button color="primary" routerLink="/register">
            <mat-icon>add</mat-icon>
            Register New Complaint
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</ion-content>
