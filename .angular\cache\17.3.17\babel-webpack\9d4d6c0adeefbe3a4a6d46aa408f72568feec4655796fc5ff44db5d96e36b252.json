{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/radio\";\nfunction RegisterPage_div_40_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 23);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_40_div_8_Template_div_click_0_listener() {\n      const type_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.selectComplaintType(type_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 24);\n    i0.ɵɵelement(2, \"ion-icon\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 26)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 27);\n    i0.ɵɵelement(9, \"ion-radio\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const type_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r2.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_3_0.value) === type_r2.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", type_r2.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(type_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(type_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", type_r2.value);\n  }\n}\nfunction RegisterPage_div_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"form\", 18)(2, \"div\", 19)(3, \"h3\");\n    i0.ɵɵtext(4, \"Select Complaint Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6, \"Choose the category that best describes your complaint\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 21);\n    i0.ɵɵtemplate(8, RegisterPage_div_40_div_8_Template, 10, 6, \"div\", 22);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintTypeForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.complaintTypes);\n  }\n}\nfunction RegisterPage_div_41_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"h4\");\n    i0.ɵɵtext(2, \"Selected Complaint Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 35);\n    i0.ɵɵelement(4, \"ion-icon\", 25);\n    i0.ɵɵelementStart(5, \"div\")(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"name\", (tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate((tmp_3_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_3_0.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_4_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_4_0.description);\n  }\n}\nfunction RegisterPage_div_41_mat_radio_button_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-radio-button\", 36)(1, \"div\", 37)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const desc_r5 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", desc_r5.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(desc_r5.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(desc_r5.description);\n  }\n}\nfunction RegisterPage_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"form\", 18)(2, \"div\", 19)(3, \"h3\");\n    i0.ɵɵtext(4, \"Select Complaint Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6, \"Choose the specific description that best matches your complaint\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, RegisterPage_div_41_div_7_Template, 10, 3, \"div\", 29);\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"h4\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 31)(12, \"mat-radio-group\", 32);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_41_Template_mat_radio_group_change_12_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onDescriptionChange($event));\n    });\n    i0.ɵɵtemplate(13, RegisterPage_div_41_mat_radio_button_13_Template, 6, 3, \"mat-radio-button\", 33);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintDescriptionForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.getSelectedComplaintType());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"Available Descriptions for \", (tmp_3_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_3_0.label, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getComplaintDescriptions());\n  }\n}\nfunction RegisterPage_div_42_mat_error_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.invoiceSearchForm, \"searchTerm\"), \" \");\n  }\n}\nfunction RegisterPage_div_42_div_27_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" Search Results (\", ctx_r2.invoiceSearchResults.length, \" found) \");\n  }\n}\nfunction RegisterPage_div_42_div_27_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" All Available Invoices (\", ctx_r2.invoiceSearchResults.length, \" total) \");\n  }\n}\nfunction RegisterPage_div_42_div_27_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 52);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_42_div_27_div_5_Template_div_click_0_listener() {\n      const invoice_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.selectInvoice(invoice_r8));\n    });\n    i0.ɵɵelementStart(1, \"div\", 53)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 54);\n    i0.ɵɵtext(5);\n    i0.ɵɵpipe(6, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 55);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 56);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const invoice_r8 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(invoice_r8.invoiceNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(6, 5, invoice_r8.invoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(invoice_r8.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", invoice_r8.zone, \" - \", invoice_r8.operatingUnit, \"\");\n  }\n}\nfunction RegisterPage_div_42_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"h4\");\n    i0.ɵɵtemplate(2, RegisterPage_div_42_div_27_span_2_Template, 2, 1, \"span\", 45)(3, RegisterPage_div_42_div_27_span_3_Template, 2, 1, \"span\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 50);\n    i0.ɵɵtemplate(5, RegisterPage_div_42_div_27_div_5_Template, 11, 8, \"div\", 51);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_2_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_2_0.value) && ((tmp_2_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_2_0.value.trim()) !== \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !((tmp_3_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_3_0.value) || ((tmp_3_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_3_0.value.trim()) === \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.invoiceSearchResults);\n  }\n}\nfunction RegisterPage_div_42_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"p\");\n    i0.ɵɵtext(2, \"No invoices found matching your search criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 58);\n    i0.ɵɵtext(4, \"Try searching with different keywords or clear the search to see all invoices.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterPage_div_42_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"h4\");\n    i0.ɵɵtext(2, \"Selected Invoice Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-card\", 60)(4, \"mat-card-content\")(5, \"div\", 61)(6, \"div\", 62)(7, \"label\");\n    i0.ɵɵtext(8, \"Invoice Number:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 62)(12, \"label\");\n    i0.ɵɵtext(13, \"Invoice Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 62)(18, \"label\");\n    i0.ɵɵtext(19, \"Customer Name:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(22, \"div\", 62)(23, \"label\");\n    i0.ɵɵtext(24, \"Customer Address:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"span\");\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 62)(28, \"label\");\n    i0.ɵɵtext(29, \"Zone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"span\");\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(32, \"div\", 62)(33, \"label\");\n    i0.ɵɵtext(34, \"Operating Unit:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\");\n    i0.ɵɵtext(36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 62)(38, \"label\");\n    i0.ɵɵtext(39, \"Organization:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"span\");\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 62)(43, \"label\");\n    i0.ɵɵtext(44, \"Bill To Location:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\");\n    i0.ɵɵtext(46);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 62)(48, \"label\");\n    i0.ɵɵtext(49, \"Ship To Location:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"span\");\n    i0.ɵɵtext(51);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(52, \"div\", 63)(53, \"button\", 64);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_42_div_29_Template_button_click_53_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearInvoiceSelection());\n    });\n    i0.ɵɵelementStart(54, \"mat-icon\");\n    i0.ɵɵtext(55, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(56, \" Clear Selection \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.invoiceNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(16, 9, ctx_r2.selectedInvoice.invoiceDate, \"dd/MM/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.customerAddress);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.zone);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.operatingUnit);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.organization);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.billToLocation);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.selectedInvoice.shipToLocation);\n  }\n}\nfunction RegisterPage_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"form\", 18)(2, \"div\", 19)(3, \"h3\");\n    i0.ɵɵtext(4, \"Search and Select Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 20);\n    i0.ɵɵtext(6, \"Search for your invoice by invoice number or customer name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 38)(8, \"h4\");\n    i0.ɵɵtext(9, \"Complaint Summary\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 39)(11, \"div\", 40)(12, \"strong\");\n    i0.ɵɵtext(13, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 40)(16, \"strong\");\n    i0.ɵɵtext(17, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"div\", 41)(20, \"mat-form-field\", 42)(21, \"mat-label\");\n    i0.ɵɵtext(22, \"Search Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"input\", 43);\n    i0.ɵɵlistener(\"input\", function RegisterPage_div_42_Template_input_input_23_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onInvoiceSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"mat-icon\", 44);\n    i0.ɵɵtext(25, \"search\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, RegisterPage_div_42_mat_error_26_Template, 2, 1, \"mat-error\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(27, RegisterPage_div_42_div_27_Template, 6, 3, \"div\", 46)(28, RegisterPage_div_42_div_28_Template, 5, 0, \"div\", 47);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(29, RegisterPage_div_42_div_29_Template, 57, 12, \"div\", 48);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.invoiceSearchForm);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_2_0.label, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r2.getSelectedComplaintDescription()) == null ? null : tmp_3_0.label, \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_4_0.invalid) && ((tmp_4_0 = ctx_r2.invoiceSearchForm.get(\"searchTerm\")) == null ? null : tmp_4_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showInvoiceResults && ctx_r2.invoiceSearchResults.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n  }\n}\nfunction RegisterPage_div_43_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 68)(1, \"h5\");\n    i0.ɵɵtext(2, \"Selected Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"strong\");\n    i0.ɵɵtext(5, \"Invoice Number:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 40)(8, \"strong\");\n    i0.ɵɵtext(9, \"Customer:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 40)(12, \"strong\");\n    i0.ɵɵtext(13, \"Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedInvoice.invoiceNumber, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.selectedInvoice.customerName, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(15, 3, ctx_r2.selectedInvoice.invoiceDate, \"dd/MM/yyyy\"), \" \");\n  }\n}\nfunction RegisterPage_div_43_mat_error_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactPersonName\"), \" \");\n  }\n}\nfunction RegisterPage_div_43_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getErrorMessage(ctx_r2.complaintDetailsForm, \"contactNumber\"), \" \");\n  }\n}\nfunction RegisterPage_div_43_div_62_div_16_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"mat-icon\", 96);\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 97)(4, \"span\", 98);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 99);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 100);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_43_div_62_div_16_div_4_Template_button_click_8_listener() {\n      const i_r14 = i0.ɵɵrestoreView(_r13).index;\n      const ctx_r2 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r2.removeFile(i_r14));\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const file_r15 = ctx.$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(file_r15.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", (file_r15.size / 1024 / 1024).toFixed(2), \" MB)\");\n  }\n}\nfunction RegisterPage_div_43_div_62_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 92)(1, \"h6\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 93);\n    i0.ɵɵtemplate(4, RegisterPage_div_43_div_62_div_16_div_4_Template, 11, 2, \"div\", 94);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Selected Files (\", ctx_r2.selectedFiles.length, \"):\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.selectedFiles);\n  }\n}\nfunction RegisterPage_div_43_div_62_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"h5\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Upload Supporting Documents \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 87)(6, \"input\", 88, 0);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_43_div_62_Template_input_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_43_div_62_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const fileInput_r12 = i0.ɵɵreference(7);\n      return i0.ɵɵresetView(fileInput_r12.click());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"cloud_upload\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11, \" Choose Files \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"p\", 90)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each) \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(16, RegisterPage_div_43_div_62_div_16_Template, 5, 2, \"div\", 91);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(16);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedFiles.length > 0);\n  }\n}\nfunction RegisterPage_div_43_mat_icon_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_43_mat_spinner_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 101);\n  }\n}\nfunction RegisterPage_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"div\", 19)(2, \"h3\");\n    i0.ɵɵtext(3, \"Final Complaint Details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 20);\n    i0.ɵɵtext(5, \"Review all information and provide contact details\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 65)(7, \"h4\")(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"assignment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Complete Complaint Summary \");\n    i0.ɵɵelementStart(11, \"span\", 66);\n    i0.ɵɵtext(12, \"FINAL REVIEW\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 67)(14, \"div\", 68)(15, \"h5\");\n    i0.ɵɵtext(16, \"Complaint Information\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 40)(18, \"strong\");\n    i0.ɵɵtext(19, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 40)(22, \"strong\");\n    i0.ɵɵtext(23, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(25, RegisterPage_div_43_div_25_Template, 16, 6, \"div\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(26, \"form\", 18)(27, \"div\", 70)(28, \"h4\")(29, \"mat-icon\");\n    i0.ɵɵtext(30, \"contact_phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(31, \" Contact Information (Required) \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 71)(33, \"div\", 72)(34, \"mat-form-field\", 73)(35, \"mat-label\");\n    i0.ɵɵtext(36, \"Contact Person Name *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(37, \"input\", 74);\n    i0.ɵɵelementStart(38, \"mat-icon\", 44);\n    i0.ɵɵtext(39, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(40, RegisterPage_div_43_mat_error_40_Template, 2, 1, \"mat-error\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"mat-form-field\", 73)(42, \"mat-label\");\n    i0.ɵɵtext(43, \"Contact Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"input\", 75);\n    i0.ɵɵelementStart(45, \"mat-icon\", 44);\n    i0.ɵɵtext(46, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, RegisterPage_div_43_mat_error_47_Template, 2, 1, \"mat-error\", 45);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"mat-form-field\", 76)(49, \"mat-label\");\n    i0.ɵɵtext(50, \"Additional Comments (Optional)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"textarea\", 77);\n    i0.ɵɵelementStart(52, \"mat-icon\", 44);\n    i0.ɵɵtext(53, \"comment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(54, \"mat-hint\");\n    i0.ɵɵtext(55, \"Optional: Add any additional information that might be helpful\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"div\", 78)(57, \"mat-checkbox\", 79)(58, \"strong\");\n    i0.ɵɵtext(59, \"Do you have complaint letters to attach?\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"p\", 80);\n    i0.ɵɵtext(61, \"Check this box if you have supporting documents, photos, or letters related to your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(62, RegisterPage_div_43_div_62_Template, 17, 1, \"div\", 81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 82)(64, \"button\", 83);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_43_Template_button_click_64_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.goBackToStep3());\n    });\n    i0.ɵɵelementStart(65, \"mat-icon\");\n    i0.ɵɵtext(66, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(67, \" Back to Invoice Selection \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"button\", 84);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_43_Template_button_click_68_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSubmitComplaint());\n    });\n    i0.ɵɵtemplate(69, RegisterPage_div_43_mat_icon_69_Template, 2, 0, \"mat-icon\", 45)(70, RegisterPage_div_43_mat_spinner_70_Template, 1, 0, \"mat-spinner\", 85);\n    i0.ɵɵtext(71);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_1_0 = ctx_r2.getSelectedComplaintType()) == null ? null : tmp_1_0.label, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r2.getSelectedComplaintDescription()) == null ? null : tmp_2_0.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.selectedInvoice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"formGroup\", ctx_r2.complaintDetailsForm);\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_5_0.invalid) && ((tmp_5_0 = ctx_r2.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_5_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r2.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r2.complaintDetailsForm.get(\"hasComplaintLetters\")) == null ? null : tmp_7_0.value);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"disabled\", ctx_r2.isLoading || !ctx_r2.complaintDetailsForm.valid);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.isLoading ? \"Submitting Complaint...\" : \"Submit Complaint\", \" \");\n  }\n}\nexport class RegisterPage {\n  constructor(formBuilder, router, loadingController, toastController) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.isLoading = false;\n    this.selectedFiles = [];\n    this.selectedInvoice = null;\n    this.invoiceSearchResults = [];\n    this.showInvoiceResults = false;\n    this.currentStep = 1;\n    this.complaintTypes = [{\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    }, {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    }, {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    }, {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    }, {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    }, {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }];\n    this.complaintDescriptions = {\n      'glass_quality': [{\n        value: 'scratches',\n        label: 'Scratches on Glass Surface',\n        description: 'Visible scratches or marks on the glass surface'\n      }, {\n        value: 'cracks',\n        label: 'Cracks or Chips',\n        description: 'Cracks, chips, or fractures in the glass'\n      }, {\n        value: 'bubbles',\n        label: 'Air Bubbles',\n        description: 'Air bubbles or inclusions within the glass'\n      }, {\n        value: 'discoloration',\n        label: 'Discoloration',\n        description: 'Color variations or discoloration in the glass'\n      }, {\n        value: 'thickness',\n        label: 'Thickness Issues',\n        description: 'Incorrect thickness or uneven glass thickness'\n      }],\n      'installation': [{\n        value: 'alignment',\n        label: 'Alignment Problems',\n        description: 'Glass not properly aligned during installation'\n      }, {\n        value: 'sealing',\n        label: 'Sealing Issues',\n        description: 'Poor sealing or gaps around the glass'\n      }, {\n        value: 'hardware',\n        label: 'Hardware Problems',\n        description: 'Issues with hinges, handles, or other hardware'\n      }, {\n        value: 'fitting',\n        label: 'Poor Fitting',\n        description: 'Glass does not fit properly in the frame'\n      }, {\n        value: 'damage_during',\n        label: 'Damage During Installation',\n        description: 'Glass damaged during the installation process'\n      }],\n      'delivery_damage': [{\n        value: 'broken_transit',\n        label: 'Broken in Transit',\n        description: 'Glass broken during transportation'\n      }, {\n        value: 'packaging',\n        label: 'Poor Packaging',\n        description: 'Inadequate packaging causing damage'\n      }, {\n        value: 'handling',\n        label: 'Rough Handling',\n        description: 'Damage due to rough handling during delivery'\n      }, {\n        value: 'delayed',\n        label: 'Delayed Delivery',\n        description: 'Delivery was significantly delayed'\n      }, {\n        value: 'wrong_item',\n        label: 'Wrong Item Delivered',\n        description: 'Incorrect glass type or specifications delivered'\n      }],\n      'measurement': [{\n        value: 'wrong_size',\n        label: 'Wrong Size',\n        description: 'Glass delivered in incorrect dimensions'\n      }, {\n        value: 'measurement_error',\n        label: 'Measurement Error',\n        description: 'Error in initial measurements taken'\n      }, {\n        value: 'specification',\n        label: 'Specification Mismatch',\n        description: 'Glass does not match ordered specifications'\n      }, {\n        value: 'template',\n        label: 'Template Issues',\n        description: 'Problems with measurement template or pattern'\n      }],\n      'service': [{\n        value: 'communication',\n        label: 'Poor Communication',\n        description: 'Lack of proper communication from service team'\n      }, {\n        value: 'response_time',\n        label: 'Slow Response Time',\n        description: 'Delayed response to queries or complaints'\n      }, {\n        value: 'unprofessional',\n        label: 'Unprofessional Behavior',\n        description: 'Unprofessional conduct by service personnel'\n      }, {\n        value: 'incomplete_work',\n        label: 'Incomplete Work',\n        description: 'Service work left incomplete or unfinished'\n      }],\n      'billing': [{\n        value: 'wrong_amount',\n        label: 'Incorrect Amount',\n        description: 'Wrong amount charged in the invoice'\n      }, {\n        value: 'missing_details',\n        label: 'Missing Details',\n        description: 'Important details missing from invoice'\n      }, {\n        value: 'duplicate',\n        label: 'Duplicate Billing',\n        description: 'Charged multiple times for the same service'\n      }, {\n        value: 'tax_error',\n        label: 'Tax Calculation Error',\n        description: 'Incorrect tax calculation or application'\n      }]\n    };\n    // Sample invoice data for demonstration\n    this.sampleInvoices = [{\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    }, {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    }, {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    }, {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai'\n    }, {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata'\n    }, {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad'\n    }, {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat'\n    }, {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi'\n    }];\n    this.createForms();\n  }\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n  selectComplaintType(type) {\n    this.complaintTypeForm.patchValue({\n      selectedType: type.value\n    });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n  onDescriptionChange(event) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice => invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase()));\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n  selectInvoice(invoice) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: invoice.invoiceNumber\n    });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: ''\n    });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n  onSubmitComplaint() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.complaintTypeForm.valid && _this.complaintDescriptionForm.valid && _this.selectedInvoice && _this.complaintDetailsForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Registering complaint...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate registration process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          const toast = yield _this.toastController.create({\n            message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n            duration: 4000,\n            color: 'success',\n            position: 'top'\n          });\n          yield toast.present();\n          // Navigate to track page\n          _this.router.navigate(['/track']);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please complete all required steps and fill in all required fields.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({\n        attachedFile: files[0]\n      });\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({\n        attachedFile: null\n      });\n    }\n  }\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n  isStepCompleted(step) {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n  getErrorMessage(form, field) {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function RegisterPage_Factory(t) {\n      return new (t || RegisterPage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.LoadingController), i0.ɵɵdirectiveInject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterPage,\n      selectors: [[\"app-register\"]],\n      decls: 44,\n      vars: 28,\n      consts: [[\"fileInput\", \"\"], [3, \"translucent\"], [\"color\", \"primary\"], [\"slot\", \"start\"], [3, \"click\"], [\"name\", \"arrow-back\"], [1, \"register-content\", 3, \"fullscreen\"], [1, \"container\"], [1, \"header-section\"], [1, \"header-content\"], [1, \"progress-section\"], [1, \"progress-steps\"], [1, \"step\"], [1, \"step-number\"], [1, \"step-label\"], [1, \"step-connector\"], [\"class\", \"step-content\", 4, \"ngIf\"], [1, \"step-content\"], [3, \"formGroup\"], [1, \"form-section\"], [1, \"section-description\"], [1, \"complaint-types-grid\"], [\"class\", \"complaint-type-card\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"complaint-type-card\", 3, \"click\"], [1, \"card-icon\"], [3, \"name\"], [1, \"card-content\"], [1, \"card-radio\"], [\"formControlName\", \"selectedType\", 3, \"value\"], [\"class\", \"selected-type-display\", 4, \"ngIf\"], [1, \"complaint-descriptions-section\"], [1, \"description-options\"], [\"formControlName\", \"selectedDescription\", 1, \"description-radio-group\", 3, \"change\"], [\"class\", \"description-option\", 3, \"value\", 4, \"ngFor\", \"ngForOf\"], [1, \"selected-type-display\"], [1, \"type-display-card\"], [1, \"description-option\", 3, \"value\"], [1, \"description-content\"], [1, \"complaint-summary-display\"], [1, \"summary-card\"], [1, \"summary-item\"], [1, \"search-section\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"formControlName\", \"searchTerm\", \"placeholder\", \"Enter invoice number, customer name, or leave empty to see all\", 3, \"input\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"class\", \"search-results\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"class\", \"selected-invoice\", 4, \"ngIf\"], [1, \"search-results\"], [1, \"invoice-list\"], [\"class\", \"invoice-item\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"invoice-item\", 3, \"click\"], [1, \"invoice-header\"], [1, \"invoice-date\"], [1, \"invoice-customer\"], [1, \"invoice-zone\"], [1, \"no-results\"], [1, \"search-hint\"], [1, \"selected-invoice\"], [1, \"invoice-details-card\"], [1, \"invoice-details-grid\"], [1, \"detail-item\"], [1, \"invoice-actions\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"complete-summary-display\"], [1, \"final-review-badge\"], [1, \"summary-sections\"], [1, \"summary-section\"], [\"class\", \"summary-section\", 4, \"ngIf\"], [1, \"contact-form-section\"], [1, \"contact-form-card\"], [1, \"form-row\"], [\"appearance\", \"outline\", 1, \"half-width\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonName\", \"placeholder\", \"Enter contact person name\"], [\"matInput\", \"\", \"formControlName\", \"contactNumber\", \"placeholder\", \"Enter 10-digit contact number\", \"type\", \"tel\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"comments\", \"rows\", \"3\", \"placeholder\", \"Any additional comments or information\"], [1, \"complaint-letters-section\"], [\"formControlName\", \"hasComplaintLetters\", \"color\", \"primary\"], [1, \"checkbox-hint\"], [\"class\", \"file-upload-section\", 4, \"ngIf\"], [1, \"step-actions\"], [\"mat-button\", \"\", 1, \"back-button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"submit-button\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [1, \"file-upload-section\"], [1, \"upload-area\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \".pdf,.jpg,.jpeg,.png,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"upload-button\", 3, \"click\"], [1, \"upload-hint\"], [\"class\", \"selected-files\", 4, \"ngIf\"], [1, \"selected-files\"], [1, \"file-list\"], [\"class\", \"file-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"file-item\"], [1, \"file-icon\"], [1, \"file-info\"], [1, \"file-name\"], [1, \"file-size\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", 1, \"remove-file\", 3, \"click\"], [\"diameter\", \"20\"]],\n      template: function RegisterPage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-header\", 1)(1, \"ion-toolbar\", 2)(2, \"ion-buttons\", 3)(3, \"ion-button\", 4);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_ion_button_click_3_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelement(4, \"ion-icon\", 5);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"ion-title\");\n          i0.ɵɵtext(6, \"Register Complaint\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(7, \"ion-content\", 6)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"h1\");\n          i0.ɵɵtext(12, \"Register New Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\");\n          i0.ɵɵtext(14, \"Follow the steps below to register your complaint\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13);\n          i0.ɵɵtext(19, \"1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 14);\n          i0.ɵɵtext(21, \"Complaint Type\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(22, \"div\", 15);\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"div\", 13);\n          i0.ɵɵtext(25, \"2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 14);\n          i0.ɵɵtext(27, \"Complaint Description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(28, \"div\", 15);\n          i0.ɵɵelementStart(29, \"div\", 12)(30, \"div\", 13);\n          i0.ɵɵtext(31, \"3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 14);\n          i0.ɵɵtext(33, \"Invoice Selection\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelement(34, \"div\", 15);\n          i0.ɵɵelementStart(35, \"div\", 12)(36, \"div\", 13);\n          i0.ɵɵtext(37, \"4\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"div\", 14);\n          i0.ɵɵtext(39, \"Complaint Details\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵtemplate(40, RegisterPage_div_40_Template, 9, 2, \"div\", 16)(41, RegisterPage_div_41_Template, 14, 4, \"div\", 16)(42, RegisterPage_div_42_Template, 30, 7, \"div\", 16)(43, RegisterPage_div_43_Template, 72, 11, \"div\", 16);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"translucent\", true);\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"fullscreen\", true);\n          i0.ɵɵadvance(10);\n          i0.ɵɵclassProp(\"active\", true)(\"completed\", ctx.isStepCompleted(1));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isStepCompleted(1))(\"completed\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isStepCompleted(2))(\"completed\", ctx.isStepCompleted(3));\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"completed\", ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.isStepCompleted(3))(\"completed\", ctx.isStepCompleted(4));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isStepCompleted(1));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(1) && !ctx.isStepCompleted(2));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(2) && !ctx.isStepCompleted(3));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(3) && !ctx.isStepCompleted(4));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.IonButton, i3.IonButtons, i3.IonContent, i3.IonHeader, i3.IonIcon, i3.IonRadio, i3.IonTitle, i3.IonToolbar, i3.RadioValueAccessor, i5.MatFormField, i5.MatLabel, i5.MatHint, i5.MatError, i5.MatSuffix, i6.MatInput, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i9.MatIcon, i10.MatCheckbox, i11.MatProgressSpinner, i12.MatRadioGroup, i12.MatRadioButton, i4.DatePipe],\n      styles: [\".register-content[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n}\\n.register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  color: #999;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.active[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: #e0e0e0;\\n  margin: 0 16px;\\n  margin-bottom: 32px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 32px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0 0 32px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 12px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card.selected[_ngcontent-%COMP%] {\\n  border-color: #1976d2;\\n  background: #e3f2fd;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]   ion-radio[_ngcontent-%COMP%] {\\n  --color: #1976d2;\\n  --color-checked: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: white;\\n  transition: all 0.3s ease;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]:hover {\\n  border-color: #ff9800;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option.mat-radio-checked[_ngcontent-%COMP%] {\\n  border-color: #ff9800;\\n  background: #fff3e0;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%] {\\n  margin-left: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #f3e5f5;\\n  border: 2px solid #9c27b0;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 22px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   .final-review-badge[_ngcontent-%COMP%] {\\n  background: #1565c0;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-left: auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 2px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"CONTACT DETAILS\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   .invoice-date[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-customer[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-zone[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p.search-hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  font-style: italic;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #666;\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e0e0e0;\\n  padding-top: 16px;\\n  text-align: right;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-size: 14px;\\n  font-weight: 500;\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"EDITABLE\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  padding: 16px;\\n  background: #f3e5f5;\\n  border: 1px solid #9c27b0;\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   .checkbox-hint[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 32px;\\n  color: #666;\\n  font-size: 13px;\\n  font-style: italic;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%] {\\n  margin: 24px 0 0 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border: 2px dashed #9c27b0;\\n  border-radius: 8px;\\n  background: #fce4ec;\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  border-color: #9c27b0;\\n  color: #7b1fa2;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(156, 39, 176, 0.04);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n  margin: 8px 0 0 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: white;\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  border: 1px solid #e1bee7;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  border-color: #9c27b0;\\n  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-size[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: space-between;\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  border-color: #ccc;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #999;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);\\n  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);\\n  transform: translateY(-1px);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  color: #999;\\n  box-shadow: none;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n    padding: 24px 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    margin: 0 8px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵlistener", "RegisterPage_div_40_div_8_Template_div_click_0_listener", "type_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "selectComplaintType", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵclassProp", "tmp_3_0", "complaintTypeForm", "get", "value", "ɵɵadvance", "ɵɵproperty", "icon", "ɵɵtextInterpolate", "label", "description", "ɵɵtemplate", "RegisterPage_div_40_div_8_Template", "complaintTypes", "tmp_2_0", "getSelectedComplaintType", "tmp_4_0", "desc_r5", "RegisterPage_div_41_div_7_Template", "RegisterPage_div_41_Template_mat_radio_group_change_12_listener", "$event", "_r4", "onDescriptionChange", "RegisterPage_div_41_mat_radio_button_13_Template", "complaintDescriptionForm", "ɵɵtextInterpolate1", "getComplaintDescriptions", "getErrorMessage", "invoiceSearchForm", "invoiceSearchResults", "length", "RegisterPage_div_42_div_27_div_5_Template_div_click_0_listener", "invoice_r8", "_r7", "selectInvoice", "invoiceNumber", "ɵɵpipeBind2", "invoiceDate", "customerName", "ɵɵtextInterpolate2", "zone", "operatingUnit", "RegisterPage_div_42_div_27_span_2_Template", "RegisterPage_div_42_div_27_span_3_Template", "RegisterPage_div_42_div_27_div_5_Template", "trim", "RegisterPage_div_42_div_29_Template_button_click_53_listener", "_r9", "clearInvoiceSelection", "selectedInvoice", "customerAddress", "organization", "billToLocation", "shipToLocation", "RegisterPage_div_42_Template_input_input_23_listener", "_r6", "onInvoiceSearch", "RegisterPage_div_42_mat_error_26_Template", "RegisterPage_div_42_div_27_Template", "RegisterPage_div_42_div_28_Template", "RegisterPage_div_42_div_29_Template", "getSelectedComplaintDescription", "invalid", "touched", "showInvoiceResults", "complaintDetailsForm", "RegisterPage_div_43_div_62_div_16_div_4_Template_button_click_8_listener", "i_r14", "_r13", "index", "removeFile", "file_r15", "name", "size", "toFixed", "RegisterPage_div_43_div_62_div_16_div_4_Template", "selectedFiles", "RegisterPage_div_43_div_62_Template_input_change_6_listener", "_r11", "onFileSelected", "RegisterPage_div_43_div_62_Template_button_click_8_listener", "fileInput_r12", "ɵɵreference", "click", "RegisterPage_div_43_div_62_div_16_Template", "RegisterPage_div_43_div_25_Template", "RegisterPage_div_43_mat_error_40_Template", "RegisterPage_div_43_mat_error_47_Template", "RegisterPage_div_43_div_62_Template", "RegisterPage_div_43_Template_button_click_64_listener", "_r10", "goBackToStep3", "RegisterPage_div_43_Template_button_click_68_listener", "onSubmitComplaint", "RegisterPage_div_43_mat_icon_69_Template", "RegisterPage_div_43_mat_spinner_70_Template", "tmp_1_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "isLoading", "valid", "RegisterPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "currentStep", "complaintDescriptions", "sampleInvoices", "Date", "createForms", "ngOnInit", "showAllInvoices", "group", "selectedType", "required", "selectedDescription", "searchTerm", "contactPersonName", "<PERSON><PERSON><PERSON><PERSON>", "contactNumber", "pattern", "comments", "hasComplaintLetters", "attachedFile", "type", "patchValue", "setTimeout", "goToStep2", "selected<PERSON><PERSON><PERSON>", "descriptions", "find", "desc", "event", "goToStep3", "goToStep4", "goBackToStep1", "goBackToStep2", "filter", "invoice", "toLowerCase", "includes", "_this", "_asyncToGenerator", "loading", "create", "message", "duration", "present", "dismiss", "toast", "now", "color", "position", "navigate", "files", "target", "Array", "from", "splice", "isStepCompleted", "step", "form", "field", "control", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "goBack", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "LoadingController", "ToastController", "selectors", "decls", "vars", "consts", "template", "RegisterPage_Template", "rf", "ctx", "RegisterPage_Template_ion_button_click_3_listener", "RegisterPage_div_40_Template", "RegisterPage_div_41_Template", "RegisterPage_div_42_Template", "RegisterPage_div_43_Template"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\n\nexport interface InvoiceData {\n  invoiceNumber: string;\n  invoiceDate: Date;\n  customerName: string;\n  customerAddress: string;\n  zone: string;\n  operatingUnit: string;\n  organization: string;\n  billToLocation: string;\n  shipToLocation: string;\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss'],\n})\nexport class RegisterPage implements OnInit {\n\n  complaintTypeForm!: FormGroup;\n  complaintDescriptionForm!: FormGroup;\n  invoiceSearchForm!: FormGroup;\n  complaintDetailsForm!: FormGroup;\n  isLoading = false;\n  selectedFiles: File[] = [];\n  selectedInvoice: InvoiceData | null = null;\n  invoiceSearchResults: InvoiceData[] = [];\n  showInvoiceResults = false;\n  currentStep = 1;\n\n  complaintTypes = [\n    {\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond-outline',\n      description: 'Scratches, cracks, or defects in glass products'\n    },\n    {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'construct-outline',\n      description: 'Issues during glass installation process'\n    },\n    {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'car-outline',\n      description: 'Damage during delivery or transportation'\n    },\n    {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'resize-outline',\n      description: 'Incorrect measurements or sizing problems'\n    },\n    {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'people-outline',\n      description: 'Customer service or support issues'\n    },\n    {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'document-text-outline',\n      description: 'Invoice, billing, or documentation issues'\n    }\n  ];\n\n  complaintDescriptions: { [key: string]: any[] } = {\n    'glass_quality': [\n      { value: 'scratches', label: 'Scratches on Glass Surface', description: 'Visible scratches or marks on the glass surface' },\n      { value: 'cracks', label: 'Cracks or Chips', description: 'Cracks, chips, or fractures in the glass' },\n      { value: 'bubbles', label: 'Air Bubbles', description: 'Air bubbles or inclusions within the glass' },\n      { value: 'discoloration', label: 'Discoloration', description: 'Color variations or discoloration in the glass' },\n      { value: 'thickness', label: 'Thickness Issues', description: 'Incorrect thickness or uneven glass thickness' }\n    ],\n    'installation': [\n      { value: 'alignment', label: 'Alignment Problems', description: 'Glass not properly aligned during installation' },\n      { value: 'sealing', label: 'Sealing Issues', description: 'Poor sealing or gaps around the glass' },\n      { value: 'hardware', label: 'Hardware Problems', description: 'Issues with hinges, handles, or other hardware' },\n      { value: 'fitting', label: 'Poor Fitting', description: 'Glass does not fit properly in the frame' },\n      { value: 'damage_during', label: 'Damage During Installation', description: 'Glass damaged during the installation process' }\n    ],\n    'delivery_damage': [\n      { value: 'broken_transit', label: 'Broken in Transit', description: 'Glass broken during transportation' },\n      { value: 'packaging', label: 'Poor Packaging', description: 'Inadequate packaging causing damage' },\n      { value: 'handling', label: 'Rough Handling', description: 'Damage due to rough handling during delivery' },\n      { value: 'delayed', label: 'Delayed Delivery', description: 'Delivery was significantly delayed' },\n      { value: 'wrong_item', label: 'Wrong Item Delivered', description: 'Incorrect glass type or specifications delivered' }\n    ],\n    'measurement': [\n      { value: 'wrong_size', label: 'Wrong Size', description: 'Glass delivered in incorrect dimensions' },\n      { value: 'measurement_error', label: 'Measurement Error', description: 'Error in initial measurements taken' },\n      { value: 'specification', label: 'Specification Mismatch', description: 'Glass does not match ordered specifications' },\n      { value: 'template', label: 'Template Issues', description: 'Problems with measurement template or pattern' }\n    ],\n    'service': [\n      { value: 'communication', label: 'Poor Communication', description: 'Lack of proper communication from service team' },\n      { value: 'response_time', label: 'Slow Response Time', description: 'Delayed response to queries or complaints' },\n      { value: 'unprofessional', label: 'Unprofessional Behavior', description: 'Unprofessional conduct by service personnel' },\n      { value: 'incomplete_work', label: 'Incomplete Work', description: 'Service work left incomplete or unfinished' }\n    ],\n    'billing': [\n      { value: 'wrong_amount', label: 'Incorrect Amount', description: 'Wrong amount charged in the invoice' },\n      { value: 'missing_details', label: 'Missing Details', description: 'Important details missing from invoice' },\n      { value: 'duplicate', label: 'Duplicate Billing', description: 'Charged multiple times for the same service' },\n      { value: 'tax_error', label: 'Tax Calculation Error', description: 'Incorrect tax calculation or application' }\n    ]\n  };\n\n  // Sample invoice data for demonstration\n  sampleInvoices: InvoiceData[] = [\n    {\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP'\n    },\n    {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab'\n    },\n    {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore'\n    },\n    {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai'\n    },\n    {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata'\n    },\n    {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad'\n    },\n    {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat'\n    },\n    {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi'\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController\n  ) {\n    this.createForms();\n  }\n\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n\n  selectComplaintType(type: any) {\n    this.complaintTypeForm.patchValue({ selectedType: type.value });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n\n  onDescriptionChange(event: any) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice =>\n          invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n\n  selectInvoice(invoice: InvoiceData) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({ searchTerm: invoice.invoiceNumber });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({ searchTerm: '' });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n\n  async onSubmitComplaint() {\n    if (this.complaintTypeForm.valid && this.complaintDescriptionForm.valid && this.selectedInvoice && this.complaintDetailsForm.valid) {\n      this.isLoading = true;\n\n      const loading = await this.loadingController.create({\n        message: 'Registering complaint...',\n        duration: 3000\n      });\n\n      await loading.present();\n\n      // Simulate registration process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n\n        const toast = await this.toastController.create({\n          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n          duration: 4000,\n          color: 'success',\n          position: 'top'\n        });\n        await toast.present();\n\n        // Navigate to track page\n        this.router.navigate(['/track']);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please complete all required steps and fill in all required fields.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({ attachedFile: files[0] });\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({ attachedFile: null });\n    }\n  }\n\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n\n  getErrorMessage(form: FormGroup, field: string): string {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n}\n", "<ion-header [translucent]=\"true\">\n  <ion-toolbar color=\"primary\">\n    <ion-buttons slot=\"start\">\n      <ion-button (click)=\"goBack()\">\n        <ion-icon name=\"arrow-back\"></ion-icon>\n      </ion-button>\n    </ion-buttons>\n    <ion-title>Register Complaint</ion-title>\n  </ion-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\" class=\"register-content\">\n  <div class=\"container\">\n    <!-- Header Section -->\n    <div class=\"header-section\">\n      <div class=\"header-content\">\n        <h1>Register New Complaint</h1>\n        <p>Follow the steps below to register your complaint</p>\n      </div>\n    </div>\n\n    <!-- Progress Indicator -->\n    <div class=\"progress-section\">\n      <div class=\"progress-steps\">\n        <div class=\"step\" [class.active]=\"true\" [class.completed]=\"isStepCompleted(1)\">\n          <div class=\"step-number\">1</div>\n          <div class=\"step-label\">Complaint Type</div>\n        </div>\n        <div class=\"step-connector\" [class.completed]=\"isStepCompleted(1)\"></div>\n        <div class=\"step\" [class.active]=\"isStepCompleted(1)\" [class.completed]=\"isStepCompleted(2)\">\n          <div class=\"step-number\">2</div>\n          <div class=\"step-label\">Complaint Description</div>\n        </div>\n        <div class=\"step-connector\" [class.completed]=\"isStepCompleted(2)\"></div>\n        <div class=\"step\" [class.active]=\"isStepCompleted(2)\" [class.completed]=\"isStepCompleted(3)\">\n          <div class=\"step-number\">3</div>\n          <div class=\"step-label\">Invoice Selection</div>\n        </div>\n        <div class=\"step-connector\" [class.completed]=\"isStepCompleted(3)\"></div>\n        <div class=\"step\" [class.active]=\"isStepCompleted(3)\" [class.completed]=\"isStepCompleted(4)\">\n          <div class=\"step-number\">4</div>\n          <div class=\"step-label\">Complaint Details</div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Step 1: Complaint Type Selection -->\n    <div class=\"step-content\" *ngIf=\"!isStepCompleted(1)\">\n      <form [formGroup]=\"complaintTypeForm\">\n        <div class=\"form-section\">\n          <h3>Select Complaint Type</h3>\n          <p class=\"section-description\">Choose the category that best describes your complaint</p>\n\n          <div class=\"complaint-types-grid\">\n            <div class=\"complaint-type-card\"\n                 *ngFor=\"let type of complaintTypes\"\n                 [class.selected]=\"complaintTypeForm.get('selectedType')?.value === type.value\"\n                 (click)=\"selectComplaintType(type)\">\n              <div class=\"card-icon\">\n                <ion-icon [name]=\"type.icon\"></ion-icon>\n              </div>\n              <div class=\"card-content\">\n                <h4>{{ type.label }}</h4>\n                <p>{{ type.description }}</p>\n              </div>\n              <div class=\"card-radio\">\n                <ion-radio [value]=\"type.value\" formControlName=\"selectedType\"></ion-radio>\n              </div>\n            </div>\n          </div>\n\n\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 2: Complaint Description Selection -->\n    <div class=\"step-content\" *ngIf=\"isStepCompleted(1) && !isStepCompleted(2)\">\n      <form [formGroup]=\"complaintDescriptionForm\">\n        <div class=\"form-section\">\n          <h3>Select Complaint Description</h3>\n          <p class=\"section-description\">Choose the specific description that best matches your complaint</p>\n\n          <!-- Selected Complaint Type Display -->\n          <div class=\"selected-type-display\" *ngIf=\"getSelectedComplaintType()\">\n            <h4>Selected Complaint Type</h4>\n            <div class=\"type-display-card\">\n              <ion-icon [name]=\"getSelectedComplaintType()?.icon\"></ion-icon>\n              <div>\n                <strong>{{ getSelectedComplaintType()?.label }}</strong>\n                <p>{{ getSelectedComplaintType()?.description }}</p>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"complaint-descriptions-section\">\n            <h4>Available Descriptions for {{ getSelectedComplaintType()?.label }}</h4>\n            <div class=\"description-options\">\n              <mat-radio-group formControlName=\"selectedDescription\" class=\"description-radio-group\" (change)=\"onDescriptionChange($event)\">\n                <mat-radio-button\n                  *ngFor=\"let desc of getComplaintDescriptions()\"\n                  [value]=\"desc.value\"\n                  class=\"description-option\">\n                  <div class=\"description-content\">\n                    <strong>{{ desc.label }}</strong>\n                    <p>{{ desc.description }}</p>\n                  </div>\n                </mat-radio-button>\n              </mat-radio-group>\n            </div>\n          </div>\n\n\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 3: Invoice Selection -->\n    <div class=\"step-content\" *ngIf=\"isStepCompleted(2) && !isStepCompleted(3)\">\n      <form [formGroup]=\"invoiceSearchForm\">\n        <div class=\"form-section\">\n          <h3>Search and Select Invoice</h3>\n          <p class=\"section-description\">Search for your invoice by invoice number or customer name</p>\n\n          <!-- Complaint Summary Display -->\n          <div class=\"complaint-summary-display\">\n            <h4>Complaint Summary</h4>\n            <div class=\"summary-card\">\n              <div class=\"summary-item\">\n                <strong>Type:</strong> {{ getSelectedComplaintType()?.label }}\n              </div>\n              <div class=\"summary-item\">\n                <strong>Description:</strong> {{ getSelectedComplaintDescription()?.label }}\n              </div>\n            </div>\n          </div>\n\n          <div class=\"search-section\">\n            <mat-form-field appearance=\"outline\" class=\"search-field\">\n              <mat-label>Search Invoice</mat-label>\n              <input matInput formControlName=\"searchTerm\" placeholder=\"Enter invoice number, customer name, or leave empty to see all\" (input)=\"onInvoiceSearch()\">\n              <mat-icon matSuffix>search</mat-icon>\n              <mat-error *ngIf=\"invoiceSearchForm.get('searchTerm')?.invalid && invoiceSearchForm.get('searchTerm')?.touched\">\n                {{ getErrorMessage(invoiceSearchForm, 'searchTerm') }}\n              </mat-error>\n            </mat-form-field>\n\n            <div class=\"search-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length > 0\">\n              <h4>\n                <span *ngIf=\"invoiceSearchForm.get('searchTerm')?.value && invoiceSearchForm.get('searchTerm')?.value.trim() !== ''\">\n                  Search Results ({{ invoiceSearchResults.length }} found)\n                </span>\n                <span *ngIf=\"!invoiceSearchForm.get('searchTerm')?.value || invoiceSearchForm.get('searchTerm')?.value.trim() === ''\">\n                  All Available Invoices ({{ invoiceSearchResults.length }} total)\n                </span>\n              </h4>\n              <div class=\"invoice-list\">\n                <div class=\"invoice-item\" *ngFor=\"let invoice of invoiceSearchResults\" (click)=\"selectInvoice(invoice)\">\n                  <div class=\"invoice-header\">\n                    <strong>{{ invoice.invoiceNumber }}</strong>\n                    <span class=\"invoice-date\">{{ invoice.invoiceDate | date:'dd/MM/yyyy' }}</span>\n                  </div>\n                  <div class=\"invoice-customer\">{{ invoice.customerName }}</div>\n                  <div class=\"invoice-zone\">{{ invoice.zone }} - {{ invoice.operatingUnit }}</div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"no-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length === 0\">\n              <p>No invoices found matching your search criteria.</p>\n              <p class=\"search-hint\">Try searching with different keywords or clear the search to see all invoices.</p>\n            </div>\n          </div>\n\n          <!-- Selected Invoice Display -->\n          <div class=\"selected-invoice\" *ngIf=\"selectedInvoice\">\n            <h4>Selected Invoice Details</h4>\n            <mat-card class=\"invoice-details-card\">\n              <mat-card-content>\n                <div class=\"invoice-details-grid\">\n                  <div class=\"detail-item\">\n                    <label>Invoice Number:</label>\n                    <span>{{ selectedInvoice.invoiceNumber }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Invoice Date:</label>\n                    <span>{{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Customer Name:</label>\n                    <span>{{ selectedInvoice.customerName }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Customer Address:</label>\n                    <span>{{ selectedInvoice.customerAddress }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Zone:</label>\n                    <span>{{ selectedInvoice.zone }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Operating Unit:</label>\n                    <span>{{ selectedInvoice.operatingUnit }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Organization:</label>\n                    <span>{{ selectedInvoice.organization }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Bill To Location:</label>\n                    <span>{{ selectedInvoice.billToLocation }}</span>\n                  </div>\n                  <div class=\"detail-item\">\n                    <label>Ship To Location:</label>\n                    <span>{{ selectedInvoice.shipToLocation }}</span>\n                  </div>\n                </div>\n                <div class=\"invoice-actions\">\n                  <button mat-button color=\"warn\" (click)=\"clearInvoiceSelection()\">\n                    <mat-icon>clear</mat-icon>\n                    Clear Selection\n                  </button>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n\n\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 4: Complaint Details -->\n    <div class=\"step-content\" *ngIf=\"isStepCompleted(3) && !isStepCompleted(4)\">\n      <div class=\"form-section\">\n        <h3>Final Complaint Details</h3>\n        <p class=\"section-description\">Review all information and provide contact details</p>\n\n        <!-- Complete Summary Display -->\n        <div class=\"complete-summary-display\">\n          <h4>\n            <mat-icon>assignment</mat-icon>\n            Complete Complaint Summary\n            <span class=\"final-review-badge\">FINAL REVIEW</span>\n          </h4>\n          <div class=\"summary-sections\">\n            <div class=\"summary-section\">\n              <h5>Complaint Information</h5>\n              <div class=\"summary-item\">\n                <strong>Type:</strong> {{ getSelectedComplaintType()?.label }}\n              </div>\n              <div class=\"summary-item\">\n                <strong>Description:</strong> {{ getSelectedComplaintDescription()?.label }}\n              </div>\n            </div>\n\n            <div class=\"summary-section\" *ngIf=\"selectedInvoice\">\n              <h5>Selected Invoice</h5>\n              <div class=\"summary-item\">\n                <strong>Invoice Number:</strong> {{ selectedInvoice.invoiceNumber }}\n              </div>\n              <div class=\"summary-item\">\n                <strong>Customer:</strong> {{ selectedInvoice.customerName }}\n              </div>\n              <div class=\"summary-item\">\n                <strong>Date:</strong> {{ selectedInvoice.invoiceDate | date:'dd/MM/yyyy' }}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Contact Details Form -->\n        <form [formGroup]=\"complaintDetailsForm\">\n          <div class=\"contact-form-section\">\n            <h4>\n              <mat-icon>contact_phone</mat-icon>\n              Contact Information (Required)\n            </h4>\n\n            <div class=\"contact-form-card\">\n              <div class=\"form-row\">\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Contact Person Name *</mat-label>\n                  <input matInput formControlName=\"contactPersonName\" placeholder=\"Enter contact person name\">\n                  <mat-icon matSuffix>person</mat-icon>\n                  <mat-error *ngIf=\"complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched\">\n                    {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}\n                  </mat-error>\n                </mat-form-field>\n\n                <mat-form-field appearance=\"outline\" class=\"half-width\">\n                  <mat-label>Contact Number *</mat-label>\n                  <input matInput formControlName=\"contactNumber\" placeholder=\"Enter 10-digit contact number\" type=\"tel\">\n                  <mat-icon matSuffix>phone</mat-icon>\n                  <mat-error *ngIf=\"complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched\">\n                    {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}\n                  </mat-error>\n                </mat-form-field>\n              </div>\n\n              <mat-form-field appearance=\"outline\" class=\"full-width\">\n                <mat-label>Additional Comments (Optional)</mat-label>\n                <textarea matInput formControlName=\"comments\" rows=\"3\" placeholder=\"Any additional comments or information\"></textarea>\n                <mat-icon matSuffix>comment</mat-icon>\n                <mat-hint>Optional: Add any additional information that might be helpful</mat-hint>\n              </mat-form-field>\n\n              <!-- Complaint Letters Checkbox -->\n              <div class=\"complaint-letters-section\">\n                <mat-checkbox formControlName=\"hasComplaintLetters\" color=\"primary\">\n                  <strong>Do you have complaint letters to attach?</strong>\n                </mat-checkbox>\n                <p class=\"checkbox-hint\">Check this box if you have supporting documents, photos, or letters related to your complaint</p>\n              </div>\n\n              <!-- File Upload Section -->\n              <div class=\"file-upload-section\" *ngIf=\"complaintDetailsForm.get('hasComplaintLetters')?.value\">\n                <h5>\n                  <mat-icon>attach_file</mat-icon>\n                  Upload Supporting Documents\n                </h5>\n                <div class=\"upload-area\">\n                  <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" multiple accept=\".pdf,.jpg,.jpeg,.png,.doc,.docx\" style=\"display: none;\">\n\n                  <button mat-stroked-button color=\"primary\" (click)=\"fileInput.click()\" class=\"upload-button\">\n                    <mat-icon>cloud_upload</mat-icon>\n                    Choose Files\n                  </button>\n                  <p class=\"upload-hint\">\n                    <mat-icon>info</mat-icon>\n                    Supported formats: PDF, JPG, PNG, DOC, DOCX (Max 5MB each)\n                  </p>\n                </div>\n\n                <div class=\"selected-files\" *ngIf=\"selectedFiles.length > 0\">\n                  <h6>Selected Files ({{ selectedFiles.length }}):</h6>\n                  <div class=\"file-list\">\n                    <div class=\"file-item\" *ngFor=\"let file of selectedFiles; let i = index\">\n                      <mat-icon class=\"file-icon\">description</mat-icon>\n                      <div class=\"file-info\">\n                        <span class=\"file-name\">{{ file.name }}</span>\n                        <span class=\"file-size\">({{ (file.size / 1024 / 1024).toFixed(2) }} MB)</span>\n                      </div>\n                      <button mat-icon-button (click)=\"removeFile(i)\" class=\"remove-file\" color=\"warn\">\n                        <mat-icon>delete</mat-icon>\n                      </button>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <div class=\"step-actions\">\n            <button mat-button (click)=\"goBackToStep3()\" class=\"back-button\">\n              <mat-icon>arrow_back</mat-icon>\n              Back to Invoice Selection\n            </button>\n            <button mat-raised-button color=\"primary\" (click)=\"onSubmitComplaint()\" [disabled]=\"isLoading || !complaintDetailsForm.valid\" class=\"submit-button\">\n              <mat-icon *ngIf=\"!isLoading\">send</mat-icon>\n              <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n              {{ isLoading ? 'Submitting Complaint...' : 'Submit Complaint' }}\n            </button>\n          </div>\n        </form>\n      </div>\n\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;ICqDvDC,EAAA,CAAAC,cAAA,cAGyC;IAApCD,EAAA,CAAAE,UAAA,mBAAAC,wDAAA;MAAA,MAAAC,OAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,mBAAA,CAAAP,OAAA,CAAyB;IAAA,EAAC;IACtCJ,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAY,SAAA,mBAAwC;IAC1CZ,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAc,MAAA,GAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IACzBb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,GAAsB;IAC3Bd,EAD2B,CAAAa,YAAA,EAAI,EACzB;IACNb,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAY,SAAA,oBAA2E;IAE/EZ,EADE,CAAAa,YAAA,EAAM,EACF;;;;;;IAZDb,EAAA,CAAAe,WAAA,eAAAC,OAAA,GAAAR,MAAA,CAAAS,iBAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,KAAA,MAAAf,OAAA,CAAAe,KAAA,CAA8E;IAGrEnB,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAqB,UAAA,SAAAjB,OAAA,CAAAkB,IAAA,CAAkB;IAGxBtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAuB,iBAAA,CAAAnB,OAAA,CAAAoB,KAAA,CAAgB;IACjBxB,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAuB,iBAAA,CAAAnB,OAAA,CAAAqB,WAAA,CAAsB;IAGdzB,EAAA,CAAAoB,SAAA,GAAoB;IAApBpB,EAAA,CAAAqB,UAAA,UAAAjB,OAAA,CAAAe,KAAA,CAAoB;;;;;IAhBrCnB,EAHN,CAAAC,cAAA,cAAsD,eACd,cACV,SACpB;IAAAD,EAAA,CAAAc,MAAA,4BAAqB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAC9Bb,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAc,MAAA,6DAAsD;IAAAd,EAAA,CAAAa,YAAA,EAAI;IAEzFb,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAA0B,UAAA,IAAAC,kCAAA,mBAGyC;IAiBjD3B,EALM,CAAAa,YAAA,EAAM,EAGF,EACD,EACH;;;;IA1BEb,EAAA,CAAAoB,SAAA,EAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAAS,iBAAA,CAA+B;IAOTjB,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAAoB,cAAA,CAAiB;;;;;IA8BvC5B,EADF,CAAAC,cAAA,cAAsE,SAChE;IAAAD,EAAA,CAAAc,MAAA,8BAAuB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAChCb,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAY,SAAA,mBAA+D;IAE7DZ,EADF,CAAAC,cAAA,UAAK,aACK;IAAAD,EAAA,CAAAc,MAAA,GAAuC;IAAAd,EAAA,CAAAa,YAAA,EAAS;IACxDb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,GAA6C;IAGtDd,EAHsD,CAAAa,YAAA,EAAI,EAChD,EACF,EACF;;;;;;;IANQb,EAAA,CAAAoB,SAAA,GAAyC;IAAzCpB,EAAA,CAAAqB,UAAA,UAAAQ,OAAA,GAAArB,MAAA,CAAAsB,wBAAA,qBAAAD,OAAA,CAAAP,IAAA,CAAyC;IAEzCtB,EAAA,CAAAoB,SAAA,GAAuC;IAAvCpB,EAAA,CAAAuB,iBAAA,EAAAP,OAAA,GAAAR,MAAA,CAAAsB,wBAAA,qBAAAd,OAAA,CAAAQ,KAAA,CAAuC;IAC5CxB,EAAA,CAAAoB,SAAA,GAA6C;IAA7CpB,EAAA,CAAAuB,iBAAA,EAAAQ,OAAA,GAAAvB,MAAA,CAAAsB,wBAAA,qBAAAC,OAAA,CAAAN,WAAA,CAA6C;;;;;IAc5CzB,EALJ,CAAAC,cAAA,2BAG6B,cACM,aACvB;IAAAD,EAAA,CAAAc,MAAA,GAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAS;IACjCb,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAc,MAAA,GAAsB;IAE7Bd,EAF6B,CAAAa,YAAA,EAAI,EACzB,EACW;;;;IANjBb,EAAA,CAAAqB,UAAA,UAAAW,OAAA,CAAAb,KAAA,CAAoB;IAGVnB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAuB,iBAAA,CAAAS,OAAA,CAAAR,KAAA,CAAgB;IACrBxB,EAAA,CAAAoB,SAAA,GAAsB;IAAtBpB,EAAA,CAAAuB,iBAAA,CAAAS,OAAA,CAAAP,WAAA,CAAsB;;;;;;IAzBnCzB,EAHN,CAAAC,cAAA,cAA4E,eAC7B,cACjB,SACpB;IAAAD,EAAA,CAAAc,MAAA,mCAA4B;IAAAd,EAAA,CAAAa,YAAA,EAAK;IACrCb,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAc,MAAA,uEAAgE;IAAAd,EAAA,CAAAa,YAAA,EAAI;IAGnGb,EAAA,CAAA0B,UAAA,IAAAO,kCAAA,mBAAsE;IAYpEjC,EADF,CAAAC,cAAA,cAA4C,SACtC;IAAAD,EAAA,CAAAc,MAAA,IAAkE;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAEzEb,EADF,CAAAC,cAAA,eAAiC,2BAC+F;IAAvCD,EAAA,CAAAE,UAAA,oBAAAgC,gEAAAC,MAAA;MAAAnC,EAAA,CAAAK,aAAA,CAAA+B,GAAA;MAAA,MAAA5B,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAA6B,mBAAA,CAAAF,MAAA,CAA2B;IAAA,EAAC;IAC3HnC,EAAA,CAAA0B,UAAA,KAAAY,gDAAA,+BAG6B;IAazCtC,EAPU,CAAAa,YAAA,EAAkB,EACd,EACF,EAGF,EACD,EACH;;;;;IArCEb,EAAA,CAAAoB,SAAA,EAAsC;IAAtCpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAA+B,wBAAA,CAAsC;IAMJvC,EAAA,CAAAoB,SAAA,GAAgC;IAAhCpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAsB,wBAAA,GAAgC;IAY9D9B,EAAA,CAAAoB,SAAA,GAAkE;IAAlEpB,EAAA,CAAAwC,kBAAA,iCAAAxB,OAAA,GAAAR,MAAA,CAAAsB,wBAAA,qBAAAd,OAAA,CAAAQ,KAAA,KAAkE;IAI/CxB,EAAA,CAAAoB,SAAA,GAA6B;IAA7BpB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAAiC,wBAAA,GAA6B;;;;;IA0ClDzC,EAAA,CAAAC,cAAA,gBAAgH;IAC9GD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IADVb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAwC,kBAAA,MAAAhC,MAAA,CAAAkC,eAAA,CAAAlC,MAAA,CAAAmC,iBAAA,qBACF;;;;;IAKE3C,EAAA,CAAAC,cAAA,WAAqH;IACnHD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAO;;;;IADLb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAwC,kBAAA,sBAAAhC,MAAA,CAAAoC,oBAAA,CAAAC,MAAA,aACF;;;;;IACA7C,EAAA,CAAAC,cAAA,WAAsH;IACpHD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAO;;;;IADLb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAwC,kBAAA,8BAAAhC,MAAA,CAAAoC,oBAAA,CAAAC,MAAA,aACF;;;;;;IAGA7C,EAAA,CAAAC,cAAA,cAAwG;IAAjCD,EAAA,CAAAE,UAAA,mBAAA4C,+DAAA;MAAA,MAAAC,UAAA,GAAA/C,EAAA,CAAAK,aAAA,CAAA2C,GAAA,EAAAzC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAyC,aAAA,CAAAF,UAAA,CAAsB;IAAA,EAAC;IAEnG/C,EADF,CAAAC,cAAA,cAA4B,aAClB;IAAAD,EAAA,CAAAc,MAAA,GAA2B;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAC5Cb,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAc,MAAA,GAA6C;;IAC1Ed,EAD0E,CAAAa,YAAA,EAAO,EAC3E;IACNb,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAc,MAAA,GAA0B;IAAAd,EAAA,CAAAa,YAAA,EAAM;IAC9Db,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAc,MAAA,IAAgD;IAC5Ed,EAD4E,CAAAa,YAAA,EAAM,EAC5E;;;;IALMb,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAAuB,iBAAA,CAAAwB,UAAA,CAAAG,aAAA,CAA2B;IACRlD,EAAA,CAAAoB,SAAA,GAA6C;IAA7CpB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAmD,WAAA,OAAAJ,UAAA,CAAAK,WAAA,gBAA6C;IAE5CpD,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAuB,iBAAA,CAAAwB,UAAA,CAAAM,YAAA,CAA0B;IAC9BrD,EAAA,CAAAoB,SAAA,GAAgD;IAAhDpB,EAAA,CAAAsD,kBAAA,KAAAP,UAAA,CAAAQ,IAAA,SAAAR,UAAA,CAAAS,aAAA,KAAgD;;;;;IAf9ExD,EADF,CAAAC,cAAA,cAA0F,SACpF;IAIFD,EAHA,CAAA0B,UAAA,IAAA+B,0CAAA,mBAAqH,IAAAC,0CAAA,mBAGC;IAGxH1D,EAAA,CAAAa,YAAA,EAAK;IACLb,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAA0B,UAAA,IAAAiC,yCAAA,mBAAwG;IAS5G3D,EADE,CAAAa,YAAA,EAAM,EACF;;;;;;IAjBKb,EAAA,CAAAoB,SAAA,GAA4G;IAA5GpB,EAAA,CAAAqB,UAAA,WAAAQ,OAAA,GAAArB,MAAA,CAAAmC,iBAAA,CAAAzB,GAAA,iCAAAW,OAAA,CAAAV,KAAA,OAAAU,OAAA,GAAArB,MAAA,CAAAmC,iBAAA,CAAAzB,GAAA,iCAAAW,OAAA,CAAAV,KAAA,CAAAyC,IAAA,WAA4G;IAG5G5D,EAAA,CAAAoB,SAAA,EAA6G;IAA7GpB,EAAA,CAAAqB,UAAA,YAAAL,OAAA,GAAAR,MAAA,CAAAmC,iBAAA,CAAAzB,GAAA,iCAAAF,OAAA,CAAAG,KAAA,OAAAH,OAAA,GAAAR,MAAA,CAAAmC,iBAAA,CAAAzB,GAAA,iCAAAF,OAAA,CAAAG,KAAA,CAAAyC,IAAA,WAA6G;IAKtE5D,EAAA,CAAAoB,SAAA,GAAuB;IAAvBpB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAAoC,oBAAA,CAAuB;;;;;IAYvE5C,EADF,CAAAC,cAAA,cAAwF,QACnF;IAAAD,EAAA,CAAAc,MAAA,uDAAgD;IAAAd,EAAA,CAAAa,YAAA,EAAI;IACvDb,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAc,MAAA,qFAA8E;IACvGd,EADuG,CAAAa,YAAA,EAAI,EACrG;;;;;;IAKNb,EADF,CAAAC,cAAA,cAAsD,SAChD;IAAAD,EAAA,CAAAc,MAAA,+BAAwB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAKzBb,EAJR,CAAAC,cAAA,mBAAuC,uBACnB,cACkB,cACP,YAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAe;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC9Bb,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAc,MAAA,IAAmC;IAC3Cd,EAD2C,CAAAa,YAAA,EAAO,EAC5C;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,qBAAa;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC5Bb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAqD;;IAC7Dd,EAD6D,CAAAa,YAAA,EAAO,EAC9D;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC7Bb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAkC;IAC1Cd,EAD0C,CAAAa,YAAA,EAAO,EAC3C;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,yBAAiB;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAChCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAqC;IAC7Cd,EAD6C,CAAAa,YAAA,EAAO,EAC9C;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IACpBb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAA0B;IAClCd,EADkC,CAAAa,YAAA,EAAO,EACnC;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,uBAAe;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC9Bb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAmC;IAC3Cd,EAD2C,CAAAa,YAAA,EAAO,EAC5C;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,qBAAa;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAC5Bb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAkC;IAC1Cd,EAD0C,CAAAa,YAAA,EAAO,EAC3C;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,yBAAiB;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAChCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAoC;IAC5Cd,EAD4C,CAAAa,YAAA,EAAO,EAC7C;IAEJb,EADF,CAAAC,cAAA,eAAyB,aAChB;IAAAD,EAAA,CAAAc,MAAA,yBAAiB;IAAAd,EAAA,CAAAa,YAAA,EAAQ;IAChCb,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAc,MAAA,IAAoC;IAE9Cd,EAF8C,CAAAa,YAAA,EAAO,EAC7C,EACF;IAEJb,EADF,CAAAC,cAAA,eAA6B,kBACuC;IAAlCD,EAAA,CAAAE,UAAA,mBAAA2D,6DAAA;MAAA7D,EAAA,CAAAK,aAAA,CAAAyD,GAAA;MAAA,MAAAtD,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAuD,qBAAA,EAAuB;IAAA,EAAC;IAC/D/D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC1Bb,EAAA,CAAAc,MAAA,yBACF;IAIRd,EAJQ,CAAAa,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;IA3CUb,EAAA,CAAAoB,SAAA,IAAmC;IAAnCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAd,aAAA,CAAmC;IAInClD,EAAA,CAAAoB,SAAA,GAAqD;IAArDpB,EAAA,CAAAuB,iBAAA,CAAAvB,EAAA,CAAAmD,WAAA,QAAA3C,MAAA,CAAAwD,eAAA,CAAAZ,WAAA,gBAAqD;IAIrDpD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAX,YAAA,CAAkC;IAIlCrD,EAAA,CAAAoB,SAAA,GAAqC;IAArCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAC,eAAA,CAAqC;IAIrCjE,EAAA,CAAAoB,SAAA,GAA0B;IAA1BpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAT,IAAA,CAA0B;IAI1BvD,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAR,aAAA,CAAmC;IAInCxD,EAAA,CAAAoB,SAAA,GAAkC;IAAlCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAE,YAAA,CAAkC;IAIlClE,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAG,cAAA,CAAoC;IAIpCnE,EAAA,CAAAoB,SAAA,GAAoC;IAApCpB,EAAA,CAAAuB,iBAAA,CAAAf,MAAA,CAAAwD,eAAA,CAAAI,cAAA,CAAoC;;;;;;IA7FpDpE,EAHN,CAAAC,cAAA,cAA4E,eACpC,cACV,SACpB;IAAAD,EAAA,CAAAc,MAAA,gCAAyB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAClCb,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAc,MAAA,iEAA0D;IAAAd,EAAA,CAAAa,YAAA,EAAI;IAI3Fb,EADF,CAAAC,cAAA,cAAuC,SACjC;IAAAD,EAAA,CAAAc,MAAA,wBAAiB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAGtBb,EAFJ,CAAAC,cAAA,eAA0B,eACE,cAChB;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAc,MAAA,IACzB;IAAAd,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,eAA0B,cAChB;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAc,MAAA,IAChC;IAEJd,EAFI,CAAAa,YAAA,EAAM,EACF,EACF;IAIFb,EAFJ,CAAAC,cAAA,eAA4B,0BACgC,iBAC7C;IAAAD,EAAA,CAAAc,MAAA,sBAAc;IAAAd,EAAA,CAAAa,YAAA,EAAY;IACrCb,EAAA,CAAAC,cAAA,iBAAsJ;IAA5BD,EAAA,CAAAE,UAAA,mBAAAmE,qDAAA;MAAArE,EAAA,CAAAK,aAAA,CAAAiE,GAAA;MAAA,MAAA9D,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+D,eAAA,EAAiB;IAAA,EAAC;IAArJvE,EAAA,CAAAa,YAAA,EAAsJ;IACtJb,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACrCb,EAAA,CAAA0B,UAAA,KAAA8C,yCAAA,wBAAgH;IAGlHxE,EAAA,CAAAa,YAAA,EAAiB;IAuBjBb,EArBA,CAAA0B,UAAA,KAAA+C,mCAAA,kBAA0F,KAAAC,mCAAA,kBAqBF;IAI1F1E,EAAA,CAAAa,YAAA,EAAM;IAGNb,EAAA,CAAA0B,UAAA,KAAAiD,mCAAA,oBAAsD;IAuD5D3E,EAFI,CAAAa,YAAA,EAAM,EACD,EACH;;;;;;;IA/GEb,EAAA,CAAAoB,SAAA,EAA+B;IAA/BpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAAmC,iBAAA,CAA+B;IAUJ3C,EAAA,CAAAoB,SAAA,IACzB;IADyBpB,EAAA,CAAAwC,kBAAA,OAAAX,OAAA,GAAArB,MAAA,CAAAsB,wBAAA,qBAAAD,OAAA,CAAAL,KAAA,MACzB;IAEgCxB,EAAA,CAAAoB,SAAA,GAChC;IADgCpB,EAAA,CAAAwC,kBAAA,OAAAxB,OAAA,GAAAR,MAAA,CAAAoE,+BAAA,qBAAA5D,OAAA,CAAAQ,KAAA,MAChC;IASYxB,EAAA,CAAAoB,SAAA,GAAkG;IAAlGpB,EAAA,CAAAqB,UAAA,WAAAU,OAAA,GAAAvB,MAAA,CAAAmC,iBAAA,CAAAzB,GAAA,iCAAAa,OAAA,CAAA8C,OAAA,OAAA9C,OAAA,GAAAvB,MAAA,CAAAmC,iBAAA,CAAAzB,GAAA,iCAAAa,OAAA,CAAA+C,OAAA,EAAkG;IAKnF9E,EAAA,CAAAoB,SAAA,EAA2D;IAA3DpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAuE,kBAAA,IAAAvE,MAAA,CAAAoC,oBAAA,CAAAC,MAAA,KAA2D;IAqB/D7C,EAAA,CAAAoB,SAAA,EAA6D;IAA7DpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAuE,kBAAA,IAAAvE,MAAA,CAAAoC,oBAAA,CAAAC,MAAA,OAA6D;IAOzD7C,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAwD,eAAA,CAAqB;;;;;IAkFhDhE,EADF,CAAAC,cAAA,cAAqD,SAC/C;IAAAD,EAAA,CAAAc,MAAA,uBAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAEvBb,EADF,CAAAC,cAAA,cAA0B,aAChB;IAAAD,EAAA,CAAAc,MAAA,sBAAe;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAc,MAAA,GACnC;IAAAd,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,cAA0B,aAChB;IAAAD,EAAA,CAAAc,MAAA,gBAAS;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAc,MAAA,IAC7B;IAAAd,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,eAA0B,cAChB;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAc,MAAA,IACzB;;IACFd,EADE,CAAAa,YAAA,EAAM,EACF;;;;IAR+Bb,EAAA,CAAAoB,SAAA,GACnC;IADmCpB,EAAA,CAAAwC,kBAAA,MAAAhC,MAAA,CAAAwD,eAAA,CAAAd,aAAA,MACnC;IAE6BlD,EAAA,CAAAoB,SAAA,GAC7B;IAD6BpB,EAAA,CAAAwC,kBAAA,MAAAhC,MAAA,CAAAwD,eAAA,CAAAX,YAAA,MAC7B;IAEyBrD,EAAA,CAAAoB,SAAA,GACzB;IADyBpB,EAAA,CAAAwC,kBAAA,MAAAxC,EAAA,CAAAmD,WAAA,QAAA3C,MAAA,CAAAwD,eAAA,CAAAZ,WAAA,qBACzB;;;;;IAmBIpD,EAAA,CAAAC,cAAA,gBAAoI;IAClID,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IADVb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAwC,kBAAA,MAAAhC,MAAA,CAAAkC,eAAA,CAAAlC,MAAA,CAAAwE,oBAAA,4BACF;;;;;IAOAhF,EAAA,CAAAC,cAAA,gBAA4H;IAC1HD,EAAA,CAAAc,MAAA,GACF;IAAAd,EAAA,CAAAa,YAAA,EAAY;;;;IADVb,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAwC,kBAAA,MAAAhC,MAAA,CAAAkC,eAAA,CAAAlC,MAAA,CAAAwE,oBAAA,wBACF;;;;;;IA0CIhF,EADF,CAAAC,cAAA,cAAyE,mBAC3C;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAEhDb,EADF,CAAAC,cAAA,cAAuB,eACG;IAAAD,EAAA,CAAAc,MAAA,GAAe;IAAAd,EAAA,CAAAa,YAAA,EAAO;IAC9Cb,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAc,MAAA,GAA+C;IACzEd,EADyE,CAAAa,YAAA,EAAO,EAC1E;IACNb,EAAA,CAAAC,cAAA,kBAAiF;IAAzDD,EAAA,CAAAE,UAAA,mBAAA+E,yEAAA;MAAA,MAAAC,KAAA,GAAAlF,EAAA,CAAAK,aAAA,CAAA8E,IAAA,EAAAC,KAAA;MAAA,MAAA5E,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6E,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IAC7ClF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAEpBd,EAFoB,CAAAa,YAAA,EAAW,EACpB,EACL;;;;IANsBb,EAAA,CAAAoB,SAAA,GAAe;IAAfpB,EAAA,CAAAuB,iBAAA,CAAA+D,QAAA,CAAAC,IAAA,CAAe;IACfvF,EAAA,CAAAoB,SAAA,GAA+C;IAA/CpB,EAAA,CAAAwC,kBAAA,OAAA8C,QAAA,CAAAE,IAAA,gBAAAC,OAAA,YAA+C;;;;;IAN7EzF,EADF,CAAAC,cAAA,cAA6D,SACvD;IAAAD,EAAA,CAAAc,MAAA,GAA4C;IAAAd,EAAA,CAAAa,YAAA,EAAK;IACrDb,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAA0B,UAAA,IAAAgE,gDAAA,mBAAyE;IAW7E1F,EADE,CAAAa,YAAA,EAAM,EACF;;;;IAbAb,EAAA,CAAAoB,SAAA,GAA4C;IAA5CpB,EAAA,CAAAwC,kBAAA,qBAAAhC,MAAA,CAAAmF,aAAA,CAAA9C,MAAA,OAA4C;IAEN7C,EAAA,CAAAoB,SAAA,GAAkB;IAAlBpB,EAAA,CAAAqB,UAAA,YAAAb,MAAA,CAAAmF,aAAA,CAAkB;;;;;;IAnB5D3F,EAFJ,CAAAC,cAAA,cAAgG,SAC1F,eACQ;IAAAD,EAAA,CAAAc,MAAA,kBAAW;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAChCb,EAAA,CAAAc,MAAA,oCACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAEHb,EADF,CAAAC,cAAA,cAAyB,mBACkH;IAA3GD,EAAA,CAAAE,UAAA,oBAAA0F,4DAAAzD,MAAA;MAAAnC,EAAA,CAAAK,aAAA,CAAAwF,IAAA;MAAA,MAAArF,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAsF,cAAA,CAAA3D,MAAA,CAAsB;IAAA,EAAC;IAA/DnC,EAAA,CAAAa,YAAA,EAAyI;IAEzIb,EAAA,CAAAC,cAAA,iBAA6F;IAAlDD,EAAA,CAAAE,UAAA,mBAAA6F,4DAAA;MAAA/F,EAAA,CAAAK,aAAA,CAAAwF,IAAA;MAAA,MAAAG,aAAA,GAAAhG,EAAA,CAAAiG,WAAA;MAAA,OAAAjG,EAAA,CAAAU,WAAA,CAASsF,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IACpElG,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACjCb,EAAA,CAAAc,MAAA,sBACF;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAEPb,EADF,CAAAC,cAAA,aAAuB,gBACX;IAAAD,EAAA,CAAAc,MAAA,YAAI;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACzBb,EAAA,CAAAc,MAAA,oEACF;IACFd,EADE,CAAAa,YAAA,EAAI,EACA;IAENb,EAAA,CAAA0B,UAAA,KAAAyE,0CAAA,kBAA6D;IAe/DnG,EAAA,CAAAa,YAAA,EAAM;;;;IAfyBb,EAAA,CAAAoB,SAAA,IAA8B;IAA9BpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAmF,aAAA,CAAA9C,MAAA,KAA8B;;;;;IAyB7D7C,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAc,MAAA,WAAI;IAAAd,EAAA,CAAAa,YAAA,EAAW;;;;;IAC5Cb,EAAA,CAAAY,SAAA,uBAA2D;;;;;;IA7HjEZ,EAFJ,CAAAC,cAAA,cAA4E,cAChD,SACpB;IAAAD,EAAA,CAAAc,MAAA,8BAAuB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAChCb,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAc,MAAA,yDAAkD;IAAAd,EAAA,CAAAa,YAAA,EAAI;IAKjFb,EAFJ,CAAAC,cAAA,cAAsC,SAChC,eACQ;IAAAD,EAAA,CAAAc,MAAA,iBAAU;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC/Bb,EAAA,CAAAc,MAAA,oCACA;IAAAd,EAAA,CAAAC,cAAA,gBAAiC;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAC/Cd,EAD+C,CAAAa,YAAA,EAAO,EACjD;IAGDb,EAFJ,CAAAC,cAAA,eAA8B,eACC,UACvB;IAAAD,EAAA,CAAAc,MAAA,6BAAqB;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAE5Bb,EADF,CAAAC,cAAA,eAA0B,cAChB;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAc,MAAA,IACzB;IAAAd,EAAA,CAAAa,YAAA,EAAM;IAEJb,EADF,CAAAC,cAAA,eAA0B,cAChB;IAAAD,EAAA,CAAAc,MAAA,oBAAY;IAAAd,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAc,MAAA,IAChC;IACFd,EADE,CAAAa,YAAA,EAAM,EACF;IAENb,EAAA,CAAA0B,UAAA,KAAA0E,mCAAA,mBAAqD;IAazDpG,EADE,CAAAa,YAAA,EAAM,EACF;IAMAb,EAHN,CAAAC,cAAA,gBAAyC,eACL,UAC5B,gBACQ;IAAAD,EAAA,CAAAc,MAAA,qBAAa;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAClCb,EAAA,CAAAc,MAAA,wCACF;IAAAd,EAAA,CAAAa,YAAA,EAAK;IAKCb,EAHN,CAAAC,cAAA,eAA+B,eACP,0BACoC,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,6BAAqB;IAAAd,EAAA,CAAAa,YAAA,EAAY;IAC5Cb,EAAA,CAAAY,SAAA,iBAA4F;IAC5FZ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,cAAM;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACrCb,EAAA,CAAA0B,UAAA,KAAA2E,yCAAA,wBAAoI;IAGtIrG,EAAA,CAAAa,YAAA,EAAiB;IAGfb,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,wBAAgB;IAAAd,EAAA,CAAAa,YAAA,EAAY;IACvCb,EAAA,CAAAY,SAAA,iBAAuG;IACvGZ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,aAAK;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACpCb,EAAA,CAAA0B,UAAA,KAAA4E,yCAAA,wBAA4H;IAIhItG,EADE,CAAAa,YAAA,EAAiB,EACb;IAGJb,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAc,MAAA,sCAA8B;IAAAd,EAAA,CAAAa,YAAA,EAAY;IACrDb,EAAA,CAAAY,SAAA,oBAAuH;IACvHZ,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAc,MAAA,eAAO;IAAAd,EAAA,CAAAa,YAAA,EAAW;IACtCb,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,sEAA8D;IAC1Ed,EAD0E,CAAAa,YAAA,EAAW,EACpE;IAKbb,EAFJ,CAAAC,cAAA,eAAuC,wBAC+B,cAC1D;IAAAD,EAAA,CAAAc,MAAA,gDAAwC;IAClDd,EADkD,CAAAa,YAAA,EAAS,EAC5C;IACfb,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAc,MAAA,qGAA6F;IACxHd,EADwH,CAAAa,YAAA,EAAI,EACtH;IAGNb,EAAA,CAAA0B,UAAA,KAAA6E,mCAAA,mBAAgG;IAmCpGvG,EADE,CAAAa,YAAA,EAAM,EACF;IAGJb,EADF,CAAAC,cAAA,eAA0B,kBACyC;IAA9CD,EAAA,CAAAE,UAAA,mBAAAsG,sDAAA;MAAAxG,EAAA,CAAAK,aAAA,CAAAoG,IAAA;MAAA,MAAAjG,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAkG,aAAA,EAAe;IAAA,EAAC;IAC1C1G,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAc,MAAA,kBAAU;IAAAd,EAAA,CAAAa,YAAA,EAAW;IAC/Bb,EAAA,CAAAc,MAAA,mCACF;IAAAd,EAAA,CAAAa,YAAA,EAAS;IACTb,EAAA,CAAAC,cAAA,kBAAoJ;IAA1GD,EAAA,CAAAE,UAAA,mBAAAyG,sDAAA;MAAA3G,EAAA,CAAAK,aAAA,CAAAoG,IAAA;MAAA,MAAAjG,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAoG,iBAAA,EAAmB;IAAA,EAAC;IAErE5G,EADA,CAAA0B,UAAA,KAAAmF,wCAAA,uBAA6B,KAAAC,2CAAA,0BACgB;IAC7C9G,EAAA,CAAAc,MAAA,IACF;IAGNd,EAHM,CAAAa,YAAA,EAAS,EACL,EACD,EACH,EApIoE;;;;;;;;;IAgBzCb,EAAA,CAAAoB,SAAA,IACzB;IADyBpB,EAAA,CAAAwC,kBAAA,OAAAuE,OAAA,GAAAvG,MAAA,CAAAsB,wBAAA,qBAAAiF,OAAA,CAAAvF,KAAA,MACzB;IAEgCxB,EAAA,CAAAoB,SAAA,GAChC;IADgCpB,EAAA,CAAAwC,kBAAA,OAAAX,OAAA,GAAArB,MAAA,CAAAoE,+BAAA,qBAAA/C,OAAA,CAAAL,KAAA,MAChC;IAG4BxB,EAAA,CAAAoB,SAAA,EAAqB;IAArBpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAAwD,eAAA,CAAqB;IAgBjDhE,EAAA,CAAAoB,SAAA,EAAkC;IAAlCpB,EAAA,CAAAqB,UAAA,cAAAb,MAAA,CAAAwE,oBAAA,CAAkC;IAalBhF,EAAA,CAAAoB,SAAA,IAAsH;IAAtHpB,EAAA,CAAAqB,UAAA,WAAA2F,OAAA,GAAAxG,MAAA,CAAAwE,oBAAA,CAAA9D,GAAA,wCAAA8F,OAAA,CAAAnC,OAAA,OAAAmC,OAAA,GAAAxG,MAAA,CAAAwE,oBAAA,CAAA9D,GAAA,wCAAA8F,OAAA,CAAAlC,OAAA,EAAsH;IAStH9E,EAAA,CAAAoB,SAAA,GAA8G;IAA9GpB,EAAA,CAAAqB,UAAA,WAAA4F,OAAA,GAAAzG,MAAA,CAAAwE,oBAAA,CAAA9D,GAAA,oCAAA+F,OAAA,CAAApC,OAAA,OAAAoC,OAAA,GAAAzG,MAAA,CAAAwE,oBAAA,CAAA9D,GAAA,oCAAA+F,OAAA,CAAAnC,OAAA,EAA8G;IAsB5F9E,EAAA,CAAAoB,SAAA,IAA4D;IAA5DpB,EAAA,CAAAqB,UAAA,UAAA6F,OAAA,GAAA1G,MAAA,CAAAwE,oBAAA,CAAA9D,GAAA,0CAAAgG,OAAA,CAAA/F,KAAA,CAA4D;IA0CxBnB,EAAA,CAAAoB,SAAA,GAAqD;IAArDpB,EAAA,CAAAqB,UAAA,aAAAb,MAAA,CAAA2G,SAAA,KAAA3G,MAAA,CAAAwE,oBAAA,CAAAoC,KAAA,CAAqD;IAChHpH,EAAA,CAAAoB,SAAA,EAAgB;IAAhBpB,EAAA,CAAAqB,UAAA,UAAAb,MAAA,CAAA2G,SAAA,CAAgB;IACbnH,EAAA,CAAAoB,SAAA,EAAe;IAAfpB,EAAA,CAAAqB,UAAA,SAAAb,MAAA,CAAA2G,SAAA,CAAe;IAC7BnH,EAAA,CAAAoB,SAAA,EACF;IADEpB,EAAA,CAAAwC,kBAAA,MAAAhC,MAAA,CAAA2G,SAAA,uDACF;;;ADpVZ,OAAM,MAAOE,YAAY;EA0LvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IAxLzB,KAAAP,SAAS,GAAG,KAAK;IACjB,KAAAxB,aAAa,GAAW,EAAE;IAC1B,KAAA3B,eAAe,GAAuB,IAAI;IAC1C,KAAApB,oBAAoB,GAAkB,EAAE;IACxC,KAAAmC,kBAAkB,GAAG,KAAK;IAC1B,KAAA4C,WAAW,GAAG,CAAC;IAEf,KAAA/F,cAAc,GAAG,CACf;MACET,KAAK,EAAE,eAAe;MACtBK,KAAK,EAAE,sBAAsB;MAC7BF,IAAI,EAAE,iBAAiB;MACvBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,cAAc;MACrBK,KAAK,EAAE,uBAAuB;MAC9BF,IAAI,EAAE,mBAAmB;MACzBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,iBAAiB;MACxBK,KAAK,EAAE,2BAA2B;MAClCF,IAAI,EAAE,aAAa;MACnBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,aAAa;MACpBK,KAAK,EAAE,oBAAoB;MAC3BF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,SAAS;MAChBK,KAAK,EAAE,iBAAiB;MACxBF,IAAI,EAAE,gBAAgB;MACtBG,WAAW,EAAE;KACd,EACD;MACEN,KAAK,EAAE,SAAS;MAChBK,KAAK,EAAE,yBAAyB;MAChCF,IAAI,EAAE,uBAAuB;MAC7BG,WAAW,EAAE;KACd,CACF;IAED,KAAAmG,qBAAqB,GAA6B;MAChD,eAAe,EAAE,CACf;QAAEzG,KAAK,EAAE,WAAW;QAAEK,KAAK,EAAE,4BAA4B;QAAEC,WAAW,EAAE;MAAiD,CAAE,EAC3H;QAAEN,KAAK,EAAE,QAAQ;QAAEK,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA0C,CAAE,EACtG;QAAEN,KAAK,EAAE,SAAS;QAAEK,KAAK,EAAE,aAAa;QAAEC,WAAW,EAAE;MAA4C,CAAE,EACrG;QAAEN,KAAK,EAAE,eAAe;QAAEK,KAAK,EAAE,eAAe;QAAEC,WAAW,EAAE;MAAgD,CAAE,EACjH;QAAEN,KAAK,EAAE,WAAW;QAAEK,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAChH;MACD,cAAc,EAAE,CACd;QAAEN,KAAK,EAAE,WAAW;QAAEK,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EAClH;QAAEN,KAAK,EAAE,SAAS;QAAEK,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAAuC,CAAE,EACnG;QAAEN,KAAK,EAAE,UAAU;QAAEK,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EAChH;QAAEN,KAAK,EAAE,SAAS;QAAEK,KAAK,EAAE,cAAc;QAAEC,WAAW,EAAE;MAA0C,CAAE,EACpG;QAAEN,KAAK,EAAE,eAAe;QAAEK,KAAK,EAAE,4BAA4B;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAC9H;MACD,iBAAiB,EAAE,CACjB;QAAEN,KAAK,EAAE,gBAAgB;QAAEK,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAoC,CAAE,EAC1G;QAAEN,KAAK,EAAE,WAAW;QAAEK,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EACnG;QAAEN,KAAK,EAAE,UAAU;QAAEK,KAAK,EAAE,gBAAgB;QAAEC,WAAW,EAAE;MAA8C,CAAE,EAC3G;QAAEN,KAAK,EAAE,SAAS;QAAEK,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAAoC,CAAE,EAClG;QAAEN,KAAK,EAAE,YAAY;QAAEK,KAAK,EAAE,sBAAsB;QAAEC,WAAW,EAAE;MAAkD,CAAE,CACxH;MACD,aAAa,EAAE,CACb;QAAEN,KAAK,EAAE,YAAY;QAAEK,KAAK,EAAE,YAAY;QAAEC,WAAW,EAAE;MAAyC,CAAE,EACpG;QAAEN,KAAK,EAAE,mBAAmB;QAAEK,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EAC9G;QAAEN,KAAK,EAAE,eAAe;QAAEK,KAAK,EAAE,wBAAwB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EACvH;QAAEN,KAAK,EAAE,UAAU;QAAEK,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA+C,CAAE,CAC9G;MACD,SAAS,EAAE,CACT;QAAEN,KAAK,EAAE,eAAe;QAAEK,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAAgD,CAAE,EACtH;QAAEN,KAAK,EAAE,eAAe;QAAEK,KAAK,EAAE,oBAAoB;QAAEC,WAAW,EAAE;MAA2C,CAAE,EACjH;QAAEN,KAAK,EAAE,gBAAgB;QAAEK,KAAK,EAAE,yBAAyB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EACzH;QAAEN,KAAK,EAAE,iBAAiB;QAAEK,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAA4C,CAAE,CAClH;MACD,SAAS,EAAE,CACT;QAAEN,KAAK,EAAE,cAAc;QAAEK,KAAK,EAAE,kBAAkB;QAAEC,WAAW,EAAE;MAAqC,CAAE,EACxG;QAAEN,KAAK,EAAE,iBAAiB;QAAEK,KAAK,EAAE,iBAAiB;QAAEC,WAAW,EAAE;MAAwC,CAAE,EAC7G;QAAEN,KAAK,EAAE,WAAW;QAAEK,KAAK,EAAE,mBAAmB;QAAEC,WAAW,EAAE;MAA6C,CAAE,EAC9G;QAAEN,KAAK,EAAE,WAAW;QAAEK,KAAK,EAAE,uBAAuB;QAAEC,WAAW,EAAE;MAA0C,CAAE;KAElH;IAED;IACA,KAAAoG,cAAc,GAAkB,CAC9B;MACE3E,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,uBAAuB;MACrCY,eAAe,EAAE,yDAAyD;MAC1EV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,uBAAuB;MACvCC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,wBAAwB;MACtCY,eAAe,EAAE,mDAAmD;MACpEV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,uBAAuB;MACtCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,8BAA8B;MAC9CC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,wBAAwB;MACtCY,eAAe,EAAE,oDAAoD;MACrEV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,6BAA6B;MAC3CY,eAAe,EAAE,0DAA0D;MAC3EV,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,mBAAmB;MAClCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,+BAA+B;MAC/CC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,kBAAkB;MAChCY,eAAe,EAAE,0CAA0C;MAC3DV,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,oBAAoB;MACnCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,gCAAgC;MAChDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,oBAAoB;MAClCY,eAAe,EAAE,wDAAwD;MACzEV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,sBAAsB;MACrCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,mBAAmB;MACjCY,eAAe,EAAE,2CAA2C;MAC5DV,IAAI,EAAE,WAAW;MACjBC,aAAa,EAAE,sBAAsB;MACrCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,kCAAkC;MAClDC,cAAc,EAAE;KACjB,EACD;MACElB,aAAa,EAAE,cAAc;MAC7BE,WAAW,EAAE,IAAI0E,IAAI,CAAC,YAAY,CAAC;MACnCzE,YAAY,EAAE,wBAAwB;MACtCY,eAAe,EAAE,0DAA0D;MAC3EV,IAAI,EAAE,YAAY;MAClBC,aAAa,EAAE,kBAAkB;MACjCU,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,yBAAyB;MACzCC,cAAc,EAAE;KACjB,CACF;IAQC,IAAI,CAAC2D,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAF,WAAWA,CAAA;IACT,IAAI,CAAC9G,iBAAiB,GAAG,IAAI,CAACsG,WAAW,CAACW,KAAK,CAAC;MAC9CC,YAAY,EAAE,CAAC,EAAE,EAAEpI,UAAU,CAACqI,QAAQ;KACvC,CAAC;IAEF,IAAI,CAAC7F,wBAAwB,GAAG,IAAI,CAACgF,WAAW,CAACW,KAAK,CAAC;MACrDG,mBAAmB,EAAE,CAAC,EAAE,EAAEtI,UAAU,CAACqI,QAAQ;KAC9C,CAAC;IAEF,IAAI,CAACzF,iBAAiB,GAAG,IAAI,CAAC4E,WAAW,CAACW,KAAK,CAAC;MAC9CI,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;KAClB,CAAC;IAEF,IAAI,CAACtD,oBAAoB,GAAG,IAAI,CAACuC,WAAW,CAACW,KAAK,CAAC;MACjDK,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAACxI,UAAU,CAACqI,QAAQ,EAAErI,UAAU,CAACyI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC1I,UAAU,CAACqI,QAAQ,EAAErI,UAAU,CAAC2I,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAC7EC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,YAAY,EAAE,CAAC,IAAI;KACpB,CAAC;EACJ;EAEAlI,mBAAmBA,CAACmI,IAAS;IAC3B,IAAI,CAAC7H,iBAAiB,CAAC8H,UAAU,CAAC;MAAEZ,YAAY,EAAEW,IAAI,CAAC3H;IAAK,CAAE,CAAC;IAC/D;IACA6H,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAxG,wBAAwBA,CAAA;IACtB,MAAM0F,YAAY,GAAG,IAAI,CAAClH,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACtE,OAAO,IAAI,CAACyG,qBAAqB,CAACO,YAAY,CAAC,IAAI,EAAE;EACvD;EAEAvD,+BAA+BA,CAAA;IAC7B,MAAMsE,aAAa,GAAG,IAAI,CAAC3G,wBAAwB,CAACrB,GAAG,CAAC,qBAAqB,CAAC,EAAEC,KAAK;IACrF,MAAMgI,YAAY,GAAG,IAAI,CAAC1G,wBAAwB,EAAE;IACpD,OAAO0G,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAClI,KAAK,KAAK+H,aAAa,CAAC;EAChE;EAEA7G,mBAAmBA,CAACiH,KAAU;IAC5B;IACAN,UAAU,CAAC,MAAK;MACd,IAAI,CAACO,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAN,SAASA,CAAA;IACP,IAAI,IAAI,CAAChI,iBAAiB,CAACmG,KAAK,EAAE;MAChC,IAAI,CAACO,WAAW,GAAG,CAAC;;EAExB;EAEA4B,SAASA,CAAA;IACP,IAAI,IAAI,CAAChH,wBAAwB,CAAC6E,KAAK,EAAE;MACvC,IAAI,CAACO,WAAW,GAAG,CAAC;MACpB,IAAI,CAACM,eAAe,EAAE;;EAE1B;EAEAuB,SAASA,CAAA;IACP,IAAI,IAAI,CAACxF,eAAe,EAAE;MACxB,IAAI,CAAC2D,WAAW,GAAG,CAAC;;EAExB;EAEA8B,aAAaA,CAAA;IACX,IAAI,CAAC9B,WAAW,GAAG,CAAC;EACtB;EAEA+B,aAAaA,CAAA;IACX,IAAI,CAAC/B,WAAW,GAAG,CAAC;EACtB;EAEAjB,aAAaA,CAAA;IACX,IAAI,CAACiB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACM,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAACrF,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAACiF,cAAc,CAAC;IACpD,IAAI,CAAC9C,kBAAkB,GAAG,IAAI;EAChC;EAEAR,eAAeA,CAAA;IACb,MAAM+D,UAAU,GAAG,IAAI,CAAC3F,iBAAiB,CAACzB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAElE,IAAI,CAACmH,UAAU,IAAIA,UAAU,CAAC1E,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3C;MACA,IAAI,CAACqE,eAAe,EAAE;MACtB;;IAGF,IAAIK,UAAU,CAACzF,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACsE,SAAS,GAAG,IAAI;MAErB;MACA6B,UAAU,CAAC,MAAK;QACd,IAAI,CAACpG,oBAAoB,GAAG,IAAI,CAACiF,cAAc,CAAC8B,MAAM,CAACC,OAAO,IAC5DA,OAAO,CAAC1G,aAAa,CAAC2G,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,IACtED,OAAO,CAACvG,YAAY,CAACwG,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,IACrED,OAAO,CAACrG,IAAI,CAACsG,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,IAC7DD,OAAO,CAACpG,aAAa,CAACqG,WAAW,EAAE,CAACC,QAAQ,CAACxB,UAAU,CAACuB,WAAW,EAAE,CAAC,CACvE;QACD,IAAI,CAAC9E,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACoC,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEAlE,aAAaA,CAAC2G,OAAoB;IAChC,IAAI,CAAC5F,eAAe,GAAG4F,OAAO;IAC9B,IAAI,CAAC7E,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACpC,iBAAiB,CAACoG,UAAU,CAAC;MAAET,UAAU,EAAEsB,OAAO,CAAC1G;IAAa,CAAE,CAAC;IACxE;IACA8F,UAAU,CAAC,MAAK;MACd,IAAI,CAACQ,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAzF,qBAAqBA,CAAA;IACnB,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACrB,iBAAiB,CAACoG,UAAU,CAAC;MAAET,UAAU,EAAE;IAAE,CAAE,CAAC;IACrD;IACA,IAAI,CAACL,eAAe,EAAE;EACxB;EAEMrB,iBAAiBA,CAAA;IAAA,IAAAmD,KAAA;IAAA,OAAAC,iBAAA;MACrB,IAAID,KAAI,CAAC9I,iBAAiB,CAACmG,KAAK,IAAI2C,KAAI,CAACxH,wBAAwB,CAAC6E,KAAK,IAAI2C,KAAI,CAAC/F,eAAe,IAAI+F,KAAI,CAAC/E,oBAAoB,CAACoC,KAAK,EAAE;QAClI2C,KAAI,CAAC5C,SAAS,GAAG,IAAI;QAErB,MAAM8C,OAAO,SAASF,KAAI,CAACtC,iBAAiB,CAACyC,MAAM,CAAC;UAClDC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB;QACArB,UAAU,cAAAgB,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAAC5C,SAAS,GAAG,KAAK;UACtB,MAAM8C,OAAO,CAACK,OAAO,EAAE;UAEvB,MAAMC,KAAK,SAASR,KAAI,CAACrC,eAAe,CAACwC,MAAM,CAAC;YAC9CC,OAAO,EAAE,uDAAuD,GAAGrC,IAAI,CAAC0C,GAAG,EAAE;YAC7EJ,QAAQ,EAAE,IAAI;YACdK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,MAAMH,KAAK,CAACF,OAAO,EAAE;UAErB;UACAN,KAAI,CAACvC,MAAM,CAACmD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMJ,KAAK,SAASR,KAAI,CAACrC,eAAe,CAACwC,MAAM,CAAC;UAC9CC,OAAO,EAAE,qEAAqE;UAC9EC,QAAQ,EAAE,IAAI;UACdK,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;SACX,CAAC;QACF,MAAMH,KAAK,CAACF,OAAO,EAAE;;IACtB;EACH;EAEAvE,cAAcA,CAACwD,KAAU;IACvB,MAAMsB,KAAK,GAAGtB,KAAK,CAACuB,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAAC/H,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC8C,aAAa,GAAGmF,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACtC,IAAI,CAAC5F,oBAAoB,CAAC+D,UAAU,CAAC;QAAEF,YAAY,EAAE+B,KAAK,CAAC,CAAC;MAAC,CAAE,CAAC;;EAEpE;EAEAvF,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACO,aAAa,CAACqF,MAAM,CAAC5F,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,IAAI,CAACO,aAAa,CAAC9C,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACmC,oBAAoB,CAAC+D,UAAU,CAAC;QAAEF,YAAY,EAAE;MAAI,CAAE,CAAC;;EAEhE;EAEA/G,wBAAwBA,CAAA;IACtB,MAAMoH,aAAa,GAAG,IAAI,CAACjI,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACvE,OAAO,IAAI,CAACS,cAAc,CAACwH,IAAI,CAACN,IAAI,IAAIA,IAAI,CAAC3H,KAAK,KAAK+H,aAAa,CAAC;EACvE;EAEA+B,eAAeA,CAACC,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO,IAAI,CAACjK,iBAAiB,CAACmG,KAAK,IAAI,IAAI,CAACO,WAAW,GAAG,CAAC;MAC7D,KAAK,CAAC;QACJ,OAAO,IAAI,CAACpF,wBAAwB,CAAC6E,KAAK,IAAI,IAAI,CAACO,WAAW,GAAG,CAAC;MACpE,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC3D,eAAe,KAAK,IAAI,IAAI,IAAI,CAAC2D,WAAW,GAAG,CAAC;MAC9D,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC3C,oBAAoB,CAACoC,KAAK,IAAI,IAAI,CAACO,WAAW,GAAG,CAAC;MAChE;QACE,OAAO,KAAK;;EAElB;EAEAjF,eAAeA,CAACyI,IAAe,EAAEC,KAAa;IAC5C,MAAMC,OAAO,GAAGF,IAAI,CAACjK,GAAG,CAACkK,KAAK,CAAC;IAC/B,IAAIC,OAAO,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAM9C,SAAS,GAAG6C,OAAO,CAACG,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACH,KAAK,CAAC,qBAAqB5C,SAAS,aAAa;;IAEhF,IAAI6C,OAAO,EAAEC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACH,KAAa;IACjC,MAAMM,MAAM,GAA8B;MACxCvD,YAAY,EAAE,gBAAgB;MAC9BE,mBAAmB,EAAE,uBAAuB;MAC5CC,UAAU,EAAE,aAAa;MACzBC,iBAAiB,EAAE,qBAAqB;MACxCE,aAAa,EAAE,gBAAgB;MAC/BE,QAAQ,EAAE;KACX;IACD,OAAO+C,MAAM,CAACN,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAO,MAAMA,CAAA;IACJ,IAAI,CAACnE,MAAM,CAACmD,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBA/aWtD,YAAY,EAAArH,EAAA,CAAA4L,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9L,EAAA,CAAA4L,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAhM,EAAA,CAAA4L,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAlM,EAAA,CAAA4L,iBAAA,CAAAK,EAAA,CAAAE,eAAA;IAAA;EAAA;;;YAAZ9E,YAAY;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCnBnB1M,EAHN,CAAAC,cAAA,oBAAiC,qBACF,qBACD,oBACO;UAAnBD,EAAA,CAAAE,UAAA,mBAAA0M,kDAAA;YAAA,OAASD,GAAA,CAAAhB,MAAA,EAAQ;UAAA,EAAC;UAC5B3L,EAAA,CAAAY,SAAA,kBAAuC;UAE3CZ,EADE,CAAAa,YAAA,EAAa,EACD;UACdb,EAAA,CAAAC,cAAA,gBAAW;UAAAD,EAAA,CAAAc,MAAA,yBAAkB;UAEjCd,EAFiC,CAAAa,YAAA,EAAY,EAC7B,EACH;UAOLb,EALR,CAAAC,cAAA,qBAA0D,aACjC,aAEO,cACE,UACtB;UAAAD,EAAA,CAAAc,MAAA,8BAAsB;UAAAd,EAAA,CAAAa,YAAA,EAAK;UAC/Bb,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAc,MAAA,yDAAiD;UAExDd,EAFwD,CAAAa,YAAA,EAAI,EACpD,EACF;UAMAb,EAHN,CAAAC,cAAA,eAA8B,eACA,eACqD,eACpD;UAAAD,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAa,YAAA,EAAM;UAChCb,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAc,MAAA,sBAAc;UACxCd,EADwC,CAAAa,YAAA,EAAM,EACxC;UACNb,EAAA,CAAAY,SAAA,eAAyE;UAEvEZ,EADF,CAAAC,cAAA,eAA6F,eAClE;UAAAD,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAa,YAAA,EAAM;UAChCb,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAc,MAAA,6BAAqB;UAC/Cd,EAD+C,CAAAa,YAAA,EAAM,EAC/C;UACNb,EAAA,CAAAY,SAAA,eAAyE;UAEvEZ,EADF,CAAAC,cAAA,eAA6F,eAClE;UAAAD,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAa,YAAA,EAAM;UAChCb,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAc,MAAA,yBAAiB;UAC3Cd,EAD2C,CAAAa,YAAA,EAAM,EAC3C;UACNb,EAAA,CAAAY,SAAA,eAAyE;UAEvEZ,EADF,CAAAC,cAAA,eAA6F,eAClE;UAAAD,EAAA,CAAAc,MAAA,SAAC;UAAAd,EAAA,CAAAa,YAAA,EAAM;UAChCb,EAAA,CAAAC,cAAA,eAAwB;UAAAD,EAAA,CAAAc,MAAA,yBAAiB;UAG/Cd,EAH+C,CAAAa,YAAA,EAAM,EAC3C,EACF,EACF;UA6LNb,EA1LA,CAAA0B,UAAA,KAAAmL,4BAAA,kBAAsD,KAAAC,4BAAA,mBA8BsB,KAAAC,4BAAA,mBAyCA,KAAAC,4BAAA,oBAmHA;UA7N9EhN,EAAA,CAAAa,YAAA,EAAuB,EADiC;;;UAX9Cb,EAAA,CAAAqB,UAAA,qBAAoB;UAWnBrB,EAAA,CAAAoB,SAAA,GAAmB;UAAnBpB,EAAA,CAAAqB,UAAA,oBAAmB;UAaNrB,EAAA,CAAAoB,SAAA,IAAqB;UAACpB,EAAtB,CAAAe,WAAA,gBAAqB,cAAA4L,GAAA,CAAA1B,eAAA,IAAuC;UAIlDjL,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAA4L,GAAA,CAAA1B,eAAA,IAAsC;UAChDjL,EAAA,CAAAoB,SAAA,EAAmC;UAACpB,EAApC,CAAAe,WAAA,WAAA4L,GAAA,CAAA1B,eAAA,IAAmC,cAAA0B,GAAA,CAAA1B,eAAA,IAAuC;UAIhEjL,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAA4L,GAAA,CAAA1B,eAAA,IAAsC;UAChDjL,EAAA,CAAAoB,SAAA,EAAmC;UAACpB,EAApC,CAAAe,WAAA,WAAA4L,GAAA,CAAA1B,eAAA,IAAmC,cAAA0B,GAAA,CAAA1B,eAAA,IAAuC;UAIhEjL,EAAA,CAAAoB,SAAA,GAAsC;UAAtCpB,EAAA,CAAAe,WAAA,cAAA4L,GAAA,CAAA1B,eAAA,IAAsC;UAChDjL,EAAA,CAAAoB,SAAA,EAAmC;UAACpB,EAApC,CAAAe,WAAA,WAAA4L,GAAA,CAAA1B,eAAA,IAAmC,cAAA0B,GAAA,CAAA1B,eAAA,IAAuC;UAQrEjL,EAAA,CAAAoB,SAAA,GAAyB;UAAzBpB,EAAA,CAAAqB,UAAA,UAAAsL,GAAA,CAAA1B,eAAA,IAAyB;UA8BzBjL,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAAsL,GAAA,CAAA1B,eAAA,QAAA0B,GAAA,CAAA1B,eAAA,IAA+C;UAyC/CjL,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAAsL,GAAA,CAAA1B,eAAA,QAAA0B,GAAA,CAAA1B,eAAA,IAA+C;UAmH/CjL,EAAA,CAAAoB,SAAA,EAA+C;UAA/CpB,EAAA,CAAAqB,UAAA,SAAAsL,GAAA,CAAA1B,eAAA,QAAA0B,GAAA,CAAA1B,eAAA,IAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}