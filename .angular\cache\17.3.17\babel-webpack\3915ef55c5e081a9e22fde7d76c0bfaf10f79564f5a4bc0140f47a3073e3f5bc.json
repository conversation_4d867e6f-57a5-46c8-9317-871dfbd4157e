{"ast": null, "code": "import _asyncToGenerator from \"C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ionic/angular\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/form-field\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/card\";\nimport * as i9 from \"@angular/material/icon\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/toolbar\";\nimport * as i13 from \"@angular/material/radio\";\nfunction RegisterPage_mat_icon_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"1\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"2\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"3\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_mat_icon_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"check\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"4\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_39_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 27)(1, \"mat-icon\", 28);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate((tmp_2_0 = ctx_r0.getSelectedComplaintType()) == null ? null : tmp_2_0.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r0.getSelectedComplaintType()) == null ? null : tmp_3_0.label, \" \");\n  }\n}\nfunction RegisterPage_div_39_mat_chip_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r0.getSelectedComplaintDescription()) == null ? null : tmp_2_0.label, \" \");\n  }\n}\nfunction RegisterPage_div_39_mat_chip_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedInvoice.invoiceNumber, \" \");\n  }\n}\nfunction RegisterPage_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"mat-chip-set\");\n    i0.ɵɵtemplate(2, RegisterPage_div_39_mat_chip_2_Template, 4, 2, \"mat-chip\", 24)(3, RegisterPage_div_39_mat_chip_3_Template, 2, 1, \"mat-chip\", 25)(4, RegisterPage_div_39_mat_chip_4_Template, 2, 1, \"mat-chip\", 26);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getSelectedComplaintType());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.getSelectedComplaintDescription());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedInvoice);\n  }\n}\nfunction RegisterPage_div_41_mat_card_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 36);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_41_mat_card_8_Template_mat_card_click_0_listener() {\n      const type_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.selectComplaintType(type_r3));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-content\")(2, \"div\", 37)(3, \"mat-icon\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 39)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(10, \"mat-radio-button\", 40);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    let tmp_4_0;\n    const type_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r0.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_3_0.value) === type_r3.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ((tmp_4_0 = ctx_r0.complaintTypeForm.get(\"selectedType\")) == null ? null : tmp_4_0.value) === type_r3.value ? \"primary\" : \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r3.icon, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(type_r3.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(type_r3.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", type_r3.value);\n  }\n}\nfunction RegisterPage_div_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"h2\");\n    i0.ɵɵtext(3, \"Select Complaint Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Choose the category that best describes your issue\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"form\", 33)(7, \"div\", 34);\n    i0.ɵɵtemplate(8, RegisterPage_div_41_mat_card_8_Template, 11, 7, \"mat-card\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.complaintTypeForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.complaintTypes);\n  }\n}\nfunction RegisterPage_div_42_mat_card_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 44)(1, \"mat-card-content\")(2, \"div\", 45);\n    i0.ɵɵelement(3, \"mat-radio-button\", 46);\n    i0.ɵɵelementStart(4, \"div\", 47)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const desc_r5 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"selected\", ((tmp_3_0 = ctx_r0.complaintDescriptionForm.get(\"selectedDescription\")) == null ? null : tmp_3_0.value) === desc_r5.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"value\", desc_r5.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(desc_r5.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(desc_r5.description);\n  }\n}\nfunction RegisterPage_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"h2\");\n    i0.ɵɵtext(3, \"Select Specific Issue\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Choose the description that best matches your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"form\", 33)(7, \"div\", 41)(8, \"mat-radio-group\", 42);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_42_Template_mat_radio_group_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onDescriptionChange($event));\n    });\n    i0.ɵɵtemplate(9, RegisterPage_div_42_mat_card_9_Template, 9, 5, \"mat-card\", 43);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.complaintDescriptionForm);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getComplaintDescriptions());\n  }\n}\nfunction RegisterPage_div_43_div_13_mat_card_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 58);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_43_div_13_mat_card_4_Template_mat_card_click_0_listener() {\n      const invoice_r8 = i0.ɵɵrestoreView(_r7).$implicit;\n      const ctx_r0 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r0.selectInvoice(invoice_r8));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-content\")(2, \"div\", 59)(3, \"div\", 60)(4, \"div\", 61);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 62);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 63)(10, \"div\", 64);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 65);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"mat-icon\", 66);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const invoice_r8 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵclassProp(\"selected\", (ctx_r0.selectedInvoice == null ? null : ctx_r0.selectedInvoice.invoiceNumber) === invoice_r8.invoiceNumber);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(invoice_r8.invoiceNumber);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(8, 7, invoice_r8.invoiceDate, \"MMM dd\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(invoice_r8.customerName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(invoice_r8.zone);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (ctx_r0.selectedInvoice == null ? null : ctx_r0.selectedInvoice.invoiceNumber) === invoice_r8.invoiceNumber ? \"check_circle\" : \"radio_button_unchecked\", \" \");\n  }\n}\nfunction RegisterPage_div_43_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 54)(1, \"div\", 55);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 56);\n    i0.ɵɵtemplate(4, RegisterPage_div_43_div_13_mat_card_4_Template, 16, 10, \"mat-card\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.invoiceSearchResults.length, \" invoices found\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.invoiceSearchResults);\n  }\n}\nfunction RegisterPage_div_43_mat_card_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 67)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\", 68);\n    i0.ɵɵtext(4, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 69)(8, \"div\")(9, \"strong\");\n    i0.ɵɵtext(10, \"Customer:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\")(13, \"strong\");\n    i0.ɵɵtext(14, \"Date:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵpipe(16, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\")(18, \"strong\");\n    i0.ɵɵtext(19, \"Zone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\")(22, \"strong\");\n    i0.ɵɵtext(23, \"Items:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"button\", 70);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_43_mat_card_14_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.clearInvoiceSelection());\n    });\n    i0.ɵɵelementStart(26, \"mat-icon\");\n    i0.ɵɵtext(27, \"clear\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \" Change Selection \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Selected: \", ctx_r0.selectedInvoice.invoiceNumber, \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedInvoice.customerName, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind2(16, 5, ctx_r0.selectedInvoice.invoiceDate, \"MMM dd, yyyy\"), \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedInvoice.zone, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.selectedInvoice.items.length, \" items\");\n  }\n}\nfunction RegisterPage_div_43_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"search_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No invoices found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 72);\n    i0.ɵɵtext(6, \"Try different keywords or clear search\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction RegisterPage_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"h2\");\n    i0.ɵɵtext(3, \"Select Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Search and select the invoice related to your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"form\", 33)(7, \"mat-form-field\", 48)(8, \"mat-label\");\n    i0.ɵɵtext(9, \"Search Invoice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"input\", 49);\n    i0.ɵɵlistener(\"input\", function RegisterPage_div_43_Template_input_input_10_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onInvoiceSearch());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"mat-icon\", 50);\n    i0.ɵɵtext(12, \"search\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, RegisterPage_div_43_div_13_Template, 5, 2, \"div\", 51)(14, RegisterPage_div_43_mat_card_14_Template, 29, 8, \"mat-card\", 52)(15, RegisterPage_div_43_div_15_Template, 7, 0, \"div\", 53);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.invoiceSearchForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showInvoiceResults && ctx_r0.invoiceSearchResults.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedInvoice);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.showInvoiceResults && ctx_r0.invoiceSearchResults.length === 0);\n  }\n}\nfunction RegisterPage_div_44_mat_chip_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", ctx_r0.selectedInvoice.invoiceNumber, \" \\u2022 \", ctx_r0.selectedInvoice.customerName, \" \");\n  }\n}\nfunction RegisterPage_div_44_mat_error_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(ctx_r0.complaintDetailsForm, \"contactPersonName\"), \" \");\n  }\n}\nfunction RegisterPage_div_44_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getErrorMessage(ctx_r0.complaintDetailsForm, \"contactNumber\"), \" \");\n  }\n}\nfunction RegisterPage_div_44_div_49_div_9_mat_chip_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-chip\", 93);\n    i0.ɵɵlistener(\"removed\", function RegisterPage_div_44_div_49_div_9_mat_chip_2_Template_mat_chip_removed_0_listener() {\n      const i_r13 = i0.ɵɵrestoreView(_r12).index;\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.removeFile(i_r13));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\", 28);\n    i0.ɵɵtext(2, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementStart(4, \"mat-icon\", 94);\n    i0.ɵɵtext(5, \"cancel\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r14 = ctx.$implicit;\n    i0.ɵɵproperty(\"removable\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", file_r14.name, \" \");\n  }\n}\nfunction RegisterPage_div_44_div_49_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"mat-chip-set\");\n    i0.ɵɵtemplate(2, RegisterPage_div_44_div_49_div_9_mat_chip_2_Template, 6, 2, \"mat-chip\", 92);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.selectedFiles);\n  }\n}\nfunction RegisterPage_div_44_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 86)(1, \"input\", 87, 0);\n    i0.ɵɵlistener(\"change\", function RegisterPage_div_44_div_49_Template_input_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onFileSelected($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 88);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_44_div_49_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const fileInput_r11 = i0.ɵɵreference(2);\n      return i0.ɵɵresetView(fileInput_r11.click());\n    });\n    i0.ɵɵelementStart(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"attach_file\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Choose Files \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 89);\n    i0.ɵɵtext(8, \"PDF, JPG, PNG, DOC (Max 5MB each)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, RegisterPage_div_44_div_49_div_9_Template, 3, 1, \"div\", 90);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedFiles.length > 0);\n  }\n}\nfunction RegisterPage_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 32)(2, \"h2\");\n    i0.ɵɵtext(3, \"Contact Details & Submit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"Provide your contact information and submit your complaint\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card\", 73)(7, \"mat-card-content\")(8, \"div\", 74)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"assignment\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Complaint Summary\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 75)(14, \"mat-chip-set\")(15, \"mat-chip\", 27)(16, \"mat-icon\", 28);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"mat-chip\", 29);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, RegisterPage_div_44_mat_chip_21_Template, 2, 2, \"mat-chip\", 26);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"mat-card\", 76)(23, \"mat-card-content\")(24, \"form\", 33)(25, \"div\", 77)(26, \"mat-form-field\", 78)(27, \"mat-label\");\n    i0.ɵɵtext(28, \"Contact Person *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(29, \"input\", 79);\n    i0.ɵɵelementStart(30, \"mat-icon\", 50);\n    i0.ɵɵtext(31, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, RegisterPage_div_44_mat_error_32_Template, 2, 1, \"mat-error\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"mat-form-field\", 78)(34, \"mat-label\");\n    i0.ɵɵtext(35, \"Contact Number *\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(36, \"input\", 80);\n    i0.ɵɵelementStart(37, \"mat-icon\", 50);\n    i0.ɵɵtext(38, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, RegisterPage_div_44_mat_error_39_Template, 2, 1, \"mat-error\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"mat-form-field\", 81)(41, \"mat-label\");\n    i0.ɵɵtext(42, \"Additional Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"textarea\", 82);\n    i0.ɵɵelementStart(44, \"mat-icon\", 50);\n    i0.ɵɵtext(45, \"comment\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"div\", 83)(47, \"mat-checkbox\", 84);\n    i0.ɵɵtext(48, \" Attach supporting documents \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(49, RegisterPage_div_44_div_49_Template, 10, 1, \"div\", 85);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate((tmp_1_0 = ctx_r0.getSelectedComplaintType()) == null ? null : tmp_1_0.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", (tmp_2_0 = ctx_r0.getSelectedComplaintType()) == null ? null : tmp_2_0.label, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", (tmp_3_0 = ctx_r0.getSelectedComplaintDescription()) == null ? null : tmp_3_0.label, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedInvoice);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.complaintDetailsForm);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.complaintDetailsForm.get(\"contactPersonName\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_7_0 = ctx_r0.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r0.complaintDetailsForm.get(\"contactNumber\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx_r0.complaintDetailsForm.get(\"hasComplaintLetters\")) == null ? null : tmp_8_0.value);\n  }\n}\nfunction RegisterPage_button_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r15 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function RegisterPage_button_46_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r15);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousStep());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_back\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Back \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Continue\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_50_mat_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"send\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_span_50_mat_spinner_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 97);\n  }\n}\nfunction RegisterPage_span_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, RegisterPage_span_50_mat_icon_1_Template, 2, 0, \"mat-icon\", 12)(2, RegisterPage_span_50_mat_spinner_2_Template, 1, 0, \"mat-spinner\", 96);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.isLoading ? \"Submitting...\" : \"Submit Complaint\", \" \");\n  }\n}\nfunction RegisterPage_mat_icon_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RegisterPage_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 98)(1, \"mat-card\", 99)(2, \"mat-card-content\")(3, \"mat-icon\", 100);\n    i0.ɵɵtext(4, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Complaint Submitted Successfully!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Your complaint has been registered and will be processed shortly.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 101);\n    i0.ɵɵlistener(\"click\", function RegisterPage_div_52_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goBack());\n    });\n    i0.ɵɵelementStart(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"home\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12, \" Back to Home \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nexport class RegisterPage {\n  constructor(formBuilder, router, loadingController, toastController) {\n    this.formBuilder = formBuilder;\n    this.router = router;\n    this.loadingController = loadingController;\n    this.toastController = toastController;\n    this.isLoading = false;\n    this.selectedFiles = [];\n    this.selectedInvoice = null;\n    this.invoiceSearchResults = [];\n    this.showInvoiceResults = false;\n    this.currentStep = 1;\n    this.complaintTypes = [{\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond',\n      description: 'Scratches, cracks, or defects in glass products'\n    }, {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'build',\n      description: 'Issues during glass installation process'\n    }, {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'local_shipping',\n      description: 'Damage during delivery or transportation'\n    }, {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'straighten',\n      description: 'Incorrect measurements or sizing problems'\n    }, {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'support_agent',\n      description: 'Customer service or support issues'\n    }, {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'receipt_long',\n      description: 'Invoice, billing, or documentation issues'\n    }];\n    this.complaintDescriptions = {\n      'glass_quality': [{\n        value: 'scratches',\n        label: 'Scratches on Glass Surface',\n        description: 'Visible scratches or marks on the glass surface'\n      }, {\n        value: 'cracks',\n        label: 'Cracks or Chips',\n        description: 'Cracks, chips, or fractures in the glass'\n      }, {\n        value: 'bubbles',\n        label: 'Air Bubbles',\n        description: 'Air bubbles or inclusions within the glass'\n      }, {\n        value: 'discoloration',\n        label: 'Discoloration',\n        description: 'Color variations or discoloration in the glass'\n      }, {\n        value: 'thickness',\n        label: 'Thickness Issues',\n        description: 'Incorrect thickness or uneven glass thickness'\n      }],\n      'installation': [{\n        value: 'alignment',\n        label: 'Alignment Problems',\n        description: 'Glass not properly aligned during installation'\n      }, {\n        value: 'sealing',\n        label: 'Sealing Issues',\n        description: 'Poor sealing or gaps around the glass'\n      }, {\n        value: 'hardware',\n        label: 'Hardware Problems',\n        description: 'Issues with hinges, handles, or other hardware'\n      }, {\n        value: 'fitting',\n        label: 'Poor Fitting',\n        description: 'Glass does not fit properly in the frame'\n      }, {\n        value: 'damage_during',\n        label: 'Damage During Installation',\n        description: 'Glass damaged during the installation process'\n      }],\n      'delivery_damage': [{\n        value: 'broken_transit',\n        label: 'Broken in Transit',\n        description: 'Glass broken during transportation'\n      }, {\n        value: 'packaging',\n        label: 'Poor Packaging',\n        description: 'Inadequate packaging causing damage'\n      }, {\n        value: 'handling',\n        label: 'Rough Handling',\n        description: 'Damage due to rough handling during delivery'\n      }, {\n        value: 'delayed',\n        label: 'Delayed Delivery',\n        description: 'Delivery was significantly delayed'\n      }, {\n        value: 'wrong_item',\n        label: 'Wrong Item Delivered',\n        description: 'Incorrect glass type or specifications delivered'\n      }],\n      'measurement': [{\n        value: 'wrong_size',\n        label: 'Wrong Size',\n        description: 'Glass delivered in incorrect dimensions'\n      }, {\n        value: 'measurement_error',\n        label: 'Measurement Error',\n        description: 'Error in initial measurements taken'\n      }, {\n        value: 'specification',\n        label: 'Specification Mismatch',\n        description: 'Glass does not match ordered specifications'\n      }, {\n        value: 'template',\n        label: 'Template Issues',\n        description: 'Problems with measurement template or pattern'\n      }],\n      'service': [{\n        value: 'communication',\n        label: 'Poor Communication',\n        description: 'Lack of proper communication from service team'\n      }, {\n        value: 'response_time',\n        label: 'Slow Response Time',\n        description: 'Delayed response to queries or complaints'\n      }, {\n        value: 'unprofessional',\n        label: 'Unprofessional Behavior',\n        description: 'Unprofessional conduct by service personnel'\n      }, {\n        value: 'incomplete_work',\n        label: 'Incomplete Work',\n        description: 'Service work left incomplete or unfinished'\n      }],\n      'billing': [{\n        value: 'wrong_amount',\n        label: 'Incorrect Amount',\n        description: 'Wrong amount charged in the invoice'\n      }, {\n        value: 'missing_details',\n        label: 'Missing Details',\n        description: 'Important details missing from invoice'\n      }, {\n        value: 'duplicate',\n        label: 'Duplicate Billing',\n        description: 'Charged multiple times for the same service'\n      }, {\n        value: 'tax_error',\n        label: 'Tax Calculation Error',\n        description: 'Incorrect tax calculation or application'\n      }]\n    };\n    // Sample invoice data for demonstration\n    this.sampleInvoices = [{\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP',\n      items: [{\n        itemCode: 'FGDGAGA100.36602440LN',\n        description: 'DARK GREY 100-3660x2440 DARK GREY 100',\n        thickness: 10,\n        width: 2440,\n        height: 3660,\n        quantity: 176,\n        csqm: 392.9376,\n        receivedBoxes: 4\n      }, {\n        itemCode: 'FGDGAGA120.36602440LN',\n        description: 'DARK GREY 120-3660x2440 DARK GREY 120',\n        thickness: 12,\n        width: 2440,\n        height: 3660,\n        quantity: 212,\n        csqm: 160.7472,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab',\n      items: [{\n        itemCode: 'FGCGAGA120.36602770LN',\n        description: 'CLEAR GREY 120-3660x2770 CLEAR GREY 120',\n        thickness: 12,\n        width: 2770,\n        height: 3660,\n        quantity: 150,\n        csqm: 278.5420,\n        receivedBoxes: 3\n      }, {\n        itemCode: 'FGTGAGA080.24401830LN',\n        description: 'TINTED GREY 080-2440x1830 TINTED GREY 080',\n        thickness: 8,\n        width: 2440,\n        height: 1830,\n        quantity: 95,\n        csqm: 124.3680,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore',\n      items: [{\n        itemCode: 'FGBGAGA060.18302440LN',\n        description: 'BLUE GREY 060-1830x2440 BLUE GREY 060',\n        thickness: 6,\n        width: 1830,\n        height: 2440,\n        quantity: 88,\n        csqm: 195.2640,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai',\n      items: [{\n        itemCode: 'FGGGAGA100.24403660LN',\n        description: 'GREEN GREY 100-2440x3660 GREEN GREY 100',\n        thickness: 10,\n        width: 2440,\n        height: 3660,\n        quantity: 120,\n        csqm: 267.8880,\n        receivedBoxes: 3\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata',\n      items: [{\n        itemCode: 'FGRGAGA080.18302440LN',\n        description: 'RED GREY 080-1830x2440 RED GREY 080',\n        thickness: 8,\n        width: 1830,\n        height: 2440,\n        quantity: 75,\n        csqm: 133.4400,\n        receivedBoxes: 2\n      }, {\n        itemCode: 'FGWGAGA120.36602440LN',\n        description: 'WHITE GREY 120-3660x2440 WHITE GREY 120',\n        thickness: 12,\n        width: 3660,\n        height: 2440,\n        quantity: 95,\n        csqm: 218.5680,\n        receivedBoxes: 1\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad',\n      items: [{\n        itemCode: 'FGYGAGA060.24401830LN',\n        description: 'YELLOW GREY 060-2440x1830 YELLOW GREY 060',\n        thickness: 6,\n        width: 2440,\n        height: 1830,\n        quantity: 65,\n        csqm: 145.2720,\n        receivedBoxes: 2\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat',\n      items: [{\n        itemCode: 'FGPGAGA100.36602770LN',\n        description: 'PURPLE GREY 100-3660x2770 PURPLE GREY 100',\n        thickness: 10,\n        width: 3660,\n        height: 2770,\n        quantity: 180,\n        csqm: 364.4520,\n        receivedBoxes: 4\n      }]\n    }, {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi',\n      items: [{\n        itemCode: 'FGOGAGA080.24402440LN',\n        description: 'ORANGE GREY 080-2440x2440 ORANGE GREY 080',\n        thickness: 8,\n        width: 2440,\n        height: 2440,\n        quantity: 110,\n        csqm: 195.3760,\n        receivedBoxes: 3\n      }, {\n        itemCode: 'FGSGAGA120.18303660LN',\n        description: 'SILVER GREY 120-1830x3660 SILVER GREY 120',\n        thickness: 12,\n        width: 1830,\n        height: 3660,\n        quantity: 85,\n        csqm: 142.8180,\n        receivedBoxes: 2\n      }]\n    }];\n    this.createForms();\n  }\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n  selectComplaintType(type) {\n    this.complaintTypeForm.patchValue({\n      selectedType: type.value\n    });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n  onDescriptionChange(event) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice => invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase()));\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n  selectInvoice(invoice) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: invoice.invoiceNumber\n    });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({\n      searchTerm: ''\n    });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n  onSubmitComplaint() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.complaintTypeForm.valid && _this.complaintDescriptionForm.valid && _this.selectedInvoice && _this.complaintDetailsForm.valid) {\n        _this.isLoading = true;\n        const loading = yield _this.loadingController.create({\n          message: 'Registering complaint...',\n          duration: 3000\n        });\n        yield loading.present();\n        // Simulate registration process\n        setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n          _this.isLoading = false;\n          yield loading.dismiss();\n          const toast = yield _this.toastController.create({\n            message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n            duration: 4000,\n            color: 'success',\n            position: 'top'\n          });\n          yield toast.present();\n          // Navigate to track page\n          _this.router.navigate(['/track']);\n        }), 3000);\n      } else {\n        const toast = yield _this.toastController.create({\n          message: 'Please complete all required steps and fill in all required fields.',\n          duration: 3000,\n          color: 'danger',\n          position: 'top'\n        });\n        yield toast.present();\n      }\n    })();\n  }\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({\n        attachedFile: files[0]\n      });\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({\n        attachedFile: null\n      });\n    }\n  }\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n  isStepCompleted(step) {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n  getErrorMessage(form, field) {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n  getFieldLabel(field) {\n    const labels = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n  // Navigation methods for ultra-modern UI\n  goToNextStep() {\n    if (this.canProceedToNextStep()) {\n      if (this.currentStep === 4) {\n        this.onSubmitComplaint();\n      } else {\n        this.currentStep++;\n        this.updateStepCompletion();\n      }\n    }\n  }\n  goToPreviousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n  canProceedToNextStep() {\n    switch (this.currentStep) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDescriptionForm.valid;\n      case 3:\n        return !!this.selectedInvoice;\n      case 4:\n        return this.complaintDetailsForm.valid;\n      default:\n        return false;\n    }\n  }\n  updateStepCompletion() {\n    // Update step completion based on current step\n    if (this.currentStep > 1 && this.complaintTypeForm.valid) {\n      // Step 1 completed\n    }\n    if (this.currentStep > 2 && this.complaintDescriptionForm.valid) {\n      // Step 2 completed\n    }\n    if (this.currentStep > 3 && this.selectedInvoice) {\n      // Step 3 completed\n    }\n  }\n  // Helper methods for template\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n  isStepCompleted(step) {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDescriptionForm.valid;\n      case 3:\n        return !!this.selectedInvoice;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n  // File handling methods\n  onFileSelected(event) {\n    const files = event.target.files;\n    if (files) {\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (file.size <= 5 * 1024 * 1024) {\n          // 5MB limit\n          this.selectedFiles.push(file);\n        }\n      }\n    }\n  }\n  removeFile(index) {\n    this.selectedFiles.splice(index, 1);\n  }\n  // Error message helper\n  getErrorMessage(form, fieldName) {\n    const field = form.get(fieldName);\n    if (field?.hasError('required')) {\n      return `${fieldName} is required`;\n    }\n    if (field?.hasError('pattern')) {\n      return `Invalid ${fieldName} format`;\n    }\n    if (field?.hasError('minlength')) {\n      return `${fieldName} is too short`;\n    }\n    return '';\n  }\n  static {\n    this.ɵfac = function RegisterPage_Factory(t) {\n      return new (t || RegisterPage)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.LoadingController), i0.ɵɵdirectiveInject(i3.ToastController));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterPage,\n      selectors: [[\"app-register\"]],\n      decls: 53,\n      vars: 51,\n      consts: [[\"fileInput\", \"\"], [\"color\", \"primary\", 1, \"ultra-modern-toolbar\"], [\"mat-icon-button\", \"\", 3, \"click\"], [1, \"toolbar-content\"], [1, \"toolbar-title\"], [1, \"mini-progress\"], [1, \"progress-bar\"], [\"mat-icon-button\", \"\"], [1, \"ultra-compact-container\"], [1, \"inline-stepper\"], [1, \"step-progress\"], [1, \"step-dot\"], [4, \"ngIf\"], [1, \"step-line\"], [1, \"step-labels\"], [\"class\", \"breadcrumb-summary\", 4, \"ngIf\"], [1, \"ultra-compact-step\"], [\"class\", \"step-container\", 4, \"ngIf\"], [1, \"bottom-navigation\"], [\"mat-button\", \"\", \"class\", \"nav-button\", 3, \"click\", 4, \"ngIf\"], [1, \"spacer\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"nav-button\", 3, \"click\", \"disabled\"], [\"class\", \"success-state\", 4, \"ngIf\"], [1, \"breadcrumb-summary\"], [\"color\", \"primary\", \"selected\", \"\", 4, \"ngIf\"], [\"color\", \"accent\", \"selected\", \"\", 4, \"ngIf\"], [\"color\", \"warn\", \"selected\", \"\", 4, \"ngIf\"], [\"color\", \"primary\", \"selected\", \"\"], [\"matChipAvatar\", \"\"], [\"color\", \"accent\", \"selected\", \"\"], [\"color\", \"warn\", \"selected\", \"\"], [1, \"step-container\"], [1, \"step-header\"], [3, \"formGroup\"], [1, \"compact-grid\"], [\"class\", \"type-option\", \"matRipple\", \"\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"matRipple\", \"\", 1, \"type-option\", 3, \"click\"], [1, \"option-layout\"], [1, \"option-icon\", 3, \"color\"], [1, \"option-content\"], [\"formControlName\", \"selectedType\", \"color\", \"primary\", 3, \"value\"], [1, \"compact-list\"], [\"formControlName\", \"selectedDescription\", 3, \"change\"], [\"class\", \"description-option\", \"matRipple\", \"\", 3, \"selected\", 4, \"ngFor\", \"ngForOf\"], [\"matRipple\", \"\", 1, \"description-option\"], [1, \"radio-layout\"], [\"color\", \"primary\", 3, \"value\"], [1, \"radio-content\"], [\"appearance\", \"outline\", 1, \"search-field\"], [\"matInput\", \"\", \"formControlName\", \"searchTerm\", \"placeholder\", \"Invoice number, customer name, or leave empty for all\", 3, \"input\"], [\"matSuffix\", \"\"], [\"class\", \"invoice-list\", 4, \"ngIf\"], [\"class\", \"selected-preview\", 4, \"ngIf\"], [\"class\", \"no-results\", 4, \"ngIf\"], [1, \"invoice-list\"], [1, \"results-info\"], [1, \"compact-invoice-grid\"], [\"class\", \"invoice-option\", \"matRipple\", \"\", 3, \"selected\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"matRipple\", \"\", 1, \"invoice-option\", 3, \"click\"], [1, \"invoice-layout\"], [1, \"invoice-main\"], [1, \"invoice-number\"], [1, \"invoice-date\"], [1, \"invoice-details\"], [1, \"customer\"], [1, \"location\"], [1, \"select-icon\"], [1, \"selected-preview\"], [\"color\", \"primary\"], [1, \"preview-grid\"], [\"mat-button\", \"\", \"color\", \"warn\", 3, \"click\"], [1, \"no-results\"], [1, \"hint\"], [1, \"final-summary\"], [1, \"summary-title\"], [1, \"summary-chips\"], [1, \"contact-form\"], [1, \"form-grid\"], [\"appearance\", \"outline\"], [\"matInput\", \"\", \"formControlName\", \"contactPersonName\", \"placeholder\", \"Your name\"], [\"matInput\", \"\", \"formControlName\", \"contactNumber\", \"placeholder\", \"10-digit number\", \"type\", \"tel\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"formControlName\", \"comments\", \"rows\", \"2\", \"placeholder\", \"Optional additional information\"], [1, \"upload-section\"], [\"formControlName\", \"hasComplaintLetters\", \"color\", \"primary\"], [\"class\", \"upload-area\", 4, \"ngIf\"], [1, \"upload-area\"], [\"type\", \"file\", \"multiple\", \"\", \"accept\", \".pdf,.jpg,.jpeg,.png,.doc,.docx\", 2, \"display\", \"none\", 3, \"change\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"upload-hint\"], [\"class\", \"file-chips\", 4, \"ngIf\"], [1, \"file-chips\"], [3, \"removable\", \"removed\", 4, \"ngFor\", \"ngForOf\"], [3, \"removed\", \"removable\"], [\"matChipRemove\", \"\"], [\"mat-button\", \"\", 1, \"nav-button\", 3, \"click\"], [\"diameter\", \"20\", 4, \"ngIf\"], [\"diameter\", \"20\"], [1, \"success-state\"], [1, \"success-card\"], [\"color\", \"primary\", 1, \"success-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n      template: function RegisterPage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-toolbar\", 1)(1, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_button_click_1_listener() {\n            return ctx.goBack();\n          });\n          i0.ɵɵelementStart(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"arrow_back\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"div\", 3)(5, \"span\", 4);\n          i0.ɵɵtext(6, \"Register Complaint\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵelement(8, \"div\", 6);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"button\", 7)(10, \"mat-icon\");\n          i0.ɵɵtext(11, \"help_outline\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(12, \"div\", 8)(13, \"div\", 9)(14, \"div\", 10)(15, \"div\", 11);\n          i0.ɵɵtemplate(16, RegisterPage_mat_icon_16_Template, 2, 0, \"mat-icon\", 12)(17, RegisterPage_span_17_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(18, \"div\", 13);\n          i0.ɵɵelementStart(19, \"div\", 11);\n          i0.ɵɵtemplate(20, RegisterPage_mat_icon_20_Template, 2, 0, \"mat-icon\", 12)(21, RegisterPage_span_21_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"div\", 13);\n          i0.ɵɵelementStart(23, \"div\", 11);\n          i0.ɵɵtemplate(24, RegisterPage_mat_icon_24_Template, 2, 0, \"mat-icon\", 12)(25, RegisterPage_span_25_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(26, \"div\", 13);\n          i0.ɵɵelementStart(27, \"div\", 11);\n          i0.ɵɵtemplate(28, RegisterPage_mat_icon_28_Template, 2, 0, \"mat-icon\", 12)(29, RegisterPage_span_29_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"div\", 14)(31, \"span\");\n          i0.ɵɵtext(32, \"Type\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"span\");\n          i0.ɵɵtext(34, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\");\n          i0.ɵɵtext(36, \"Invoice\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"span\");\n          i0.ɵɵtext(38, \"Submit\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(39, RegisterPage_div_39_Template, 5, 3, \"div\", 15);\n          i0.ɵɵelementStart(40, \"div\", 16);\n          i0.ɵɵtemplate(41, RegisterPage_div_41_Template, 9, 2, \"div\", 17)(42, RegisterPage_div_42_Template, 10, 2, \"div\", 17)(43, RegisterPage_div_43_Template, 16, 4, \"div\", 17)(44, RegisterPage_div_44_Template, 50, 8, \"div\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"div\", 18);\n          i0.ɵɵtemplate(46, RegisterPage_button_46_Template, 4, 0, \"button\", 19);\n          i0.ɵɵelement(47, \"div\", 20);\n          i0.ɵɵelementStart(48, \"button\", 21);\n          i0.ɵɵlistener(\"click\", function RegisterPage_Template_button_click_48_listener() {\n            return ctx.goToNextStep();\n          });\n          i0.ɵɵtemplate(49, RegisterPage_span_49_Template, 2, 0, \"span\", 12)(50, RegisterPage_span_50_Template, 4, 3, \"span\", 12)(51, RegisterPage_mat_icon_51_Template, 2, 0, \"mat-icon\", 12);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(52, RegisterPage_div_52_Template, 13, 0, \"div\", 22);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵstyleProp(\"width\", ctx.currentStep / 4 * 100, \"%\");\n          i0.ɵɵadvance(7);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 1)(\"completed\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep <= 1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep > 1);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 2)(\"completed\", ctx.currentStep > 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep <= 2);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep > 2);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 3)(\"completed\", ctx.currentStep > 3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep <= 3);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep > 3);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"active\", ctx.currentStep >= 4)(\"completed\", ctx.currentStep > 4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep <= 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep === 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep === 2);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep === 3);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"active\", ctx.currentStep === 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 2);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 3);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep > 1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.canProceedToNextStep());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep === 4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.currentStep < 4);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isStepCompleted(4));\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.MatFormField, i5.MatLabel, i5.MatError, i5.MatSuffix, i6.MatInput, i7.MatButton, i7.MatIconButton, i8.MatCard, i8.MatCardContent, i8.MatCardHeader, i8.MatCardTitle, i9.MatIcon, i10.MatCheckbox, i11.MatProgressSpinner, i12.MatToolbar, i13.MatRadioGroup, i13.MatRadioButton, i4.DatePipe],\n      styles: [\".register-content[_ngcontent-%COMP%] {\\n  --background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n}\\n.register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 28px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .header-section[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  max-width: 600px;\\n  margin: 0 auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: #e0e0e0;\\n  color: #999;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 600;\\n  font-size: 16px;\\n  margin-bottom: 8px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.active[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-number.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.active[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step.completed[_ngcontent-%COMP%]   .step-number[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n  color: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 2px;\\n  background: #e0e0e0;\\n  margin: 0 16px;\\n  margin-bottom: 32px;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector.completed[_ngcontent-%COMP%] {\\n  background: #4caf50;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  padding: 32px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 24px;\\n  font-weight: 600;\\n  margin: 0 0 8px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-section[_ngcontent-%COMP%]   .section-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0 0 32px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 12px;\\n  padding: 20px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card.selected[_ngcontent-%COMP%] {\\n  border-color: #1976d2;\\n  background: #e3f2fd;\\n  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 4px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%]   .complaint-type-card[_ngcontent-%COMP%]   .card-radio[_ngcontent-%COMP%]   ion-radio[_ngcontent-%COMP%] {\\n  --color: #1976d2;\\n  --color-checked: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%] {\\n  border: 2px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  background: white;\\n  transition: all 0.3s ease;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]:hover {\\n  border-color: #ff9800;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option.mat-radio-checked[_ngcontent-%COMP%] {\\n  border-color: #ff9800;\\n  background: #fff3e0;\\n  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.2);\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%] {\\n  margin-left: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]   .description-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-outer-circle {\\n  border-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-descriptions-section[_ngcontent-%COMP%]   .description-options[_ngcontent-%COMP%]   .description-radio-group[_ngcontent-%COMP%]   .description-option[_ngcontent-%COMP%]     .mat-radio-checked .mat-radio-container .mat-radio-inner-circle {\\n  background-color: #ff9800;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%] {\\n  background: #f3e5f5;\\n  border: 2px solid #9c27b0;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-summary-display[_ngcontent-%COMP%]   .summary-card[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 20px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 22px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   .final-review-badge[_ngcontent-%COMP%] {\\n  background: #1565c0;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n  margin-left: auto;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 2px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1565c0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 20px;\\n  margin-top: 16px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%] {\\n  margin: 16px 0 12px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #c8e6c9;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #2e7d32;\\n  font-weight: 600;\\n  font-size: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .section-header-readonly[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 11px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-details-grid[_ngcontent-%COMP%]   .readonly-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: block;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 4px;\\n  padding: 8px;\\n  color: #1b5e20;\\n  font-size: 13px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-item-header[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-weight: 600;\\n  font-size: 14px;\\n  margin-bottom: 8px;\\n  padding-bottom: 4px;\\n  border-bottom: 1px solid #e8f5e8;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-item-description[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-weight: 500;\\n  font-size: 13px;\\n  margin-bottom: 12px;\\n  background: #f1f8e9;\\n  padding: 6px;\\n  border-radius: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));\\n  gap: 8px;\\n  margin-bottom: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 10px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .readonly-specs-grid[_ngcontent-%COMP%]   .readonly-spec[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  background: #e8f5e8;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 3px;\\n  padding: 4px 6px;\\n  color: #1b5e20;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding-top: 12px;\\n  border-top: 1px solid #c8e6c9;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 11px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n  display: block;\\n}\\n.register-content[_ngcontent-%COMP%]   .complete-summary-display[_ngcontent-%COMP%]   .summary-sections[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .invoice-details-readonly[_ngcontent-%COMP%]   .readonly-items-container[_ngcontent-%COMP%]   .readonly-item-card[_ngcontent-%COMP%]   .defect-selection-readonly[_ngcontent-%COMP%]   .defect-value[_ngcontent-%COMP%] {\\n  background: #ffebee;\\n  border: 1px solid #f8bbd9;\\n  border-radius: 4px;\\n  padding: 6px 8px;\\n  color: #c62828;\\n  font-size: 12px;\\n  font-weight: 600;\\n  display: inline-block;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .contact-form-section[_ngcontent-%COMP%]   .contact-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"CONTACT DETAILS\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-bottom: 24px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 12px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  background: white;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]:hover {\\n  border-color: #1976d2;\\n  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-header[_ngcontent-%COMP%]   .invoice-date[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-customer[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .search-results[_ngcontent-%COMP%]   .invoice-list[_ngcontent-%COMP%]   .invoice-item[_ngcontent-%COMP%]   .invoice-zone[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n  font-size: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .no-results[_ngcontent-%COMP%]   p.search-hint[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #999;\\n  font-style: italic;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%] {\\n  margin: 20px 0 16px 0;\\n  padding-bottom: 8px;\\n  border-bottom: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n  font-weight: 600;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #666;\\n  font-size: 12px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 14px;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%] {\\n  margin-bottom: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  padding: 12px 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .mat-mdc-card-header[_ngcontent-%COMP%]   .item-code[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #1976d2;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-description[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  margin-bottom: 16px;\\n  padding: 8px;\\n  background-color: #f9f9f9;\\n  border-radius: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #666;\\n  font-size: 0.8rem;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .item-specs-grid[_ngcontent-%COMP%]   .spec-item[_ngcontent-%COMP%]   .spec-value[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #333;\\n  font-size: 0.9rem;\\n  padding: 6px 8px;\\n  background-color: #e3f2fd;\\n  border-radius: 4px;\\n  text-align: center;\\n  border: 1px solid #bbdefb;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .defect-selection[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .items-container[_ngcontent-%COMP%]   .item-card[_ngcontent-%COMP%]   .defect-selection[_ngcontent-%COMP%]   .defect-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-invoice[_ngcontent-%COMP%]   .invoice-details-card[_ngcontent-%COMP%]   .invoice-actions[_ngcontent-%COMP%] {\\n  border-top: 1px solid #e0e0e0;\\n  padding-top: 16px;\\n  text-align: right;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #1976d2;\\n  border-radius: 8px;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   ion-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-size: 16px;\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .selected-type-display[_ngcontent-%COMP%]   .type-display-card[_ngcontent-%COMP%]   div[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin: 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #2e7d32;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%] {\\n  background: #f1f8e9;\\n  border: 2px solid #4caf50;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]::before {\\n  content: \\\"READ ONLY\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #4caf50;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  gap: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 1fr 1fr;\\n  gap: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item.full-width[_ngcontent-%COMP%] {\\n  grid-column: 1/-1;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2e7d32;\\n  font-size: 12px;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  margin-bottom: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: white;\\n  border: 1px solid #c8e6c9;\\n  border-radius: 6px;\\n  padding: 12px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .invoice-details-section[_ngcontent-%COMP%]   .readonly-invoice-card[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%]   .detail-group[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   .readonly-field[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #1b5e20;\\n  font-size: 14px;\\n  font-weight: 500;\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin: 0 0 20px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%] {\\n  background: #fff3e0;\\n  border: 2px solid #ff9800;\\n  border-radius: 12px;\\n  padding: 24px;\\n  position: relative;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-form-section[_ngcontent-%COMP%]   .editable-form-card[_ngcontent-%COMP%]::before {\\n  content: \\\"EDITABLE\\\";\\n  position: absolute;\\n  top: -10px;\\n  right: 20px;\\n  background: #ff9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 11px;\\n  font-weight: 600;\\n  letter-spacing: 0.5px;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0;\\n  }\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .half-width[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n.register-content[_ngcontent-%COMP%]   .form-row[_ngcontent-%COMP%]   .full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.search-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.register-content[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  padding: 16px;\\n  background: #f3e5f5;\\n  border: 1px solid #9c27b0;\\n  border-radius: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-label[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .complaint-letters-section[_ngcontent-%COMP%]   .checkbox-hint[_ngcontent-%COMP%] {\\n  margin: 8px 0 0 32px;\\n  color: #666;\\n  font-size: 13px;\\n  font-style: italic;\\n  line-height: 1.4;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%] {\\n  margin: 24px 0 0 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin: 0 0 16px 0;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  border: 2px dashed #9c27b0;\\n  border-radius: 8px;\\n  background: #fce4ec;\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%] {\\n  margin-bottom: 12px;\\n  border-color: #9c27b0;\\n  color: #7b1fa2;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(156, 39, 176, 0.04);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 12px;\\n  margin: 8px 0 0 0;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 4px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .upload-area[_ngcontent-%COMP%]   .upload-hint[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  width: 14px;\\n  height: 14px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 14px;\\n  font-weight: 600;\\n  margin: 0 0 12px 0;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: white;\\n  border-radius: 8px;\\n  margin-bottom: 8px;\\n  border: 1px solid #e1bee7;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]:hover {\\n  border-color: #9c27b0;\\n  box-shadow: 0 2px 8px rgba(156, 39, 176, 0.15);\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-icon[_ngcontent-%COMP%] {\\n  color: #7b1fa2;\\n  font-size: 20px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  flex-direction: column;\\n  gap: 2px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-name[_ngcontent-%COMP%] {\\n  font-size: 14px;\\n  color: #333;\\n  font-weight: 500;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .file-info[_ngcontent-%COMP%]   .file-size[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #666;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%] {\\n  width: 36px;\\n  height: 36px;\\n}\\n.register-content[_ngcontent-%COMP%]   .file-upload-section[_ngcontent-%COMP%]   .selected-files[_ngcontent-%COMP%]   .file-list[_ngcontent-%COMP%]   .file-item[_ngcontent-%COMP%]   .remove-file[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  justify-content: space-between;\\n  margin-top: 32px;\\n  padding-top: 24px;\\n  border-top: 2px solid #e0e0e0;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%] {\\n  color: #666;\\n  border-color: #ccc;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  border-color: #999;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);\\n  color: white;\\n  font-weight: 600;\\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);\\n  box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);\\n  transform: translateY(-1px);\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   .submit-button[_ngcontent-%COMP%]:disabled {\\n  background: #ccc;\\n  color: #999;\\n  box-shadow: none;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  height: 44px;\\n  border-radius: 8px;\\n  font-weight: 500;\\n  transition: all 0.3s ease;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child   mat-icon[_ngcontent-%COMP%] {\\n  margin-left: 0;\\n  margin-right: 8px;\\n}\\n.register-content[_ngcontent-%COMP%]   .step-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-spinner[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .register-content[_ngcontent-%COMP%]   .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .step-content[_ngcontent-%COMP%] {\\n    padding: 24px 16px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .complaint-types-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-label[_ngcontent-%COMP%] {\\n    font-size: 10px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .progress-section[_ngcontent-%COMP%]   .progress-steps[_ngcontent-%COMP%]   .step-connector[_ngcontent-%COMP%] {\\n    margin: 0 8px;\\n  }\\n  .register-content[_ngcontent-%COMP%]   .invoice-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "tmp_2_0", "ctx_r0", "getSelectedComplaintType", "icon", "ɵɵtextInterpolate1", "tmp_3_0", "label", "getSelectedComplaintDescription", "selectedInvoice", "invoiceNumber", "ɵɵtemplate", "RegisterPage_div_39_mat_chip_2_Template", "RegisterPage_div_39_mat_chip_3_Template", "RegisterPage_div_39_mat_chip_4_Template", "ɵɵproperty", "ɵɵlistener", "RegisterPage_div_41_mat_card_8_Template_mat_card_click_0_listener", "type_r3", "ɵɵrestoreView", "_r2", "$implicit", "ɵɵnextContext", "ɵɵresetView", "selectComplaintType", "ɵɵelement", "ɵɵclassProp", "complaintTypeForm", "get", "value", "tmp_4_0", "description", "RegisterPage_div_41_mat_card_8_Template", "complaintTypes", "complaintDescriptionForm", "desc_r5", "RegisterPage_div_42_Template_mat_radio_group_change_8_listener", "$event", "_r4", "onDescriptionChange", "RegisterPage_div_42_mat_card_9_Template", "getComplaintDescriptions", "RegisterPage_div_43_div_13_mat_card_4_Template_mat_card_click_0_listener", "invoice_r8", "_r7", "selectInvoice", "ɵɵpipeBind2", "invoiceDate", "customerName", "zone", "RegisterPage_div_43_div_13_mat_card_4_Template", "invoiceSearchResults", "length", "RegisterPage_div_43_mat_card_14_Template_button_click_25_listener", "_r9", "clearInvoiceSelection", "items", "RegisterPage_div_43_Template_input_input_10_listener", "_r6", "onInvoiceSearch", "RegisterPage_div_43_div_13_Template", "RegisterPage_div_43_mat_card_14_Template", "RegisterPage_div_43_div_15_Template", "invoiceSearchForm", "showInvoiceResults", "ɵɵtextInterpolate2", "getErrorMessage", "complaintDetailsForm", "RegisterPage_div_44_div_49_div_9_mat_chip_2_Template_mat_chip_removed_0_listener", "i_r13", "_r12", "index", "removeFile", "file_r14", "name", "RegisterPage_div_44_div_49_div_9_mat_chip_2_Template", "selectedFiles", "RegisterPage_div_44_div_49_Template_input_change_1_listener", "_r10", "onFileSelected", "RegisterPage_div_44_div_49_Template_button_click_3_listener", "fileInput_r11", "ɵɵreference", "click", "RegisterPage_div_44_div_49_div_9_Template", "RegisterPage_div_44_mat_chip_21_Template", "RegisterPage_div_44_mat_error_32_Template", "RegisterPage_div_44_mat_error_39_Template", "RegisterPage_div_44_div_49_Template", "tmp_1_0", "tmp_6_0", "invalid", "touched", "tmp_7_0", "tmp_8_0", "RegisterPage_button_46_Template_button_click_0_listener", "_r15", "goToPreviousStep", "RegisterPage_span_50_mat_icon_1_Template", "RegisterPage_span_50_mat_spinner_2_Template", "isLoading", "RegisterPage_div_52_Template_button_click_9_listener", "_r16", "goBack", "RegisterPage", "constructor", "formBuilder", "router", "loadingController", "toastController", "currentStep", "complaintDescriptions", "sampleInvoices", "Date", "customerAddress", "operatingUnit", "organization", "billToLocation", "shipToLocation", "itemCode", "thickness", "width", "height", "quantity", "csqm", "receivedBoxes", "createForms", "ngOnInit", "showAllInvoices", "group", "selectedType", "required", "selectedDescription", "searchTerm", "contactPersonName", "<PERSON><PERSON><PERSON><PERSON>", "contactNumber", "pattern", "comments", "hasComplaintLetters", "attachedFile", "type", "patchValue", "setTimeout", "goToStep2", "selected<PERSON><PERSON><PERSON>", "descriptions", "find", "desc", "event", "goToStep3", "valid", "goToStep4", "goBackToStep1", "goBackToStep2", "goBackToStep3", "trim", "filter", "invoice", "toLowerCase", "includes", "onSubmitComplaint", "_this", "_asyncToGenerator", "loading", "create", "message", "duration", "present", "dismiss", "toast", "now", "color", "position", "navigate", "files", "target", "Array", "from", "splice", "isStepCompleted", "step", "form", "field", "control", "<PERSON><PERSON><PERSON><PERSON>", "getFieldLabel", "errors", "<PERSON><PERSON><PERSON><PERSON>", "labels", "goToNextStep", "canProceedToNextStep", "updateStepCompletion", "i", "file", "size", "push", "fieldName", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "Router", "i3", "LoadingController", "ToastController", "selectors", "decls", "vars", "consts", "template", "RegisterPage_Template", "rf", "ctx", "RegisterPage_Template_button_click_1_listener", "RegisterPage_mat_icon_16_Template", "RegisterPage_span_17_Template", "RegisterPage_mat_icon_20_Template", "RegisterPage_span_21_Template", "RegisterPage_mat_icon_24_Template", "RegisterPage_span_25_Template", "RegisterPage_mat_icon_28_Template", "RegisterPage_span_29_Template", "RegisterPage_div_39_Template", "RegisterPage_div_41_Template", "RegisterPage_div_42_Template", "RegisterPage_div_43_Template", "RegisterPage_div_44_Template", "RegisterPage_button_46_Template", "RegisterPage_Template_button_click_48_listener", "RegisterPage_span_49_Template", "RegisterPage_span_50_Template", "RegisterPage_mat_icon_51_Template", "RegisterPage_div_52_Template", "ɵɵstyleProp"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\register\\register.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { LoadingController, ToastController } from '@ionic/angular';\n\nexport interface ItemInfo {\n  itemCode: string;\n  description: string;\n  thickness: number;\n  width: number;\n  height: number;\n  quantity: number;\n  csqm: number;\n  receivedBoxes: number;\n  defectedBarCode?: string;\n}\n\nexport interface InvoiceData {\n  invoiceNumber: string;\n  invoiceDate: Date;\n  customerName: string;\n  customerAddress: string;\n  zone: string;\n  operatingUnit: string;\n  organization: string;\n  billToLocation: string;\n  shipToLocation: string;\n  items: ItemInfo[];\n}\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.page.html',\n  styleUrls: ['./register.page.scss'],\n})\nexport class RegisterPage implements OnInit {\n\n  complaintTypeForm!: FormGroup;\n  complaintDescriptionForm!: FormGroup;\n  invoiceSearchForm!: FormGroup;\n  complaintDetailsForm!: FormGroup;\n  isLoading = false;\n  selectedFiles: File[] = [];\n  selectedInvoice: InvoiceData | null = null;\n  invoiceSearchResults: InvoiceData[] = [];\n  showInvoiceResults = false;\n  currentStep = 1;\n\n  complaintTypes = [\n    {\n      value: 'glass_quality',\n      label: 'Glass Quality Issues',\n      icon: 'diamond',\n      description: 'Scratches, cracks, or defects in glass products'\n    },\n    {\n      value: 'installation',\n      label: 'Installation Problems',\n      icon: 'build',\n      description: 'Issues during glass installation process'\n    },\n    {\n      value: 'delivery_damage',\n      label: 'Delivery & Transportation',\n      icon: 'local_shipping',\n      description: 'Damage during delivery or transportation'\n    },\n    {\n      value: 'measurement',\n      label: 'Measurement Issues',\n      icon: 'straighten',\n      description: 'Incorrect measurements or sizing problems'\n    },\n    {\n      value: 'service',\n      label: 'Service Related',\n      icon: 'support_agent',\n      description: 'Customer service or support issues'\n    },\n    {\n      value: 'billing',\n      label: 'Billing & Documentation',\n      icon: 'receipt_long',\n      description: 'Invoice, billing, or documentation issues'\n    }\n  ];\n\n  complaintDescriptions: { [key: string]: any[] } = {\n    'glass_quality': [\n      { value: 'scratches', label: 'Scratches on Glass Surface', description: 'Visible scratches or marks on the glass surface' },\n      { value: 'cracks', label: 'Cracks or Chips', description: 'Cracks, chips, or fractures in the glass' },\n      { value: 'bubbles', label: 'Air Bubbles', description: 'Air bubbles or inclusions within the glass' },\n      { value: 'discoloration', label: 'Discoloration', description: 'Color variations or discoloration in the glass' },\n      { value: 'thickness', label: 'Thickness Issues', description: 'Incorrect thickness or uneven glass thickness' }\n    ],\n    'installation': [\n      { value: 'alignment', label: 'Alignment Problems', description: 'Glass not properly aligned during installation' },\n      { value: 'sealing', label: 'Sealing Issues', description: 'Poor sealing or gaps around the glass' },\n      { value: 'hardware', label: 'Hardware Problems', description: 'Issues with hinges, handles, or other hardware' },\n      { value: 'fitting', label: 'Poor Fitting', description: 'Glass does not fit properly in the frame' },\n      { value: 'damage_during', label: 'Damage During Installation', description: 'Glass damaged during the installation process' }\n    ],\n    'delivery_damage': [\n      { value: 'broken_transit', label: 'Broken in Transit', description: 'Glass broken during transportation' },\n      { value: 'packaging', label: 'Poor Packaging', description: 'Inadequate packaging causing damage' },\n      { value: 'handling', label: 'Rough Handling', description: 'Damage due to rough handling during delivery' },\n      { value: 'delayed', label: 'Delayed Delivery', description: 'Delivery was significantly delayed' },\n      { value: 'wrong_item', label: 'Wrong Item Delivered', description: 'Incorrect glass type or specifications delivered' }\n    ],\n    'measurement': [\n      { value: 'wrong_size', label: 'Wrong Size', description: 'Glass delivered in incorrect dimensions' },\n      { value: 'measurement_error', label: 'Measurement Error', description: 'Error in initial measurements taken' },\n      { value: 'specification', label: 'Specification Mismatch', description: 'Glass does not match ordered specifications' },\n      { value: 'template', label: 'Template Issues', description: 'Problems with measurement template or pattern' }\n    ],\n    'service': [\n      { value: 'communication', label: 'Poor Communication', description: 'Lack of proper communication from service team' },\n      { value: 'response_time', label: 'Slow Response Time', description: 'Delayed response to queries or complaints' },\n      { value: 'unprofessional', label: 'Unprofessional Behavior', description: 'Unprofessional conduct by service personnel' },\n      { value: 'incomplete_work', label: 'Incomplete Work', description: 'Service work left incomplete or unfinished' }\n    ],\n    'billing': [\n      { value: 'wrong_amount', label: 'Incorrect Amount', description: 'Wrong amount charged in the invoice' },\n      { value: 'missing_details', label: 'Missing Details', description: 'Important details missing from invoice' },\n      { value: 'duplicate', label: 'Duplicate Billing', description: 'Charged multiple times for the same service' },\n      { value: 'tax_error', label: 'Tax Calculation Error', description: 'Incorrect tax calculation or application' }\n    ]\n  };\n\n  // Sample invoice data for demonstration\n  sampleInvoices: InvoiceData[] = [\n    {\n      invoiceNumber: 'INV-2024-001',\n      invoiceDate: new Date('2024-01-15'),\n      customerName: 'ABC Construction Ltd.',\n      customerAddress: '123 Business Park, Sector 18, Gurgaon, Haryana - 122015',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - Gurgaon',\n      shipToLocation: 'Site Office - Noida, UP',\n      items: [\n        {\n          itemCode: 'FGDGAGA100.36602440LN',\n          description: 'DARK GREY 100-3660x2440 DARK GREY 100',\n          thickness: 10,\n          width: 2440,\n          height: 3660,\n          quantity: 176,\n          csqm: 392.9376,\n          receivedBoxes: 4\n        },\n        {\n          itemCode: 'FGDGAGA120.36602440LN',\n          description: 'DARK GREY 120-3660x2440 DARK GREY 120',\n          thickness: 12,\n          width: 2440,\n          height: 3660,\n          quantity: 212,\n          csqm: 160.7472,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-002',\n      invoiceDate: new Date('2024-01-18'),\n      customerName: 'XYZ Builders Pvt. Ltd.',\n      customerAddress: '456 Industrial Area, Phase 2, Chandigarh - 160002',\n      zone: 'North Zone',\n      operatingUnit: 'Chandigarh Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Regional Office - Chandigarh',\n      shipToLocation: 'Project Site - Mohali, Punjab',\n      items: [\n        {\n          itemCode: 'FGCGAGA120.36602770LN',\n          description: 'CLEAR GREY 120-3660x2770 CLEAR GREY 120',\n          thickness: 12,\n          width: 2770,\n          height: 3660,\n          quantity: 150,\n          csqm: 278.5420,\n          receivedBoxes: 3\n        },\n        {\n          itemCode: 'FGTGAGA080.24401830LN',\n          description: 'TINTED GREY 080-2440x1830 TINTED GREY 080',\n          thickness: 8,\n          width: 2440,\n          height: 1830,\n          quantity: 95,\n          csqm: 124.3680,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-003',\n      invoiceDate: new Date('2024-01-20'),\n      customerName: 'Modern Glass Solutions',\n      customerAddress: '789 Tech City, Electronic City, Bangalore - 560100',\n      zone: 'South Zone',\n      operatingUnit: 'Bangalore Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Bangalore',\n      shipToLocation: 'Client Location - Whitefield, Bangalore',\n      items: [\n        {\n          itemCode: 'FGBGAGA060.18302440LN',\n          description: 'BLUE GREY 060-1830x2440 BLUE GREY 060',\n          thickness: 6,\n          width: 1830,\n          height: 2440,\n          quantity: 88,\n          csqm: 195.2640,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-004',\n      invoiceDate: new Date('2024-01-22'),\n      customerName: 'Premium Interiors Pvt. Ltd.',\n      customerAddress: '321 Corporate Hub, Bandra Kurla Complex, Mumbai - 400051',\n      zone: 'West Zone',\n      operatingUnit: 'Mumbai Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Mumbai',\n      shipToLocation: 'Project Site - Andheri, Mumbai',\n      items: [\n        {\n          itemCode: 'FGGGAGA100.24403660LN',\n          description: 'GREEN GREY 100-2440x3660 GREEN GREY 100',\n          thickness: 10,\n          width: 2440,\n          height: 3660,\n          quantity: 120,\n          csqm: 267.8880,\n          receivedBoxes: 3\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-005',\n      invoiceDate: new Date('2024-01-25'),\n      customerName: 'Elite Developers',\n      customerAddress: '567 IT Park, Salt Lake, Kolkata - 700091',\n      zone: 'East Zone',\n      operatingUnit: 'Kolkata Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'East Regional Office - Kolkata',\n      shipToLocation: 'Construction Site - New Town, Kolkata',\n      items: [\n        {\n          itemCode: 'FGRGAGA080.18302440LN',\n          description: 'RED GREY 080-1830x2440 RED GREY 080',\n          thickness: 8,\n          width: 1830,\n          height: 2440,\n          quantity: 75,\n          csqm: 133.4400,\n          receivedBoxes: 2\n        },\n        {\n          itemCode: 'FGWGAGA120.36602440LN',\n          description: 'WHITE GREY 120-3660x2440 WHITE GREY 120',\n          thickness: 12,\n          width: 3660,\n          height: 2440,\n          quantity: 95,\n          csqm: 218.5680,\n          receivedBoxes: 1\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-006',\n      invoiceDate: new Date('2024-01-28'),\n      customerName: 'Skyline Architects',\n      customerAddress: '890 Design District, Jubilee Hills, Hyderabad - 500033',\n      zone: 'South Zone',\n      operatingUnit: 'Hyderabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'South Regional Office - Hyderabad',\n      shipToLocation: 'Project Location - Gachibowli, Hyderabad',\n      items: [\n        {\n          itemCode: 'FGYGAGA060.24401830LN',\n          description: 'YELLOW GREY 060-2440x1830 YELLOW GREY 060',\n          thickness: 6,\n          width: 2440,\n          height: 1830,\n          quantity: 65,\n          csqm: 145.2720,\n          receivedBoxes: 2\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-007',\n      invoiceDate: new Date('2024-02-01'),\n      customerName: 'Royal Glass Works',\n      customerAddress: '234 Industrial Estate, Ahmedabad - 380015',\n      zone: 'West Zone',\n      operatingUnit: 'Ahmedabad Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'West Regional Office - Ahmedabad',\n      shipToLocation: 'Factory Location - Gandhinagar, Gujarat',\n      items: [\n        {\n          itemCode: 'FGPGAGA100.36602770LN',\n          description: 'PURPLE GREY 100-3660x2770 PURPLE GREY 100',\n          thickness: 10,\n          width: 3660,\n          height: 2770,\n          quantity: 180,\n          csqm: 364.4520,\n          receivedBoxes: 4\n        }\n      ]\n    },\n    {\n      invoiceNumber: 'INV-2024-008',\n      invoiceDate: new Date('2024-02-05'),\n      customerName: 'Metro Construction Co.',\n      customerAddress: '456 Business Center, Connaught Place, New Delhi - 110001',\n      zone: 'North Zone',\n      operatingUnit: 'Delhi Operations',\n      organization: 'AIS Glass Solutions Pvt. Ltd.',\n      billToLocation: 'Head Office - New Delhi',\n      shipToLocation: 'Metro Station Site - Dwarka, Delhi',\n      items: [\n        {\n          itemCode: 'FGOGAGA080.24402440LN',\n          description: 'ORANGE GREY 080-2440x2440 ORANGE GREY 080',\n          thickness: 8,\n          width: 2440,\n          height: 2440,\n          quantity: 110,\n          csqm: 195.3760,\n          receivedBoxes: 3\n        },\n        {\n          itemCode: 'FGSGAGA120.18303660LN',\n          description: 'SILVER GREY 120-1830x3660 SILVER GREY 120',\n          thickness: 12,\n          width: 1830,\n          height: 3660,\n          quantity: 85,\n          csqm: 142.8180,\n          receivedBoxes: 2\n        }\n      ]\n    }\n  ];\n\n  constructor(\n    private formBuilder: FormBuilder,\n    private router: Router,\n    private loadingController: LoadingController,\n    private toastController: ToastController\n  ) {\n    this.createForms();\n  }\n\n  ngOnInit() {\n    // Show all invoices initially\n    this.showAllInvoices();\n  }\n\n  createForms() {\n    this.complaintTypeForm = this.formBuilder.group({\n      selectedType: ['', Validators.required]\n    });\n\n    this.complaintDescriptionForm = this.formBuilder.group({\n      selectedDescription: ['', Validators.required]\n    });\n\n    this.invoiceSearchForm = this.formBuilder.group({\n      searchTerm: [''] // No validation required since empty search shows all invoices\n    });\n\n    this.complaintDetailsForm = this.formBuilder.group({\n      contactPersonName: ['', [Validators.required, Validators.minLength(2)]],\n      contactNumber: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],\n      comments: [''],\n      hasComplaintLetters: [false],\n      attachedFile: [null]\n    });\n  }\n\n  selectComplaintType(type: any) {\n    this.complaintTypeForm.patchValue({ selectedType: type.value });\n    // Automatically go to step 2 when type is selected\n    setTimeout(() => {\n      this.goToStep2();\n    }, 300);\n  }\n\n  getComplaintDescriptions() {\n    const selectedType = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintDescriptions[selectedType] || [];\n  }\n\n  getSelectedComplaintDescription() {\n    const selectedValue = this.complaintDescriptionForm.get('selectedDescription')?.value;\n    const descriptions = this.getComplaintDescriptions();\n    return descriptions.find(desc => desc.value === selectedValue);\n  }\n\n  onDescriptionChange(event: any) {\n    // Automatically go to step 3 when description is selected\n    setTimeout(() => {\n      this.goToStep3();\n    }, 300);\n  }\n\n  // Navigation methods\n  goToStep2() {\n    if (this.complaintTypeForm.valid) {\n      this.currentStep = 2;\n    }\n  }\n\n  goToStep3() {\n    if (this.complaintDescriptionForm.valid) {\n      this.currentStep = 3;\n      this.showAllInvoices();\n    }\n  }\n\n  goToStep4() {\n    if (this.selectedInvoice) {\n      this.currentStep = 4;\n    }\n  }\n\n  goBackToStep1() {\n    this.currentStep = 1;\n  }\n\n  goBackToStep2() {\n    this.currentStep = 2;\n  }\n\n  goBackToStep3() {\n    this.currentStep = 3;\n    this.showAllInvoices();\n  }\n\n  showAllInvoices() {\n    this.invoiceSearchResults = [...this.sampleInvoices];\n    this.showInvoiceResults = true;\n  }\n\n  onInvoiceSearch() {\n    const searchTerm = this.invoiceSearchForm.get('searchTerm')?.value;\n\n    if (!searchTerm || searchTerm.trim() === '') {\n      // Show all invoices when search is empty\n      this.showAllInvoices();\n      return;\n    }\n\n    if (searchTerm.length >= 1) {\n      this.isLoading = true;\n\n      // Simulate search delay\n      setTimeout(() => {\n        this.invoiceSearchResults = this.sampleInvoices.filter(invoice =>\n          invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.zone.toLowerCase().includes(searchTerm.toLowerCase()) ||\n          invoice.operatingUnit.toLowerCase().includes(searchTerm.toLowerCase())\n        );\n        this.showInvoiceResults = true;\n        this.isLoading = false;\n      }, 500);\n    }\n  }\n\n  selectInvoice(invoice: InvoiceData) {\n    this.selectedInvoice = invoice;\n    this.showInvoiceResults = false;\n    this.invoiceSearchForm.patchValue({ searchTerm: invoice.invoiceNumber });\n    // Automatically go to step 4 when invoice is selected\n    setTimeout(() => {\n      this.goToStep4();\n    }, 300);\n  }\n\n  clearInvoiceSelection() {\n    this.selectedInvoice = null;\n    this.invoiceSearchForm.patchValue({ searchTerm: '' });\n    // Show all invoices again when clearing selection\n    this.showAllInvoices();\n  }\n\n  async onSubmitComplaint() {\n    if (this.complaintTypeForm.valid && this.complaintDescriptionForm.valid && this.selectedInvoice && this.complaintDetailsForm.valid) {\n      this.isLoading = true;\n\n      const loading = await this.loadingController.create({\n        message: 'Registering complaint...',\n        duration: 3000\n      });\n\n      await loading.present();\n\n      // Simulate registration process\n      setTimeout(async () => {\n        this.isLoading = false;\n        await loading.dismiss();\n\n        const toast = await this.toastController.create({\n          message: 'Complaint registered successfully! Reference ID: #AIS' + Date.now(),\n          duration: 4000,\n          color: 'success',\n          position: 'top'\n        });\n        await toast.present();\n\n        // Navigate to track page\n        this.router.navigate(['/track']);\n      }, 3000);\n    } else {\n      const toast = await this.toastController.create({\n        message: 'Please complete all required steps and fill in all required fields.',\n        duration: 3000,\n        color: 'danger',\n        position: 'top'\n      });\n      await toast.present();\n    }\n  }\n\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files && files.length > 0) {\n      this.selectedFiles = Array.from(files);\n      this.complaintDetailsForm.patchValue({ attachedFile: files[0] });\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n    if (this.selectedFiles.length === 0) {\n      this.complaintDetailsForm.patchValue({ attachedFile: null });\n    }\n  }\n\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid && this.currentStep > 1;\n      case 2:\n        return this.complaintDescriptionForm.valid && this.currentStep > 2;\n      case 3:\n        return this.selectedInvoice !== null && this.currentStep > 3;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n\n  getErrorMessage(form: FormGroup, field: string): string {\n    const control = form.get(field);\n    if (control?.hasError('required')) {\n      return `${this.getFieldLabel(field)} is required`;\n    }\n    if (control?.hasError('minlength')) {\n      const minLength = control.errors?.['minlength'].requiredLength;\n      return `${this.getFieldLabel(field)} must be at least ${minLength} characters`;\n    }\n    if (control?.hasError('pattern')) {\n      return 'Please enter a valid phone number (10 digits)';\n    }\n    return '';\n  }\n\n  private getFieldLabel(field: string): string {\n    const labels: { [key: string]: string } = {\n      selectedType: 'Complaint Type',\n      selectedDescription: 'Complaint Description',\n      searchTerm: 'Search Term',\n      contactPersonName: 'Contact Person Name',\n      contactNumber: 'Contact Number',\n      comments: 'Comments'\n    };\n    return labels[field] || field;\n  }\n\n  goBack() {\n    this.router.navigate(['/home']);\n  }\n\n  // Navigation methods for ultra-modern UI\n  goToNextStep() {\n    if (this.canProceedToNextStep()) {\n      if (this.currentStep === 4) {\n        this.onSubmitComplaint();\n      } else {\n        this.currentStep++;\n        this.updateStepCompletion();\n      }\n    }\n  }\n\n  goToPreviousStep() {\n    if (this.currentStep > 1) {\n      this.currentStep--;\n    }\n  }\n\n  canProceedToNextStep(): boolean {\n    switch (this.currentStep) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDescriptionForm.valid;\n      case 3:\n        return !!this.selectedInvoice;\n      case 4:\n        return this.complaintDetailsForm.valid;\n      default:\n        return false;\n    }\n  }\n\n  updateStepCompletion() {\n    // Update step completion based on current step\n    if (this.currentStep > 1 && this.complaintTypeForm.valid) {\n      // Step 1 completed\n    }\n    if (this.currentStep > 2 && this.complaintDescriptionForm.valid) {\n      // Step 2 completed\n    }\n    if (this.currentStep > 3 && this.selectedInvoice) {\n      // Step 3 completed\n    }\n  }\n\n  // Helper methods for template\n  getSelectedComplaintType() {\n    const selectedValue = this.complaintTypeForm.get('selectedType')?.value;\n    return this.complaintTypes.find(type => type.value === selectedValue);\n  }\n\n  isStepCompleted(step: number): boolean {\n    switch (step) {\n      case 1:\n        return this.complaintTypeForm.valid;\n      case 2:\n        return this.complaintDescriptionForm.valid;\n      case 3:\n        return !!this.selectedInvoice;\n      case 4:\n        return this.complaintDetailsForm.valid && this.currentStep > 4;\n      default:\n        return false;\n    }\n  }\n\n  // File handling methods\n  onFileSelected(event: any) {\n    const files = event.target.files;\n    if (files) {\n      for (let i = 0; i < files.length; i++) {\n        const file = files[i];\n        if (file.size <= 5 * 1024 * 1024) { // 5MB limit\n          this.selectedFiles.push(file);\n        }\n      }\n    }\n  }\n\n  removeFile(index: number) {\n    this.selectedFiles.splice(index, 1);\n  }\n\n  // Error message helper\n  getErrorMessage(form: FormGroup, fieldName: string): string {\n    const field = form.get(fieldName);\n    if (field?.hasError('required')) {\n      return `${fieldName} is required`;\n    }\n    if (field?.hasError('pattern')) {\n      return `Invalid ${fieldName} format`;\n    }\n    if (field?.hasError('minlength')) {\n      return `${fieldName} is too short`;\n    }\n    return '';\n  }\n}\n", "<!-- Ultra-Modern Compact Header -->\n<mat-toolbar color=\"primary\" class=\"ultra-modern-toolbar\">\n  <button mat-icon-button (click)=\"goBack()\">\n    <mat-icon>arrow_back</mat-icon>\n  </button>\n  <div class=\"toolbar-content\">\n    <span class=\"toolbar-title\">Register Complaint</span>\n    <div class=\"mini-progress\">\n      <div class=\"progress-bar\" [style.width.%]=\"(currentStep / 4) * 100\"></div>\n    </div>\n  </div>\n  <button mat-icon-button>\n    <mat-icon>help_outline</mat-icon>\n  </button>\n</mat-toolbar>\n\n<!-- Ultra-Compact Main Container -->\n<div class=\"ultra-compact-container\">\n  <!-- Inline Progress Stepper -->\n  <div class=\"inline-stepper\">\n    <div class=\"step-progress\">\n      <div class=\"step-dot\" [class.active]=\"currentStep >= 1\" [class.completed]=\"currentStep > 1\">\n        <mat-icon *ngIf=\"currentStep > 1\">check</mat-icon>\n        <span *ngIf=\"currentStep <= 1\">1</span>\n      </div>\n      <div class=\"step-line\" [class.active]=\"currentStep > 1\"></div>\n      <div class=\"step-dot\" [class.active]=\"currentStep >= 2\" [class.completed]=\"currentStep > 2\">\n        <mat-icon *ngIf=\"currentStep > 2\">check</mat-icon>\n        <span *ngIf=\"currentStep <= 2\">2</span>\n      </div>\n      <div class=\"step-line\" [class.active]=\"currentStep > 2\"></div>\n      <div class=\"step-dot\" [class.active]=\"currentStep >= 3\" [class.completed]=\"currentStep > 3\">\n        <mat-icon *ngIf=\"currentStep > 3\">check</mat-icon>\n        <span *ngIf=\"currentStep <= 3\">3</span>\n      </div>\n      <div class=\"step-line\" [class.active]=\"currentStep > 3\"></div>\n      <div class=\"step-dot\" [class.active]=\"currentStep >= 4\" [class.completed]=\"currentStep > 4\">\n        <mat-icon *ngIf=\"currentStep > 4\">check</mat-icon>\n        <span *ngIf=\"currentStep <= 4\">4</span>\n      </div>\n    </div>\n    <div class=\"step-labels\">\n      <span [class.active]=\"currentStep === 1\">Type</span>\n      <span [class.active]=\"currentStep === 2\">Description</span>\n      <span [class.active]=\"currentStep === 3\">Invoice</span>\n      <span [class.active]=\"currentStep === 4\">Submit</span>\n    </div>\n  </div>\n\n  <!-- Breadcrumb Summary (Shows completed steps) -->\n  <div class=\"breadcrumb-summary\" *ngIf=\"currentStep > 1\">\n    <mat-chip-set>\n      <mat-chip *ngIf=\"getSelectedComplaintType()\" color=\"primary\" selected>\n        <mat-icon matChipAvatar>{{ getSelectedComplaintType()?.icon }}</mat-icon>\n        {{ getSelectedComplaintType()?.label }}\n      </mat-chip>\n      <mat-chip *ngIf=\"getSelectedComplaintDescription()\" color=\"accent\" selected>\n        {{ getSelectedComplaintDescription()?.label }}\n      </mat-chip>\n      <mat-chip *ngIf=\"selectedInvoice\" color=\"warn\" selected>\n        {{ selectedInvoice.invoiceNumber }}\n      </mat-chip>\n    </mat-chip-set>\n  </div>\n\n  <!-- Ultra-Compact Step Content -->\n  <div class=\"ultra-compact-step\">\n    \n    <!-- Step 1: Complaint Type Selection -->\n    <div *ngIf=\"currentStep === 1\" class=\"step-container\">\n      <div class=\"step-header\">\n        <h2>Select Complaint Type</h2>\n        <p>Choose the category that best describes your issue</p>\n      </div>\n      \n      <form [formGroup]=\"complaintTypeForm\">\n        <div class=\"compact-grid\">\n          <mat-card \n            class=\"type-option\" \n            *ngFor=\"let type of complaintTypes\"\n            [class.selected]=\"complaintTypeForm.get('selectedType')?.value === type.value\"\n            (click)=\"selectComplaintType(type)\"\n            matRipple>\n            <mat-card-content>\n              <div class=\"option-layout\">\n                <mat-icon class=\"option-icon\" [color]=\"complaintTypeForm.get('selectedType')?.value === type.value ? 'primary' : ''\">\n                  {{ type.icon }}\n                </mat-icon>\n                <div class=\"option-content\">\n                  <h3>{{ type.label }}</h3>\n                  <p>{{ type.description }}</p>\n                </div>\n                <mat-radio-button \n                  [value]=\"type.value\" \n                  formControlName=\"selectedType\"\n                  color=\"primary\">\n                </mat-radio-button>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 2: Complaint Description Selection -->\n    <div *ngIf=\"currentStep === 2\" class=\"step-container\">\n      <div class=\"step-header\">\n        <h2>Select Specific Issue</h2>\n        <p>Choose the description that best matches your complaint</p>\n      </div>\n      \n      <form [formGroup]=\"complaintDescriptionForm\">\n        <div class=\"compact-list\">\n          <mat-radio-group \n            formControlName=\"selectedDescription\" \n            (change)=\"onDescriptionChange($event)\">\n            <mat-card \n              class=\"description-option\"\n              *ngFor=\"let desc of getComplaintDescriptions()\"\n              [class.selected]=\"complaintDescriptionForm.get('selectedDescription')?.value === desc.value\"\n              matRipple>\n              <mat-card-content>\n                <div class=\"radio-layout\">\n                  <mat-radio-button \n                    [value]=\"desc.value\" \n                    color=\"primary\">\n                  </mat-radio-button>\n                  <div class=\"radio-content\">\n                    <h4>{{ desc.label }}</h4>\n                    <p>{{ desc.description }}</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </mat-radio-group>\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 3: Invoice Selection -->\n    <div *ngIf=\"currentStep === 3\" class=\"step-container\">\n      <div class=\"step-header\">\n        <h2>Select Invoice</h2>\n        <p>Search and select the invoice related to your complaint</p>\n      </div>\n      \n      <form [formGroup]=\"invoiceSearchForm\">\n        <!-- Compact Search -->\n        <mat-form-field appearance=\"outline\" class=\"search-field\">\n          <mat-label>Search Invoice</mat-label>\n          <input matInput formControlName=\"searchTerm\" \n                 placeholder=\"Invoice number, customer name, or leave empty for all\" \n                 (input)=\"onInvoiceSearch()\">\n          <mat-icon matSuffix>search</mat-icon>\n        </mat-form-field>\n\n        <!-- Compact Invoice List -->\n        <div class=\"invoice-list\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length > 0\">\n          <div class=\"results-info\">{{ invoiceSearchResults.length }} invoices found</div>\n          \n          <div class=\"compact-invoice-grid\">\n            <mat-card \n              class=\"invoice-option\" \n              *ngFor=\"let invoice of invoiceSearchResults\" \n              (click)=\"selectInvoice(invoice)\"\n              [class.selected]=\"selectedInvoice?.invoiceNumber === invoice.invoiceNumber\"\n              matRipple>\n              <mat-card-content>\n                <div class=\"invoice-layout\">\n                  <div class=\"invoice-main\">\n                    <div class=\"invoice-number\">{{ invoice.invoiceNumber }}</div>\n                    <div class=\"invoice-date\">{{ invoice.invoiceDate | date:'MMM dd' }}</div>\n                  </div>\n                  <div class=\"invoice-details\">\n                    <div class=\"customer\">{{ invoice.customerName }}</div>\n                    <div class=\"location\">{{ invoice.zone }}</div>\n                  </div>\n                  <mat-icon class=\"select-icon\">\n                    {{ selectedInvoice?.invoiceNumber === invoice.invoiceNumber ? 'check_circle' : 'radio_button_unchecked' }}\n                  </mat-icon>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n\n        <!-- Selected Invoice Preview -->\n        <mat-card class=\"selected-preview\" *ngIf=\"selectedInvoice\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon color=\"primary\">check_circle</mat-icon>\n              Selected: {{ selectedInvoice.invoiceNumber }}\n            </mat-card-title>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"preview-grid\">\n              <div><strong>Customer:</strong> {{ selectedInvoice.customerName }}</div>\n              <div><strong>Date:</strong> {{ selectedInvoice.invoiceDate | date:'MMM dd, yyyy' }}</div>\n              <div><strong>Zone:</strong> {{ selectedInvoice.zone }}</div>\n              <div><strong>Items:</strong> {{ selectedInvoice.items.length }} items</div>\n            </div>\n            <button mat-button color=\"warn\" (click)=\"clearInvoiceSelection()\">\n              <mat-icon>clear</mat-icon>\n              Change Selection\n            </button>\n          </mat-card-content>\n        </mat-card>\n\n        <!-- No Results -->\n        <div class=\"no-results\" *ngIf=\"showInvoiceResults && invoiceSearchResults.length === 0\">\n          <mat-icon>search_off</mat-icon>\n          <p>No invoices found</p>\n          <p class=\"hint\">Try different keywords or clear search</p>\n        </div>\n      </form>\n    </div>\n\n    <!-- Step 4: Final Details & Submit -->\n    <div *ngIf=\"currentStep === 4\" class=\"step-container\">\n      <div class=\"step-header\">\n        <h2>Contact Details & Submit</h2>\n        <p>Provide your contact information and submit your complaint</p>\n      </div>\n      \n      <!-- Compact Final Summary -->\n      <mat-card class=\"final-summary\">\n        <mat-card-content>\n          <div class=\"summary-title\">\n            <mat-icon>assignment</mat-icon>\n            <span>Complaint Summary</span>\n          </div>\n          <div class=\"summary-chips\">\n            <mat-chip-set>\n              <mat-chip color=\"primary\" selected>\n                <mat-icon matChipAvatar>{{ getSelectedComplaintType()?.icon }}</mat-icon>\n                {{ getSelectedComplaintType()?.label }}\n              </mat-chip>\n              <mat-chip color=\"accent\" selected>\n                {{ getSelectedComplaintDescription()?.label }}\n              </mat-chip>\n              <mat-chip color=\"warn\" selected *ngIf=\"selectedInvoice\">\n                {{ selectedInvoice.invoiceNumber }} • {{ selectedInvoice.customerName }}\n              </mat-chip>\n            </mat-chip-set>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Compact Contact Form -->\n      <mat-card class=\"contact-form\">\n        <mat-card-content>\n          <form [formGroup]=\"complaintDetailsForm\">\n            <div class=\"form-grid\">\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Contact Person *</mat-label>\n                <input matInput formControlName=\"contactPersonName\" placeholder=\"Your name\">\n                <mat-icon matSuffix>person</mat-icon>\n                <mat-error *ngIf=\"complaintDetailsForm.get('contactPersonName')?.invalid && complaintDetailsForm.get('contactPersonName')?.touched\">\n                  {{ getErrorMessage(complaintDetailsForm, 'contactPersonName') }}\n                </mat-error>\n              </mat-form-field>\n\n              <mat-form-field appearance=\"outline\">\n                <mat-label>Contact Number *</mat-label>\n                <input matInput formControlName=\"contactNumber\" placeholder=\"10-digit number\" type=\"tel\">\n                <mat-icon matSuffix>phone</mat-icon>\n                <mat-error *ngIf=\"complaintDetailsForm.get('contactNumber')?.invalid && complaintDetailsForm.get('contactNumber')?.touched\">\n                  {{ getErrorMessage(complaintDetailsForm, 'contactNumber') }}\n                </mat-error>\n              </mat-form-field>\n            </div>\n\n            <mat-form-field appearance=\"outline\" class=\"full-width\">\n              <mat-label>Additional Comments</mat-label>\n              <textarea matInput formControlName=\"comments\" rows=\"2\" placeholder=\"Optional additional information\"></textarea>\n              <mat-icon matSuffix>comment</mat-icon>\n            </mat-form-field>\n\n            <!-- Compact File Upload -->\n            <div class=\"upload-section\">\n              <mat-checkbox formControlName=\"hasComplaintLetters\" color=\"primary\">\n                Attach supporting documents\n              </mat-checkbox>\n              \n              <div class=\"upload-area\" *ngIf=\"complaintDetailsForm.get('hasComplaintLetters')?.value\">\n                <input type=\"file\" #fileInput (change)=\"onFileSelected($event)\" multiple accept=\".pdf,.jpg,.jpeg,.png,.doc,.docx\" style=\"display: none;\">\n                <button mat-stroked-button color=\"primary\" (click)=\"fileInput.click()\">\n                  <mat-icon>attach_file</mat-icon>\n                  Choose Files\n                </button>\n                <span class=\"upload-hint\">PDF, JPG, PNG, DOC (Max 5MB each)</span>\n                \n                <div class=\"file-chips\" *ngIf=\"selectedFiles.length > 0\">\n                  <mat-chip-set>\n                    <mat-chip *ngFor=\"let file of selectedFiles; let i = index\" [removable]=\"true\" (removed)=\"removeFile(i)\">\n                      <mat-icon matChipAvatar>description</mat-icon>\n                      {{ file.name }}\n                      <mat-icon matChipRemove>cancel</mat-icon>\n                    </mat-chip>\n                  </mat-chip-set>\n                </div>\n              </div>\n            </div>\n          </form>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Fixed Bottom Navigation -->\n  <div class=\"bottom-navigation\">\n    <button mat-button \n            *ngIf=\"currentStep > 1\" \n            (click)=\"goToPreviousStep()\" \n            class=\"nav-button\">\n      <mat-icon>arrow_back</mat-icon>\n      Back\n    </button>\n    \n    <div class=\"spacer\"></div>\n    \n    <button mat-raised-button \n            color=\"primary\" \n            (click)=\"goToNextStep()\" \n            [disabled]=\"!canProceedToNextStep()\"\n            class=\"nav-button\">\n      <span *ngIf=\"currentStep < 4\">Continue</span>\n      <span *ngIf=\"currentStep === 4\">\n        <mat-icon *ngIf=\"!isLoading\">send</mat-icon>\n        <mat-spinner *ngIf=\"isLoading\" diameter=\"20\"></mat-spinner>\n        {{ isLoading ? 'Submitting...' : 'Submit Complaint' }}\n      </span>\n      <mat-icon *ngIf=\"currentStep < 4\">arrow_forward</mat-icon>\n    </button>\n  </div>\n\n  <!-- Success State -->\n  <div class=\"success-state\" *ngIf=\"isStepCompleted(4)\">\n    <mat-card class=\"success-card\">\n      <mat-card-content>\n        <mat-icon class=\"success-icon\" color=\"primary\">check_circle</mat-icon>\n        <h2>Complaint Submitted Successfully!</h2>\n        <p>Your complaint has been registered and will be processed shortly.</p>\n        <button mat-raised-button color=\"primary\" (click)=\"goBack()\">\n          <mat-icon>home</mat-icon>\n          Back to Home\n        </button>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;ICqB3DC,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAClDH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAIvCH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAClDH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAIvCH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAClDH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAIvCH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAClDH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAevCH,EADF,CAAAC,cAAA,mBAAsE,mBAC5C;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzEH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAFeH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,EAAAC,OAAA,GAAAC,MAAA,CAAAC,wBAAA,qBAAAF,OAAA,CAAAG,IAAA,CAAsC;IAC9DT,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,OAAAC,OAAA,GAAAJ,MAAA,CAAAC,wBAAA,qBAAAG,OAAA,CAAAC,KAAA,MACF;;;;;IACAZ,EAAA,CAAAC,cAAA,mBAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,OAAAJ,OAAA,GAAAC,MAAA,CAAAM,+BAAA,qBAAAP,OAAA,CAAAM,KAAA,MACF;;;;;IACAZ,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAO,eAAA,CAAAC,aAAA,MACF;;;;;IAVFf,EADF,CAAAC,cAAA,cAAwD,mBACxC;IAQZD,EAPA,CAAAgB,UAAA,IAAAC,uCAAA,uBAAsE,IAAAC,uCAAA,uBAIM,IAAAC,uCAAA,uBAGpB;IAI5DnB,EADE,CAAAG,YAAA,EAAe,EACX;;;;IAXSH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAC,wBAAA,GAAgC;IAIhCR,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAM,+BAAA,GAAuC;IAGvCb,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAO,eAAA,CAAqB;;;;;;IAkB5Bd,EAAA,CAAAC,cAAA,mBAKY;IADVD,EAAA,CAAAqB,UAAA,mBAAAC,kEAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASrB,MAAA,CAAAsB,mBAAA,CAAAN,OAAA,CAAyB;IAAA,EAAC;IAI/BvB,EAFJ,CAAAC,cAAA,uBAAkB,cACW,mBAC4F;IACnHD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAETH,EADF,CAAAC,cAAA,cAA4B,SACtB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;IACNH,EAAA,CAAA8B,SAAA,4BAImB;IAGzB9B,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;;;;;;;IAnBTH,EAAA,CAAA+B,WAAA,eAAApB,OAAA,GAAAJ,MAAA,CAAAyB,iBAAA,CAAAC,GAAA,mCAAAtB,OAAA,CAAAuB,KAAA,MAAAX,OAAA,CAAAW,KAAA,CAA8E;IAK5ClC,EAAA,CAAAI,SAAA,GAAsF;IAAtFJ,EAAA,CAAAoB,UAAA,YAAAe,OAAA,GAAA5B,MAAA,CAAAyB,iBAAA,CAAAC,GAAA,mCAAAE,OAAA,CAAAD,KAAA,MAAAX,OAAA,CAAAW,KAAA,kBAAsF;IAClHlC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAa,OAAA,CAAAd,IAAA,MACF;IAEMT,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAX,KAAA,CAAgB;IACjBZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAkB,OAAA,CAAAa,WAAA,CAAsB;IAGzBpC,EAAA,CAAAI,SAAA,EAAoB;IAApBJ,EAAA,CAAAoB,UAAA,UAAAG,OAAA,CAAAW,KAAA,CAAoB;;;;;IAtB9BlC,EAFJ,CAAAC,cAAA,cAAsD,cAC3B,SACnB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yDAAkD;IACvDF,EADuD,CAAAG,YAAA,EAAI,EACrD;IAGJH,EADF,CAAAC,cAAA,eAAsC,cACV;IACxBD,EAAA,CAAAgB,UAAA,IAAAqB,uCAAA,wBAKY;IAoBlBrC,EAFI,CAAAG,YAAA,EAAM,EACD,EACH;;;;IA3BEH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAoB,UAAA,cAAAb,MAAA,CAAAyB,iBAAA,CAA+B;IAIdhC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAoB,UAAA,YAAAb,MAAA,CAAA+B,cAAA,CAAiB;;;;;IA2C9BtC,EANJ,CAAAC,cAAA,mBAIY,uBACQ,cACU;IACxBD,EAAA,CAAA8B,SAAA,2BAGmB;IAEjB9B,EADF,CAAAC,cAAA,cAA2B,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAIjCF,EAJiC,CAAAG,YAAA,EAAI,EACzB,EACF,EACW,EACV;;;;;;IAdTH,EAAA,CAAA+B,WAAA,eAAApB,OAAA,GAAAJ,MAAA,CAAAgC,wBAAA,CAAAN,GAAA,0CAAAtB,OAAA,CAAAuB,KAAA,MAAAM,OAAA,CAAAN,KAAA,CAA4F;IAKtFlC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAoB,UAAA,UAAAoB,OAAA,CAAAN,KAAA,CAAoB;IAIhBlC,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAmC,OAAA,CAAA5B,KAAA,CAAgB;IACjBZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAmC,OAAA,CAAAJ,WAAA,CAAsB;;;;;;IAtBrCpC,EAFJ,CAAAC,cAAA,cAAsD,cAC3B,SACnB;IAAAD,EAAA,CAAAE,MAAA,4BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8DAAuD;IAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;IAIFH,EAFJ,CAAAC,cAAA,eAA6C,cACjB,0BAGiB;IAAvCD,EAAA,CAAAqB,UAAA,oBAAAoB,+DAAAC,MAAA;MAAA1C,EAAA,CAAAwB,aAAA,CAAAmB,GAAA;MAAA,MAAApC,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAAUrB,MAAA,CAAAqC,mBAAA,CAAAF,MAAA,CAA2B;IAAA,EAAC;IACtC1C,EAAA,CAAAgB,UAAA,IAAA6B,uCAAA,uBAIY;IAiBpB7C,EAHM,CAAAG,YAAA,EAAkB,EACd,EACD,EACH;;;;IA1BEH,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAoB,UAAA,cAAAb,MAAA,CAAAgC,wBAAA,CAAsC;IAOnBvC,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAoB,UAAA,YAAAb,MAAA,CAAAuC,wBAAA,GAA6B;;;;;;IA2ChD9C,EAAA,CAAAC,cAAA,mBAKY;IAFVD,EAAA,CAAAqB,UAAA,mBAAA0B,yEAAA;MAAA,MAAAC,UAAA,GAAAhD,EAAA,CAAAwB,aAAA,CAAAyB,GAAA,EAAAvB,SAAA;MAAA,MAAAnB,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASrB,MAAA,CAAA2C,aAAA,CAAAF,UAAA,CAAsB;IAAA,EAAC;IAM1BhD,EAHN,CAAAC,cAAA,uBAAkB,cACY,cACA,cACI;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC7DH,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAyC;;IACrEF,EADqE,CAAAG,YAAA,EAAM,EACrE;IAEJH,EADF,CAAAC,cAAA,cAA6B,eACL;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACtDH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAC1CF,EAD0C,CAAAG,YAAA,EAAM,EAC1C;IACNH,EAAA,CAAAC,cAAA,oBAA8B;IAC5BD,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAW,EACP,EACW,EACV;;;;;IAjBTH,EAAA,CAAA+B,WAAA,cAAAxB,MAAA,CAAAO,eAAA,kBAAAP,MAAA,CAAAO,eAAA,CAAAC,aAAA,MAAAiC,UAAA,CAAAjC,aAAA,CAA2E;IAKzCf,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAK,iBAAA,CAAA2C,UAAA,CAAAjC,aAAA,CAA2B;IAC7Bf,EAAA,CAAAI,SAAA,GAAyC;IAAzCJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAmD,WAAA,OAAAH,UAAA,CAAAI,WAAA,YAAyC;IAG7CpD,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAK,iBAAA,CAAA2C,UAAA,CAAAK,YAAA,CAA0B;IAC1BrD,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAA2C,UAAA,CAAAM,IAAA,CAAkB;IAGxCtD,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,OAAAH,MAAA,CAAAO,eAAA,kBAAAP,MAAA,CAAAO,eAAA,CAAAC,aAAA,MAAAiC,UAAA,CAAAjC,aAAA,kDACF;;;;;IArBRf,EADF,CAAAC,cAAA,cAAwF,cAC5D;IAAAD,EAAA,CAAAE,MAAA,GAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEhFH,EAAA,CAAAC,cAAA,cAAkC;IAChCD,EAAA,CAAAgB,UAAA,IAAAuC,8CAAA,yBAKY;IAkBhBvD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA1BsBH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAU,kBAAA,KAAAH,MAAA,CAAAiD,oBAAA,CAAAC,MAAA,oBAAgD;IAKlDzD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAoB,UAAA,YAAAb,MAAA,CAAAiD,oBAAA,CAAuB;;;;;;IA2B3CxD,EAHN,CAAAC,cAAA,mBAA2D,sBACxC,qBACC,mBACY;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjDH,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAGTH,EAFT,CAAAC,cAAA,uBAAkB,cACU,UACnB,aAAQ;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACnEH,EAAL,CAAAC,cAAA,WAAK,cAAQ;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAuD;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACpFH,EAAL,CAAAC,cAAA,WAAK,cAAQ;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACvDH,EAAL,CAAAC,cAAA,WAAK,cAAQ;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAAwC;IACvEF,EADuE,CAAAG,YAAA,EAAM,EACvE;IACNH,EAAA,CAAAC,cAAA,kBAAkE;IAAlCD,EAAA,CAAAqB,UAAA,mBAAAqC,kEAAA;MAAA1D,EAAA,CAAAwB,aAAA,CAAAmC,GAAA;MAAA,MAAApD,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASrB,MAAA,CAAAqD,qBAAA,EAAuB;IAAA,EAAC;IAC/D5D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,0BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IAfLH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,gBAAAH,MAAA,CAAAO,eAAA,CAAAC,aAAA,MACF;IAIkCf,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAO,eAAA,CAAAuC,YAAA,KAAkC;IACtCrD,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAU,kBAAA,MAAAV,EAAA,CAAAmD,WAAA,QAAA5C,MAAA,CAAAO,eAAA,CAAAsC,WAAA,sBAAuD;IACvDpD,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAO,eAAA,CAAAwC,IAAA,KAA0B;IACzBtD,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAO,eAAA,CAAA+C,KAAA,CAAAJ,MAAA,WAAwC;;;;;IAWzEzD,EADF,CAAAC,cAAA,cAAwF,eAC5E;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxBH,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAE,MAAA,6CAAsC;IACxDF,EADwD,CAAAG,YAAA,EAAI,EACtD;;;;;;IAvENH,EAFJ,CAAAC,cAAA,cAAsD,cAC3B,SACnB;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8DAAuD;IAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;IAKFH,EAHJ,CAAAC,cAAA,eAAsC,yBAEsB,gBAC7C;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAC,cAAA,iBAEmC;IAA5BD,EAAA,CAAAqB,UAAA,mBAAAyC,qDAAA;MAAA9D,EAAA,CAAAwB,aAAA,CAAAuC,GAAA;MAAA,MAAAxD,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASrB,MAAA,CAAAyD,eAAA,EAAiB;IAAA,EAAC;IAFlChE,EAAA,CAAAG,YAAA,EAEmC;IACnCH,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;IAuDjBH,EApDA,CAAAgB,UAAA,KAAAiD,mCAAA,kBAAwF,KAAAC,wCAAA,wBA8B7B,KAAAC,mCAAA,kBAsB6B;IAM5FnE,EADE,CAAAG,YAAA,EAAO,EACH;;;;IArEEH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAoB,UAAA,cAAAb,MAAA,CAAA6D,iBAAA,CAA+B;IAWRpE,EAAA,CAAAI,SAAA,GAA2D;IAA3DJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAA8D,kBAAA,IAAA9D,MAAA,CAAAiD,oBAAA,CAAAC,MAAA,KAA2D;IA8BlDzD,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAO,eAAA,CAAqB;IAsBhCd,EAAA,CAAAI,SAAA,EAA6D;IAA7DJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAA8D,kBAAA,IAAA9D,MAAA,CAAAiD,oBAAA,CAAAC,MAAA,OAA6D;;;;;IA+BhFzD,EAAA,CAAAC,cAAA,mBAAwD;IACtDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IADTH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAsE,kBAAA,MAAA/D,MAAA,CAAAO,eAAA,CAAAC,aAAA,cAAAR,MAAA,CAAAO,eAAA,CAAAuC,YAAA,MACF;;;;;IAeErD,EAAA,CAAAC,cAAA,gBAAoI;IAClID,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAgE,eAAA,CAAAhE,MAAA,CAAAiE,oBAAA,4BACF;;;;;IAOAxE,EAAA,CAAAC,cAAA,gBAA4H;IAC1HD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;IADVH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAgE,eAAA,CAAAhE,MAAA,CAAAiE,oBAAA,wBACF;;;;;;IA0BIxE,EAAA,CAAAC,cAAA,mBAAyG;IAA1BD,EAAA,CAAAqB,UAAA,qBAAAoD,iFAAA;MAAA,MAAAC,KAAA,GAAA1E,EAAA,CAAAwB,aAAA,CAAAmD,IAAA,EAAAC,KAAA;MAAA,MAAArE,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAAWrB,MAAA,CAAAsE,UAAA,CAAAH,KAAA,CAAa;IAAA,EAAC;IACtG1E,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAE,MAAA,GACA;IAAAF,EAAA,CAAAC,cAAA,mBAAwB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAChCF,EADgC,CAAAG,YAAA,EAAW,EAChC;;;;IAJiDH,EAAA,CAAAoB,UAAA,mBAAkB;IAE5EpB,EAAA,CAAAI,SAAA,GACA;IADAJ,EAAA,CAAAU,kBAAA,MAAAoE,QAAA,CAAAC,IAAA,MACA;;;;;IAJJ/E,EADF,CAAAC,cAAA,cAAyD,mBACzC;IACZD,EAAA,CAAAgB,UAAA,IAAAgE,oDAAA,uBAAyG;IAM7GhF,EADE,CAAAG,YAAA,EAAe,EACX;;;;IANyBH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAoB,UAAA,YAAAb,MAAA,CAAA0E,aAAA,CAAkB;;;;;;IATjDjF,EADF,CAAAC,cAAA,cAAwF,mBACmD;IAA3GD,EAAA,CAAAqB,UAAA,oBAAA6D,4DAAAxC,MAAA;MAAA1C,EAAA,CAAAwB,aAAA,CAAA2D,IAAA;MAAA,MAAA5E,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAAUrB,MAAA,CAAA6E,cAAA,CAAA1C,MAAA,CAAsB;IAAA,EAAC;IAA/D1C,EAAA,CAAAG,YAAA,EAAyI;IACzIH,EAAA,CAAAC,cAAA,iBAAuE;IAA5BD,EAAA,CAAAqB,UAAA,mBAAAgE,4DAAA;MAAArF,EAAA,CAAAwB,aAAA,CAAA2D,IAAA;MAAA,MAAAG,aAAA,GAAAtF,EAAA,CAAAuF,WAAA;MAAA,OAAAvF,EAAA,CAAA4B,WAAA,CAAS0D,aAAA,CAAAE,KAAA,EAAiB;IAAA,EAAC;IACpExF,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAElEH,EAAA,CAAAgB,UAAA,IAAAyE,yCAAA,kBAAyD;IAS3DzF,EAAA,CAAAG,YAAA,EAAM;;;;IATqBH,EAAA,CAAAI,SAAA,GAA8B;IAA9BJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAA0E,aAAA,CAAAxB,MAAA,KAA8B;;;;;IAxE/DzD,EAFJ,CAAAC,cAAA,cAAsD,cAC3B,SACnB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iEAA0D;IAC/DF,EAD+D,CAAAG,YAAA,EAAI,EAC7D;IAMAH,EAHN,CAAAC,cAAA,mBAAgC,uBACZ,cACW,eACf;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;IAIAH,EAHN,CAAAC,cAAA,eAA2B,oBACX,oBACuB,oBACT;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzEH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,oBAAkC;IAChCD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAgB,UAAA,KAAA0E,wCAAA,uBAAwD;IAMhE1F,EAHM,CAAAG,YAAA,EAAe,EACX,EACW,EACV;IAQDH,EALV,CAAAC,cAAA,oBAA+B,wBACX,gBACyB,eAChB,0BACgB,iBACxB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAA8B,SAAA,iBAA4E;IAC5E9B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrCH,EAAA,CAAAgB,UAAA,KAAA2E,yCAAA,wBAAoI;IAGtI3F,EAAA,CAAAG,YAAA,EAAiB;IAGfH,EADF,CAAAC,cAAA,0BAAqC,iBACxB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAA8B,SAAA,iBAAyF;IACzF9B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAgB,UAAA,KAAA4E,yCAAA,wBAA4H;IAIhI5F,EADE,CAAAG,YAAA,EAAiB,EACb;IAGJH,EADF,CAAAC,cAAA,0BAAwD,iBAC3C;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC1CH,EAAA,CAAA8B,SAAA,oBAAgH;IAChH9B,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAC7BF,EAD6B,CAAAG,YAAA,EAAW,EACvB;IAIfH,EADF,CAAAC,cAAA,eAA4B,wBAC0C;IAClED,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAe;IAEfH,EAAA,CAAAgB,UAAA,KAAA6E,mCAAA,mBAAwF;IAsBlG7F,EAJQ,CAAAG,YAAA,EAAM,EACD,EACU,EACV,EACP;;;;;;;;;;IAxE8BH,EAAA,CAAAI,SAAA,IAAsC;IAAtCJ,EAAA,CAAAK,iBAAA,EAAAyF,OAAA,GAAAvF,MAAA,CAAAC,wBAAA,qBAAAsF,OAAA,CAAArF,IAAA,CAAsC;IAC9DT,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,OAAAJ,OAAA,GAAAC,MAAA,CAAAC,wBAAA,qBAAAF,OAAA,CAAAM,KAAA,MACF;IAEEZ,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,OAAAC,OAAA,GAAAJ,MAAA,CAAAM,+BAAA,qBAAAF,OAAA,CAAAC,KAAA,MACF;IACiCZ,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAO,eAAA,CAAqB;IAWpDd,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAoB,UAAA,cAAAb,MAAA,CAAAiE,oBAAA,CAAkC;IAMtBxE,EAAA,CAAAI,SAAA,GAAsH;IAAtHJ,EAAA,CAAAoB,UAAA,WAAA2E,OAAA,GAAAxF,MAAA,CAAAiE,oBAAA,CAAAvC,GAAA,wCAAA8D,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAxF,MAAA,CAAAiE,oBAAA,CAAAvC,GAAA,wCAAA8D,OAAA,CAAAE,OAAA,EAAsH;IAStHjG,EAAA,CAAAI,SAAA,GAA8G;IAA9GJ,EAAA,CAAAoB,UAAA,WAAA8E,OAAA,GAAA3F,MAAA,CAAAiE,oBAAA,CAAAvC,GAAA,oCAAAiE,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA3F,MAAA,CAAAiE,oBAAA,CAAAvC,GAAA,oCAAAiE,OAAA,CAAAD,OAAA,EAA8G;IAkBlGjG,EAAA,CAAAI,SAAA,IAA4D;IAA5DJ,EAAA,CAAAoB,UAAA,UAAA+E,OAAA,GAAA5F,MAAA,CAAAiE,oBAAA,CAAAvC,GAAA,0CAAAkE,OAAA,CAAAjE,KAAA,CAA4D;;;;;;IA2BhGlC,EAAA,CAAAC,cAAA,iBAG2B;IADnBD,EAAA,CAAAqB,UAAA,mBAAA+E,wDAAA;MAAApG,EAAA,CAAAwB,aAAA,CAAA6E,IAAA;MAAA,MAAA9F,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASrB,MAAA,CAAA+F,gBAAA,EAAkB;IAAA,EAAC;IAElCtG,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IASPH,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAE3CH,EAAA,CAAAC,cAAA,eAA6B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC5CH,EAAA,CAAA8B,SAAA,sBAA2D;;;;;IAF7D9B,EAAA,CAAAC,cAAA,WAAgC;IAE9BD,EADA,CAAAgB,UAAA,IAAAuF,wCAAA,uBAA6B,IAAAC,2CAAA,0BACgB;IAC7CxG,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAHMH,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAoB,UAAA,UAAAb,MAAA,CAAAkG,SAAA,CAAgB;IACbzG,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAoB,UAAA,SAAAb,MAAA,CAAAkG,SAAA,CAAe;IAC7BzG,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAH,MAAA,CAAAkG,SAAA,6CACF;;;;;IACAzG,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAQxDH,EAHN,CAAAC,cAAA,cAAsD,mBACrB,uBACX,oBAC+B;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtEH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wCAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,wEAAiE;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxEH,EAAA,CAAAC,cAAA,kBAA6D;IAAnBD,EAAA,CAAAqB,UAAA,mBAAAqF,qDAAA;MAAA1G,EAAA,CAAAwB,aAAA,CAAAmF,IAAA;MAAA,MAAApG,MAAA,GAAAP,EAAA,CAAA2B,aAAA;MAAA,OAAA3B,EAAA,CAAA4B,WAAA,CAASrB,MAAA,CAAAqG,MAAA,EAAQ;IAAA,EAAC;IAC1D5G,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACQ,EACV,EACP;;;AD1TR,OAAM,MAAO0G,YAAY;EAkUvBC,YACUC,WAAwB,EACxBC,MAAc,EACdC,iBAAoC,EACpCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,eAAe,GAAfA,eAAe;IAhUzB,KAAAT,SAAS,GAAG,KAAK;IACjB,KAAAxB,aAAa,GAAW,EAAE;IAC1B,KAAAnE,eAAe,GAAuB,IAAI;IAC1C,KAAA0C,oBAAoB,GAAkB,EAAE;IACxC,KAAAa,kBAAkB,GAAG,KAAK;IAC1B,KAAA8C,WAAW,GAAG,CAAC;IAEf,KAAA7E,cAAc,GAAG,CACf;MACEJ,KAAK,EAAE,eAAe;MACtBtB,KAAK,EAAE,sBAAsB;MAC7BH,IAAI,EAAE,SAAS;MACf2B,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,cAAc;MACrBtB,KAAK,EAAE,uBAAuB;MAC9BH,IAAI,EAAE,OAAO;MACb2B,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,iBAAiB;MACxBtB,KAAK,EAAE,2BAA2B;MAClCH,IAAI,EAAE,gBAAgB;MACtB2B,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,aAAa;MACpBtB,KAAK,EAAE,oBAAoB;MAC3BH,IAAI,EAAE,YAAY;MAClB2B,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,SAAS;MAChBtB,KAAK,EAAE,iBAAiB;MACxBH,IAAI,EAAE,eAAe;MACrB2B,WAAW,EAAE;KACd,EACD;MACEF,KAAK,EAAE,SAAS;MAChBtB,KAAK,EAAE,yBAAyB;MAChCH,IAAI,EAAE,cAAc;MACpB2B,WAAW,EAAE;KACd,CACF;IAED,KAAAgF,qBAAqB,GAA6B;MAChD,eAAe,EAAE,CACf;QAAElF,KAAK,EAAE,WAAW;QAAEtB,KAAK,EAAE,4BAA4B;QAAEwB,WAAW,EAAE;MAAiD,CAAE,EAC3H;QAAEF,KAAK,EAAE,QAAQ;QAAEtB,KAAK,EAAE,iBAAiB;QAAEwB,WAAW,EAAE;MAA0C,CAAE,EACtG;QAAEF,KAAK,EAAE,SAAS;QAAEtB,KAAK,EAAE,aAAa;QAAEwB,WAAW,EAAE;MAA4C,CAAE,EACrG;QAAEF,KAAK,EAAE,eAAe;QAAEtB,KAAK,EAAE,eAAe;QAAEwB,WAAW,EAAE;MAAgD,CAAE,EACjH;QAAEF,KAAK,EAAE,WAAW;QAAEtB,KAAK,EAAE,kBAAkB;QAAEwB,WAAW,EAAE;MAA+C,CAAE,CAChH;MACD,cAAc,EAAE,CACd;QAAEF,KAAK,EAAE,WAAW;QAAEtB,KAAK,EAAE,oBAAoB;QAAEwB,WAAW,EAAE;MAAgD,CAAE,EAClH;QAAEF,KAAK,EAAE,SAAS;QAAEtB,KAAK,EAAE,gBAAgB;QAAEwB,WAAW,EAAE;MAAuC,CAAE,EACnG;QAAEF,KAAK,EAAE,UAAU;QAAEtB,KAAK,EAAE,mBAAmB;QAAEwB,WAAW,EAAE;MAAgD,CAAE,EAChH;QAAEF,KAAK,EAAE,SAAS;QAAEtB,KAAK,EAAE,cAAc;QAAEwB,WAAW,EAAE;MAA0C,CAAE,EACpG;QAAEF,KAAK,EAAE,eAAe;QAAEtB,KAAK,EAAE,4BAA4B;QAAEwB,WAAW,EAAE;MAA+C,CAAE,CAC9H;MACD,iBAAiB,EAAE,CACjB;QAAEF,KAAK,EAAE,gBAAgB;QAAEtB,KAAK,EAAE,mBAAmB;QAAEwB,WAAW,EAAE;MAAoC,CAAE,EAC1G;QAAEF,KAAK,EAAE,WAAW;QAAEtB,KAAK,EAAE,gBAAgB;QAAEwB,WAAW,EAAE;MAAqC,CAAE,EACnG;QAAEF,KAAK,EAAE,UAAU;QAAEtB,KAAK,EAAE,gBAAgB;QAAEwB,WAAW,EAAE;MAA8C,CAAE,EAC3G;QAAEF,KAAK,EAAE,SAAS;QAAEtB,KAAK,EAAE,kBAAkB;QAAEwB,WAAW,EAAE;MAAoC,CAAE,EAClG;QAAEF,KAAK,EAAE,YAAY;QAAEtB,KAAK,EAAE,sBAAsB;QAAEwB,WAAW,EAAE;MAAkD,CAAE,CACxH;MACD,aAAa,EAAE,CACb;QAAEF,KAAK,EAAE,YAAY;QAAEtB,KAAK,EAAE,YAAY;QAAEwB,WAAW,EAAE;MAAyC,CAAE,EACpG;QAAEF,KAAK,EAAE,mBAAmB;QAAEtB,KAAK,EAAE,mBAAmB;QAAEwB,WAAW,EAAE;MAAqC,CAAE,EAC9G;QAAEF,KAAK,EAAE,eAAe;QAAEtB,KAAK,EAAE,wBAAwB;QAAEwB,WAAW,EAAE;MAA6C,CAAE,EACvH;QAAEF,KAAK,EAAE,UAAU;QAAEtB,KAAK,EAAE,iBAAiB;QAAEwB,WAAW,EAAE;MAA+C,CAAE,CAC9G;MACD,SAAS,EAAE,CACT;QAAEF,KAAK,EAAE,eAAe;QAAEtB,KAAK,EAAE,oBAAoB;QAAEwB,WAAW,EAAE;MAAgD,CAAE,EACtH;QAAEF,KAAK,EAAE,eAAe;QAAEtB,KAAK,EAAE,oBAAoB;QAAEwB,WAAW,EAAE;MAA2C,CAAE,EACjH;QAAEF,KAAK,EAAE,gBAAgB;QAAEtB,KAAK,EAAE,yBAAyB;QAAEwB,WAAW,EAAE;MAA6C,CAAE,EACzH;QAAEF,KAAK,EAAE,iBAAiB;QAAEtB,KAAK,EAAE,iBAAiB;QAAEwB,WAAW,EAAE;MAA4C,CAAE,CAClH;MACD,SAAS,EAAE,CACT;QAAEF,KAAK,EAAE,cAAc;QAAEtB,KAAK,EAAE,kBAAkB;QAAEwB,WAAW,EAAE;MAAqC,CAAE,EACxG;QAAEF,KAAK,EAAE,iBAAiB;QAAEtB,KAAK,EAAE,iBAAiB;QAAEwB,WAAW,EAAE;MAAwC,CAAE,EAC7G;QAAEF,KAAK,EAAE,WAAW;QAAEtB,KAAK,EAAE,mBAAmB;QAAEwB,WAAW,EAAE;MAA6C,CAAE,EAC9G;QAAEF,KAAK,EAAE,WAAW;QAAEtB,KAAK,EAAE,uBAAuB;QAAEwB,WAAW,EAAE;MAA0C,CAAE;KAElH;IAED;IACA,KAAAiF,cAAc,GAAkB,CAC9B;MACEtG,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,uBAAuB;MACrCkE,eAAe,EAAE,yDAAyD;MAC1EjE,IAAI,EAAE,YAAY;MAClBkE,aAAa,EAAE,kBAAkB;MACjCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,uBAAuB;MACvCC,cAAc,EAAE,yBAAyB;MACzC9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,uCAAuC;QACpDyF,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,uCAAuC;QACpDyF,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEnH,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,wBAAwB;MACtCkE,eAAe,EAAE,mDAAmD;MACpEjE,IAAI,EAAE,YAAY;MAClBkE,aAAa,EAAE,uBAAuB;MACtCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,8BAA8B;MAC9CC,cAAc,EAAE,+BAA+B;MAC/C9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,yCAAyC;QACtDyF,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,2CAA2C;QACxDyF,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEnH,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,wBAAwB;MACtCkE,eAAe,EAAE,oDAAoD;MACrEjE,IAAI,EAAE,YAAY;MAClBkE,aAAa,EAAE,sBAAsB;MACrCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE,yCAAyC;MACzD9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,uCAAuC;QACpDyF,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEnH,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,6BAA6B;MAC3CkE,eAAe,EAAE,0DAA0D;MAC3EjE,IAAI,EAAE,WAAW;MACjBkE,aAAa,EAAE,mBAAmB;MAClCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,+BAA+B;MAC/CC,cAAc,EAAE,gCAAgC;MAChD9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,yCAAyC;QACtDyF,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEnH,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,kBAAkB;MAChCkE,eAAe,EAAE,0CAA0C;MAC3DjE,IAAI,EAAE,WAAW;MACjBkE,aAAa,EAAE,oBAAoB;MACnCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,gCAAgC;MAChDC,cAAc,EAAE,uCAAuC;MACvD9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,qCAAqC;QAClDyF,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,yCAAyC;QACtDyF,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEnH,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,oBAAoB;MAClCkE,eAAe,EAAE,wDAAwD;MACzEjE,IAAI,EAAE,YAAY;MAClBkE,aAAa,EAAE,sBAAsB;MACrCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,mCAAmC;MACnDC,cAAc,EAAE,0CAA0C;MAC1D9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,2CAA2C;QACxDyF,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEnH,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,mBAAmB;MACjCkE,eAAe,EAAE,2CAA2C;MAC5DjE,IAAI,EAAE,WAAW;MACjBkE,aAAa,EAAE,sBAAsB;MACrCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,kCAAkC;MAClDC,cAAc,EAAE,yCAAyC;MACzD9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,2CAA2C;QACxDyF,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,EACD;MACEnH,aAAa,EAAE,cAAc;MAC7BqC,WAAW,EAAE,IAAIkE,IAAI,CAAC,YAAY,CAAC;MACnCjE,YAAY,EAAE,wBAAwB;MACtCkE,eAAe,EAAE,0DAA0D;MAC3EjE,IAAI,EAAE,YAAY;MAClBkE,aAAa,EAAE,kBAAkB;MACjCC,YAAY,EAAE,+BAA+B;MAC7CC,cAAc,EAAE,yBAAyB;MACzCC,cAAc,EAAE,oCAAoC;MACpD9D,KAAK,EAAE,CACL;QACE+D,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,2CAA2C;QACxDyF,SAAS,EAAE,CAAC;QACZC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,GAAG;QACbC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB,EACD;QACEN,QAAQ,EAAE,uBAAuB;QACjCxF,WAAW,EAAE,2CAA2C;QACxDyF,SAAS,EAAE,EAAE;QACbC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,EAAE;QACZC,IAAI,EAAE,QAAQ;QACdC,aAAa,EAAE;OAChB;KAEJ,CACF;IAQC,IAAI,CAACC,WAAW,EAAE;EACpB;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;EACxB;EAEAF,WAAWA,CAAA;IACT,IAAI,CAACnG,iBAAiB,GAAG,IAAI,CAAC+E,WAAW,CAACuB,KAAK,CAAC;MAC9CC,YAAY,EAAE,CAAC,EAAE,EAAExI,UAAU,CAACyI,QAAQ;KACvC,CAAC;IAEF,IAAI,CAACjG,wBAAwB,GAAG,IAAI,CAACwE,WAAW,CAACuB,KAAK,CAAC;MACrDG,mBAAmB,EAAE,CAAC,EAAE,EAAE1I,UAAU,CAACyI,QAAQ;KAC9C,CAAC;IAEF,IAAI,CAACpE,iBAAiB,GAAG,IAAI,CAAC2C,WAAW,CAACuB,KAAK,CAAC;MAC9CI,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;KAClB,CAAC;IAEF,IAAI,CAAClE,oBAAoB,GAAG,IAAI,CAACuC,WAAW,CAACuB,KAAK,CAAC;MACjDK,iBAAiB,EAAE,CAAC,EAAE,EAAE,CAAC5I,UAAU,CAACyI,QAAQ,EAAEzI,UAAU,CAAC6I,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACvEC,aAAa,EAAE,CAAC,EAAE,EAAE,CAAC9I,UAAU,CAACyI,QAAQ,EAAEzI,UAAU,CAAC+I,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;MAC7EC,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdC,mBAAmB,EAAE,CAAC,KAAK,CAAC;MAC5BC,YAAY,EAAE,CAAC,IAAI;KACpB,CAAC;EACJ;EAEApH,mBAAmBA,CAACqH,IAAS;IAC3B,IAAI,CAAClH,iBAAiB,CAACmH,UAAU,CAAC;MAAEZ,YAAY,EAAEW,IAAI,CAAChH;IAAK,CAAE,CAAC;IAC/D;IACAkH,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAvG,wBAAwBA,CAAA;IACtB,MAAMyF,YAAY,GAAG,IAAI,CAACvG,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACtE,OAAO,IAAI,CAACkF,qBAAqB,CAACmB,YAAY,CAAC,IAAI,EAAE;EACvD;EAEA1H,+BAA+BA,CAAA;IAC7B,MAAMyI,aAAa,GAAG,IAAI,CAAC/G,wBAAwB,CAACN,GAAG,CAAC,qBAAqB,CAAC,EAAEC,KAAK;IACrF,MAAMqH,YAAY,GAAG,IAAI,CAACzG,wBAAwB,EAAE;IACpD,OAAOyG,YAAY,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACvH,KAAK,KAAKoH,aAAa,CAAC;EAChE;EAEA1G,mBAAmBA,CAAC8G,KAAU;IAC5B;IACAN,UAAU,CAAC,MAAK;MACd,IAAI,CAACO,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACAN,SAASA,CAAA;IACP,IAAI,IAAI,CAACrH,iBAAiB,CAAC4H,KAAK,EAAE;MAChC,IAAI,CAACzC,WAAW,GAAG,CAAC;;EAExB;EAEAwC,SAASA,CAAA;IACP,IAAI,IAAI,CAACpH,wBAAwB,CAACqH,KAAK,EAAE;MACvC,IAAI,CAACzC,WAAW,GAAG,CAAC;MACpB,IAAI,CAACkB,eAAe,EAAE;;EAE1B;EAEAwB,SAASA,CAAA;IACP,IAAI,IAAI,CAAC/I,eAAe,EAAE;MACxB,IAAI,CAACqG,WAAW,GAAG,CAAC;;EAExB;EAEA2C,aAAaA,CAAA;IACX,IAAI,CAAC3C,WAAW,GAAG,CAAC;EACtB;EAEA4C,aAAaA,CAAA;IACX,IAAI,CAAC5C,WAAW,GAAG,CAAC;EACtB;EAEA6C,aAAaA,CAAA;IACX,IAAI,CAAC7C,WAAW,GAAG,CAAC;IACpB,IAAI,CAACkB,eAAe,EAAE;EACxB;EAEAA,eAAeA,CAAA;IACb,IAAI,CAAC7E,oBAAoB,GAAG,CAAC,GAAG,IAAI,CAAC6D,cAAc,CAAC;IACpD,IAAI,CAAChD,kBAAkB,GAAG,IAAI;EAChC;EAEAL,eAAeA,CAAA;IACb,MAAM0E,UAAU,GAAG,IAAI,CAACtE,iBAAiB,CAACnC,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAElE,IAAI,CAACwG,UAAU,IAAIA,UAAU,CAACuB,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3C;MACA,IAAI,CAAC5B,eAAe,EAAE;MACtB;;IAGF,IAAIK,UAAU,CAACjF,MAAM,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACgD,SAAS,GAAG,IAAI;MAErB;MACA2C,UAAU,CAAC,MAAK;QACd,IAAI,CAAC5F,oBAAoB,GAAG,IAAI,CAAC6D,cAAc,CAAC6C,MAAM,CAACC,OAAO,IAC5DA,OAAO,CAACpJ,aAAa,CAACqJ,WAAW,EAAE,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,EAAE,CAAC,IACtED,OAAO,CAAC9G,YAAY,CAAC+G,WAAW,EAAE,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,EAAE,CAAC,IACrED,OAAO,CAAC7G,IAAI,CAAC8G,WAAW,EAAE,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,EAAE,CAAC,IAC7DD,OAAO,CAAC3C,aAAa,CAAC4C,WAAW,EAAE,CAACC,QAAQ,CAAC3B,UAAU,CAAC0B,WAAW,EAAE,CAAC,CACvE;QACD,IAAI,CAAC/F,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAACoC,SAAS,GAAG,KAAK;MACxB,CAAC,EAAE,GAAG,CAAC;;EAEX;EAEAvD,aAAaA,CAACiH,OAAoB;IAChC,IAAI,CAACrJ,eAAe,GAAGqJ,OAAO;IAC9B,IAAI,CAAC9F,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACD,iBAAiB,CAAC+E,UAAU,CAAC;MAAET,UAAU,EAAEyB,OAAO,CAACpJ;IAAa,CAAE,CAAC;IACxE;IACAqI,UAAU,CAAC,MAAK;MACd,IAAI,CAACS,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEAjG,qBAAqBA,CAAA;IACnB,IAAI,CAAC9C,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACsD,iBAAiB,CAAC+E,UAAU,CAAC;MAAET,UAAU,EAAE;IAAE,CAAE,CAAC;IACrD;IACA,IAAI,CAACL,eAAe,EAAE;EACxB;EAEMiC,iBAAiBA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACrB,IAAID,KAAI,CAACvI,iBAAiB,CAAC4H,KAAK,IAAIW,KAAI,CAAChI,wBAAwB,CAACqH,KAAK,IAAIW,KAAI,CAACzJ,eAAe,IAAIyJ,KAAI,CAAC/F,oBAAoB,CAACoF,KAAK,EAAE;QAClIW,KAAI,CAAC9D,SAAS,GAAG,IAAI;QAErB,MAAMgE,OAAO,SAASF,KAAI,CAACtD,iBAAiB,CAACyD,MAAM,CAAC;UAClDC,OAAO,EAAE,0BAA0B;UACnCC,QAAQ,EAAE;SACX,CAAC;QAEF,MAAMH,OAAO,CAACI,OAAO,EAAE;QAEvB;QACAzB,UAAU,cAAAoB,iBAAA,CAAC,aAAW;UACpBD,KAAI,CAAC9D,SAAS,GAAG,KAAK;UACtB,MAAMgE,OAAO,CAACK,OAAO,EAAE;UAEvB,MAAMC,KAAK,SAASR,KAAI,CAACrD,eAAe,CAACwD,MAAM,CAAC;YAC9CC,OAAO,EAAE,uDAAuD,GAAGrD,IAAI,CAAC0D,GAAG,EAAE;YAC7EJ,QAAQ,EAAE,IAAI;YACdK,KAAK,EAAE,SAAS;YAChBC,QAAQ,EAAE;WACX,CAAC;UACF,MAAMH,KAAK,CAACF,OAAO,EAAE;UAErB;UACAN,KAAI,CAACvD,MAAM,CAACmE,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAClC,CAAC,GAAE,IAAI,CAAC;OACT,MAAM;QACL,MAAMJ,KAAK,SAASR,KAAI,CAACrD,eAAe,CAACwD,MAAM,CAAC;UAC9CC,OAAO,EAAE,qEAAqE;UAC9EC,QAAQ,EAAE,IAAI;UACdK,KAAK,EAAE,QAAQ;UACfC,QAAQ,EAAE;SACX,CAAC;QACF,MAAMH,KAAK,CAACF,OAAO,EAAE;;IACtB;EACH;EAEAzF,cAAcA,CAACsE,KAAU;IACvB,MAAM0B,KAAK,GAAG1B,KAAK,CAAC2B,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,IAAIA,KAAK,CAAC3H,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACwB,aAAa,GAAGqG,KAAK,CAACC,IAAI,CAACH,KAAK,CAAC;MACtC,IAAI,CAAC5G,oBAAoB,CAAC2E,UAAU,CAAC;QAAEF,YAAY,EAAEmC,KAAK,CAAC,CAAC;MAAC,CAAE,CAAC;;EAEpE;EAEAvG,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACK,aAAa,CAACuG,MAAM,CAAC5G,KAAK,EAAE,CAAC,CAAC;IACnC,IAAI,IAAI,CAACK,aAAa,CAACxB,MAAM,KAAK,CAAC,EAAE;MACnC,IAAI,CAACe,oBAAoB,CAAC2E,UAAU,CAAC;QAAEF,YAAY,EAAE;MAAI,CAAE,CAAC;;EAEhE;EAEAzI,wBAAwBA,CAAA;IACtB,MAAM8I,aAAa,GAAG,IAAI,CAACtH,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACvE,OAAO,IAAI,CAACI,cAAc,CAACkH,IAAI,CAACN,IAAI,IAAIA,IAAI,CAAChH,KAAK,KAAKoH,aAAa,CAAC;EACvE;EAEAmC,eAAeA,CAACC,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC1J,iBAAiB,CAAC4H,KAAK,IAAI,IAAI,CAACzC,WAAW,GAAG,CAAC;MAC7D,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC5E,wBAAwB,CAACqH,KAAK,IAAI,IAAI,CAACzC,WAAW,GAAG,CAAC;MACpE,KAAK,CAAC;QACJ,OAAO,IAAI,CAACrG,eAAe,KAAK,IAAI,IAAI,IAAI,CAACqG,WAAW,GAAG,CAAC;MAC9D,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC3C,oBAAoB,CAACoF,KAAK,IAAI,IAAI,CAACzC,WAAW,GAAG,CAAC;MAChE;QACE,OAAO,KAAK;;EAElB;EAEA5C,eAAeA,CAACoH,IAAe,EAAEC,KAAa;IAC5C,MAAMC,OAAO,GAAGF,IAAI,CAAC1J,GAAG,CAAC2J,KAAK,CAAC;IAC/B,IAAIC,OAAO,EAAEC,QAAQ,CAAC,UAAU,CAAC,EAAE;MACjC,OAAO,GAAG,IAAI,CAACC,aAAa,CAACH,KAAK,CAAC,cAAc;;IAEnD,IAAIC,OAAO,EAAEC,QAAQ,CAAC,WAAW,CAAC,EAAE;MAClC,MAAMlD,SAAS,GAAGiD,OAAO,CAACG,MAAM,GAAG,WAAW,CAAC,CAACC,cAAc;MAC9D,OAAO,GAAG,IAAI,CAACF,aAAa,CAACH,KAAK,CAAC,qBAAqBhD,SAAS,aAAa;;IAEhF,IAAIiD,OAAO,EAAEC,QAAQ,CAAC,SAAS,CAAC,EAAE;MAChC,OAAO,+CAA+C;;IAExD,OAAO,EAAE;EACX;EAEQC,aAAaA,CAACH,KAAa;IACjC,MAAMM,MAAM,GAA8B;MACxC3D,YAAY,EAAE,gBAAgB;MAC9BE,mBAAmB,EAAE,uBAAuB;MAC5CC,UAAU,EAAE,aAAa;MACzBC,iBAAiB,EAAE,qBAAqB;MACxCE,aAAa,EAAE,gBAAgB;MAC/BE,QAAQ,EAAE;KACX;IACD,OAAOmD,MAAM,CAACN,KAAK,CAAC,IAAIA,KAAK;EAC/B;EAEAhF,MAAMA,CAAA;IACJ,IAAI,CAACI,MAAM,CAACmE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;EAEA;EACAgB,YAAYA,CAAA;IACV,IAAI,IAAI,CAACC,oBAAoB,EAAE,EAAE;MAC/B,IAAI,IAAI,CAACjF,WAAW,KAAK,CAAC,EAAE;QAC1B,IAAI,CAACmD,iBAAiB,EAAE;OACzB,MAAM;QACL,IAAI,CAACnD,WAAW,EAAE;QAClB,IAAI,CAACkF,oBAAoB,EAAE;;;EAGjC;EAEA/F,gBAAgBA,CAAA;IACd,IAAI,IAAI,CAACa,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAiF,oBAAoBA,CAAA;IAClB,QAAQ,IAAI,CAACjF,WAAW;MACtB,KAAK,CAAC;QACJ,OAAO,IAAI,CAACnF,iBAAiB,CAAC4H,KAAK;MACrC,KAAK,CAAC;QACJ,OAAO,IAAI,CAACrH,wBAAwB,CAACqH,KAAK;MAC5C,KAAK,CAAC;QACJ,OAAO,CAAC,CAAC,IAAI,CAAC9I,eAAe;MAC/B,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC0D,oBAAoB,CAACoF,KAAK;MACxC;QACE,OAAO,KAAK;;EAElB;EAEAyC,oBAAoBA,CAAA;IAClB;IACA,IAAI,IAAI,CAAClF,WAAW,GAAG,CAAC,IAAI,IAAI,CAACnF,iBAAiB,CAAC4H,KAAK,EAAE;MACxD;IAAA;IAEF,IAAI,IAAI,CAACzC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC5E,wBAAwB,CAACqH,KAAK,EAAE;MAC/D;IAAA;IAEF,IAAI,IAAI,CAACzC,WAAW,GAAG,CAAC,IAAI,IAAI,CAACrG,eAAe,EAAE;MAChD;IAAA;EAEJ;EAEA;EACAN,wBAAwBA,CAAA;IACtB,MAAM8I,aAAa,GAAG,IAAI,CAACtH,iBAAiB,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEC,KAAK;IACvE,OAAO,IAAI,CAACI,cAAc,CAACkH,IAAI,CAACN,IAAI,IAAIA,IAAI,CAAChH,KAAK,KAAKoH,aAAa,CAAC;EACvE;EAEAmC,eAAeA,CAACC,IAAY;IAC1B,QAAQA,IAAI;MACV,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC1J,iBAAiB,CAAC4H,KAAK;MACrC,KAAK,CAAC;QACJ,OAAO,IAAI,CAACrH,wBAAwB,CAACqH,KAAK;MAC5C,KAAK,CAAC;QACJ,OAAO,CAAC,CAAC,IAAI,CAAC9I,eAAe;MAC/B,KAAK,CAAC;QACJ,OAAO,IAAI,CAAC0D,oBAAoB,CAACoF,KAAK,IAAI,IAAI,CAACzC,WAAW,GAAG,CAAC;MAChE;QACE,OAAO,KAAK;;EAElB;EAEA;EACA/B,cAAcA,CAACsE,KAAU;IACvB,MAAM0B,KAAK,GAAG1B,KAAK,CAAC2B,MAAM,CAACD,KAAK;IAChC,IAAIA,KAAK,EAAE;MACT,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,KAAK,CAAC3H,MAAM,EAAE6I,CAAC,EAAE,EAAE;QACrC,MAAMC,IAAI,GAAGnB,KAAK,CAACkB,CAAC,CAAC;QACrB,IAAIC,IAAI,CAACC,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE;UAAE;UAClC,IAAI,CAACvH,aAAa,CAACwH,IAAI,CAACF,IAAI,CAAC;;;;EAIrC;EAEA1H,UAAUA,CAACD,KAAa;IACtB,IAAI,CAACK,aAAa,CAACuG,MAAM,CAAC5G,KAAK,EAAE,CAAC,CAAC;EACrC;EAEA;EACAL,eAAeA,CAACoH,IAAe,EAAEe,SAAiB;IAChD,MAAMd,KAAK,GAAGD,IAAI,CAAC1J,GAAG,CAACyK,SAAS,CAAC;IACjC,IAAId,KAAK,EAAEE,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC/B,OAAO,GAAGY,SAAS,cAAc;;IAEnC,IAAId,KAAK,EAAEE,QAAQ,CAAC,SAAS,CAAC,EAAE;MAC9B,OAAO,WAAWY,SAAS,SAAS;;IAEtC,IAAId,KAAK,EAAEE,QAAQ,CAAC,WAAW,CAAC,EAAE;MAChC,OAAO,GAAGY,SAAS,eAAe;;IAEpC,OAAO,EAAE;EACX;;;uBA1pBW7F,YAAY,EAAA7G,EAAA,CAAA2M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7M,EAAA,CAAA2M,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA/M,EAAA,CAAA2M,iBAAA,CAAAK,EAAA,CAAAC,iBAAA,GAAAjN,EAAA,CAAA2M,iBAAA,CAAAK,EAAA,CAAAE,eAAA;IAAA;EAAA;;;YAAZrG,YAAY;MAAAsG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjCvBzN,EADF,CAAAC,cAAA,qBAA0D,gBACb;UAAnBD,EAAA,CAAAqB,UAAA,mBAAAsM,8CAAA;YAAA,OAASD,GAAA,CAAA9G,MAAA,EAAQ;UAAA,EAAC;UACxC5G,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;UAEPH,EADF,CAAAC,cAAA,aAA6B,cACC;UAAAD,EAAA,CAAAE,MAAA,yBAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACrDH,EAAA,CAAAC,cAAA,aAA2B;UACzBD,EAAA,CAAA8B,SAAA,aAA0E;UAE9E9B,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,gBAAwB,gBACZ;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAE1BF,EAF0B,CAAAG,YAAA,EAAW,EAC1B,EACG;UAORH,EAJN,CAAAC,cAAA,cAAqC,cAEP,eACC,eACmE;UAE1FD,EADA,CAAAgB,UAAA,KAAA4M,iCAAA,uBAAkC,KAAAC,6BAAA,mBACH;UACjC7N,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA8B,SAAA,eAA8D;UAC9D9B,EAAA,CAAAC,cAAA,eAA4F;UAE1FD,EADA,CAAAgB,UAAA,KAAA8M,iCAAA,uBAAkC,KAAAC,6BAAA,mBACH;UACjC/N,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA8B,SAAA,eAA8D;UAC9D9B,EAAA,CAAAC,cAAA,eAA4F;UAE1FD,EADA,CAAAgB,UAAA,KAAAgN,iCAAA,uBAAkC,KAAAC,6BAAA,mBACH;UACjCjO,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAA8B,SAAA,eAA8D;UAC9D9B,EAAA,CAAAC,cAAA,eAA4F;UAE1FD,EADA,CAAAgB,UAAA,KAAAkN,iCAAA,uBAAkC,KAAAC,6BAAA,mBACH;UAEnCnO,EADE,CAAAG,YAAA,EAAM,EACF;UAEJH,EADF,CAAAC,cAAA,eAAyB,YACkB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACpDH,EAAA,CAAAC,cAAA,YAAyC;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC3DH,EAAA,CAAAC,cAAA,YAAyC;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACvDH,EAAA,CAAAC,cAAA,YAAyC;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAEnDF,EAFmD,CAAAG,YAAA,EAAO,EAClD,EACF;UAGNH,EAAA,CAAAgB,UAAA,KAAAoN,4BAAA,kBAAwD;UAgBxDpO,EAAA,CAAAC,cAAA,eAAgC;UAwJ9BD,EArJA,CAAAgB,UAAA,KAAAqN,4BAAA,kBAAsD,KAAAC,4BAAA,mBAoCA,KAAAC,4BAAA,mBAmCA,KAAAC,4BAAA,mBA8EA;UAyFxDxO,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,eAA+B;UAC7BD,EAAA,CAAAgB,UAAA,KAAAyN,+BAAA,qBAG2B;UAK3BzO,EAAA,CAAA8B,SAAA,eAA0B;UAE1B9B,EAAA,CAAAC,cAAA,kBAI2B;UAFnBD,EAAA,CAAAqB,UAAA,mBAAAqN,+CAAA;YAAA,OAAShB,GAAA,CAAAvB,YAAA,EAAc;UAAA,EAAC;UAS9BnM,EANA,CAAAgB,UAAA,KAAA2N,6BAAA,mBAA8B,KAAAC,6BAAA,mBACE,KAAAC,iCAAA,uBAKE;UAEtC7O,EADE,CAAAG,YAAA,EAAS,EACL;UAGNH,EAAA,CAAAgB,UAAA,KAAA8N,4BAAA,mBAAsD;UAaxD9O,EAAA,CAAAG,YAAA,EAAM;;;UAtV0BH,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAA+O,WAAA,UAAArB,GAAA,CAAAvG,WAAA,gBAAyC;UAa7CnH,EAAA,CAAAI,SAAA,GAAiC;UAACJ,EAAlC,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,MAAiC,cAAAuG,GAAA,CAAAvG,WAAA,KAAoC;UAC9EnH,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UACzBnH,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,MAAsB;UAERnH,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,KAAgC;UACjCnH,EAAA,CAAAI,SAAA,EAAiC;UAACJ,EAAlC,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,MAAiC,cAAAuG,GAAA,CAAAvG,WAAA,KAAoC;UAC9EnH,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UACzBnH,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,MAAsB;UAERnH,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,KAAgC;UACjCnH,EAAA,CAAAI,SAAA,EAAiC;UAACJ,EAAlC,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,MAAiC,cAAAuG,GAAA,CAAAvG,WAAA,KAAoC;UAC9EnH,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UACzBnH,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,MAAsB;UAERnH,EAAA,CAAAI,SAAA,EAAgC;UAAhCJ,EAAA,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,KAAgC;UACjCnH,EAAA,CAAAI,SAAA,EAAiC;UAACJ,EAAlC,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,MAAiC,cAAAuG,GAAA,CAAAvG,WAAA,KAAoC;UAC9EnH,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UACzBnH,EAAA,CAAAI,SAAA,EAAsB;UAAtBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,MAAsB;UAIzBnH,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,OAAkC;UAClCnH,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,OAAkC;UAClCnH,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,OAAkC;UAClCnH,EAAA,CAAAI,SAAA,GAAkC;UAAlCJ,EAAA,CAAA+B,WAAA,WAAA2L,GAAA,CAAAvG,WAAA,OAAkC;UAKXnH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UAmB9CnH,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,OAAuB;UAoCvBnH,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,OAAuB;UAmCvBnH,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,OAAuB;UA8EvBnH,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,OAAuB;UA8FpBnH,EAAA,CAAAI,SAAA,GAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UAYtBnH,EAAA,CAAAI,SAAA,GAAoC;UAApCJ,EAAA,CAAAoB,UAAA,cAAAsM,GAAA,CAAAtB,oBAAA,GAAoC;UAEnCpM,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UACrBnH,EAAA,CAAAI,SAAA,EAAuB;UAAvBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,OAAuB;UAKnBnH,EAAA,CAAAI,SAAA,EAAqB;UAArBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAvG,WAAA,KAAqB;UAKRnH,EAAA,CAAAI,SAAA,EAAwB;UAAxBJ,EAAA,CAAAoB,UAAA,SAAAsM,GAAA,CAAAjC,eAAA,IAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}