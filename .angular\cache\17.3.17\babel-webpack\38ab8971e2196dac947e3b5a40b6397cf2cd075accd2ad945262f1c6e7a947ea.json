{"ast": null, "code": "/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { u as now, v as pointerCoord } from './helpers-be245865.js';\nconst startTapClick = config => {\n  if (doc === undefined) {\n    return;\n  }\n  let lastTouch = -MOUSE_WAIT * 10;\n  let lastActivated = 0;\n  let activatableEle;\n  let activeRipple;\n  let activeDefer;\n  const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n  const clearDefers = new WeakMap();\n  // Touch Events\n  const onTouchStart = ev => {\n    lastTouch = now(ev);\n    pointerDown(ev);\n  };\n  const onTouchEnd = ev => {\n    lastTouch = now(ev);\n    pointerUp(ev);\n  };\n  const onMouseDown = ev => {\n    // Ignore right clicks\n    if (ev.button === 2) {\n      return;\n    }\n    const t = now(ev) - MOUSE_WAIT;\n    if (lastTouch < t) {\n      pointerDown(ev);\n    }\n  };\n  const onMouseUp = ev => {\n    const t = now(ev) - MOUSE_WAIT;\n    if (lastTouch < t) {\n      pointerUp(ev);\n    }\n  };\n  const cancelActive = () => {\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    if (activatableEle) {\n      removeActivated(false);\n      activatableEle = undefined;\n    }\n  };\n  const pointerDown = ev => {\n    if (activatableEle) {\n      return;\n    }\n    setActivatedElement(getActivatableTarget(ev), ev);\n  };\n  const pointerUp = ev => {\n    setActivatedElement(undefined, ev);\n  };\n  const setActivatedElement = (el, ev) => {\n    // do nothing\n    if (el && el === activatableEle) {\n      return;\n    }\n    if (activeDefer) clearTimeout(activeDefer);\n    activeDefer = undefined;\n    const {\n      x,\n      y\n    } = pointerCoord(ev);\n    // deactivate selected\n    if (activatableEle) {\n      if (clearDefers.has(activatableEle)) {\n        throw new Error('internal error');\n      }\n      if (!activatableEle.classList.contains(ACTIVATED)) {\n        addActivated(activatableEle, x, y);\n      }\n      removeActivated(true);\n    }\n    // activate\n    if (el) {\n      const deferId = clearDefers.get(el);\n      if (deferId) {\n        clearTimeout(deferId);\n        clearDefers.delete(el);\n      }\n      el.classList.remove(ACTIVATED);\n      const callback = () => {\n        addActivated(el, x, y);\n        activeDefer = undefined;\n      };\n      if (isInstant(el)) {\n        callback();\n      } else {\n        activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n      }\n    }\n    activatableEle = el;\n  };\n  const addActivated = (el, x, y) => {\n    lastActivated = Date.now();\n    el.classList.add(ACTIVATED);\n    if (!useRippleEffect) return;\n    const rippleEffect = getRippleEffect(el);\n    if (rippleEffect !== null) {\n      removeRipple();\n      activeRipple = rippleEffect.addRipple(x, y);\n    }\n  };\n  const removeRipple = () => {\n    if (activeRipple !== undefined) {\n      activeRipple.then(remove => remove());\n      activeRipple = undefined;\n    }\n  };\n  const removeActivated = smooth => {\n    removeRipple();\n    const active = activatableEle;\n    if (!active) {\n      return;\n    }\n    const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n    if (smooth && time > 0 && !isInstant(active)) {\n      const deferId = setTimeout(() => {\n        active.classList.remove(ACTIVATED);\n        clearDefers.delete(active);\n      }, CLEAR_STATE_DEFERS);\n      clearDefers.set(active, deferId);\n    } else {\n      active.classList.remove(ACTIVATED);\n    }\n  };\n  doc.addEventListener('ionGestureCaptured', cancelActive);\n  doc.addEventListener('touchstart', onTouchStart, true);\n  doc.addEventListener('touchcancel', onTouchEnd, true);\n  doc.addEventListener('touchend', onTouchEnd, true);\n  /**\n   * Tap click effects such as the ripple effect should\n   * not happen when scrolling. For example, if a user scrolls\n   * the page but also happens to do a touchstart on a button\n   * as part of the scroll, the ripple effect should not\n   * be dispatched. The ripple effect should only happen\n   * if the button is activated and the page is not scrolling.\n   *\n   * pointercancel is dispatched on a gesture when scrolling\n   * starts, so this lets us avoid having to listen for\n   * ion-content's scroll events.\n   */\n  doc.addEventListener('pointercancel', cancelActive, true);\n  doc.addEventListener('mousedown', onMouseDown, true);\n  doc.addEventListener('mouseup', onMouseUp, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = ev => {\n  if (ev.composedPath !== undefined) {\n    /**\n     * composedPath returns EventTarget[]. However,\n     * objects other than Element can be targets too.\n     * For example, AudioContext can be a target. In this\n     * case, we know that the event is a UIEvent so we\n     * can assume that the path will contain either Element\n     * or ShadowRoot.\n     */\n    const path = ev.composedPath();\n    for (let i = 0; i < path.length - 2; i++) {\n      const el = path[i];\n      if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n        return el;\n      }\n    }\n  } else {\n    return ev.target.closest('.ion-activatable');\n  }\n};\nconst isInstant = el => {\n  return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = el => {\n  if (el.shadowRoot) {\n    const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n    if (ripple) {\n      return ripple;\n    }\n  }\n  return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\nconst MOUSE_WAIT = 2500;\nexport { startTapClick };", "map": {"version": 3, "names": ["d", "doc", "u", "now", "v", "pointerCoord", "startTapClick", "config", "undefined", "lastTouch", "MOUSE_WAIT", "lastActivated", "activatableEle", "activeRipple", "activeDefer", "useRippleEffect", "getBoolean", "clearDefers", "WeakMap", "onTouchStart", "ev", "pointerDown", "onTouchEnd", "pointerUp", "onMouseDown", "button", "t", "onMouseUp", "cancelActive", "clearTimeout", "removeActivated", "setActivatedElement", "getActivatableTarget", "el", "x", "y", "has", "Error", "classList", "contains", "ACTIVATED", "addActivated", "deferId", "get", "delete", "remove", "callback", "isInstant", "setTimeout", "ADD_ACTIVATED_DEFERS", "Date", "add", "rippleEffect", "getRippleEffect", "removeRipple", "addRipple", "then", "smooth", "active", "time", "CLEAR_STATE_DEFERS", "set", "addEventListener", "<PERSON><PERSON><PERSON>", "path", "i", "length", "ShadowRoot", "target", "closest", "shadowRoot", "ripple", "querySelector"], "sources": ["C:/D drive/AIS-SMART CARE/AISSmartCare_New/AISSmartCare_New/node_modules/@ionic/core/dist/esm/index-020f5464.js"], "sourcesContent": ["/*!\n * (C) Ionic http://ionicframework.com - MIT License\n */\nimport { d as doc } from './index-a5d50daf.js';\nimport { u as now, v as pointerCoord } from './helpers-be245865.js';\n\nconst startTapClick = (config) => {\n    if (doc === undefined) {\n        return;\n    }\n    let lastTouch = -MOUSE_WAIT * 10;\n    let lastActivated = 0;\n    let activatableEle;\n    let activeRipple;\n    let activeDefer;\n    const useRippleEffect = config.getBoolean('animated', true) && config.getBoolean('rippleEffect', true);\n    const clearDefers = new WeakMap();\n    // Touch Events\n    const onTouchStart = (ev) => {\n        lastTouch = now(ev);\n        pointerDown(ev);\n    };\n    const onTouchEnd = (ev) => {\n        lastTouch = now(ev);\n        pointerUp(ev);\n    };\n    const onMouseDown = (ev) => {\n        // Ignore right clicks\n        if (ev.button === 2) {\n            return;\n        }\n        const t = now(ev) - MOUSE_WAIT;\n        if (lastTouch < t) {\n            pointerDown(ev);\n        }\n    };\n    const onMouseUp = (ev) => {\n        const t = now(ev) - MOUSE_WAIT;\n        if (lastTouch < t) {\n            pointerUp(ev);\n        }\n    };\n    const cancelActive = () => {\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        if (activatableEle) {\n            removeActivated(false);\n            activatableEle = undefined;\n        }\n    };\n    const pointerDown = (ev) => {\n        if (activatableEle) {\n            return;\n        }\n        setActivatedElement(getActivatableTarget(ev), ev);\n    };\n    const pointerUp = (ev) => {\n        setActivatedElement(undefined, ev);\n    };\n    const setActivatedElement = (el, ev) => {\n        // do nothing\n        if (el && el === activatableEle) {\n            return;\n        }\n        if (activeDefer)\n            clearTimeout(activeDefer);\n        activeDefer = undefined;\n        const { x, y } = pointerCoord(ev);\n        // deactivate selected\n        if (activatableEle) {\n            if (clearDefers.has(activatableEle)) {\n                throw new Error('internal error');\n            }\n            if (!activatableEle.classList.contains(ACTIVATED)) {\n                addActivated(activatableEle, x, y);\n            }\n            removeActivated(true);\n        }\n        // activate\n        if (el) {\n            const deferId = clearDefers.get(el);\n            if (deferId) {\n                clearTimeout(deferId);\n                clearDefers.delete(el);\n            }\n            el.classList.remove(ACTIVATED);\n            const callback = () => {\n                addActivated(el, x, y);\n                activeDefer = undefined;\n            };\n            if (isInstant(el)) {\n                callback();\n            }\n            else {\n                activeDefer = setTimeout(callback, ADD_ACTIVATED_DEFERS);\n            }\n        }\n        activatableEle = el;\n    };\n    const addActivated = (el, x, y) => {\n        lastActivated = Date.now();\n        el.classList.add(ACTIVATED);\n        if (!useRippleEffect)\n            return;\n        const rippleEffect = getRippleEffect(el);\n        if (rippleEffect !== null) {\n            removeRipple();\n            activeRipple = rippleEffect.addRipple(x, y);\n        }\n    };\n    const removeRipple = () => {\n        if (activeRipple !== undefined) {\n            activeRipple.then((remove) => remove());\n            activeRipple = undefined;\n        }\n    };\n    const removeActivated = (smooth) => {\n        removeRipple();\n        const active = activatableEle;\n        if (!active) {\n            return;\n        }\n        const time = CLEAR_STATE_DEFERS - Date.now() + lastActivated;\n        if (smooth && time > 0 && !isInstant(active)) {\n            const deferId = setTimeout(() => {\n                active.classList.remove(ACTIVATED);\n                clearDefers.delete(active);\n            }, CLEAR_STATE_DEFERS);\n            clearDefers.set(active, deferId);\n        }\n        else {\n            active.classList.remove(ACTIVATED);\n        }\n    };\n    doc.addEventListener('ionGestureCaptured', cancelActive);\n    doc.addEventListener('touchstart', onTouchStart, true);\n    doc.addEventListener('touchcancel', onTouchEnd, true);\n    doc.addEventListener('touchend', onTouchEnd, true);\n    /**\n     * Tap click effects such as the ripple effect should\n     * not happen when scrolling. For example, if a user scrolls\n     * the page but also happens to do a touchstart on a button\n     * as part of the scroll, the ripple effect should not\n     * be dispatched. The ripple effect should only happen\n     * if the button is activated and the page is not scrolling.\n     *\n     * pointercancel is dispatched on a gesture when scrolling\n     * starts, so this lets us avoid having to listen for\n     * ion-content's scroll events.\n     */\n    doc.addEventListener('pointercancel', cancelActive, true);\n    doc.addEventListener('mousedown', onMouseDown, true);\n    doc.addEventListener('mouseup', onMouseUp, true);\n};\n// TODO(FW-2832): type\nconst getActivatableTarget = (ev) => {\n    if (ev.composedPath !== undefined) {\n        /**\n         * composedPath returns EventTarget[]. However,\n         * objects other than Element can be targets too.\n         * For example, AudioContext can be a target. In this\n         * case, we know that the event is a UIEvent so we\n         * can assume that the path will contain either Element\n         * or ShadowRoot.\n         */\n        const path = ev.composedPath();\n        for (let i = 0; i < path.length - 2; i++) {\n            const el = path[i];\n            if (!(el instanceof ShadowRoot) && el.classList.contains('ion-activatable')) {\n                return el;\n            }\n        }\n    }\n    else {\n        return ev.target.closest('.ion-activatable');\n    }\n};\nconst isInstant = (el) => {\n    return el.classList.contains('ion-activatable-instant');\n};\nconst getRippleEffect = (el) => {\n    if (el.shadowRoot) {\n        const ripple = el.shadowRoot.querySelector('ion-ripple-effect');\n        if (ripple) {\n            return ripple;\n        }\n    }\n    return el.querySelector('ion-ripple-effect');\n};\nconst ACTIVATED = 'ion-activated';\nconst ADD_ACTIVATED_DEFERS = 100;\nconst CLEAR_STATE_DEFERS = 150;\nconst MOUSE_WAIT = 2500;\n\nexport { startTapClick };\n"], "mappings": "AAAA;AACA;AACA;AACA,SAASA,CAAC,IAAIC,GAAG,QAAQ,qBAAqB;AAC9C,SAASC,CAAC,IAAIC,GAAG,EAAEC,CAAC,IAAIC,YAAY,QAAQ,uBAAuB;AAEnE,MAAMC,aAAa,GAAIC,MAAM,IAAK;EAC9B,IAAIN,GAAG,KAAKO,SAAS,EAAE;IACnB;EACJ;EACA,IAAIC,SAAS,GAAG,CAACC,UAAU,GAAG,EAAE;EAChC,IAAIC,aAAa,GAAG,CAAC;EACrB,IAAIC,cAAc;EAClB,IAAIC,YAAY;EAChB,IAAIC,WAAW;EACf,MAAMC,eAAe,GAAGR,MAAM,CAACS,UAAU,CAAC,UAAU,EAAE,IAAI,CAAC,IAAIT,MAAM,CAACS,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC;EACtG,MAAMC,WAAW,GAAG,IAAIC,OAAO,CAAC,CAAC;EACjC;EACA,MAAMC,YAAY,GAAIC,EAAE,IAAK;IACzBX,SAAS,GAAGN,GAAG,CAACiB,EAAE,CAAC;IACnBC,WAAW,CAACD,EAAE,CAAC;EACnB,CAAC;EACD,MAAME,UAAU,GAAIF,EAAE,IAAK;IACvBX,SAAS,GAAGN,GAAG,CAACiB,EAAE,CAAC;IACnBG,SAAS,CAACH,EAAE,CAAC;EACjB,CAAC;EACD,MAAMI,WAAW,GAAIJ,EAAE,IAAK;IACxB;IACA,IAAIA,EAAE,CAACK,MAAM,KAAK,CAAC,EAAE;MACjB;IACJ;IACA,MAAMC,CAAC,GAAGvB,GAAG,CAACiB,EAAE,CAAC,GAAGV,UAAU;IAC9B,IAAID,SAAS,GAAGiB,CAAC,EAAE;MACfL,WAAW,CAACD,EAAE,CAAC;IACnB;EACJ,CAAC;EACD,MAAMO,SAAS,GAAIP,EAAE,IAAK;IACtB,MAAMM,CAAC,GAAGvB,GAAG,CAACiB,EAAE,CAAC,GAAGV,UAAU;IAC9B,IAAID,SAAS,GAAGiB,CAAC,EAAE;MACfH,SAAS,CAACH,EAAE,CAAC;IACjB;EACJ,CAAC;EACD,MAAMQ,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAId,WAAW,EACXe,YAAY,CAACf,WAAW,CAAC;IAC7BA,WAAW,GAAGN,SAAS;IACvB,IAAII,cAAc,EAAE;MAChBkB,eAAe,CAAC,KAAK,CAAC;MACtBlB,cAAc,GAAGJ,SAAS;IAC9B;EACJ,CAAC;EACD,MAAMa,WAAW,GAAID,EAAE,IAAK;IACxB,IAAIR,cAAc,EAAE;MAChB;IACJ;IACAmB,mBAAmB,CAACC,oBAAoB,CAACZ,EAAE,CAAC,EAAEA,EAAE,CAAC;EACrD,CAAC;EACD,MAAMG,SAAS,GAAIH,EAAE,IAAK;IACtBW,mBAAmB,CAACvB,SAAS,EAAEY,EAAE,CAAC;EACtC,CAAC;EACD,MAAMW,mBAAmB,GAAGA,CAACE,EAAE,EAAEb,EAAE,KAAK;IACpC;IACA,IAAIa,EAAE,IAAIA,EAAE,KAAKrB,cAAc,EAAE;MAC7B;IACJ;IACA,IAAIE,WAAW,EACXe,YAAY,CAACf,WAAW,CAAC;IAC7BA,WAAW,GAAGN,SAAS;IACvB,MAAM;MAAE0B,CAAC;MAAEC;IAAE,CAAC,GAAG9B,YAAY,CAACe,EAAE,CAAC;IACjC;IACA,IAAIR,cAAc,EAAE;MAChB,IAAIK,WAAW,CAACmB,GAAG,CAACxB,cAAc,CAAC,EAAE;QACjC,MAAM,IAAIyB,KAAK,CAAC,gBAAgB,CAAC;MACrC;MACA,IAAI,CAACzB,cAAc,CAAC0B,SAAS,CAACC,QAAQ,CAACC,SAAS,CAAC,EAAE;QAC/CC,YAAY,CAAC7B,cAAc,EAAEsB,CAAC,EAAEC,CAAC,CAAC;MACtC;MACAL,eAAe,CAAC,IAAI,CAAC;IACzB;IACA;IACA,IAAIG,EAAE,EAAE;MACJ,MAAMS,OAAO,GAAGzB,WAAW,CAAC0B,GAAG,CAACV,EAAE,CAAC;MACnC,IAAIS,OAAO,EAAE;QACTb,YAAY,CAACa,OAAO,CAAC;QACrBzB,WAAW,CAAC2B,MAAM,CAACX,EAAE,CAAC;MAC1B;MACAA,EAAE,CAACK,SAAS,CAACO,MAAM,CAACL,SAAS,CAAC;MAC9B,MAAMM,QAAQ,GAAGA,CAAA,KAAM;QACnBL,YAAY,CAACR,EAAE,EAAEC,CAAC,EAAEC,CAAC,CAAC;QACtBrB,WAAW,GAAGN,SAAS;MAC3B,CAAC;MACD,IAAIuC,SAAS,CAACd,EAAE,CAAC,EAAE;QACfa,QAAQ,CAAC,CAAC;MACd,CAAC,MACI;QACDhC,WAAW,GAAGkC,UAAU,CAACF,QAAQ,EAAEG,oBAAoB,CAAC;MAC5D;IACJ;IACArC,cAAc,GAAGqB,EAAE;EACvB,CAAC;EACD,MAAMQ,YAAY,GAAGA,CAACR,EAAE,EAAEC,CAAC,EAAEC,CAAC,KAAK;IAC/BxB,aAAa,GAAGuC,IAAI,CAAC/C,GAAG,CAAC,CAAC;IAC1B8B,EAAE,CAACK,SAAS,CAACa,GAAG,CAACX,SAAS,CAAC;IAC3B,IAAI,CAACzB,eAAe,EAChB;IACJ,MAAMqC,YAAY,GAAGC,eAAe,CAACpB,EAAE,CAAC;IACxC,IAAImB,YAAY,KAAK,IAAI,EAAE;MACvBE,YAAY,CAAC,CAAC;MACdzC,YAAY,GAAGuC,YAAY,CAACG,SAAS,CAACrB,CAAC,EAAEC,CAAC,CAAC;IAC/C;EACJ,CAAC;EACD,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACvB,IAAIzC,YAAY,KAAKL,SAAS,EAAE;MAC5BK,YAAY,CAAC2C,IAAI,CAAEX,MAAM,IAAKA,MAAM,CAAC,CAAC,CAAC;MACvChC,YAAY,GAAGL,SAAS;IAC5B;EACJ,CAAC;EACD,MAAMsB,eAAe,GAAI2B,MAAM,IAAK;IAChCH,YAAY,CAAC,CAAC;IACd,MAAMI,MAAM,GAAG9C,cAAc;IAC7B,IAAI,CAAC8C,MAAM,EAAE;MACT;IACJ;IACA,MAAMC,IAAI,GAAGC,kBAAkB,GAAGV,IAAI,CAAC/C,GAAG,CAAC,CAAC,GAAGQ,aAAa;IAC5D,IAAI8C,MAAM,IAAIE,IAAI,GAAG,CAAC,IAAI,CAACZ,SAAS,CAACW,MAAM,CAAC,EAAE;MAC1C,MAAMhB,OAAO,GAAGM,UAAU,CAAC,MAAM;QAC7BU,MAAM,CAACpB,SAAS,CAACO,MAAM,CAACL,SAAS,CAAC;QAClCvB,WAAW,CAAC2B,MAAM,CAACc,MAAM,CAAC;MAC9B,CAAC,EAAEE,kBAAkB,CAAC;MACtB3C,WAAW,CAAC4C,GAAG,CAACH,MAAM,EAAEhB,OAAO,CAAC;IACpC,CAAC,MACI;MACDgB,MAAM,CAACpB,SAAS,CAACO,MAAM,CAACL,SAAS,CAAC;IACtC;EACJ,CAAC;EACDvC,GAAG,CAAC6D,gBAAgB,CAAC,oBAAoB,EAAElC,YAAY,CAAC;EACxD3B,GAAG,CAAC6D,gBAAgB,CAAC,YAAY,EAAE3C,YAAY,EAAE,IAAI,CAAC;EACtDlB,GAAG,CAAC6D,gBAAgB,CAAC,aAAa,EAAExC,UAAU,EAAE,IAAI,CAAC;EACrDrB,GAAG,CAAC6D,gBAAgB,CAAC,UAAU,EAAExC,UAAU,EAAE,IAAI,CAAC;EAClD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIrB,GAAG,CAAC6D,gBAAgB,CAAC,eAAe,EAAElC,YAAY,EAAE,IAAI,CAAC;EACzD3B,GAAG,CAAC6D,gBAAgB,CAAC,WAAW,EAAEtC,WAAW,EAAE,IAAI,CAAC;EACpDvB,GAAG,CAAC6D,gBAAgB,CAAC,SAAS,EAAEnC,SAAS,EAAE,IAAI,CAAC;AACpD,CAAC;AACD;AACA,MAAMK,oBAAoB,GAAIZ,EAAE,IAAK;EACjC,IAAIA,EAAE,CAAC2C,YAAY,KAAKvD,SAAS,EAAE;IAC/B;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,MAAMwD,IAAI,GAAG5C,EAAE,CAAC2C,YAAY,CAAC,CAAC;IAC9B,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,EAAE,EAAE;MACtC,MAAMhC,EAAE,GAAG+B,IAAI,CAACC,CAAC,CAAC;MAClB,IAAI,EAAEhC,EAAE,YAAYkC,UAAU,CAAC,IAAIlC,EAAE,CAACK,SAAS,CAACC,QAAQ,CAAC,iBAAiB,CAAC,EAAE;QACzE,OAAON,EAAE;MACb;IACJ;EACJ,CAAC,MACI;IACD,OAAOb,EAAE,CAACgD,MAAM,CAACC,OAAO,CAAC,kBAAkB,CAAC;EAChD;AACJ,CAAC;AACD,MAAMtB,SAAS,GAAId,EAAE,IAAK;EACtB,OAAOA,EAAE,CAACK,SAAS,CAACC,QAAQ,CAAC,yBAAyB,CAAC;AAC3D,CAAC;AACD,MAAMc,eAAe,GAAIpB,EAAE,IAAK;EAC5B,IAAIA,EAAE,CAACqC,UAAU,EAAE;IACf,MAAMC,MAAM,GAAGtC,EAAE,CAACqC,UAAU,CAACE,aAAa,CAAC,mBAAmB,CAAC;IAC/D,IAAID,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;EACJ;EACA,OAAOtC,EAAE,CAACuC,aAAa,CAAC,mBAAmB,CAAC;AAChD,CAAC;AACD,MAAMhC,SAAS,GAAG,eAAe;AACjC,MAAMS,oBAAoB,GAAG,GAAG;AAChC,MAAMW,kBAAkB,GAAG,GAAG;AAC9B,MAAMlD,UAAU,GAAG,IAAI;AAEvB,SAASJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}