{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ionic/angular\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/toolbar\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/tabs\";\nimport * as i9 from \"@angular/material/core\";\nfunction HomePage_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 29);\n    i0.ɵɵtext(1, \"home\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \" Home \");\n  }\n}\nfunction HomePage_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 29);\n    i0.ɵɵtext(1, \"add_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \" Register \");\n  }\n}\nfunction HomePage_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 29);\n    i0.ɵɵtext(1, \"track_changes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \" Track \");\n  }\n}\nfunction HomePage_ng_template_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 29);\n    i0.ɵɵtext(1, \"feedback\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(2, \" Feedback \");\n  }\n}\nfunction HomePage_div_25_mat_card_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 36);\n    i0.ɵɵlistener(\"click\", function HomePage_div_25_mat_card_7_Template_mat_card_click_0_listener() {\n      const item_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.onCardClick(item_r2));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-header\")(2, \"div\", 37);\n    i0.ɵɵelement(3, \"ion-icon\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\", 39);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\", 40);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"mat-card-actions\", 41)(10, \"button\", 42)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_forward\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" View \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵclassMap(\"card-\" + item_r2.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"name\", item_r2.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", item_r2.color);\n  }\n}\nfunction HomePage_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"div\", 31)(2, \"h2\", 32);\n    i0.ɵɵtext(3, \"Quick Access\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 33);\n    i0.ɵɵtext(5, \"Access important documents and guidelines\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 34);\n    i0.ɵɵtemplate(7, HomePage_div_25_mat_card_7_Template, 14, 6, \"mat-card\", 35);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.menuItems);\n  }\n}\nfunction HomePage_ion_fab_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ion-fab\", 43)(1, \"ion-fab-button\", 44);\n    i0.ɵɵlistener(\"click\", function HomePage_ion_fab_26_Template_ion_fab_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onTabChange(\"register\"));\n    });\n    i0.ɵɵelement(2, \"ion-icon\", 45);\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class HomePage {\n  constructor(router, menuController) {\n    this.router = router;\n    this.menuController = menuController;\n    this.activeTab = 'home';\n    this.menuItems = [{\n      title: 'AIS Quality Policy',\n      description: 'Learn about our quality standards and policies',\n      icon: 'document-text',\n      color: 'primary',\n      route: '/videoplayer',\n      data: 'AIS Quality Policy'\n    }, {\n      title: 'Glass Unloading Guidelines',\n      description: 'Proper procedures for glass handling and unloading',\n      icon: 'cube',\n      color: 'secondary',\n      route: '/videoplayer',\n      data: 'Glass Unloading Guidelines'\n    }, {\n      title: 'Requirements to Register a Complaint',\n      description: 'What you need to know before registering a complaint',\n      icon: 'clipboard',\n      color: 'tertiary',\n      route: '/videoplayer',\n      data: 'Requirements to Register a Complaint'\n    }, {\n      title: 'Complaint Handling Procedure',\n      description: 'How we process and resolve your complaints',\n      icon: 'settings',\n      color: 'success',\n      route: '/videoplayer',\n      data: 'Complaint handling procedure'\n    }, {\n      title: 'Glass Installation Guideline',\n      description: 'Best practices for glass installation',\n      icon: 'construct',\n      color: 'warning',\n      route: '/videoplayer',\n      data: 'Glass Installation Guideline'\n    }];\n  }\n  ngOnInit() {\n    this.menuController.enable(true);\n  }\n  onTabChange(tab) {\n    this.activeTab = tab;\n    switch (tab) {\n      case 'register':\n        this.router.navigate(['/register']);\n        break;\n      case 'track':\n        this.router.navigate(['/track']);\n        break;\n      case 'feedback':\n        this.router.navigate(['/feedback']);\n        break;\n      default:\n        this.activeTab = 'home';\n        break;\n    }\n  }\n  onCardClick(item) {\n    this.router.navigate([item.route], {\n      queryParams: {\n        data: item.data\n      }\n    });\n  }\n  openMenu() {\n    this.menuController.open();\n  }\n  static {\n    this.ɵfac = function HomePage_Factory(t) {\n      return new (t || HomePage)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.MenuController));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomePage,\n      selectors: [[\"app-home\"]],\n      decls: 50,\n      vars: 5,\n      consts: [[1, \"material-header\", 3, \"translucent\"], [1, \"material-toolbar\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Menu\", 3, \"click\"], [1, \"toolbar-title\"], [1, \"spacer\"], [1, \"material-content\", 3, \"fullscreen\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"tab-navigation\"], [\"mat-stretch-tabs\", \"\", 1, \"custom-tabs\", 3, \"selectedTabChange\", \"selectedIndex\"], [\"label\", \"Home\"], [\"mat-tab-label\", \"\"], [\"label\", \"Register\"], [\"label\", \"Track\"], [\"label\", \"Feedback\"], [\"class\", \"content-section\", 4, \"ngIf\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\", 4, \"ngIf\"], [\"side\", \"start\", \"menuId\", \"main-menu\", \"contentId\", \"main-content\"], [\"color\", \"primary\"], [\"button\", \"\", 3, \"click\"], [\"name\", \"home\", \"slot\", \"start\"], [\"button\", \"\", \"routerLink\", \"/creditnote\"], [\"name\", \"document-text\", \"slot\", \"start\"], [\"button\", \"\"], [\"name\", \"information-circle\", \"slot\", \"start\"], [\"button\", \"\", \"routerLink\", \"/login\"], [\"name\", \"log-out\", \"slot\", \"start\"], [1, \"tab-icon\"], [1, \"content-section\"], [1, \"container\"], [1, \"section-title\"], [1, \"section-subtitle\"], [1, \"cards-grid\"], [\"class\", \"info-card\", \"matRipple\", \"\", 3, \"class\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"matRipple\", \"\", 1, \"info-card\", 3, \"click\"], [\"mat-card-avatar\", \"\", 1, \"card-avatar\"], [1, \"card-icon\", 3, \"name\"], [1, \"card-title\"], [1, \"card-description\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"color\"], [\"vertical\", \"bottom\", \"horizontal\", \"end\", \"slot\", \"fixed\"], [\"color\", \"primary\", 3, \"click\"], [\"name\", \"add\"]],\n      template: function HomePage_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"ion-header\", 0)(1, \"mat-toolbar\", 1)(2, \"button\", 2);\n          i0.ɵɵlistener(\"click\", function HomePage_Template_button_click_2_listener() {\n            return ctx.openMenu();\n          });\n          i0.ɵɵelementStart(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"menu\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(5, \"span\", 3);\n          i0.ɵɵtext(6, \"AIS Smart Care\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 4);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"ion-content\", 5)(9, \"div\", 6)(10, \"div\", 7)(11, \"h1\", 8);\n          i0.ɵɵtext(12, \"Welcome to AIS Smart Care\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\", 9);\n          i0.ɵɵtext(14, \"Your comprehensive glass care solution\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 10)(16, \"mat-tab-group\", 11);\n          i0.ɵɵlistener(\"selectedTabChange\", function HomePage_Template_mat_tab_group_selectedTabChange_16_listener($event) {\n            return ctx.onTabChange($event.index === 0 ? \"home\" : $event.index === 1 ? \"register\" : $event.index === 2 ? \"track\" : \"feedback\");\n          });\n          i0.ɵɵelementStart(17, \"mat-tab\", 12);\n          i0.ɵɵtemplate(18, HomePage_ng_template_18_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(19, \"mat-tab\", 14);\n          i0.ɵɵtemplate(20, HomePage_ng_template_20_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-tab\", 15);\n          i0.ɵɵtemplate(22, HomePage_ng_template_22_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"mat-tab\", 16);\n          i0.ɵɵtemplate(24, HomePage_ng_template_24_Template, 3, 0, \"ng-template\", 13);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(25, HomePage_div_25_Template, 8, 1, \"div\", 17)(26, HomePage_ion_fab_26_Template, 3, 0, \"ion-fab\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"ion-menu\", 19)(28, \"ion-header\")(29, \"ion-toolbar\", 20)(30, \"ion-title\");\n          i0.ɵɵtext(31, \"Menu\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"ion-content\")(33, \"ion-list\")(34, \"ion-item\", 21);\n          i0.ɵɵlistener(\"click\", function HomePage_Template_ion_item_click_34_listener() {\n            return ctx.onTabChange(\"home\");\n          });\n          i0.ɵɵelement(35, \"ion-icon\", 22);\n          i0.ɵɵelementStart(36, \"ion-label\");\n          i0.ɵɵtext(37, \"Home\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"ion-item\", 23);\n          i0.ɵɵelement(39, \"ion-icon\", 24);\n          i0.ɵɵelementStart(40, \"ion-label\");\n          i0.ɵɵtext(41, \"Credit Note\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"ion-item\", 25);\n          i0.ɵɵelement(43, \"ion-icon\", 26);\n          i0.ɵɵelementStart(44, \"ion-label\");\n          i0.ɵɵtext(45, \"About\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"ion-item\", 27);\n          i0.ɵɵelement(47, \"ion-icon\", 28);\n          i0.ɵɵelementStart(48, \"ion-label\");\n          i0.ɵɵtext(49, \"Log Out\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"translucent\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"fullscreen\", true);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"selectedIndex\", ctx.activeTab === \"home\" ? 0 : ctx.activeTab === \"register\" ? 1 : ctx.activeTab === \"track\" ? 2 : 3);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"home\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.activeTab === \"home\");\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i2.IonContent, i2.IonFab, i2.IonFabButton, i2.IonHeader, i2.IonIcon, i2.IonItem, i2.IonLabel, i2.IonList, i2.IonMenu, i2.IonTitle, i2.IonToolbar, i2.RouterLinkDelegate, i1.RouterLink, i4.MatToolbar, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardActions, i6.MatCardAvatar, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatIcon, i8.MatTabLabel, i8.MatTab, i8.MatTabGroup, i9.MatRipple],\n      styles: [\"\\n\\n.material-header[_ngcontent-%COMP%] {\\n  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2), 0px 4px 5px 0px rgba(0, 0, 0, 0.14), 0px 1px 10px 0px rgba(0, 0, 0, 0.12);\\n}\\n\\n.material-toolbar[_ngcontent-%COMP%] {\\n  height: 64px;\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\\n  color: white;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n  font-weight: 500;\\n  margin-left: 16px;\\n}\\n.material-toolbar[_ngcontent-%COMP%]   .spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.material-content[_ngcontent-%COMP%] {\\n  --background: #fafafa;\\n}\\n\\n\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);\\n  color: white;\\n  padding: 40px 20px;\\n  text-align: center;\\n  position: relative;\\n  overflow: hidden;\\n}\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"1\\\" fill=\\\"rgba(255,255,255,0.1)\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  opacity: 0.3;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n  font-size: 28px;\\n  font-weight: 300;\\n  margin: 0 0 8px 0;\\n  letter-spacing: 0.5px;\\n}\\n.hero-section[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  opacity: 0.9;\\n  margin: 0;\\n  font-weight: 400;\\n}\\n\\n\\n\\n.tab-navigation[_ngcontent-%COMP%] {\\n  background: white;\\n  box-shadow: 0px 2px 4px -1px rgba(0, 0, 0, 0.2);\\n}\\n.tab-navigation[_ngcontent-%COMP%]   .custom-tabs[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%] {\\n  min-width: 0;\\n  padding: 12px 16px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.tab-navigation[_ngcontent-%COMP%]   .custom-tabs[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n}\\n.tab-navigation[_ngcontent-%COMP%]   .custom-tabs[_ngcontent-%COMP%]   .mat-ink-bar[_ngcontent-%COMP%] {\\n  height: 3px;\\n  background: var(--ion-color-primary);\\n}\\n\\n\\n\\n.content-section[_ngcontent-%COMP%] {\\n  padding: 24px 0;\\n  min-height: calc(100vh - 300px);\\n}\\n\\n.container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 0 16px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  font-weight: 400;\\n  color: #333;\\n  margin: 0 0 8px 0;\\n  text-align: center;\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #666;\\n  margin: 0 0 32px 0;\\n  text-align: center;\\n}\\n\\n\\n\\n.cards-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 20px;\\n  padding: 0 8px;\\n}\\n\\n.info-card[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  border-radius: 12px;\\n  overflow: hidden;\\n}\\n.info-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0px 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%] {\\n  padding: 20px 20px 16px 20px;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .card-avatar[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  margin-right: 16px;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .card-avatar[_ngcontent-%COMP%]   .card-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  color: white;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  line-height: 1.3;\\n  margin: 0;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%] {\\n  padding: 0 20px 16px 20px;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-content[_ngcontent-%COMP%]   .card-description[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  line-height: 1.5;\\n  margin: 0;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-actions[_ngcontent-%COMP%] {\\n  padding: 8px 20px 20px 20px;\\n}\\n.info-card[_ngcontent-%COMP%]   .mat-card-actions[_ngcontent-%COMP%]   .mat-button[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n\\n\\n.card-primary[_ngcontent-%COMP%]   .card-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #1976d2, #1565c0);\\n}\\n\\n.card-secondary[_ngcontent-%COMP%]   .card-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3dc2ff, #36abe0);\\n}\\n\\n.card-tertiary[_ngcontent-%COMP%]   .card-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #5260ff, #4854e0);\\n}\\n\\n.card-success[_ngcontent-%COMP%]   .card-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #2dd36f, #28ba62);\\n}\\n\\n.card-warning[_ngcontent-%COMP%]   .card-avatar[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ffc409, #e0ac08);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%] {\\n    padding: 32px 16px;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 24px;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-subtitle[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .cards-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 16px;\\n    padding: 0 4px;\\n  }\\n  .container[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n  }\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .section-subtitle[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .material-toolbar[_ngcontent-%COMP%]   .toolbar-title[_ngcontent-%COMP%] {\\n    font-size: 18px;\\n  }\\n  .hero-section[_ngcontent-%COMP%] {\\n    padding: 24px 12px;\\n  }\\n  .hero-section[_ngcontent-%COMP%]   .hero-title[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n  }\\n  .tab-navigation[_ngcontent-%COMP%]   .custom-tabs[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%] {\\n    padding: 12px 8px;\\n    font-size: 12px;\\n  }\\n  .tab-navigation[_ngcontent-%COMP%]   .custom-tabs[_ngcontent-%COMP%]   .mat-tab-label[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\\n    margin-right: 4px;\\n    font-size: 16px;\\n  }\\n}\\n\\n\\n.fade-in[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.slide-up[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideUp 0.4s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideUp {\\n  from {\\n    transform: translateY(30px);\\n    opacity: 0;\\n  }\\n  to {\\n    transform: translateY(0);\\n    opacity: 1;\\n  }\\n}\\n\\n\\nion-fab-button[_ngcontent-%COMP%] {\\n  --background: var(--ion-color-primary);\\n  --color: white;\\n  --box-shadow: 0px 3px 5px -1px rgba(0, 0, 0, 0.2),\\n                0px 6px 10px 0px rgba(0, 0, 0, 0.14),\\n                0px 1px 18px 0px rgba(0, 0, 0, 0.12);\\n}\\nion-fab-button[_ngcontent-%COMP%]:hover {\\n  --box-shadow: 0px 5px 5px -3px rgba(0, 0, 0, 0.2),\\n                0px 8px 10px 1px rgba(0, 0, 0, 0.14),\\n                0px 3px 14px 2px rgba(0, 0, 0, 0.12);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "HomePage_div_25_mat_card_7_Template_mat_card_click_0_listener", "item_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onCardClick", "ɵɵelement", "ɵɵclassMap", "color", "ɵɵadvance", "ɵɵproperty", "icon", "ɵɵtextInterpolate", "title", "description", "ɵɵtemplate", "HomePage_div_25_mat_card_7_Template", "menuItems", "HomePage_ion_fab_26_Template_ion_fab_button_click_1_listener", "_r4", "onTabChange", "HomePage", "constructor", "router", "menuController", "activeTab", "route", "data", "ngOnInit", "enable", "tab", "navigate", "item", "queryParams", "openMenu", "open", "ɵɵdirectiveInject", "i1", "Router", "i2", "MenuController", "selectors", "decls", "vars", "consts", "template", "HomePage_Template", "rf", "ctx", "HomePage_Template_button_click_2_listener", "HomePage_Template_mat_tab_group_selectedTabChange_16_listener", "$event", "index", "HomePage_ng_template_18_Template", "HomePage_ng_template_20_Template", "HomePage_ng_template_22_Template", "HomePage_ng_template_24_Template", "HomePage_div_25_Template", "HomePage_ion_fab_26_Template", "HomePage_Template_ion_item_click_34_listener"], "sources": ["C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\home\\home.page.ts", "C:\\D drive\\AIS-SMART CARE\\AISSmartCare_New\\AISSmartCare_New\\src\\app\\pages\\home\\home.page.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { MenuController } from '@ionic/angular';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.page.html',\n  styleUrls: ['./home.page.scss'],\n})\nexport class HomePage implements OnInit {\n\n  activeTab: string = 'home';\n  \n  menuItems = [\n    {\n      title: 'AIS Quality Policy',\n      description: 'Learn about our quality standards and policies',\n      icon: 'document-text',\n      color: 'primary',\n      route: '/videoplayer',\n      data: 'AIS Quality Policy'\n    },\n    {\n      title: 'Glass Unloading Guidelines',\n      description: 'Proper procedures for glass handling and unloading',\n      icon: 'cube',\n      color: 'secondary',\n      route: '/videoplayer',\n      data: 'Glass Unloading Guidelines'\n    },\n    {\n      title: 'Requirements to Register a Complaint',\n      description: 'What you need to know before registering a complaint',\n      icon: 'clipboard',\n      color: 'tertiary',\n      route: '/videoplayer',\n      data: 'Requirements to Register a Complaint'\n    },\n    {\n      title: 'Complaint Handling Procedure',\n      description: 'How we process and resolve your complaints',\n      icon: 'settings',\n      color: 'success',\n      route: '/videoplayer',\n      data: 'Complaint handling procedure'\n    },\n    {\n      title: 'Glass Installation Guideline',\n      description: 'Best practices for glass installation',\n      icon: 'construct',\n      color: 'warning',\n      route: '/videoplayer',\n      data: 'Glass Installation Guideline'\n    }\n  ];\n\n  constructor(\n    private router: Router,\n    private menuController: MenuController\n  ) { }\n\n  ngOnInit() {\n    this.menuController.enable(true);\n  }\n\n  onTabChange(tab: string) {\n    this.activeTab = tab;\n    switch (tab) {\n      case 'register':\n        this.router.navigate(['/register']);\n        break;\n      case 'track':\n        this.router.navigate(['/track']);\n        break;\n      case 'feedback':\n        this.router.navigate(['/feedback']);\n        break;\n      default:\n        this.activeTab = 'home';\n        break;\n    }\n  }\n\n  onCardClick(item: any) {\n    this.router.navigate([item.route], { \n      queryParams: { data: item.data } \n    });\n  }\n\n  openMenu() {\n    this.menuController.open();\n  }\n}\n", "<ion-header [translucent]=\"true\" class=\"material-header\">\n  <mat-toolbar class=\"material-toolbar\">\n    <button mat-icon-button (click)=\"openMenu()\" aria-label=\"Menu\">\n      <mat-icon>menu</mat-icon>\n    </button>\n    <span class=\"toolbar-title\">AIS Smart Care</span>\n    <span class=\"spacer\"></span>\n  </mat-toolbar>\n</ion-header>\n\n<ion-content [fullscreen]=\"true\" class=\"material-content\">\n  <!-- Hero Section -->\n  <div class=\"hero-section\">\n    <div class=\"hero-content\">\n      <h1 class=\"hero-title\">Welcome to AIS Smart Care</h1>\n      <p class=\"hero-subtitle\">Your comprehensive glass care solution</p>\n    </div>\n  </div>\n\n  <!-- Navigation Tabs -->\n  <div class=\"tab-navigation\">\n    <mat-tab-group \n      [selectedIndex]=\"activeTab === 'home' ? 0 : activeTab === 'register' ? 1 : activeTab === 'track' ? 2 : 3\"\n      (selectedTabChange)=\"onTabChange($event.index === 0 ? 'home' : $event.index === 1 ? 'register' : $event.index === 2 ? 'track' : 'feedback')\"\n      mat-stretch-tabs\n      class=\"custom-tabs\">\n      <mat-tab label=\"Home\">\n        <ng-template mat-tab-label>\n          <mat-icon class=\"tab-icon\">home</mat-icon>\n          Home\n        </ng-template>\n      </mat-tab>\n      <mat-tab label=\"Register\">\n        <ng-template mat-tab-label>\n          <mat-icon class=\"tab-icon\">add_circle</mat-icon>\n          Register\n        </ng-template>\n      </mat-tab>\n      <mat-tab label=\"Track\">\n        <ng-template mat-tab-label>\n          <mat-icon class=\"tab-icon\">track_changes</mat-icon>\n          Track\n        </ng-template>\n      </mat-tab>\n      <mat-tab label=\"Feedback\">\n        <ng-template mat-tab-label>\n          <mat-icon class=\"tab-icon\">feedback</mat-icon>\n          Feedback\n        </ng-template>\n      </mat-tab>\n    </mat-tab-group>\n  </div>\n\n  <!-- Content Section -->\n  <div class=\"content-section\" *ngIf=\"activeTab === 'home'\">\n    <div class=\"container\">\n      <h2 class=\"section-title\">Quick Access</h2>\n      <p class=\"section-subtitle\">Access important documents and guidelines</p>\n      \n      <div class=\"cards-grid\">\n        <mat-card \n          *ngFor=\"let item of menuItems; let i = index\" \n          class=\"info-card\"\n          matRipple\n          (click)=\"onCardClick(item)\"\n          [class]=\"'card-' + item.color\">\n          \n          <mat-card-header>\n            <div mat-card-avatar class=\"card-avatar\">\n              <ion-icon [name]=\"item.icon\" class=\"card-icon\"></ion-icon>\n            </div>\n            <mat-card-title class=\"card-title\">{{ item.title }}</mat-card-title>\n          </mat-card-header>\n          \n          <mat-card-content>\n            <p class=\"card-description\">{{ item.description }}</p>\n          </mat-card-content>\n          \n          <mat-card-actions align=\"end\">\n            <button mat-button [color]=\"item.color\">\n              <mat-icon>arrow_forward</mat-icon>\n              View\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n\n  <!-- Floating Action Button -->\n  <ion-fab vertical=\"bottom\" horizontal=\"end\" slot=\"fixed\" *ngIf=\"activeTab === 'home'\">\n    <ion-fab-button color=\"primary\" (click)=\"onTabChange('register')\">\n      <ion-icon name=\"add\"></ion-icon>\n    </ion-fab-button>\n  </ion-fab>\n</ion-content>\n\n<!-- Side Menu -->\n<ion-menu side=\"start\" menuId=\"main-menu\" contentId=\"main-content\">\n  <ion-header>\n    <ion-toolbar color=\"primary\">\n      <ion-title>Menu</ion-title>\n    </ion-toolbar>\n  </ion-header>\n  \n  <ion-content>\n    <ion-list>\n      <ion-item button (click)=\"onTabChange('home')\">\n        <ion-icon name=\"home\" slot=\"start\"></ion-icon>\n        <ion-label>Home</ion-label>\n      </ion-item>\n      \n      <ion-item button routerLink=\"/creditnote\">\n        <ion-icon name=\"document-text\" slot=\"start\"></ion-icon>\n        <ion-label>Credit Note</ion-label>\n      </ion-item>\n      \n      <ion-item button>\n        <ion-icon name=\"information-circle\" slot=\"start\"></ion-icon>\n        <ion-label>About</ion-label>\n      </ion-item>\n      \n      <ion-item button routerLink=\"/login\">\n        <ion-icon name=\"log-out\" slot=\"start\"></ion-icon>\n        <ion-label>Log Out</ion-label>\n      </ion-item>\n    </ion-list>\n  </ion-content>\n</ion-menu>\n"], "mappings": ";;;;;;;;;;;;IC4BUA,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1CH,EAAA,CAAAE,MAAA,aACF;;;;;IAIEF,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChDH,EAAA,CAAAE,MAAA,iBACF;;;;;IAIEF,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnDH,EAAA,CAAAE,MAAA,cACF;;;;;IAIEF,EAAA,CAAAC,cAAA,mBAA2B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9CH,EAAA,CAAAE,MAAA,iBACF;;;;;;IAYAF,EAAA,CAAAC,cAAA,mBAKiC;IAD/BD,EAAA,CAAAI,UAAA,mBAAAC,8DAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAG,WAAA,CAAAP,OAAA,CAAiB;IAAA,EAAC;IAIzBN,EADF,CAAAC,cAAA,sBAAiB,cAC0B;IACvCD,EAAA,CAAAc,SAAA,mBAA0D;IAC5Dd,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,yBAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACrDF,EADqD,CAAAG,YAAA,EAAiB,EACpD;IAGhBH,EADF,CAAAC,cAAA,uBAAkB,YACY;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IACpDF,EADoD,CAAAG,YAAA,EAAI,EACrC;IAIfH,EAFJ,CAAAC,cAAA,2BAA8B,kBACY,gBAC5B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAE,MAAA,cACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IAnBTH,EAAA,CAAAe,UAAA,WAAAT,OAAA,CAAAU,KAAA,CAA8B;IAIhBhB,EAAA,CAAAiB,SAAA,GAAkB;IAAlBjB,EAAA,CAAAkB,UAAA,SAAAZ,OAAA,CAAAa,IAAA,CAAkB;IAEKnB,EAAA,CAAAiB,SAAA,GAAgB;IAAhBjB,EAAA,CAAAoB,iBAAA,CAAAd,OAAA,CAAAe,KAAA,CAAgB;IAIvBrB,EAAA,CAAAiB,SAAA,GAAsB;IAAtBjB,EAAA,CAAAoB,iBAAA,CAAAd,OAAA,CAAAgB,WAAA,CAAsB;IAI/BtB,EAAA,CAAAiB,SAAA,GAAoB;IAApBjB,EAAA,CAAAkB,UAAA,UAAAZ,OAAA,CAAAU,KAAA,CAAoB;;;;;IAvB7ChB,EAFJ,CAAAC,cAAA,cAA0D,cACjC,aACK;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3CH,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAE,MAAA,gDAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEzEH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAuB,UAAA,IAAAC,mCAAA,wBAKiC;IAsBvCxB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IA1BmBH,EAAA,CAAAiB,SAAA,GAAc;IAAdjB,EAAA,CAAAkB,UAAA,YAAAR,MAAA,CAAAe,SAAA,CAAc;;;;;;IA8BrCzB,EADF,CAAAC,cAAA,kBAAsF,yBAClB;IAAlCD,EAAA,CAAAI,UAAA,mBAAAsB,6DAAA;MAAA1B,EAAA,CAAAO,aAAA,CAAAoB,GAAA;MAAA,MAAAjB,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAAAX,EAAA,CAAAY,WAAA,CAASF,MAAA,CAAAkB,WAAA,CAAY,UAAU,CAAC;IAAA,EAAC;IAC/D5B,EAAA,CAAAc,SAAA,mBAAgC;IAEpCd,EADE,CAAAG,YAAA,EAAiB,EACT;;;ADrFZ,OAAM,MAAO0B,QAAQ;EA+CnBC,YACUC,MAAc,EACdC,cAA8B;IAD9B,KAAAD,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IA/CxB,KAAAC,SAAS,GAAW,MAAM;IAE1B,KAAAR,SAAS,GAAG,CACV;MACEJ,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,gDAAgD;MAC7DH,IAAI,EAAE,eAAe;MACrBH,KAAK,EAAE,SAAS;MAChBkB,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE;KACP,EACD;MACEd,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE,oDAAoD;MACjEH,IAAI,EAAE,MAAM;MACZH,KAAK,EAAE,WAAW;MAClBkB,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE;KACP,EACD;MACEd,KAAK,EAAE,sCAAsC;MAC7CC,WAAW,EAAE,sDAAsD;MACnEH,IAAI,EAAE,WAAW;MACjBH,KAAK,EAAE,UAAU;MACjBkB,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE;KACP,EACD;MACEd,KAAK,EAAE,8BAA8B;MACrCC,WAAW,EAAE,4CAA4C;MACzDH,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAE,SAAS;MAChBkB,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE;KACP,EACD;MACEd,KAAK,EAAE,8BAA8B;MACrCC,WAAW,EAAE,uCAAuC;MACpDH,IAAI,EAAE,WAAW;MACjBH,KAAK,EAAE,SAAS;MAChBkB,KAAK,EAAE,cAAc;MACrBC,IAAI,EAAE;KACP,CACF;EAKG;EAEJC,QAAQA,CAAA;IACN,IAAI,CAACJ,cAAc,CAACK,MAAM,CAAC,IAAI,CAAC;EAClC;EAEAT,WAAWA,CAACU,GAAW;IACrB,IAAI,CAACL,SAAS,GAAGK,GAAG;IACpB,QAAQA,GAAG;MACT,KAAK,UAAU;QACb,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACnC;MACF,KAAK,OAAO;QACV,IAAI,CAACR,MAAM,CAACQ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;QAChC;MACF,KAAK,UAAU;QACb,IAAI,CAACR,MAAM,CAACQ,QAAQ,CAAC,CAAC,WAAW,CAAC,CAAC;QACnC;MACF;QACE,IAAI,CAACN,SAAS,GAAG,MAAM;QACvB;;EAEN;EAEApB,WAAWA,CAAC2B,IAAS;IACnB,IAAI,CAACT,MAAM,CAACQ,QAAQ,CAAC,CAACC,IAAI,CAACN,KAAK,CAAC,EAAE;MACjCO,WAAW,EAAE;QAAEN,IAAI,EAAEK,IAAI,CAACL;MAAI;KAC/B,CAAC;EACJ;EAEAO,QAAQA,CAAA;IACN,IAAI,CAACV,cAAc,CAACW,IAAI,EAAE;EAC5B;;;uBAlFWd,QAAQ,EAAA7B,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAAAG,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAARnB,QAAQ;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPjBvD,EAFJ,CAAAC,cAAA,oBAAyD,qBACjB,gBAC2B;UAAvCD,EAAA,CAAAI,UAAA,mBAAAqD,0CAAA;YAAA,OAASD,GAAA,CAAAd,QAAA,EAAU;UAAA,EAAC;UAC1C1C,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,WAAI;UAChBF,EADgB,CAAAG,YAAA,EAAW,EAClB;UACTH,EAAA,CAAAC,cAAA,cAA4B;UAAAD,EAAA,CAAAE,MAAA,qBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACjDH,EAAA,CAAAc,SAAA,cAA4B;UAEhCd,EADE,CAAAG,YAAA,EAAc,EACH;UAMPH,EAJN,CAAAC,cAAA,qBAA0D,aAE9B,cACE,aACD;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrDH,EAAA,CAAAC,cAAA,YAAyB;UAAAD,EAAA,CAAAE,MAAA,8CAAsC;UAEnEF,EAFmE,CAAAG,YAAA,EAAI,EAC/D,EACF;UAIJH,EADF,CAAAC,cAAA,eAA4B,yBAKJ;UAFpBD,EAAA,CAAAI,UAAA,+BAAAsD,8DAAAC,MAAA;YAAA,OAAqBH,GAAA,CAAA5B,WAAA,CAAA+B,MAAA,CAAAC,KAAA,KAA6B,CAAC,GAAG,MAAM,GAAAD,MAAA,CAAAC,KAAA,KAAoB,CAAC,GAAG,UAAU,GAAAD,MAAA,CAAAC,KAAA,KAAoB,CAAC,GAAG,OAAO,GAAG,UAAU,CAAC;UAAA,EAAC;UAG5I5D,EAAA,CAAAC,cAAA,mBAAsB;UACpBD,EAAA,CAAAuB,UAAA,KAAAsC,gCAAA,0BAA2B;UAI7B7D,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAC,cAAA,mBAA0B;UACxBD,EAAA,CAAAuB,UAAA,KAAAuC,gCAAA,0BAA2B;UAI7B9D,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAC,cAAA,mBAAuB;UACrBD,EAAA,CAAAuB,UAAA,KAAAwC,gCAAA,0BAA2B;UAI7B/D,EAAA,CAAAG,YAAA,EAAU;UACVH,EAAA,CAAAC,cAAA,mBAA0B;UACxBD,EAAA,CAAAuB,UAAA,KAAAyC,gCAAA,0BAA2B;UAMjChE,EAFI,CAAAG,YAAA,EAAU,EACI,EACZ;UAuCNH,EApCA,CAAAuB,UAAA,KAAA0C,wBAAA,kBAA0D,KAAAC,4BAAA,sBAoC4B;UAKxFlE,EAAA,CAAAG,YAAA,EAAc;UAMRH,EAHN,CAAAC,cAAA,oBAAmE,kBACrD,uBACmB,iBAChB;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAEnBF,EAFmB,CAAAG,YAAA,EAAY,EACf,EACH;UAITH,EAFJ,CAAAC,cAAA,mBAAa,gBACD,oBACuC;UAA9BD,EAAA,CAAAI,UAAA,mBAAA+D,6CAAA;YAAA,OAASX,GAAA,CAAA5B,WAAA,CAAY,MAAM,CAAC;UAAA,EAAC;UAC5C5B,EAAA,CAAAc,SAAA,oBAA8C;UAC9Cd,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,YAAI;UACjBF,EADiB,CAAAG,YAAA,EAAY,EAClB;UAEXH,EAAA,CAAAC,cAAA,oBAA0C;UACxCD,EAAA,CAAAc,SAAA,oBAAuD;UACvDd,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UACxBF,EADwB,CAAAG,YAAA,EAAY,EACzB;UAEXH,EAAA,CAAAC,cAAA,oBAAiB;UACfD,EAAA,CAAAc,SAAA,oBAA4D;UAC5Dd,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAClBF,EADkB,CAAAG,YAAA,EAAY,EACnB;UAEXH,EAAA,CAAAC,cAAA,oBAAqC;UACnCD,EAAA,CAAAc,SAAA,oBAAiD;UACjDd,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAI1BF,EAJ0B,CAAAG,YAAA,EAAY,EACrB,EACF,EACC,EACL;;;UAhICH,EAAA,CAAAkB,UAAA,qBAAoB;UAUnBlB,EAAA,CAAAiB,SAAA,GAAmB;UAAnBjB,EAAA,CAAAkB,UAAA,oBAAmB;UAY1BlB,EAAA,CAAAiB,SAAA,GAAyG;UAAzGjB,EAAA,CAAAkB,UAAA,kBAAAsC,GAAA,CAAAvB,SAAA,kBAAAuB,GAAA,CAAAvB,SAAA,sBAAAuB,GAAA,CAAAvB,SAAA,qBAAyG;UAgC/EjC,EAAA,CAAAiB,SAAA,GAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,SAAAsC,GAAA,CAAAvB,SAAA,YAA0B;UAoCEjC,EAAA,CAAAiB,SAAA,EAA0B;UAA1BjB,EAAA,CAAAkB,UAAA,SAAAsC,GAAA,CAAAvB,SAAA,YAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}